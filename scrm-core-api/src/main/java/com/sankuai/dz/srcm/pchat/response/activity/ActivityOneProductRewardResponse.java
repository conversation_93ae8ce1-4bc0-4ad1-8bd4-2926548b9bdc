package com.sankuai.dz.srcm.pchat.response.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/7/4 16:34
 * @Version v1.0.0
 */
@Data
public class ActivityOneProductRewardResponse implements Serializable {
    /**
     * 昵称
     */
    private String wxNickname;

    /**
     * 微信id
     */
    private String wxId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机号 中间4位*
     */
    private String phone;

    /**
     * 奖品记录id
     */
    private Long rewardRecordId;

    /**
     * 奖品中心奖品记录id
     */
    private Long lotteryPrizeRecordId;

    /**
     * 奖品id
     */
    private Long rewardProductId;

    /**
     * 奖品名称
     */
    private String rewardProductName;

    /**
     * 邀请人数
     */
    private Integer inviteCount;

    /**
     * 核销码
     */
    private String verifyCode;

    /**
     * 核销状态
     */
    private Integer verifyStatus;

    /**
     * 核销日期
     */
    private Long verifyDate;

    /**
     * 核销有效日期
     */
    private Long verifyExpireDate;
}
