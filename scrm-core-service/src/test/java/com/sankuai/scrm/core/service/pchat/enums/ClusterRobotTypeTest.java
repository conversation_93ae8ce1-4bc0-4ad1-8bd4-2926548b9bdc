package com.sankuai.scrm.core.service.pchat.enums;

import org.junit.Test;
import static org.junit.Assert.*;

public class ClusterRobotTypeTest {

    /**
     * 测试 fromCode 方法，当 code 为 null 时，应返回 null
     */
    @Test
    public void testFromCodeWhenCodeIsNull() throws Throwable {
        // arrange
        Integer code = null;
        // act
        ClusterRobotType result = ClusterRobotType.fromCode(code);
        // assert
        assertNull(result);
    }

    /**
     * 测试 fromCode 方法，当 code 在枚举值中不存在时，应返回 null
     */
    @Test
    public void testFromCodeWhenCodeNotExist() throws Throwable {
        // arrange
        Integer code = 999;
        // act
        ClusterRobotType result = ClusterRobotType.fromCode(code);
        // assert
        assertNull(result);
    }

    /**
     * 测试 fromCode 方法，当 code 在枚举值中存在时，应返回对应的枚举值
     */
    @Test
    public void testFromCodeWhenCodeExist() throws Throwable {
        // arrange
        Integer code = 1;
        // act
        ClusterRobotType result = ClusterRobotType.fromCode(code);
        // assert
        assertEquals(ClusterRobotType.TUO_GUAN, result);
    }
}
