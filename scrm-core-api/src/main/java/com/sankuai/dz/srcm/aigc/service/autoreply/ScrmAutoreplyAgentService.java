package com.sankuai.dz.srcm.aigc.service.autoreply;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.aigc.service.autoreply.request.AutoreplyCreateAgentRequest;

/**
 * <AUTHOR> on 2024/11/27 16:18
 */
public interface ScrmAutoreplyAgentService {

    // 主体绑定客服智能体
    RemoteResponse<Void> bindAgent(AutoreplyCreateAgentRequest request);

    /**
     * 查询主体下的客服智能体
     * 
     * @param appId appId
     * @return 绑定智能体时返回xiaomeiAppId，未绑定时返回空字符串
     */
    RemoteResponse<String> queryAgent(String appId);

}
