package com.sankuai.scrm.core.service.robot.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.robot.dal.entity.RobotAssigningTaskEntity;
import com.sankuai.scrm.core.service.robot.dal.example.RobotAssigningTaskEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RobotAssigningTaskEntityMapper extends MybatisBaseMapper<RobotAssigningTaskEntity, RobotAssigningTaskEntityExample, Long> {
    int batchInsert(@Param("list") List<RobotAssigningTaskEntity> list);
}