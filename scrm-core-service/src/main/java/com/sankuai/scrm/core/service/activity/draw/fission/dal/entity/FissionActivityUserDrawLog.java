package com.sankuai.scrm.core.service.activity.draw.fission.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: fission_activity_user_draw_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FissionActivityUserDrawLog {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: activity_id
     *   说明: 活动id
     */
    private Long activityId;

    /**
     *   字段: union_id
     *   说明: 抽奖用户的unionId
     */
    private String unionId;

    /**
     *   字段: prize_id
     *   说明: 奖品id
     */
    private Long prizeId;

    /**
     *   字段: draw_time
     *   说明: 抽奖时间
     */
    private Date drawTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}