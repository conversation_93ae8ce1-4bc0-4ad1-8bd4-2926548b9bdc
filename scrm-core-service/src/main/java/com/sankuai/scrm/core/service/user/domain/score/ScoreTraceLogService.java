package com.sankuai.scrm.core.service.user.domain.score;

import cn.hutool.core.bean.BeanUtil;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.dz.srcm.user.score.CalcScoreContext;
import com.sankuai.scrm.core.service.user.domain.tag.ScrmUserGrowthYimeiLiveUserTagEsDocFieldDesc;
import com.sankuai.scrm.core.service.user.domain.tag.UserTagEsCommonDomainService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/24 15:43
 * @Version v1.0.0
 */
@Slf4j
@Component
public class ScoreTraceLogService {

    private final String INDEX_NAME = "scrm_user_score_log";
    @Resource
    private UserTagEsCommonDomainService userTagEsCommonDomainService;

    public void collectLog(CalcScoreContext context, String type, Object score, String remark, Map<String, Object> extra) {
        collectLog(context, type, "default", score, remark, extra);
    }

    /**
     * 收集日志
     *
     * @param context
     * @param type
     * @param score
     * @param remark
     */
    public void collectLog(CalcScoreContext<ScrmUserGrowthYimeiLiveUserTag> context, String type, String subType, Object score, String remark, Map<String, Object> extra) {
        try {
            ScrmUserGrowthYimeiLiveUserTag userTag = context.getUserTags().get(0);
            LogObject logObject = LogObject.builder()
                    .batchId(context.getBatchId())
                    .taskId(context.getTaskId())
                    .projectId(userTag.getProjectId())
                    .unionId(userTag.getUnionId())
                    .wxId(userTag.getWxId())
                    .type(type)
                    .subType(subType)
                    .score(score)
                    .remark(remark)
                    .latestTime(new Date())
                    .build();
            if (Boolean.TRUE != context.getConfig().getPrintScoreToConsole()) {
                log.info("print_score uniqueKey={},obj={},extra={}", logObject.getUniqueKey(), logObject, extra);
            }
            if (Boolean.TRUE == context.getConfig().getPrintScoreToConsole()) {
                writeEs(logObject, extra);
            }
        } catch (Exception e) {
            log.error("ScoreTraceLogService.collectLog 异常, context:{}, type:{}, score:{}, remark:{}", context, type, score, remark, e);
        }
    }

    public void writeEs(LogObject logObject, Map<String, Object> extra) {
        try {
            Map<String, Object> stringObjectMap = BeanUtil.beanToMap(logObject);
            if (MapUtils.isNotEmpty(extra)) {
                stringObjectMap.putAll(extra);
            }
            userTagEsCommonDomainService.writeUserTagEsDoc(INDEX_NAME, logObject.getUniqueKey(), stringObjectMap);
        } catch (Exception e) {
            log.error("ScoreTraceLogService.writeEs 异常, logObject:{}", logObject, e);
        }
    }

    public List queryOneCaleScoreDetail(String projectId, String unionId, String wxId) {
        try {
            int pageNo = 1;
            int pageSize = 30;
            int maxPageNo = 30;
            Long lastTaskId = null;
            boolean firstValue = true;

            List resultList = new ArrayList();
            while (pageNo <= maxPageNo) {
                SearchResponse searchResponse = pageQuery(projectId, unionId, wxId, pageNo, pageSize);
                if (searchResponse == null || searchResponse.getHits() == null || searchResponse.getHits().getHits() == null || searchResponse.getHits().getHits().length == 0)
                    return resultList;

                SearchHits hits = searchResponse.getHits();
                SearchHit[] searchHits = hits.getHits();
                for (SearchHit hit : searchHits) {
                    Map<String, Object> result = new HashMap<>();
                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();

                    if (firstValue) {
                        lastTaskId = getLastTaskId(sourceAsMap);
                        firstValue = false;
                    } else {
                        Long taskId = getLastTaskId(sourceAsMap);
                        if (!Objects.equals(lastTaskId, taskId)) {
                            break;
                        }
                    }
                    result.put("_source", sourceAsMap);
                    result.put("_id", hit.getId());
                    resultList.add(result);
                }
                pageNo++;
            }
            return resultList;

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static Long getLastTaskId(Map<String, Object> sourceAsMap) {
        Object taskId = sourceAsMap.get("taskId");
        if (taskId != null) {
            return Long.valueOf(taskId.toString());
        }
        return null;
    }

    @Nullable
    private SearchResponse pageQuery(String projectId, String unionId, String wxId, Integer pageNo, Integer pageSize) throws IOException {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery(userTagEsCommonDomainService.wrapKeyword(ScrmUserGrowthYimeiLiveUserTagEsDocFieldDesc.projectId), projectId));
        if (StringUtils.isNotBlank(unionId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(userTagEsCommonDomainService.wrapKeyword(ScrmUserGrowthYimeiLiveUserTagEsDocFieldDesc.unionId), unionId));
        }
        if (StringUtils.isNotBlank(wxId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(userTagEsCommonDomainService.wrapKeyword(ScrmUserGrowthYimeiLiveUserTagEsDocFieldDesc.wxId), wxId));
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from((pageNo - 1) * pageSize);
        searchSourceBuilder.size(pageSize);
        searchSourceBuilder.sort(userTagEsCommonDomainService.wrapKeyword("taskId"), SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = userTagEsCommonDomainService.searchEs(searchRequest);
        if (searchResponse == null || searchResponse.getHits() == null) {
            return null;
        }
        return searchResponse;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LogObject {
        private String batchId;
        private String taskId;
        private String projectId;
        private String unionId;
        private String wxId;
        private String type;
        private String subType;
        private Object score;
        private String remark;
        private Date latestTime;
        private String uniqueKey;

        public String getUniqueKey() {
            return projectId + "_" + (StringUtils.isBlank(getUnionId()) ? getWxId() : getUnionId()) + "_" + System.currentTimeMillis();
        }
    }
}

