package com.sankuai.scrm.core.service.aigc.friday.domainservice;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;

/**
 * <AUTHOR> on 2024/08/27 17:44
 */
@Slf4j
@Component
public class ScrmFridayLockService {

    // taskId分布式锁，过期时间为60秒。
    private static final String SCRM_FRIDAY_TASK_ID_LOCK_CATEGORY = "scrm_friday_task_id_lock";
    private static final String LOCK_TYPE_TASK_ID = "TaskIdLock-";

    // 漏桶分布式锁，过期时间为1秒。
    private static final String SCRM_FRIDAY_LEAKY_BUCKET_LOCK_CATEGORY = "scrm_friday_leaky_bucket_lock";
    private static final String LOCK_TYPE_LEAKY_BUCKET = "LeakyBucketLock";

    @Autowired
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient;

    public void executeWithTaskIdLock(Long taskId, @Nonnull Runnable hasLockAction, @Nonnull Runnable noLockAction) {
        boolean lockAcquired = false;
        try {
            // 获取taskId的分布式锁
            lockAcquired = acquireFridayTaskIdLock(taskId);
            if (lockAcquired) {
                log.info("TaskId: {}. Acquire friday task id lock success.", taskId);
                hasLockAction.run();
            } else {
                log.info("TaskId: {}. Acquire friday task id lock fail.", taskId);
                noLockAction.run();
            }
        } catch (Exception e) {
            log.error("TaskId: {}. Execute with task id lock error.", taskId, e);
        } finally {
            if (lockAcquired) {
                try {
                    // 释放taskId的分布式锁
                    releaseFridayTaskIdLock(taskId);
                    log.info("TaskId: {}. Release friday task id lock success.", taskId);
                } catch (Exception e) {
                    log.error("TaskId: {}. Release friday task id lock error.", taskId, e);
                }
            }
        }
    }

    public boolean acquireFridayLeakyBucketLock(Long taskId) {
        StoreKey storeKey = new StoreKey(SCRM_FRIDAY_LEAKY_BUCKET_LOCK_CATEGORY, LOCK_TYPE_LEAKY_BUCKET);
        boolean lockAcquired = acquireScrmFridayLock(storeKey);
        log.info("TaskId: {}. Acquire friday leaky bucket lock {}.", taskId, lockAcquired ? "success" : "fail");
        return lockAcquired;
    }

    public void releaseFridayLeakyBucketLock(Long taskId) {
        StoreKey storeKey = new StoreKey(SCRM_FRIDAY_LEAKY_BUCKET_LOCK_CATEGORY, LOCK_TYPE_LEAKY_BUCKET);
        releaseScrmFridayLock(storeKey);
        log.info("TaskId: {}. Release friday leaky bucket lock success.", taskId);
    }

    private boolean acquireFridayTaskIdLock(Long taskId) {
        String taskIdLockKey = LOCK_TYPE_TASK_ID + taskId;
        StoreKey storeKey = new StoreKey(SCRM_FRIDAY_TASK_ID_LOCK_CATEGORY, taskIdLockKey);
        return acquireScrmFridayLock(storeKey);
    }

    private void releaseFridayTaskIdLock(Long taskId) {
        String taskIdLockKey = LOCK_TYPE_TASK_ID + taskId;
        StoreKey storeKey = new StoreKey(SCRM_FRIDAY_TASK_ID_LOCK_CATEGORY, taskIdLockKey);
        releaseScrmFridayLock(storeKey);
    }

    private boolean acquireScrmFridayLock(StoreKey storeKey) {
        return redisStoreClient.setnx(storeKey, System.currentTimeMillis());
    }

    private void releaseScrmFridayLock(StoreKey storeKey) {
        redisStoreClient.delete(storeKey);
    }

}
