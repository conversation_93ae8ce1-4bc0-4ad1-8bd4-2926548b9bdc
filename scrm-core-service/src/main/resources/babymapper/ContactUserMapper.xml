<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="ExternalUserId" jdbcType="VARCHAR" property="externalUserId" />
    <result column="UnionId" jdbcType="VARCHAR" property="unionId" />
    <result column="CorpId" jdbcType="VARCHAR" property="corpId" />
    <result column="OrgId" jdbcType="BIGINT" property="orgId" />
    <result column="StaffId" jdbcType="VARCHAR" property="staffId" />
    <result column="State" jdbcType="VARCHAR" property="state" />
    <result column="Status" jdbcType="INTEGER" property="status" />
    <result column="AddTime" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    Id, ExternalUserId, UnionId, CorpId, OrgId, StaffId, State, Status, AddTime, UpdateTime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Baby_OperatorHelper_ContactUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Baby_OperatorHelper_ContactUser
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Baby_OperatorHelper_ContactUser
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample">
    delete from Baby_OperatorHelper_ContactUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Baby_OperatorHelper_ContactUser (ExternalUserId, UnionId, CorpId, 
      OrgId, StaffId, State, 
      Status, AddTime, UpdateTime
      )
    values (#{externalUserId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=BIGINT}, #{staffId,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Baby_OperatorHelper_ContactUser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="externalUserId != null">
        ExternalUserId,
      </if>
      <if test="unionId != null">
        UnionId,
      </if>
      <if test="corpId != null">
        CorpId,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="staffId != null">
        StaffId,
      </if>
      <if test="state != null">
        State,
      </if>
      <if test="status != null">
        Status,
      </if>
      <if test="addTime != null">
        AddTime,
      </if>
      <if test="updateTime != null">
        UpdateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample" resultType="java.lang.Long">
    select count(*) from Baby_OperatorHelper_ContactUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Baby_OperatorHelper_ContactUser
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.externalUserId != null">
        ExternalUserId = #{record.externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        UnionId = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.corpId != null">
        CorpId = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        OrgId = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.staffId != null">
        StaffId = #{record.staffId,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        State = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        Status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        AddTime = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        UpdateTime = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Baby_OperatorHelper_ContactUser
    set Id = #{record.id,jdbcType=BIGINT},
      ExternalUserId = #{record.externalUserId,jdbcType=VARCHAR},
      UnionId = #{record.unionId,jdbcType=VARCHAR},
      CorpId = #{record.corpId,jdbcType=VARCHAR},
      OrgId = #{record.orgId,jdbcType=BIGINT},
      StaffId = #{record.staffId,jdbcType=VARCHAR},
      State = #{record.state,jdbcType=VARCHAR},
      Status = #{record.status,jdbcType=INTEGER},
      AddTime = #{record.addTime,jdbcType=TIMESTAMP},
      UpdateTime = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser">
    update Baby_OperatorHelper_ContactUser
    <set>
      <if test="externalUserId != null">
        ExternalUserId = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        UnionId = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        CorpId = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        StaffId = #{staffId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        State = #{state,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        Status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        AddTime = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser">
    update Baby_OperatorHelper_ContactUser
    set ExternalUserId = #{externalUserId,jdbcType=VARCHAR},
      UnionId = #{unionId,jdbcType=VARCHAR},
      CorpId = #{corpId,jdbcType=VARCHAR},
      OrgId = #{orgId,jdbcType=BIGINT},
      StaffId = #{staffId,jdbcType=VARCHAR},
      State = #{state,jdbcType=VARCHAR},
      Status = #{status,jdbcType=INTEGER},
      AddTime = #{addTime,jdbcType=TIMESTAMP},
      UpdateTime = #{updateTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="Id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Baby_OperatorHelper_ContactUser
    (ExternalUserId, UnionId, CorpId, OrgId, StaffId, State, Status, AddTime, UpdateTime
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.externalUserId,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, #{item.corpId,jdbcType=VARCHAR}, 
        #{item.orgId,jdbcType=BIGINT}, #{item.staffId,jdbcType=VARCHAR}, #{item.state,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>