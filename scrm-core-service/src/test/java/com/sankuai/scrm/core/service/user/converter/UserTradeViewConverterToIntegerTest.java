package com.sankuai.scrm.core.service.user.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.srcm.user.dto.PeriodTradeStat;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserTradeView;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import com.sankuai.dz.srcm.user.dto.UserTradeViewResult;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.MockedStatic;

// 启用 Mockito 支持
@ExtendWith(MockitoExtension.class)
public class UserTradeViewConverterToIntegerTest {

    /**
     * 使用反射调用私有方法 toInteger
     */
    private Integer invokePrivateToInteger(Integer input) throws Exception {
        Method method = UserIntendVisitViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        method.setAccessible(true);
        return (Integer) method.invoke(null, input);
    }

    /**
     * 测试 toInteger 方法，当输入为 null 时，返回 0
     */
    @Test
    public void testToInteger_WhenInputIsNull_ReturnsZero() throws Throwable {
        // Arrange
        Integer input = null;
        Integer expected = 0;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为非 null 整数时，返回原值
     */
    @Test
    public void testToInteger_WhenInputIsNotNull_ReturnsSameValue() throws Throwable {
        // Arrange
        Integer input = 42;
        Integer expected = 42;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为 0 时，返回 0
     */
    @Test
    public void testToInteger_WhenInputIsZero_ReturnsZero() throws Throwable {
        // Arrange
        Integer input = 0;
        Integer expected = 0;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为负数时，返回原值
     */
    @Test
    public void testToInteger_WhenInputIsNegative_ReturnsSameValue() throws Throwable {
        // Arrange
        Integer input = -10;
        Integer expected = -10;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    private Long invokeToLong(Long input) throws Exception {
        Method toLongMethod = UserTradeViewConverter.class.getDeclaredMethod("toLong", Long.class);
        toLongMethod.setAccessible(true);
        return (Long) toLongMethod.invoke(null, input);
    }

    @Test
    @DisplayName("toLong should return 0L when input is null")
    public void testToLongWithNullValue() throws Throwable {
        // arrange
        Long input = null;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(0L, result);
    }

    @Test
    @DisplayName("toLong should return the same value when input is positive")
    public void testToLongWithPositiveValue() throws Throwable {
        // arrange
        Long input = 123L;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(123L, result);
    }

    @Test
    @DisplayName("toLong should return the same value when input is negative")
    public void testToLongWithNegativeValue() throws Throwable {
        // arrange
        Long input = -456L;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(-456L, result);
    }

    @Test
    @DisplayName("toLong should return the same value when input is zero")
    public void testToLongWithZeroValue() throws Throwable {
        // arrange
        Long input = 0L;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(0L, result);
    }

    @Test
    @DisplayName("toLong should return the same value when input is Long.MAX_VALUE")
    public void testToLongWithMaxValue() throws Throwable {
        // arrange
        Long input = Long.MAX_VALUE;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(Long.MAX_VALUE, result);
    }

    @Test
    @DisplayName("toLong should return the same value when input is Long.MIN_VALUE")
    public void testToLongWithMinValue() throws Throwable {
        // arrange
        Long input = Long.MIN_VALUE;
        // act
        Long result = invokeToLong(input);
        // assert
        assertEquals(Long.MIN_VALUE, result);
    }

    private void invokeSetPeriodStats(UserTradeView userTradeView, List<PeriodTradeStat> periodStats) throws Exception {
        Method method = UserTradeViewConverter.class.getDeclaredMethod("setPeriodStats", UserTradeView.class, List.class);
        method.setAccessible(true);
        method.invoke(null, userTradeView, periodStats);
    }

    @Test
    public void testSetPeriodStatsWithNullList() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        // act
        invokeSetPeriodStats(userTradeView, null);
        // assert
        assertNotNull(userTradeView);
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade7dCnt());
        assertNull(userTradeView.getTradeHisCnt());
    }

    @Test
    public void testSetPeriodStatsWithEmptyList() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        // act
        invokeSetPeriodStats(userTradeView, Collections.emptyList());
        // assert
        assertNotNull(userTradeView);
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade7dCnt());
        assertNull(userTradeView.getTradeHisCnt());
    }

    @Test
    public void testSetPeriodStatsWithNullPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod(null);
        stat.setTradeCnt(10L);
        stat.setTradeAmt(BigDecimal.valueOf(100));
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade3dCnt());
        assertNull(userTradeView.getTradeHisCnt());
    }

    @Test
    public void testSetPeriodStatsWith1dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("1d");
        stat.setTradeCnt(10L);
        stat.setTradeAmt(BigDecimal.valueOf(100));
        stat.setActualPayAmt(BigDecimal.valueOf(90));
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(10L, userTradeView.getTrade1dCnt());
        assertEquals(BigDecimal.valueOf(100), userTradeView.getTrade1dAmt());
        assertEquals(BigDecimal.valueOf(90), userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testSetPeriodStatsWith3dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("3d");
        stat.setTradeCnt(30L);
        stat.setTradeAmt(BigDecimal.valueOf(300));
        stat.setActualPayAmt(BigDecimal.valueOf(270));
        stat.setTradeDays(3);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(30L, userTradeView.getTrade3dCnt());
        assertEquals(BigDecimal.valueOf(300), userTradeView.getTrade3dAmt());
        assertEquals(BigDecimal.valueOf(270), userTradeView.getTrade3dActualPayAmt());
        assertEquals(3, userTradeView.getTrade3dDays());
    }

    @Test
    public void testSetPeriodStatsWith5dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("5d");
        stat.setTradeCnt(50L);
        stat.setTradeAmt(BigDecimal.valueOf(500));
        stat.setActualPayAmt(BigDecimal.valueOf(450));
        stat.setTradeDays(4);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(50L, userTradeView.getTrade5dCnt());
        assertEquals(BigDecimal.valueOf(500), userTradeView.getTrade5dAmt());
        assertEquals(BigDecimal.valueOf(450), userTradeView.getTrade5dActualPayAmt());
        assertEquals(4, userTradeView.getTrade5dDays());
    }

    @Test
    public void testSetPeriodStatsWith7dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("7d");
        stat.setTradeCnt(70L);
        stat.setTradeAmt(BigDecimal.valueOf(700));
        stat.setActualPayAmt(BigDecimal.valueOf(630));
        stat.setTradeDays(5);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(70L, userTradeView.getTrade7dCnt());
        assertEquals(BigDecimal.valueOf(700), userTradeView.getTrade7dAmt());
        assertEquals(BigDecimal.valueOf(630), userTradeView.getTrade7dActualPayAmt());
        assertEquals(5, userTradeView.getTrade7dDays());
    }

    @Test
    public void testSetPeriodStatsWith15dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("15d");
        stat.setTradeCnt(150L);
        stat.setTradeAmt(BigDecimal.valueOf(1500));
        stat.setActualPayAmt(BigDecimal.valueOf(1350));
        stat.setTradeDays(10);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(150L, userTradeView.getTrade15dCnt());
        assertEquals(BigDecimal.valueOf(1500), userTradeView.getTrade15dAmt());
        assertEquals(BigDecimal.valueOf(1350), userTradeView.getTrade15dActualPayAmt());
        assertEquals(10, userTradeView.getTrade15dDays());
    }

    @Test
    public void testSetPeriodStatsWith30dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("30d");
        stat.setTradeCnt(300L);
        stat.setTradeAmt(BigDecimal.valueOf(3000));
        stat.setActualPayAmt(BigDecimal.valueOf(2700));
        stat.setTradeDays(20);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(300L, userTradeView.getTrade30dCnt());
        assertEquals(BigDecimal.valueOf(3000), userTradeView.getTrade30dAmt());
        assertEquals(BigDecimal.valueOf(2700), userTradeView.getTrade30dActualPayAmt());
        assertEquals(20, userTradeView.getTrade30dDays());
    }

    @Test
    public void testSetPeriodStatsWith60dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("60d");
        stat.setTradeCnt(600L);
        stat.setTradeAmt(BigDecimal.valueOf(6000));
        stat.setActualPayAmt(BigDecimal.valueOf(5400));
        stat.setTradeDays(40);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(600L, userTradeView.getTrade60dCnt());
        assertEquals(BigDecimal.valueOf(6000), userTradeView.getTrade60dAmt());
        assertEquals(BigDecimal.valueOf(5400), userTradeView.getTrade60dActualPayAmt());
        assertEquals(40, userTradeView.getTrade60dDays());
    }

    @Test
    public void testSetPeriodStatsWith90dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("90d");
        stat.setTradeCnt(900L);
        stat.setTradeAmt(BigDecimal.valueOf(9000));
        stat.setActualPayAmt(BigDecimal.valueOf(8100));
        stat.setTradeDays(60);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(900L, userTradeView.getTrade90dCnt());
        assertEquals(BigDecimal.valueOf(9000), userTradeView.getTrade90dAmt());
        assertEquals(BigDecimal.valueOf(8100), userTradeView.getTrade90dActualPayAmt());
        assertEquals(60, userTradeView.getTrade90dDays());
    }

    @Test
    public void testSetPeriodStatsWith180dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("180d");
        stat.setTradeCnt(1800L);
        stat.setTradeAmt(BigDecimal.valueOf(18000));
        stat.setActualPayAmt(BigDecimal.valueOf(16200));
        stat.setTradeDays(120);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(1800L, userTradeView.getTrade180dCnt());
        assertEquals(BigDecimal.valueOf(18000), userTradeView.getTrade180dAmt());
        assertEquals(BigDecimal.valueOf(16200), userTradeView.getTrade180dActualPayAmt());
        assertEquals(120, userTradeView.getTrade180dDays());
    }

    @Test
    public void testSetPeriodStatsWith360dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("360d");
        stat.setTradeCnt(3600L);
        stat.setTradeAmt(BigDecimal.valueOf(36000));
        stat.setActualPayAmt(BigDecimal.valueOf(32400));
        stat.setTradeDays(240);
        stat.setFirstTradeDate("2022-01-01");
        stat.setFirstTradeDays(30);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(3600L, userTradeView.getTrade360dCnt());
        assertEquals(BigDecimal.valueOf(36000), userTradeView.getTrade360dAmt());
        assertEquals(BigDecimal.valueOf(32400), userTradeView.getTrade360dActualPayAmt());
        assertEquals(240, userTradeView.getTrade360dDays());
        assertEquals("2022-01-01", userTradeView.getFirst360dTradeDate());
        assertEquals(30, userTradeView.getFirst2td360dTradeDays());
    }

    @Test
    public void testSetPeriodStatsWith365dPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("365d");
        stat.setTradeCnt(3650L);
        stat.setTradeAmt(BigDecimal.valueOf(36500));
        stat.setActualPayAmt(BigDecimal.valueOf(32850));
        stat.setTradeDays(250);
        stat.setFirstTradeDate("2022-02-01");
        stat.setFirstTradeDays(35);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(3650L, userTradeView.getTrade365dCnt());
        assertEquals(BigDecimal.valueOf(36500), userTradeView.getTrade365dAmt());
        assertEquals(BigDecimal.valueOf(32850), userTradeView.getTrade365dActualPayAmt());
        assertEquals(250, userTradeView.getTrade365dDays());
        assertEquals("2022-02-01", userTradeView.getFirst365dTradeDate());
        assertEquals(35, userTradeView.getFirst2td365dTradeDays());
    }

    @Test
    public void testSetPeriodStatsWithHisPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("his");
        stat.setTradeCnt(5000L);
        stat.setTradeAmt(BigDecimal.valueOf(50000));
        stat.setActualPayAmt(BigDecimal.valueOf(45000));
        stat.setTradeDays(300);
        stat.setFirstTradeDate("2021-01-01");
        stat.setFirstTradeDays(500);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(5000L, userTradeView.getTradeHisCnt());
        assertEquals(BigDecimal.valueOf(50000), userTradeView.getTradeHisAmt());
        assertEquals(BigDecimal.valueOf(45000), userTradeView.getTradeHisActualPayAmt());
        assertEquals(300, userTradeView.getTradeHisDays());
        assertEquals("2021-01-01", userTradeView.getFirstHisTradeDate());
        assertEquals(500, userTradeView.getFirst2tdHisTradeDays());
    }

    @Test
    public void testSetPeriodStatsWithUnknownPeriod() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("unknown");
        stat.setTradeCnt(100L);
        stat.setTradeAmt(BigDecimal.valueOf(1000));
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade3dCnt());
        assertNull(userTradeView.getTradeHisCnt());
    }

    @Test
    public void testSetPeriodStatsWithNullValues() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("1d");
        stat.setTradeCnt(null);
        stat.setTradeAmt(null);
        stat.setActualPayAmt(null);
        // act
        invokeSetPeriodStats(userTradeView, Collections.singletonList(stat));
        // assert
        assertEquals(0L, userTradeView.getTrade1dCnt());
        assertEquals(BigDecimal.ZERO, userTradeView.getTrade1dAmt());
        assertEquals(BigDecimal.ZERO, userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testSetPeriodStatsWithMultiplePeriods() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat1d = new PeriodTradeStat();
        stat1d.setPeriod("1d");
        stat1d.setTradeCnt(10L);
        stat1d.setTradeAmt(BigDecimal.valueOf(100));
        stat1d.setActualPayAmt(BigDecimal.valueOf(90));
        PeriodTradeStat stat7d = new PeriodTradeStat();
        stat7d.setPeriod("7d");
        stat7d.setTradeCnt(70L);
        stat7d.setTradeAmt(BigDecimal.valueOf(700));
        stat7d.setActualPayAmt(BigDecimal.valueOf(630));
        stat7d.setTradeDays(5);
        PeriodTradeStat statHis = new PeriodTradeStat();
        statHis.setPeriod("his");
        statHis.setTradeCnt(5000L);
        statHis.setTradeAmt(BigDecimal.valueOf(50000));
        statHis.setActualPayAmt(BigDecimal.valueOf(45000));
        statHis.setTradeDays(300);
        statHis.setFirstTradeDate("2021-01-01");
        statHis.setFirstTradeDays(500);
        // act
        invokeSetPeriodStats(userTradeView, Arrays.asList(stat1d, stat7d, statHis));
        // assert
        // 1d period assertions
        assertEquals(10L, userTradeView.getTrade1dCnt());
        assertEquals(BigDecimal.valueOf(100), userTradeView.getTrade1dAmt());
        assertEquals(BigDecimal.valueOf(90), userTradeView.getTrade1dActualPayAmt());
        // 7d period assertions
        assertEquals(70L, userTradeView.getTrade7dCnt());
        assertEquals(BigDecimal.valueOf(700), userTradeView.getTrade7dAmt());
        assertEquals(BigDecimal.valueOf(630), userTradeView.getTrade7dActualPayAmt());
        assertEquals(5, userTradeView.getTrade7dDays());
        // his period assertions
        assertEquals(5000L, userTradeView.getTradeHisCnt());
        assertEquals(BigDecimal.valueOf(50000), userTradeView.getTradeHisAmt());
        assertEquals(BigDecimal.valueOf(45000), userTradeView.getTradeHisActualPayAmt());
        assertEquals(300, userTradeView.getTradeHisDays());
        assertEquals("2021-01-01", userTradeView.getFirstHisTradeDate());
        assertEquals(500, userTradeView.getFirst2tdHisTradeDays());
    }

    @Test
    public void testSetPeriodStatsWithMixedValidAndInvalidPeriods() throws Throwable {
        // arrange
        UserTradeView userTradeView = new UserTradeView();
        PeriodTradeStat stat1d = new PeriodTradeStat();
        stat1d.setPeriod("1d");
        stat1d.setTradeCnt(10L);
        stat1d.setTradeAmt(BigDecimal.valueOf(100));
        stat1d.setActualPayAmt(BigDecimal.valueOf(90));
        PeriodTradeStat statNull = new PeriodTradeStat();
        statNull.setPeriod(null);
        statNull.setTradeCnt(999L);
        PeriodTradeStat statUnknown = new PeriodTradeStat();
        statUnknown.setPeriod("unknown");
        statUnknown.setTradeCnt(888L);
        PeriodTradeStat stat30d = new PeriodTradeStat();
        stat30d.setPeriod("30d");
        stat30d.setTradeCnt(300L);
        stat30d.setTradeAmt(BigDecimal.valueOf(3000));
        stat30d.setActualPayAmt(BigDecimal.valueOf(2700));
        stat30d.setTradeDays(20);
        // act
        invokeSetPeriodStats(userTradeView, Arrays.asList(stat1d, statNull, statUnknown, stat30d));
        // assert
        // 1d period assertions
        assertEquals(10L, userTradeView.getTrade1dCnt());
        assertEquals(BigDecimal.valueOf(100), userTradeView.getTrade1dAmt());
        assertEquals(BigDecimal.valueOf(90), userTradeView.getTrade1dActualPayAmt());
        // 30d period assertions
        assertEquals(300L, userTradeView.getTrade30dCnt());
        assertEquals(BigDecimal.valueOf(3000), userTradeView.getTrade30dAmt());
        assertEquals(BigDecimal.valueOf(2700), userTradeView.getTrade30dActualPayAmt());
        assertEquals(20, userTradeView.getTrade30dDays());
        // Verify that invalid periods didn't set any values
        assertNull(userTradeView.getTrade15dCnt());
        assertNull(userTradeView.getTradeHisCnt());
    }

    private PeriodTradeStat createPeriodStat(String period, Long tradeCnt, BigDecimal tradeAmt, BigDecimal actualPayAmt, Integer tradeDays, String firstTradeDate, Integer firstTradeDays) {
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod(period);
        stat.setTradeCnt(tradeCnt);
        stat.setTradeAmt(tradeAmt);
        stat.setActualPayAmt(actualPayAmt);
        stat.setTradeDays(tradeDays);
        stat.setFirstTradeDate(firstTradeDate);
        stat.setFirstTradeDays(firstTradeDays);
        return stat;
    }

    @Test
    public void testConvertToUserTradeViewWithNullInput() {
        // arrange
        UserTradeViewResult result = null;
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNull(userTradeView);
    }

    @Test
    public void testConvertToUserTradeViewWithCompleteInput() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        result.setCChain(1);
        result.setAppId("app123");
        result.setPartitionDate("20230101");
        result.setCalDim("dim1");
        result.setBizlnBuCode(100);
        result.setBizlnBuName("Business Unit");
        result.setLastMtShopId(789L);
        result.setLastDpShopId(456L);
        result.setLastPoiId(123L);
        result.setLastOrderId("order123");
        result.setLastBuySucTime("2023-01-01 12:00:00");
        result.setLastBuySucDate("2023-01-01");
        result.setLast2tdBuySucDays(5);
        result.setLastOrderAmt(new BigDecimal("100.50"));
        result.setLastActualPayAmt(new BigDecimal("95.25"));
        result.setPenultBuySucTime("2022-12-25 10:00:00");
        result.setLastProductgroupPk("product123");
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add period stats for testing
        PeriodTradeStat stat1d = createPeriodStat("1d", 2L, new BigDecimal("200.00"), new BigDecimal("190.00"), 1, null, null);
        periodStats.add(stat1d);
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        assertEquals(1, userTradeView.getCChain());
        assertEquals("app123", userTradeView.getAppId());
        assertEquals("20230101", userTradeView.getPartitionDate());
        assertEquals("dim1", userTradeView.getCalDim());
        assertEquals(100, userTradeView.getBizlnBuCode());
        assertEquals("Business Unit", userTradeView.getBizlnBuName());
        assertEquals(789L, userTradeView.getLastMtShopId());
        assertEquals(456L, userTradeView.getLastDpShopId());
        assertEquals(123L, userTradeView.getLastPoiId());
        assertEquals("order123", userTradeView.getLastOrderId());
        assertEquals("2023-01-01 12:00:00", userTradeView.getLastBuySucTime());
        assertEquals("2023-01-01", userTradeView.getLastBuySucDate());
        assertEquals(5, userTradeView.getLast2tdBuySucDays());
        assertEquals(new BigDecimal("100.50"), userTradeView.getLastOrderAmt());
        assertEquals(new BigDecimal("95.25"), userTradeView.getLastActualPayAmt());
        assertEquals("2022-12-25 10:00:00", userTradeView.getPenultBuySucTime());
        assertEquals("product123", userTradeView.getLastProductgroupPk());
        // Check period stats
        assertEquals(2L, userTradeView.getTrade1dCnt());
        assertEquals(new BigDecimal("200.00"), userTradeView.getTrade1dAmt());
        assertEquals(new BigDecimal("190.00"), userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithNullFields() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        // Leave other fields as null
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        assertNull(userTradeView.getAppId());
        assertNull(userTradeView.getPartitionDate());
        assertNull(userTradeView.getCalDim());
        assertNull(userTradeView.getBizlnBuCode());
        assertNull(userTradeView.getBizlnBuName());
        assertNull(userTradeView.getLastMtShopId());
        assertNull(userTradeView.getLastDpShopId());
        assertNull(userTradeView.getLastPoiId());
        assertNull(userTradeView.getLastOrderId());
        assertNull(userTradeView.getLastBuySucTime());
        assertNull(userTradeView.getLastBuySucDate());
        assertEquals(0, userTradeView.getLast2tdBuySucDays());
        assertEquals(BigDecimal.ZERO, userTradeView.getLastOrderAmt());
        assertEquals(BigDecimal.ZERO, userTradeView.getLastActualPayAmt());
        assertNull(userTradeView.getPenultBuySucTime());
        assertNull(userTradeView.getLastProductgroupPk());
    }

    @Test
    public void testConvertToUserTradeViewWithEmptyPeriodStats() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        result.setPeriodStats(Collections.emptyList());
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        // All period stats should be default values
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade1dAmt());
        assertNull(userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithNullPeriodStats() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        result.setPeriodStats(null);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        // All period stats should be default values
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade1dAmt());
        assertNull(userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithAllPeriodTypes() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add all period types for testing
        periodStats.add(createPeriodStat("1d", 1L, new BigDecimal("100"), new BigDecimal("90"), 1, null, null));
        periodStats.add(createPeriodStat("3d", 3L, new BigDecimal("300"), new BigDecimal("290"), 2, null, null));
        periodStats.add(createPeriodStat("5d", 5L, new BigDecimal("500"), new BigDecimal("490"), 3, null, null));
        periodStats.add(createPeriodStat("7d", 7L, new BigDecimal("700"), new BigDecimal("690"), 4, null, null));
        periodStats.add(createPeriodStat("15d", 15L, new BigDecimal("1500"), new BigDecimal("1490"), 5, null, null));
        periodStats.add(createPeriodStat("30d", 30L, new BigDecimal("3000"), new BigDecimal("2990"), 10, null, null));
        periodStats.add(createPeriodStat("60d", 60L, new BigDecimal("6000"), new BigDecimal("5990"), 20, null, null));
        periodStats.add(createPeriodStat("90d", 90L, new BigDecimal("9000"), new BigDecimal("8990"), 30, null, null));
        periodStats.add(createPeriodStat("180d", 180L, new BigDecimal("18000"), new BigDecimal("17990"), 60, null, null));
        periodStats.add(createPeriodStat("360d", 360L, new BigDecimal("36000"), new BigDecimal("35990"), 90, "2022-01-01", 359));
        periodStats.add(createPeriodStat("365d", 365L, new BigDecimal("36500"), new BigDecimal("36490"), 100, "2022-01-01", 364));
        periodStats.add(createPeriodStat("his", 1000L, new BigDecimal("100000"), new BigDecimal("99990"), 200, "2020-01-01", 1000));
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        // Check 1d period
        assertEquals(1L, userTradeView.getTrade1dCnt());
        assertEquals(new BigDecimal("100"), userTradeView.getTrade1dAmt());
        assertEquals(new BigDecimal("90"), userTradeView.getTrade1dActualPayAmt());
        // Check 3d period
        assertEquals(3L, userTradeView.getTrade3dCnt());
        assertEquals(new BigDecimal("300"), userTradeView.getTrade3dAmt());
        assertEquals(new BigDecimal("290"), userTradeView.getTrade3dActualPayAmt());
        assertEquals(2, userTradeView.getTrade3dDays());
        // Check 5d period
        assertEquals(5L, userTradeView.getTrade5dCnt());
        assertEquals(new BigDecimal("500"), userTradeView.getTrade5dAmt());
        assertEquals(new BigDecimal("490"), userTradeView.getTrade5dActualPayAmt());
        assertEquals(3, userTradeView.getTrade5dDays());
        // Check 7d period
        assertEquals(7L, userTradeView.getTrade7dCnt());
        assertEquals(new BigDecimal("700"), userTradeView.getTrade7dAmt());
        assertEquals(new BigDecimal("690"), userTradeView.getTrade7dActualPayAmt());
        assertEquals(4, userTradeView.getTrade7dDays());
        // Check 15d period
        assertEquals(15L, userTradeView.getTrade15dCnt());
        assertEquals(new BigDecimal("1500"), userTradeView.getTrade15dAmt());
        assertEquals(new BigDecimal("1490"), userTradeView.getTrade15dActualPayAmt());
        assertEquals(5, userTradeView.getTrade15dDays());
        // Check 30d period
        assertEquals(30L, userTradeView.getTrade30dCnt());
        assertEquals(new BigDecimal("3000"), userTradeView.getTrade30dAmt());
        assertEquals(new BigDecimal("2990"), userTradeView.getTrade30dActualPayAmt());
        assertEquals(10, userTradeView.getTrade30dDays());
        // Check 60d period
        assertEquals(60L, userTradeView.getTrade60dCnt());
        assertEquals(new BigDecimal("6000"), userTradeView.getTrade60dAmt());
        assertEquals(new BigDecimal("5990"), userTradeView.getTrade60dActualPayAmt());
        assertEquals(20, userTradeView.getTrade60dDays());
        // Check 90d period
        assertEquals(90L, userTradeView.getTrade90dCnt());
        assertEquals(new BigDecimal("9000"), userTradeView.getTrade90dAmt());
        assertEquals(new BigDecimal("8990"), userTradeView.getTrade90dActualPayAmt());
        assertEquals(30, userTradeView.getTrade90dDays());
        // Check 180d period
        assertEquals(180L, userTradeView.getTrade180dCnt());
        assertEquals(new BigDecimal("18000"), userTradeView.getTrade180dAmt());
        assertEquals(new BigDecimal("17990"), userTradeView.getTrade180dActualPayAmt());
        assertEquals(60, userTradeView.getTrade180dDays());
        // Check 360d period
        assertEquals(360L, userTradeView.getTrade360dCnt());
        assertEquals(new BigDecimal("36000"), userTradeView.getTrade360dAmt());
        assertEquals(new BigDecimal("35990"), userTradeView.getTrade360dActualPayAmt());
        assertEquals(90, userTradeView.getTrade360dDays());
        assertEquals("2022-01-01", userTradeView.getFirst360dTradeDate());
        assertEquals(359, userTradeView.getFirst2td360dTradeDays());
        // Check 365d period
        assertEquals(365L, userTradeView.getTrade365dCnt());
        assertEquals(new BigDecimal("36500"), userTradeView.getTrade365dAmt());
        assertEquals(new BigDecimal("36490"), userTradeView.getTrade365dActualPayAmt());
        assertEquals(100, userTradeView.getTrade365dDays());
        assertEquals("2022-01-01", userTradeView.getFirst365dTradeDate());
        assertEquals(364, userTradeView.getFirst2td365dTradeDays());
        // Check his period
        assertEquals(1000L, userTradeView.getTradeHisCnt());
        assertEquals(new BigDecimal("100000"), userTradeView.getTradeHisAmt());
        assertEquals(new BigDecimal("99990"), userTradeView.getTradeHisActualPayAmt());
        assertEquals(200, userTradeView.getTradeHisDays());
        assertEquals("2020-01-01", userTradeView.getFirstHisTradeDate());
        assertEquals(1000, userTradeView.getFirst2tdHisTradeDays());
    }

    @Test
    public void testConvertToUserTradeViewWithNullValuesInPeriodStats() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add period stats with null values
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("1d");
        stat.setTradeCnt(null);
        stat.setTradeAmt(null);
        stat.setActualPayAmt(null);
        periodStats.add(stat);
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        assertEquals(0L, userTradeView.getTrade1dCnt());
        assertEquals(BigDecimal.ZERO, userTradeView.getTrade1dAmt());
        assertEquals(BigDecimal.ZERO, userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithNullPeriod() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add period stat with null period
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod(null);
        stat.setTradeCnt(10L);
        stat.setTradeAmt(new BigDecimal("1000"));
        stat.setActualPayAmt(new BigDecimal("990"));
        periodStats.add(stat);
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        // No period stats should be set
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade1dAmt());
        assertNull(userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithUnknownPeriod() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add period stat with unknown period
        PeriodTradeStat stat = new PeriodTradeStat();
        stat.setPeriod("unknown");
        stat.setTradeCnt(10L);
        stat.setTradeAmt(new BigDecimal("1000"));
        stat.setActualPayAmt(new BigDecimal("990"));
        periodStats.add(stat);
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        assertEquals(123456L, userTradeView.getUserId());
        // No period stats should be set
        assertNull(userTradeView.getTrade1dCnt());
        assertNull(userTradeView.getTrade1dAmt());
        assertNull(userTradeView.getTrade1dActualPayAmt());
    }

    @Test
    public void testConvertToUserTradeViewWithMixedPeriodTypes() {
        // arrange
        UserTradeViewResult result = new UserTradeViewResult();
        result.setUserId(123456L);
        List<PeriodTradeStat> periodStats = new ArrayList<>();
        // Add valid period
        periodStats.add(createPeriodStat("1d", 1L, new BigDecimal("100"), new BigDecimal("90"), 1, null, null));
        // Add invalid period
        periodStats.add(createPeriodStat("invalid", 999L, new BigDecimal("9999"), new BigDecimal("9990"), 10, null, null));
        // Add null period
        PeriodTradeStat nullPeriod = new PeriodTradeStat();
        nullPeriod.setTradeCnt(888L);
        periodStats.add(nullPeriod);
        result.setPeriodStats(periodStats);
        // act
        UserTradeView userTradeView = UserTradeViewConverter.convertToUserTradeView(result);
        // assert
        assertNotNull(userTradeView);
        // Only valid period should be set
        assertEquals(1L, userTradeView.getTrade1dCnt());
        assertEquals(new BigDecimal("100"), userTradeView.getTrade1dAmt());
        assertEquals(new BigDecimal("90"), userTradeView.getTrade1dActualPayAmt());
        // Other periods should not be set
        assertNull(userTradeView.getTrade3dCnt());
        assertNull(userTradeView.getTrade3dAmt());
    }

    @Test
    public void testConvertToUserTradeViewListWithNullInput() {
        // arrange - 无需准备，直接传入null
        // act
        List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(null);
        // assert
        assertNull(result, "当输入为null时，返回结果应为null");
    }

    @Test
    public void testConvertToUserTradeViewListWithEmptyList() {
        // arrange
        List<UserTradeViewResult> emptyList = Collections.emptyList();
        // act
        List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(emptyList);
        // assert
        assertNull(result, "当输入为空列表时，返回结果应为null");
    }

    @Test
    public void testConvertToUserTradeViewListWithSingleElement() {
        // arrange
        UserTradeViewResult input = new UserTradeViewResult();
        input.setUserId(123L);
        List<UserTradeViewResult> inputList = Collections.singletonList(input);
        try (MockedStatic<UserTradeViewConverter> mocked = mockStatic(UserTradeViewConverter.class)) {
            UserTradeView expectedView = new UserTradeView();
            expectedView.setUserId(123L);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(any(UserTradeViewResult.class))).thenReturn(expectedView);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeViewList(anyList())).thenCallRealMethod();
            // act
            List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(inputList);
            // assert
            assertNotNull(result, "返回结果不应为null");
            assertEquals(1, result.size(), "返回列表大小应为1");
            assertEquals(123L, result.get(0).getUserId(), "用户ID应正确转换");
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(input));
        }
    }

    @Test
    public void testConvertToUserTradeViewListWithMultipleElements() {
        // arrange
        UserTradeViewResult input1 = new UserTradeViewResult();
        input1.setUserId(123L);
        UserTradeViewResult input2 = new UserTradeViewResult();
        input2.setUserId(456L);
        List<UserTradeViewResult> inputList = Arrays.asList(input1, input2);
        try (MockedStatic<UserTradeViewConverter> mocked = mockStatic(UserTradeViewConverter.class)) {
            UserTradeView view1 = new UserTradeView();
            view1.setUserId(123L);
            UserTradeView view2 = new UserTradeView();
            view2.setUserId(456L);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(input1)).thenReturn(view1);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(input2)).thenReturn(view2);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeViewList(anyList())).thenCallRealMethod();
            // act
            List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(inputList);
            // assert
            assertNotNull(result, "返回结果不应为null");
            assertEquals(2, result.size(), "返回列表大小应为2");
            assertEquals(123L, result.get(0).getUserId(), "第一个元素的用户ID应正确转换");
            assertEquals(456L, result.get(1).getUserId(), "第二个元素的用户ID应正确转换");
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(input1));
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(input2));
        }
    }

    @Test
    public void testConvertToUserTradeViewListWithNullElement() {
        // arrange
        UserTradeViewResult input = new UserTradeViewResult();
        input.setUserId(123L);
        List<UserTradeViewResult> inputList = Arrays.asList(input, null);
        try (MockedStatic<UserTradeViewConverter> mocked = mockStatic(UserTradeViewConverter.class)) {
            UserTradeView view = new UserTradeView();
            view.setUserId(123L);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(input)).thenReturn(view);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(null)).thenReturn(null);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeViewList(anyList())).thenCallRealMethod();
            // act
            List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(inputList);
            // assert
            assertNotNull(result, "返回结果不应为null");
            assertEquals(2, result.size(), "返回列表大小应为2");
            assertNotNull(result.get(0), "第一个元素转换结果不应为null");
            assertNull(result.get(1), "第二个元素转换结果应为null");
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(input));
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(null));
        }
    }

    @Test
    public void testConvertToUserTradeViewListWithFullData() {
        // arrange
        UserTradeViewResult input = new UserTradeViewResult();
        input.setUserId(123L);
        input.setCChain(1);
        input.setAppId("testApp");
        input.setLastOrderAmt(new BigDecimal("100.00"));
        PeriodTradeStat periodStat = new PeriodTradeStat();
        periodStat.setPeriod("1d");
        periodStat.setTradeCnt(5L);
        input.setPeriodStats(Collections.singletonList(periodStat));
        List<UserTradeViewResult> inputList = Collections.singletonList(input);
        try (MockedStatic<UserTradeViewConverter> mocked = mockStatic(UserTradeViewConverter.class)) {
            UserTradeView expectedView = new UserTradeView();
            expectedView.setUserId(123L);
            expectedView.setCChain(1);
            expectedView.setAppId("testApp");
            expectedView.setLastOrderAmt(new BigDecimal("100.00"));
            expectedView.setTrade1dCnt(5L);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeView(input)).thenReturn(expectedView);
            mocked.when(() -> UserTradeViewConverter.convertToUserTradeViewList(anyList())).thenCallRealMethod();
            // act
            List<UserTradeView> result = UserTradeViewConverter.convertToUserTradeViewList(inputList);
            // assert
            assertNotNull(result, "返回结果不应为null");
            assertEquals(1, result.size(), "返回列表大小应为1");
            UserTradeView converted = result.get(0);
            assertEquals(123L, converted.getUserId(), "用户ID应正确转换");
            assertEquals(1, converted.getCChain(), "CChain应正确转换");
            assertEquals("testApp", converted.getAppId(), "AppId应正确转换");
            assertEquals(new BigDecimal("100.00"), converted.getLastOrderAmt(), "最后订单金额应正确转换");
            assertEquals(5L, converted.getTrade1dCnt(), "1天交易次数应正确转换");
            mocked.verify(() -> UserTradeViewConverter.convertToUserTradeView(input));
        }
    }
}
