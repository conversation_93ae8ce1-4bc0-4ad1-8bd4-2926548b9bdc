<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="headimg_url" jdbcType="VARCHAR" property="headimgUrl" />
    <result column="sex" jdbcType="CHAR" property="sex" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="member_type" jdbcType="TINYINT" property="memberType" />
    <result column="wx_alias" jdbcType="VARCHAR" property="wxAlias" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="qr" jdbcType="VARCHAR" property="qr" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    <result column="extra" jdbcType="LONGVARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, serial_no, wx_id, nickname, headimg_url, sex, creator, add_time, update_time, 
    app_id, member_type, wx_alias, union_id, user_id, qr, tenant_id, service_type
  </sql>
  <sql id="Blob_Column_List">
    extra
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Scrm_PersonalWx_UserInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_UserInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Scrm_PersonalWx_UserInfo
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_UserInfo
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoExample">
    delete from Scrm_PersonalWx_UserInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_UserInfo (serial_no, wx_id, nickname, 
      headimg_url, sex, creator, 
      add_time, update_time, app_id, 
      member_type, wx_alias, union_id, 
      user_id, qr, tenant_id, 
      service_type, extra)
    values (#{serialNo,jdbcType=VARCHAR}, #{wxId,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{headimgUrl,jdbcType=VARCHAR}, #{sex,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{appId,jdbcType=VARCHAR}, 
      #{memberType,jdbcType=TINYINT}, #{wxAlias,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT}, #{qr,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{serviceType,jdbcType=INTEGER}, #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_UserInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="headimgUrl != null">
        headimg_url,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="memberType != null">
        member_type,
      </if>
      <if test="wxAlias != null">
        wx_alias,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="qr != null">
        qr,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="headimgUrl != null">
        #{headimgUrl,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        #{memberType,jdbcType=TINYINT},
      </if>
      <if test="wxAlias != null">
        #{wxAlias,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="qr != null">
        #{qr,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_UserInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_UserInfo
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.serialNo != null">
        serial_no = #{record.serialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.headimgUrl != null">
        headimg_url = #{record.headimgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.memberType != null">
        member_type = #{record.memberType,jdbcType=TINYINT},
      </if>
      <if test="record.wxAlias != null">
        wx_alias = #{record.wxAlias,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.qr != null">
        qr = #{record.qr,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update Scrm_PersonalWx_UserInfo
    set id = #{record.id,jdbcType=BIGINT},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      headimg_url = #{record.headimgUrl,jdbcType=VARCHAR},
      sex = #{record.sex,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      app_id = #{record.appId,jdbcType=VARCHAR},
      member_type = #{record.memberType,jdbcType=TINYINT},
      wx_alias = #{record.wxAlias,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      qr = #{record.qr,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      service_type = #{record.serviceType,jdbcType=INTEGER},
      extra = #{record.extra,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_UserInfo
    set id = #{record.id,jdbcType=BIGINT},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      headimg_url = #{record.headimgUrl,jdbcType=VARCHAR},
      sex = #{record.sex,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      app_id = #{record.appId,jdbcType=VARCHAR},
      member_type = #{record.memberType,jdbcType=TINYINT},
      wx_alias = #{record.wxAlias,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      qr = #{record.qr,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      service_type = #{record.serviceType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    update Scrm_PersonalWx_UserInfo
    <set>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="headimgUrl != null">
        headimg_url = #{headimgUrl,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        member_type = #{memberType,jdbcType=TINYINT},
      </if>
      <if test="wxAlias != null">
        wx_alias = #{wxAlias,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="qr != null">
        qr = #{qr,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    update Scrm_PersonalWx_UserInfo
    set serial_no = #{serialNo,jdbcType=VARCHAR},
      wx_id = #{wxId,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      headimg_url = #{headimgUrl,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      app_id = #{appId,jdbcType=VARCHAR},
      member_type = #{memberType,jdbcType=TINYINT},
      wx_alias = #{wxAlias,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      qr = #{qr,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      service_type = #{serviceType,jdbcType=INTEGER},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo">
    update Scrm_PersonalWx_UserInfo
    set serial_no = #{serialNo,jdbcType=VARCHAR},
      wx_id = #{wxId,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      headimg_url = #{headimgUrl,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      app_id = #{appId,jdbcType=VARCHAR},
      member_type = #{memberType,jdbcType=TINYINT},
      wx_alias = #{wxAlias,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      qr = #{qr,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      service_type = #{serviceType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_UserInfo
    (serial_no, wx_id, nickname, headimg_url, sex, creator, add_time, update_time, app_id, 
      member_type, wx_alias, union_id, user_id, qr, tenant_id, service_type, extra)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.serialNo,jdbcType=VARCHAR}, #{item.wxId,jdbcType=VARCHAR}, #{item.nickname,jdbcType=VARCHAR}, 
        #{item.headimgUrl,jdbcType=VARCHAR}, #{item.sex,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.memberType,jdbcType=TINYINT}, #{item.wxAlias,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=BIGINT}, #{item.qr,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=BIGINT}, 
        #{item.serviceType,jdbcType=INTEGER}, #{item.extra,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>