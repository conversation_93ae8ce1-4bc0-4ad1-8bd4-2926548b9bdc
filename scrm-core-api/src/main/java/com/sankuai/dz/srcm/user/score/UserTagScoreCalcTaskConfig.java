package com.sankuai.dz.srcm.user.score;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/3 17:22
 * @Version v1.0.0
 */
@Data
public class UserTagScoreCalcTaskConfig implements Serializable {
    public static final int LIVE_WAY_USER_CHANGE = 1;
    public static final int LIVE_WAY_NOT_END = 2;
    private int queryLiveWay = 1;// 1 靠用户改变 2 未结束的直播
    private Integer batchQuerySize = 100;
    private Integer batchCalcSize = 20;
    private Integer liveValidDays = 100;
    // 打印分值
    private Boolean printScoreToConsole = true;
    private Boolean printScoreToEs = true;
    private Integer overdueDays = 2;//程序中根据日期超过这个时间的数据，做分值为0计算
    private Double productCategoryWeight = 0.7;// 商品品类权重
    private Double consumeCapacityWeight = 0.3;// 消费能力权重
    private Double[] potentialWeight = {0.5, 0.8};// 潜力权重 两值分三部分
    private boolean isSingleServerCalc = true;// 单台服务运算
    private boolean parallelLiveCalc = false;
    private boolean isAllLive = true;
    private List<String> grayLiveIds = new ArrayList<>();
    // 10001:50,10002:30,10003:0
    /**
     * 目前存储公域及私域分
     */

    private Map<String, Map<String, Integer>> consumeScore = new HashMap<>();
    /**
     * 实时计算
     */
    private boolean isRealtimeCalc = false;

    private Map<String,List<List<Integer>>> behaviorScore = new HashMap<>();
}
