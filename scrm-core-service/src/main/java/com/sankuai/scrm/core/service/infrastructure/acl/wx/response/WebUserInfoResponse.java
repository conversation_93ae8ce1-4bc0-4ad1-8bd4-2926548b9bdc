package com.sankuai.scrm.core.service.infrastructure.acl.wx.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebUserInfoResponse {

    @JsonProperty("openid")
    private String openId;

    @JsonProperty("nickname")
    private String nickName;

    private String sex;

    private String province;

    private String city;

    private String country;

    @JsonProperty("headimgurl")
    private String avatar;

    private List<String> privilege;

    @JsonProperty("unionid")
    private String unionId;
}
