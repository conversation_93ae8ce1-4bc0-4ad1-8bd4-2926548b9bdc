package com.sankuai.scrm.core.service.activity.fission.domain;

import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionInvitationRecord;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionInvitationRecordExample;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionInvitationRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FissionInvitationDomainService {

    @Autowired
    private GroupFissionInvitationRecordMapper fissionInvitationRecordMapper;

    public int countValidInvitation(GroupFissionActivity activity, String inviterUnionId) {
        if (activity == null || StringUtils.isEmpty(inviterUnionId)) {
            return 0;
        }
        GroupFissionInvitationRecordExample example = new GroupFissionInvitationRecordExample();
        example.createCriteria()
                .andActivityIdEqualTo(activity.getId())
                .andInviterUnionIdEqualTo(inviterUnionId);
        List<GroupFissionInvitationRecord> invitationRecordList = fissionInvitationRecordMapper.selectByExample(example);
        return (int) invitationRecordList.stream()
                .filter(invitationRecord -> activity.getNewUserCheck() ? invitationRecord.getNewUser() : true)
                .filter(invitationRecord -> activity.getQuitCheck() ? invitationRecord.getInGroupStatus() : true)
                .filter(invitationRecord -> !activity.getRiskCheck() || !invitationRecord.getRiskUser())
                .count();
    }

    public List<GroupFissionInvitationRecord> getGroupFissionInvitationRecordsByUnionId(String unionId) {
        GroupFissionInvitationRecordExample example = new GroupFissionInvitationRecordExample();
        example.createCriteria().andInviteeUnionIdEqualTo(unionId);
        return fissionInvitationRecordMapper.selectByExample(example);
    }
}
