package com.sankuai.scrm.core.service.group.dynamiccode.delaytask.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import lombok.Data;

import java.io.Serializable;

/**
 * 企微社群群活码相关的延迟任务的通用父类
 */
@Data
@JsonTypeInfo(use = Id.CLASS)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupDynamicCodeBaseDelayTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务类型
     *
     * @see com.sankuai.scrm.core.service.group.dynamiccode.enums.GroupDynamicCodeDelayTaskTypeEnum
     */
    private Integer taskType;

    /**
     * 剩余重试次数。若大于 0，则会失败重试；若等于 0，则不再重试；若为 null，则不会重试。
     */
    private Integer remainingRetryTimes;

    /**
     * 延迟时间（毫秒）。mafka 要求最小延迟时间 5000 毫秒。
     */
    private Long delayMillis;
}
