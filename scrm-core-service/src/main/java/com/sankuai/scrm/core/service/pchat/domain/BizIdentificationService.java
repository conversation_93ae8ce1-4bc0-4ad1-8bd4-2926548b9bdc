package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BizIdentificationService {

    @Autowired
    private ScrmLiveInfoDomainService scrmLiveInfoDomainService;

    public String getAppIdByLiveId(String liveId) {
        if (StringUtils.isBlank(liveId)) {
            return PchatConfig.AppID;
        }
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        if (liveInfo == null || StringUtils.isBlank(liveInfo.getAppId())) {
            return PchatConfig.AppID;
        }
        return liveInfo.getAppId();
    }

}
