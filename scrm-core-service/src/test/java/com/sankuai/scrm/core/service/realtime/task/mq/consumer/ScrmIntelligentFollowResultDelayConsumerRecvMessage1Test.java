package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.aigc.service.enums.AISceneABTestStatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.AISceneExecuteDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrmIntelligentFollowResultDelayConsumerRecvMessage1Test {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private AISceneExecuteDomainService aiSceneExecuteDomainService;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @InjectMocks
    private ScrmIntelligentFollowResultDelayConsumer consumer;

    private MafkaMessage<String> message;

    private MessagetContext context;

    private Method recvMessageMethod;

    @BeforeEach
    void setUp() throws Exception {
        message = mock(MafkaMessage.class);
        context = mock(MessagetContext.class);
        recvMessageMethod = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    private ConsumeStatus invokeRecvMessage() throws Exception {
        return (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
    }

    /**
     * Test when message body is null
     */
    @Test
    public void testRecvMessageWhenMessageBodyIsNull() throws Throwable {
        // arrange
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = invokeRecvMessage();
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when JSON parsing fails
     */
    @Test
    public void testRecvMessageWhenJsonParsingFails() throws Throwable {
        // arrange
        String invalidJson = "{invalid json}";
        when(message.getBody()).thenReturn(invalidJson);
        // act
        ConsumeStatus result = invokeRecvMessage();
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }


    /**
     * Test when process ID is 0 (no need to send message)
     */
    @Test
    public void testRecvMessageWhenProcessIdIsZero() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setNeedSendMsg(false);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage();
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }


    /**
     * Test when message body is empty string
     */
    @Test
    public void testRecvMessageWhenMessageBodyIsEmpty() throws Throwable {
        // arrange
        when(message.getBody()).thenReturn("");
        // act
        ConsumeStatus result = invokeRecvMessage();
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
