package com.sankuai.scrm.core.service.pchat.event;

import com.sankuai.scrm.core.service.pchat.dto.groupMsg.GroupMsgDTO;

/**
 * @Description 群聊日志事件
 * <AUTHOR>
 * @Create On 2023/12/21 16:26
 * @Version v1.0.0
 */
public class GroupChatLogEvent extends ChatLogEvent {
    private GroupMsgDTO data;

    public GroupChatLogEvent() {
        super(new GroupMsgDTO());
    }

    public GroupChatLogEvent(GroupMsgDTO source) {
        super(source);
        this.data = source;
    }

    public GroupMsgDTO getData() {
        return data;
    }

    public void setData(GroupMsgDTO data) {
        this.data = data;
    }


}
