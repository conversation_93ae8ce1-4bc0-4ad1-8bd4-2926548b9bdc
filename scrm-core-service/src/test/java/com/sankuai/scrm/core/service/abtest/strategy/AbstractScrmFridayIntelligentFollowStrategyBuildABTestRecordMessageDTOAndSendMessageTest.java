package com.sankuai.scrm.core.service.abtest.strategy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.abtest.config.ABTestConfig;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserIntentionScores;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserScoreDTO;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserScoreResult;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutorLimitDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.coupon.service.CouponSelectorService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.request.UserTrackRequest;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.user.predict.service.UserIntentionComprehensiveServiceImpl;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.lang.reflect.Field;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.*;

/**
 * Test class for AbstractScrmFridayIntelligentFollowStrategy.buildABTestRecordMessageDTOAndSendMessage method.
 */
public class AbstractScrmFridayIntelligentFollowStrategyBuildABTestRecordMessageDTOAndSendMessageTest {

    @Mock
    private ABTestConfig abTestConfig;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private UserActionScoreUtil userActionScoreUtil;

    @Mock
    private CouponSelectorService couponSelectorService;

    @Mock
    private UserTrackAcl userTrackAcl;

    @Mock
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @Mock
    private RedisStoreClient redisClient;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private UserIntentionComprehensiveServiceImpl userIntentionComprehensiveService;

    @Mock
    private ScrmFridayIntelligentFollowDomainService scrmFridayIntelligentFollowDomainService;

    private TestableAbstractScrmFridayIntelligentFollowStrategy strategy;

    private ABTestContext context;

    private String strategyName = "TestStrategy";

    /**
     * Concrete implementation of AbstractScrmFridayIntelligentFollowStrategy for testing purposes.
     */
    private static class TestableAbstractScrmFridayIntelligentFollowStrategy extends AbstractScrmFridayIntelligentFollowStrategy {

        @Override
        public void fillABTestRecordMessageDTO(ABTestContext context, GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO) {
            // Empty implementation for testing
        }

        @Override
        public ABTestContext execute(IntelligentFollowActionTrackDTO param, ABTestContext context) {
            return context;
        }

        @Override
        public String getName() {
            return "TestStrategy";
        }
    }

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        strategy = new TestableAbstractScrmFridayIntelligentFollowStrategy();
        // Inject mocks using reflection for protected fields
        setField(strategy, "abTestConfig", abTestConfig);
        setField(strategy, "testRecordMessageProducer", testRecordMessageProducer);
        setField(strategy, "consumerConfig", consumerConfig);
        setField(strategy, "mtUserCenterAclService", mtUserCenterAclService);
        setField(strategy, "userActionScoreUtil", userActionScoreUtil);
        setField(strategy, "couponSelectorService", couponSelectorService);
        setField(strategy, "userTrackAcl", userTrackAcl);
        setField(strategy, "fridayIntelligentFollowLogDOMapper", fridayIntelligentFollowLogDOMapper);
        setField(strategy, "redisClient", redisClient);
        setField(strategy, "scrmFridayIntelligentFollowDomainService", scrmFridayIntelligentFollowDomainService);
        setField(strategy, "userIntentionComprehensiveService", userIntentionComprehensiveService);
        // For private fields, use reflection
        setPrivateField(strategy, "executeManagementService", executeManagementService);
        setPrivateField(strategy, "processOrchestrationReadDomainService", processOrchestrationReadDomainService);
        context = new ABTestContext();
        context.setUserId(12345L);
        context.setAppId("testAppId");
        context.setRandomValue(50);
        context.setRandomValueStart(0);
        context.setRandomValueEnd(100);
        context.setUniqueName("uniqueName");
        context.setAbTestStatus(1);
    }

    /**
     * Helper method to set protected fields using reflection
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getSuperclass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getSuperclass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * Test case for normal execution of buildABTestRecordMessageDTOAndSendMessage
     * when appId is in the list of appIds from abTestConfig.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NormalCase() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
        verify(abTestConfig, times(1)).getTestVersion("testAppId");
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when appId is not in the list of appIds from abTestConfig.
     * The method should not send any message.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_AppIdNotInList() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
        verify(abTestConfig, never()).getTestVersion(anyString());
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when appId is null.
     * The method should not send any message.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullAppId() throws Throwable {
        // arrange
        context.setAppId(null);
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when appId is empty.
     * The method should not send any message.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_EmptyAppId() throws Throwable {
        // arrange
        context.setAppId("");
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when abTestConfig returns null appIds.
     * The method should throw NullPointerException.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullAppIds() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName));
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when abTestConfig returns empty appIds.
     * The method should not send any message.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_EmptyAppIds() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(new ArrayList<>());
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        verify(abTestConfig, times(1)).getAppIds();
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when testRecordMessageProducer throws an exception.
     * The method should propagate the exception.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_ProducerException() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        doThrow(new RuntimeException("Producer failed")).when(testRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName));
        assertEquals("Producer failed", exception.getMessage());
        verify(testRecordMessageProducer, times(1)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when context is null.
     * The method should throw NullPointerException.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullContext() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.buildABTestRecordMessageDTOAndSendMessage(null, strategyName));
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when strategyName is null.
     * The method should still send a message with null strategyName.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullStrategyName() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, null);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when strategyName is empty.
     * The method should still send a message with empty strategyName.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_EmptyStrategyName() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, "");
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage to verify the content of the message sent.
     * This test verifies that the message contains the correct values from the context.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_MessageContent() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> messageCaptor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(messageCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO sentMessage = messageCaptor.getValue();
        assertEquals("testAppId", sentMessage.getAppId());
        assertEquals(Long.valueOf(12345L), sentMessage.getMtUserId());
        assertEquals("v1.0", sentMessage.getTestVersion());
        assertEquals(Integer.valueOf(50), sentMessage.getRandomValue());
        assertEquals(Integer.valueOf(0), sentMessage.getRandomValueStart());
        assertEquals(Integer.valueOf(100), sentMessage.getRandomValueEnd());
        assertEquals("uniqueName", sentMessage.getUniqueName());
        assertEquals(Integer.valueOf(1), sentMessage.getStatus());
        assertEquals(0, sentMessage.getActionType());
        assertNotNull(sentMessage.getDateKey());
        assertEquals(strategyName, sentMessage.getStrategyName());
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when context has null userId.
     * The method should handle null userId gracefully.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullUserId() throws Throwable {
        // arrange
        context.setUserId(null);
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when getTestVersion returns null.
     * The method should handle null test version gracefully.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullTestVersion() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn(null);
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> messageCaptor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(messageCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO sentMessage = messageCaptor.getValue();
        assertNull(sentMessage.getTestVersion());
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when DateUtil.formatYMd returns null.
     * The method should handle null date key gracefully.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_DateKeyHandling() throws Throwable {
        // arrange
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> messageCaptor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(messageCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO sentMessage = messageCaptor.getValue();
        // DateUtil.formatYMd should return a valid date string for current date
        assertNotNull(sentMessage.getDateKey());
        assertTrue(sentMessage.getDateKey().matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when context has null random values.
     * The method should handle null random values gracefully.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullRandomValues() throws Throwable {
        // arrange
        context.setRandomValue(null);
        context.setRandomValueStart(null);
        context.setRandomValueEnd(null);
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> messageCaptor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(messageCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO sentMessage = messageCaptor.getValue();
        assertNull(sentMessage.getRandomValue());
        assertNull(sentMessage.getRandomValueStart());
        assertNull(sentMessage.getRandomValueEnd());
    }

    /**
     * Test case for buildABTestRecordMessageDTOAndSendMessage when context has null uniqueName and abTestStatus.
     * The method should handle null values gracefully.
     */
    @Test
    public void testBuildABTestRecordMessageDTOAndSendMessage_NullUniqueNameAndStatus() throws Throwable {
        // arrange
        context.setUniqueName(null);
        context.setAbTestStatus(null);
        List<String> appIds = Arrays.asList("testAppId", "anotherAppId");
        when(abTestConfig.getAppIds()).thenReturn(appIds);
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> messageCaptor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        // act
        strategy.buildABTestRecordMessageDTOAndSendMessage(context, strategyName);
        // assert
        verify(testRecordMessageProducer, times(1)).sendMessage(messageCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO sentMessage = messageCaptor.getValue();
        assertNull(sentMessage.getUniqueName());
        assertNull(sentMessage.getStatus());
    }
}
