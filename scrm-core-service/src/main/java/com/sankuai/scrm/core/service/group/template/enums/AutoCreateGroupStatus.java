package com.sankuai.scrm.core.service.group.template.enums;

import lombok.Getter;

@Getter
public enum  AutoCreateGroupStatus {

    FAIL(-1, "失败"),
    NO_START(0, "未开始"),
    CREATING(1, "异步创建中"),
    CREATED(2, "已完成创建"),
    HAS_SET_NOTICE(3, "已设置群公告"),
    HAS_SET_WELCOME_MSG(4, "已设置群欢迎语"),
    CANCELED(5,"已取消"),
    CANCEL_FAIL(6,"取消失败");

    public final int code;

    public final String desc;

    AutoCreateGroupStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AutoCreateGroupStatus getStatusByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AutoCreateGroupStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

}
