<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper">
    <select id="queryPageMobileAddFriendTaskIds" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample" resultType="java.lang.Long">
        select id from scrm_friend_add_friend_real_time_task
        <if test="_parameter != null">
            <include refid="com.sankuai.scrm.core.service.external.contact.dal.mapper.ScrmMobileAddFriendTaskDOMapper.Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>

</mapper>