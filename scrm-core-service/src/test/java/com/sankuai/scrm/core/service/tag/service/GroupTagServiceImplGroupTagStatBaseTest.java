package com.sankuai.scrm.core.service.tag.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.tag.request.GroupTagStatBaseRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.LoginAccessTokenAcl;
import com.sankuai.scrm.core.service.tag.domain.GroupTagDomainService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dz.srcm.tag.response.GroupTagStatBaseResponse;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupTagServiceImplGroupTagStatBaseTest {

    @InjectMocks
    private GroupTagServiceImpl groupTagService;

    @Mock(lenient = true)
    private GroupTagDomainService groupTagDomainService;
    @Mock(lenient = true)
    private LoginAccessTokenAcl loginAccessTokenAcl;

    @Before
    public void setUp() {
        when(groupTagDomainService.countGroupTagSet("AppId")).thenReturn(10L);
        when(groupTagDomainService.countGroupTag("AppId")).thenReturn(20L);
        when(loginAccessTokenAcl.currentLoginSsoUserName()).thenReturn("testUser");

    }

    /**
     * 测试 groupTagStatBase 方法，正常情况
     */
    @Test
    public void testGroupTagStatBaseNormal() {
        // arrange
        RemoteResponse<GroupTagStatBaseResponse> expectedResponse = RemoteResponse.success(new GroupTagStatBaseResponse());
        expectedResponse.getData().setGroupTagSetCount(10L);
        expectedResponse.getData().setGroupTagCount(20L);
        GroupTagStatBaseRequest groupTagStatBaseRequest = new GroupTagStatBaseRequest();
        groupTagStatBaseRequest.setAppId("AppId");
        // act
        RemoteResponse<GroupTagStatBaseResponse> actualResponse = groupTagService.groupTagStatBase(groupTagStatBaseRequest);
        // assert
        assertEquals(expectedResponse.getCode(), actualResponse.getCode());
        assertEquals(expectedResponse.getMsg(), actualResponse.getMsg());
        assertEquals(expectedResponse.getData().getGroupTagSetCount(), actualResponse.getData().getGroupTagSetCount());
        assertEquals(expectedResponse.getData().getGroupTagCount(), actualResponse.getData().getGroupTagCount());
    }

    /**
     * 测试 groupTagStatBase 方法，异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testGroupTagStatBaseException() {
        // arrange
        when(groupTagDomainService.countGroupTagSet("AppId")).thenThrow(new RuntimeException());
        GroupTagStatBaseRequest groupTagStatBaseRequest = new GroupTagStatBaseRequest();
        groupTagStatBaseRequest.setAppId("AppId");
        // act
        groupTagService.groupTagStatBase(groupTagStatBaseRequest);
    }
}
