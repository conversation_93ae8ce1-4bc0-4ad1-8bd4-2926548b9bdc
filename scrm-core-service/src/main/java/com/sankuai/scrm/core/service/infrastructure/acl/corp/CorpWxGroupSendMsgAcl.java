package com.sankuai.scrm.core.service.infrastructure.acl.corp;

import com.dianping.baby.http.HttpClientUtil;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg.GroupSendMemberTaskListResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg.GroupSendMsgListRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg.GroupSendMsgListResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg.GroupSendResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class CorpWxGroupSendMsgAcl {

    @Autowired
    private CorpWxAcl corpWxAcl;

    private static final String GROUP_SEND_MSG_LIST_QUERY_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_list_v2?access_token=%s";

    private static final String GROUP_SEND_MSG_MEMBER_TASK_QUERY_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_task?access_token=%s";

    private static final String GROUP_SEND_MSG_RESULT_QUERY_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_send_result?access_token=%s";

    public GroupSendMsgListResponse queryGroupSendList(GroupSendMsgListRequest request) {
        log.info("CorpWxGroupSendMsgAcl.queryGroupSendList request={}", request);
        if (request == null) {
            throw new IllegalArgumentException("请求为空");
        }
        if (StringUtils.isEmpty(request.getCorpId())) {
            throw new IllegalArgumentException("CorpId为空");
        }
        if (request.getTaskType() == null) {
            throw new IllegalArgumentException("群发任务类型为空");
        }
        if (request.getStartTime() == null) {
            throw new IllegalArgumentException("群发任务记录开始时间为空");
        }
        if (request.getEndTime() == null) {
            throw new IllegalArgumentException("群发任务记录结束时间为空");
        }
        String token = corpWxAcl.getTokenByCorpId(request.getCorpId());
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("获取token失败");
        }
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("chat_type", request.getTaskType().getType());
        paramMap.put("start_time", String.valueOf(request.getStartTime().getTime() / 1000));
        paramMap.put("end_time", String.valueOf(request.getEndTime().getTime() / 1000));
        if (StringUtils.isNotEmpty(request.getCreator())) {
            paramMap.put("creator", request.getCreator());
        }
        if (request.getCreatorType() != null) {
            paramMap.put("filter_type", String.valueOf(request.getCreatorType().getCode()));
        }
        if (request.getLimit() != null) {
            paramMap.put("limit", String.valueOf(request.getLimit()));
        }
        if (request.getCursor() != null) {
            paramMap.put("cursor", request.getCursor());
        }
        try {
            GroupSendMsgListResponse response = HttpClientUtil.postAsJson(String.format(GROUP_SEND_MSG_LIST_QUERY_URL, token),
                    paramMap, Maps.newHashMap(), GroupSendMsgListResponse.class);
            log.info("CorpWxGroupSendMsgAcl.queryGroupSendList reponse={}", request);
            return response;
        } catch (IOException e) {
            log.error("CorpWxGroupSendMsgAcl.queryGroupSendList has exception, request={}", request, e);
            return null;
        }
    }

    public GroupSendMemberTaskListResponse queryGroupSendMemberTaskList(String corpId, String msgId, Integer limit, String cursor) {
        log.info("CorpWxGroupSendMsgAcl.queryGroupSendMemberTaskList corpId={}, msgId={}, limit={}, cursor={}", corpId, msgId, limit, cursor);
        if (StringUtils.isEmpty(corpId)) {
            throw new IllegalArgumentException("CorpId为空");
        }
        if (StringUtils.isEmpty(msgId)) {
            throw new IllegalArgumentException("MsgId为空");
        }
        String token = corpWxAcl.getTokenByCorpId(corpId);
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("获取token失败");
        }
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("msgid", msgId);
        if (limit != null) {
            paramMap.put("limit", String.valueOf(limit));
        }
        if (StringUtils.isNotEmpty(cursor)) {
            paramMap.put("cursor", cursor);
        }
        try {
            GroupSendMemberTaskListResponse response = HttpClientUtil.postAsJson(String.format(GROUP_SEND_MSG_MEMBER_TASK_QUERY_URL, token),
                    paramMap, Maps.newHashMap(), GroupSendMemberTaskListResponse.class);
            log.info("CorpWxGroupSendMsgAcl.queryGroupSendMemberTaskList response={}", response);
            return response;
        } catch (IOException e) {
            log.error("CorpWxGroupSendMsgAcl.queryGroupSendMemberTaskList has exception, corpId={}, msgId={}, limit={}, cursor={}", corpId, msgId, limit, cursor, e);
            return null;
        }
    }

    public GroupSendResultResponse queryGroupSendResult(String corpId, String msgId, String userId, Integer limit, String cursor) {
        log.info("CorpWxGroupSendMsgAcl.queryGroupSendResult corpId={}, msgId={}, userId={}, limit={}, cursor={}", corpId, msgId, userId, limit, cursor);
        if (StringUtils.isEmpty(corpId)) {
            throw new IllegalArgumentException("CorpId为空");
        }
        if (StringUtils.isEmpty(msgId)) {
            throw new IllegalArgumentException("MsgId为空");
        }
        if (StringUtils.isEmpty(userId)) {
            throw new IllegalArgumentException("UserId为空");
        }
        String token = corpWxAcl.getTokenByCorpId(corpId);
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("获取token失败");
        }
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("msgid", msgId);
        paramMap.put("userid", userId);
        if (limit != null) {
            paramMap.put("limit", String.valueOf(limit));
        }
        if (StringUtils.isNotEmpty(cursor)) {
            paramMap.put("cursor", cursor);
        }
        try {
            GroupSendResultResponse response = HttpClientUtil.postAsJson(String.format(GROUP_SEND_MSG_RESULT_QUERY_URL, token),
                    paramMap, Maps.newHashMap(), GroupSendResultResponse.class);
            log.info("CorpWxGroupSendMsgAcl.queryGroupSendResult response={}", response);
            return response;
        } catch (IOException e) {
            log.error("CorpWxGroupSendMsgAcl.queryGroupSendResult has exception, corpId={}, msgId={}, userId={}, limit={}, cursor={}", corpId, msgId, userId, limit, cursor, e);
            return null;
        }
    }
}
