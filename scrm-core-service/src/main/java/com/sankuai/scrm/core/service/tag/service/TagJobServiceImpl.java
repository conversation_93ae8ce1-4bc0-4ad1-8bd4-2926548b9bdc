package com.sankuai.scrm.core.service.tag.service;

import com.dianping.cat.Cat;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.tag.service.TagJobService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@MdpPigeonServer
public class TagJobServiceImpl implements TagJobService {

    @Deprecated
    @Override
    public RemoteResponse<Void> executeTagPredJob() {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.tag.service.TagJobServiceImpl.executeTagPredJob()");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }
}
