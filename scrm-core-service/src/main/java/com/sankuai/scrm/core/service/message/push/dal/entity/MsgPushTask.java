package com.sankuai.scrm.core.service.message.push.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: msg_push_task
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MsgPushTask {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: task_type
     *   说明: 任务类型：1-私聊推送任务，2-群聊推送任务
     */
    private Integer taskType;

    /**
     *   字段: scene_type
     *   说明: 推送场景类型: 1-实时场景, 2-定时场景
     */
    private Integer sceneType;

    /**
     *   字段: sender
     *   说明: 发送者
     */
    private String sender;

    /**
     *   字段: content
     *   说明: 推送内容
     */
    private String content;

    /**
     *   字段: status
     *   说明: 任务状态
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}