package com.sankuai.dz.srcm.workorder.merchant.enums;

public enum CaseTypeEnum {

    UNKNOWN(0, "未知类型"),
    EVALUATION_APPEAL(1, "评价申诉"),
    EVALUATION_REPORT(2, "评价举报"),
    <PERSON><PERSON><PERSON>(3, "其它");

    private int code;

    private String desc;

    CaseTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static CaseTypeEnum getCaseTypeByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        CaseTypeEnum[] values = CaseTypeEnum.values();
        for (CaseTypeEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
