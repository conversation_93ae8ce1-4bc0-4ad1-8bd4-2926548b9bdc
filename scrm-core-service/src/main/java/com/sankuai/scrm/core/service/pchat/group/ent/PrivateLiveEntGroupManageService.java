package com.sankuai.scrm.core.service.pchat.group.ent;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxGroupAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxUserIdDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.ForbiddenModifyGroupNameEntity;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.pchat.adapter.utils.WechatTypeUtils;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupSetConfigWithBLOBs;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PrivateLiveEntGroupManageService {

    @Autowired
    private WechatTypeUtils wechatTypeUtils;

    @Autowired
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private CorpWxGroupAcl corpWxGroupAcl;

    @Autowired
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Autowired
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Autowired
    private DsAssistantAcl dsAssistantAcl;

    @Autowired
    private GroupDomainService groupDomainService;

    public void handleFissionGroup(GroupDynamicCodeInfoDTO dynamicCode, String groupId) {
        if (dynamicCode == null || StringUtils.isEmpty(groupId)) {
            return;
        }
        List<String> appIdList = wechatTypeUtils.getEnterpriseAppIdList();
        if (!appIdList.contains(dynamicCode.getAppId())) {
            return;
        }
        if (StringUtils.isEmpty(dynamicCode.getName()) || !dynamicCode.getName().startsWith("私域直播群")) {
            log.error("企微私域直播群裂变时群活码名称不符合要求: dynamicCode={}, groupId={}", dynamicCode, groupId);
            return;
        }
        long groupKey = NumberUtils.toLong(dynamicCode.getName().substring(5));
        ScrmPersonalWxGroupInfoEntity parentGroup = groupManageDomainService.queryGroupById(groupKey);
        if (parentGroup == null) {
            log.error("企微私域直播群裂变时未查询到父群");
            return;
        }
        groupManageDomainService.updateGroupFissionState(parentGroup);
        groupManageDomainService.updateGroupFissionCount(parentGroup.getId());
        WxGroupDetail wxGroupDetail = queryWxGroupDetail(dynamicCode.getAppId(), groupId);
        Long subGroupKey = saveFissionGroup(parentGroup, wxGroupDetail);
        setFissionGroupInfo(parentGroup, wxGroupDetail, subGroupKey);
    }

    private WxGroupDetail queryWxGroupDetail(String appId, String groupId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(groupId)) {
            return null;
        }
        String corpId = appConfigRepository.getCorpIdByAppId(appId);
        if (StringUtils.isEmpty(corpId)) {
            return null;
        }
        try {
            return corpWxGroupAcl.getWxGroupDetail(corpId, groupId, true);
        } catch (Exception e) {
            log.error("EnterpriseGroupManageService.queryWxGroupDetail exception, appId={}, groupId={}", appId, groupId, e);
        }
        return null;
    }

    private Long saveFissionGroup(ScrmPersonalWxGroupInfoEntity parentGroup, WxGroupDetail wxGroupDetail) {
        if (parentGroup == null || wxGroupDetail == null) {
            return null;
        }
        ScrmPersonalWxGroupInfoEntity record = new ScrmPersonalWxGroupInfoEntity();
        record.setAppId(parentGroup.getAppId());
        record.setFamilyCode(parentGroup.getFamilyCode());
        record.setGroupName(wxGroupDetail.getName());
        record.setSetId(parentGroup.getSetId());
        record.setProjectId(parentGroup.getProjectId());
        record.setIsAutoCreateNewGroup(parentGroup.getIsAutoCreateNewGroup());
        record.setGroupId(wxGroupDetail.getGroupId());
        record.setChatRoomWxSerialNo(wxGroupDetail.getGroupId());
        record.setOwner(wxGroupDetail.getOwner());
        record.setOwnerWxId(wxGroupDetail.getOwner());
        record.setCreateOwner(wxGroupDetail.getOwner());
        record.setWelcomeMsgId(parentGroup.getWelcomeMsgId());
        record.setGroupNotice(copyGroupNotice(parentGroup.getGroupNotice()));
        record.setConsultantList(parentGroup.getConsultantList());
        record.setGroupQr(parentGroup.getGroupQr());
        record.setParentGroupId(parentGroup.getGroupId());
        record.setStatus(PersonalGroupStatusEnum.SUCCESS.getCode());
        return groupManageDomainService.saveGroup(record);
    }

    private Long copyGroupNotice(Long groupNoticeId) {
        if (groupNoticeId == null) {
            return null;
        }
        ScrmPersonalWxGroupMsg groupMsg = groupManageDomainService.queryGroupMsgById(groupNoticeId);
        if (groupMsg == null) {
            return null;
        }
        groupMsg.setId(null);
        return groupManageDomainService.saveGroupMsg(groupMsg);
    }


    private void setFissionGroupInfo(ScrmPersonalWxGroupInfoEntity parentGroupEntity, WxGroupDetail wxGroupDetail, Long subGroupKey) {
        if (parentGroupEntity == null || wxGroupDetail == null || subGroupKey == null) {
            return;
        }
        String mobile = getMobile(parentGroupEntity.getAppId(), wxGroupDetail.getOwner());
        String subGroupName = genGroupName(parentGroupEntity.getSetId());
        if (StringUtils.isNotEmpty(subGroupName)) {
            boolean success = dsCorpGroupAclService.updateGroupName(wxGroupDetail.getGroupId(), subGroupName, mobile);
            if (success) {
                ScrmPersonalWxGroupInfoEntity entity = new ScrmPersonalWxGroupInfoEntity();
                entity.setId(subGroupKey);
                entity.setGroupName(subGroupName);
                groupManageDomainService.updateGroup(entity);
            }
        }
        dsCorpGroupAclService.forbiddenModifyGroupName(new ForbiddenModifyGroupNameEntity(wxGroupDetail.getGroupId(), mobile));
        ScrmPersonalWxGroupInfoEntity subGroupEntity = groupManageDomainService.queryGroupById(subGroupKey);
        setGroupNotice(wxGroupDetail, subGroupEntity);
        setGroupWelcomeMsg(subGroupEntity, mobile);
        setGroupAdmin(parentGroupEntity.getAppId(), parentGroupEntity.getGroupId(), wxGroupDetail, mobile);
    }

    private String genGroupName(Long groupSetId) {
        if (groupSetId == null) {
            return null;
        }
        ScrmPersonalWxGroupSetConfigWithBLOBs groupSetConfig = groupManageDomainService.queryGroupSetById(groupSetId);
        if (groupSetConfig == null) {
            return null;
        }
        List<String> groupNameList = personalWxCommonService.batchGenGroupName(groupSetConfig.getAppId(), groupSetConfig.getGroupName(), 1);
        if (CollectionUtils.isEmpty(groupNameList)) {
            return null;
        }
        return groupNameList.get(0);
    }

    private String getMobile(String appId, String ownerId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(ownerId)) {
            return null;
        }
        CorpAppConfig config = appConfigRepository.getConfigByAppId(appId);
        if (config == null) {
            return null;
        }
        return dsAssistantAcl.getAssistantMobileByUserId(ownerId, config.getCorpId(), config.getOrgId());
    }

    private void setGroupNotice(WxGroupDetail wxGroupDetail, ScrmPersonalWxGroupInfoEntity groupEntity) {
        if (wxGroupDetail == null || groupEntity == null) {
            return;
        }
        String wxNotice = wxGroupDetail.getNotice();
        String dbNotice = queryGroupNoticeFromDb(groupEntity.getId());
        if (!StringUtils.equals(wxNotice, dbNotice)) {
            dsCorpGroupAclService.setGroupNotifyMsg(wxGroupDetail.getGroupId(), dbNotice);
        }
    }

    private String queryGroupNoticeFromDb(Long groupNoticeId) {
        if (groupNoticeId == null) {
            return null;
        }
        ScrmPersonalWxGroupMsg groupMsg = groupManageDomainService.queryGroupMsgById(groupNoticeId);
        if (groupMsg == null || StringUtils.isBlank(groupMsg.getContent())) {
            return null;
        }
        List<GroupMsg> msgList = JsonUtils.toList(groupMsg.getContent(), GroupMsg.class);
        if (CollectionUtils.isEmpty(msgList) || msgList.get(0) == null) {
            return null;
        }
        return msgList.get(0).getMsgContent();
    }

    private void setGroupWelcomeMsg(ScrmPersonalWxGroupInfoEntity groupEntity, String mobile) {
        if (groupEntity == null || groupEntity.getWelcomeMsgId() == null || StringUtils.isEmpty(mobile)) {
            return;
        }
        dsCorpGroupAclService.setGroupWelcomeMsg(groupEntity.getWelcomeMsgId(), groupEntity.getGroupId(), mobile);
    }

    private void setGroupAdmin(String appId, String parentGroupId, WxGroupDetail wxGroupDetail, String mobile) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(parentGroupId) || wxGroupDetail == null || StringUtils.isEmpty(mobile)) {
            return;
        }
        String corpId = appConfigRepository.getCorpIdByAppId(appId);
        if (StringUtils.isEmpty(corpId)) {
            return;
        }
        GroupInfoEntity groupInfoEntity = groupDomainService.queryGroupInfo(corpId, parentGroupId);
        if (groupInfoEntity == null || StringUtils.isEmpty(groupInfoEntity.getAdminList())) {
            return;
        }
        String[] parentGroupAdminList = StringUtils.split(groupInfoEntity.getAdminList(), ",");
        if (ArrayUtils.isEmpty(parentGroupAdminList)) {
            return;
        }
        List<String> subGroupAdminList = Optional.ofNullable(wxGroupDetail.getAdminList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(WxUserIdDetail::getUserId)
                .collect(Collectors.toList());
        boolean allMatch = Arrays.stream(parentGroupAdminList).allMatch(subGroupAdminList::contains);
        if (!allMatch) {
            try {
                dsCorpGroupAclService.setMemberToAdmin(wxGroupDetail.getGroupId(), mobile,
                        Lists.newArrayList(parentGroupAdminList), null);
            } catch (Exception e) {
                log.error("EnterpriseGroupManageService.setAdmin exception", e);
            }
        }
    }
}
