package com.sankuai.scrm.core.service.group.template.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IDelayFutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.group.template.dto.GroupCreateTaskDetailEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Properties;

@Slf4j
@Service
public class ScrmDelayCreateGroupProducer implements InitializingBean {


    private static IProducerProcessor producer;

    public boolean sendAsyncDelayMessage(GroupCreateTaskDetailEntity detailDTO, long delayTime) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.group.template.mq.producer.ScrmDelayCreateGroupProducer.sendAsyncDelayMessage(com.sankuai.scrm.core.service.group.template.dto.GroupCreateTaskDetailEntity,long)");
        try {
            if (detailDTO == null) {
                return false;
            }

            AsyncDelayProducerResult delayProducerResult = producer.sendAsyncDelayMessage(JSONObject.toJSONString(detailDTO), delayTime, new IDelayFutureCallback() {
                @Override
                public void onSuccess(AsyncDelayProducerResult asyncDelayProducerResult) {
                    Cat.logMetricForCount("ScrmDelayCreateGroupProducer.sendAsyncMessage.success");
                }

                @Override
                public void onFailure(AsyncDelayProducerResult asyncDelayProducerResult) {
                    log.error("ScrmDelayCreateGroupProducer.sendAsyncMessage.fail,msg is {}", asyncDelayProducerResult.getMessage());
                    Cat.logMetricForCount("ScrmDelayCreateGroupProducer.sendAsyncMessage.fail");
                }
            });
            if (delayProducerResult == null || delayProducerResult.getProducerStatus() == null || delayProducerResult.getProducerStatus().equals(ProducerStatus.SEND_FAILURE)) {
                throw new RuntimeException(String.format("[MessageSendError] %s",
                        delayProducerResult == null ? "null result" : (delayProducerResult.getProducerStatus() == null ? "null result status" : "message send fail")));
            }
            return true;
        } catch (Exception e) {
            log.error("ScrmDelayCreateGroupProducer.sendAsyncMessage", e);
            return false;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildDelayProduceFactory(properties, "srcm.core.batch.create.group.msg");
    }

}
