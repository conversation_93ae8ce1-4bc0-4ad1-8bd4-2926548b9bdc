package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

/**
 * Unit tests for the private method {@code sendMessage} of
 * {@link RefinementOperationExecuteCheckStatusMessageProducer}.
 *
 * <p>All tests use reflection to invoke the private method and <PERSON><PERSON><PERSON> to mock the
 * static {@code producer} field. After each test the static field is reset to its
 * original value to avoid side effects between tests.</p>
 */
class RefinementOperationExecuteCheckStatusMessageProducerSendMessageTest {

    private RefinementOperationExecuteCheckStatusMessageProducer producerInstance;

    private Method sendMessageMethod;

    private Field producerStaticField;

    private IProducerProcessor<String, String> mockProcessor;

    @BeforeEach
    void setUp() throws Exception {
        // create the class under test
        producerInstance = new RefinementOperationExecuteCheckStatusMessageProducer();
        // obtain the private method via reflection
        sendMessageMethod = RefinementOperationExecuteCheckStatusMessageProducer.class.getDeclaredMethod("sendMessage", RefinementOperationExecuteMessage.class);
        sendMessageMethod.setAccessible(true);
        // obtain the static field that holds the IProducerProcessor
        producerStaticField = RefinementOperationExecuteCheckStatusMessageProducer.class.getDeclaredField("producer");
        producerStaticField.setAccessible(true);
        // create a Mockito mock for the processor
        // the generic types are irrelevant for the test – we treat it as raw
        mockProcessor = (IProducerProcessor<String, String>) Mockito.mock(IProducerProcessor.class);
        // inject the mock into the static field
        producerStaticField.set(null, mockProcessor);
    }

    @AfterEach
    void tearDown() throws Exception {
        // reset the static field to null so that other tests are not polluted
        producerStaticField.set(null, null);
    }

    /**
     * Helper method to build a minimal but valid {@link RefinementOperationExecuteMessage}
     * instance used by the tests.
     */
    private RefinementOperationExecuteMessage buildSimpleMessage() {
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setProcessOrchestrationId(1L);
        msg.setProcessOrchestrationVersion("v1");
        msg.setAppId("testApp");
        msg.setTaskType(0);
        msg.setExecuteLogIds(Collections.singletonList(100L));
        // other fields are optional for the serialization performed in the method
        return msg;
    }

    /**
     * Case 1 – the input message is {@code null}.
     * The method should return immediately and never invoke the producer.
     */
    @Test
    void testSendMessageWhenMessageIsNull() throws Throwable {
        // act
        sendMessageMethod.invoke(producerInstance, (Object) null);
        // assert
        verify(mockProcessor, never()).sendMessage(anyString());
    }

    /**
     * Case 2 – the producer returns a {@link ProducerResult} with status {@code SEND_OK}
     * on the first attempt. The method should stop after the first call.
     */
    @Test
    void testSendMessageSuccessOnFirstTry() throws Throwable {
        // arrange
        ProducerResult okResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProcessor.sendMessage(anyString())).thenReturn(okResult);
        RefinementOperationExecuteMessage msg = buildSimpleMessage();
        // act
        sendMessageMethod.invoke(producerInstance, msg);
        // assert
        verify(mockProcessor, times(1)).sendMessage(anyString());
    }

    /**
     * Case 3 – the producer returns {@code null} each time.
     * The method should retry three times and then give up.
     */
    @Test
    void testSendMessageWhenProducerReturnsNull() throws Throwable {
        // arrange
        when(mockProcessor.sendMessage(anyString())).thenReturn(null);
        RefinementOperationExecuteMessage msg = buildSimpleMessage();
        // act
        sendMessageMethod.invoke(producerInstance, msg);
        // assert
        verify(mockProcessor, times(3)).sendMessage(anyString());
    }

    /**
     * Case 4 – the producer throws an exception on every attempt.
     * The method should catch the exception, retry three times and finally exit.
     */
    @Test
    void testSendMessageWhenProducerThrowsException() throws Throwable {
        // arrange
        when(mockProcessor.sendMessage(anyString())).thenThrow(new RuntimeException("simulated failure"));
        RefinementOperationExecuteMessage msg = buildSimpleMessage();
        // act
        sendMessageMethod.invoke(producerInstance, msg);
        // assert
        verify(mockProcessor, times(3)).sendMessage(anyString());
    }

    /**
     * Case 5 – the producer returns a result whose status is not {@code SEND_OK}
     * (e.g., {@code SEND_FAILURE}). The method should retry three times.
     */
    @Test
    void testSendMessageWhenProducerReturnsFailureStatus() throws Throwable {
        // arrange
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(mockProcessor.sendMessage(anyString())).thenReturn(failureResult);
        RefinementOperationExecuteMessage msg = buildSimpleMessage();
        // act
        sendMessageMethod.invoke(producerInstance, msg);
        // assert
        verify(mockProcessor, times(3)).sendMessage(anyString());
    }
}

