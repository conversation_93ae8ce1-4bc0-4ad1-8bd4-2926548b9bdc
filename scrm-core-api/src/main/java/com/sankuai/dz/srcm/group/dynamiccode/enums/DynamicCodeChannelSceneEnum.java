package com.sankuai.dz.srcm.group.dynamiccode.enums;

public enum DynamicCodeChannelSceneEnum {
    DYNAMIC_CODE_CHANNEL(0, "群活码渠道"),
    FISSION_ACTIVITY_CHANNEL(1, "群裂变渠道"),
    PRIVATE_DOMAIN_LIVE_CHANNEL_CHANNEL(2, "私域直播渠道"),
    NON_WECOM_FISSION_ACTIVITY_CHANNEL(3, "非企微群群裂变渠道"),
    ;


    private Integer code;
    private String desc;

    DynamicCodeChannelSceneEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
