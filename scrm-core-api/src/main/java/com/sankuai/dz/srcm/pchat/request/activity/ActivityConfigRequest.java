package com.sankuai.dz.srcm.pchat.request.activity;

import com.sankuai.dz.srcm.pchat.request.Request;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/18 18:17
 * @Version v1.0.0
 */
@Data
public class ActivityConfigRequest extends Request {

    /**
     * 口令
     */
    private String countersign;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 播报内容
     */
    private String countersignAnswer;

    /**
     * 是否开启发送小程序 默认为false
     */
    private Boolean countersignMiniProg;

    /**
     * 小程序配置
     */
//    private String countersignMiniConfig;

    //  当发送消息是小程序时msgContent为小程序路径 必必填 href为小程序图片 必填，title为小程序标题 必填 ，description为小程序描述 必填
    private GroupMsg countersignMiniConfig;

    /**
     * 建议分享人群类别
     */
    private String promotionTipClassification;

    /**
     * 群名称配置
     */
    private List<String> promotionTipGroupFoo;

    private Boolean isDiscretization = true;

    @Override
    public boolean validParam() {
        this.assertNotBlank(promotionTipClassification, this.messageNotBlank("promotionTipClassification"));
        this.assertNotEmpty(promotionTipGroupFoo, this.messageNotBlank("promotionTipGroupFoo"));
        this.assertNotNull(activityId, this.messageNotBlank("activityId"));
        countersignMiniProg = countersignMiniProg == null ? Boolean.FALSE : countersignMiniProg;

        if (countersignMiniConfig != null) {
            countersignMiniConfig.setIsDiscretization(isDiscretization);
        }
        return true;
    }


}