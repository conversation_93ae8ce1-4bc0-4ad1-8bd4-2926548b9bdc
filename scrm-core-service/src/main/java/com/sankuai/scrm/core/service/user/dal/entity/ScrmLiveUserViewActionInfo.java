package com.sankuai.scrm.core.service.user.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_Live_UserViewActionInfo
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmLiveUserViewActionInfo {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: user_id
     *   说明: userid
     */
    private Long userId;

    /**
     *   字段: project_id
     *   说明: 项目id
     */
    private String projectId;

    /**
     *   字段: union_id
     *   说明: union_id
     */
    private String unionId;

    /**
     *   字段: action_type
     *   说明: 动作类型
     */
    private Integer actionType;

    /**
     *   字段: action_time
     *   说明: 动作发生时间
     */
    private Date actionTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: product_id
     *   说明: 商品id
     */
    private Long productId;

    /**
     *   字段: product_type
     *   说明: 商品类型
     */
    private Integer productType;

    /**
     *   字段: content
     *   说明: 如果是留言类型，表示内容
     */
    private String content;
}