package com.sankuai.scrm.core.service.dashboard.domain;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
@Service
@Slf4j
public class DashBoardEsDeleteService {

    @Resource
    private PorosRestHighLevelClient porosRestHighLevelClient;
    /**
     * 清空es索引中所有文档数据
     * @param indexName
     */
    public  void deleteEsData(String indexName) {
        DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
        request.setQuery(QueryBuilders.matchAllQuery());
        request.setAbortOnVersionConflict(true);
        try {
            porosRestHighLevelClient.deleteByQuery(request, RequestOptions.DEFAULT);
            log.info("成功删除索引 {} 中的所有文档", indexName);
        } catch (Exception e) {
            log.error("删除索引 {} 中的所有文档失败", indexName, e);
        }
    }
}
