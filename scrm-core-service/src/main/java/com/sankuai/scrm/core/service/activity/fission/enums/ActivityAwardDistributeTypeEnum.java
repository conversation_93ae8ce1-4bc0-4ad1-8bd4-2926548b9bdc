package com.sankuai.scrm.core.service.activity.fission.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/5/28 14:36
 * @Version v1.0.0
 */
@Getter
public enum ActivityAwardDistributeTypeEnum {
    CASH(1, "现金"),
    COUPON(2, "优惠券"),
    ;


    private int code;
    private String desc;

    ActivityAwardDistributeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityAwardDistributeTypeEnum fromCode(int code) {
        for (ActivityAwardDistributeTypeEnum enumValue : ActivityAwardDistributeTypeEnum.values()) {
            if (Objects.equals(enumValue.code, code)) {
                return enumValue;
            }
        }
        return null;
    }

}
