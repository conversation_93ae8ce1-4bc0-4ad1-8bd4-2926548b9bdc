package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.tanjing.RobotFunctionService;
import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.adapter.service.FriendProcessor;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@PrivateLiveProcessor(wechatType = WeChatType.PERSONAL_WECHAT, appId = PchatConfig.AppID)
public class TuseFriendProcessor implements FriendProcessor {

    @Resource
    private RobotFunctionService robotFunctionService;

    @Override
    public boolean setFriendRemark(String appId, String userId, String externalUserId, String remark) {
        AsyncInvokeResultDTO setupResult = robotFunctionService.setFriendRemark(ApiConstants.merchantNo, userId, externalUserId, remark);
        log.warn("imFriendSetup: result={}", setupResult);
        return setupResult != null && setupResult.isSuccess();
    }
}
