package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagsDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationProductTagsDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationProductTagsDO, ScrmAmProcessOrchestrationProductTagsDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationProductTagsDO> list) ;
}