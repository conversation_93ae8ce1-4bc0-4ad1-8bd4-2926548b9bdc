<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupMemberInfoDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vid" jdbcType="BIGINT" property="vid" />
    <result column="wxUserId" jdbcType="VARCHAR" property="wxuserid" />
    <result column="unionId" jdbcType="VARCHAR" property="unionid" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_nick_name" jdbcType="VARCHAR" property="memberNickName" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="origin_member_name" jdbcType="VARCHAR" property="originMemberName" />
    <result column="origin_member_nick_name" jdbcType="VARCHAR" property="originMemberNickName" />
    <result column="origin_avatar" jdbcType="VARCHAR" property="originAvatar" />
    <result column="member_type" jdbcType="INTEGER" property="memberType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="enter_time" jdbcType="TIMESTAMP" property="enterTime" />
    <result column="leave_time" jdbcType="TIMESTAMP" property="leaveTime" />
    <result column="ds_group_id" jdbcType="BIGINT" property="dsGroupId" />
    <result column="wx_group_id" jdbcType="VARCHAR" property="wxGroupId" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="businessCode" jdbcType="VARCHAR" property="businesscode" />
    <result column="event_time" jdbcType="BIGINT" property="eventTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vid, wxUserId, unionId, member_name, member_nick_name, avatar, origin_member_name, 
    origin_member_nick_name, origin_avatar, member_type, status, enter_time, leave_time, 
    ds_group_id, wx_group_id, corp_id, org_id, businessCode, event_time, update_time, 
    add_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupMemberInfoDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_DS_PersonalWx_Group_Member_Info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_DS_PersonalWx_Group_Member_Info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_DS_PersonalWx_Group_Member_Info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupMemberInfoDOExample">
    delete from Scrm_DS_PersonalWx_Group_Member_Info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_DS_PersonalWx_Group_Member_Info (vid, wxUserId, unionId, 
      member_name, member_nick_name, avatar, 
      origin_member_name, origin_member_nick_name, 
      origin_avatar, member_type, status, 
      enter_time, leave_time, ds_group_id, 
      wx_group_id, corp_id, org_id, 
      businessCode, event_time, update_time, 
      add_time)
    values (#{vid,jdbcType=BIGINT}, #{wxuserid,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{memberName,jdbcType=VARCHAR}, #{memberNickName,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR}, 
      #{originMemberName,jdbcType=VARCHAR}, #{originMemberNickName,jdbcType=VARCHAR}, 
      #{originAvatar,jdbcType=VARCHAR}, #{memberType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{enterTime,jdbcType=TIMESTAMP}, #{leaveTime,jdbcType=TIMESTAMP}, #{dsGroupId,jdbcType=BIGINT}, 
      #{wxGroupId,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, 
      #{businesscode,jdbcType=VARCHAR}, #{eventTime,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_DS_PersonalWx_Group_Member_Info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vid != null">
        vid,
      </if>
      <if test="wxuserid != null">
        wxUserId,
      </if>
      <if test="unionid != null">
        unionId,
      </if>
      <if test="memberName != null">
        member_name,
      </if>
      <if test="memberNickName != null">
        member_nick_name,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="originMemberName != null">
        origin_member_name,
      </if>
      <if test="originMemberNickName != null">
        origin_member_nick_name,
      </if>
      <if test="originAvatar != null">
        origin_avatar,
      </if>
      <if test="memberType != null">
        member_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="enterTime != null">
        enter_time,
      </if>
      <if test="leaveTime != null">
        leave_time,
      </if>
      <if test="dsGroupId != null">
        ds_group_id,
      </if>
      <if test="wxGroupId != null">
        wx_group_id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="businesscode != null">
        businessCode,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vid != null">
        #{vid,jdbcType=BIGINT},
      </if>
      <if test="wxuserid != null">
        #{wxuserid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="memberName != null">
        #{memberName,jdbcType=VARCHAR},
      </if>
      <if test="memberNickName != null">
        #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="originMemberName != null">
        #{originMemberName,jdbcType=VARCHAR},
      </if>
      <if test="originMemberNickName != null">
        #{originMemberNickName,jdbcType=VARCHAR},
      </if>
      <if test="originAvatar != null">
        #{originAvatar,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        #{memberType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="enterTime != null">
        #{enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveTime != null">
        #{leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dsGroupId != null">
        #{dsGroupId,jdbcType=BIGINT},
      </if>
      <if test="wxGroupId != null">
        #{wxGroupId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="businesscode != null">
        #{businesscode,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupMemberInfoDOExample" resultType="java.lang.Long">
    select count(*) from Scrm_DS_PersonalWx_Group_Member_Info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_DS_PersonalWx_Group_Member_Info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vid != null">
        vid = #{record.vid,jdbcType=BIGINT},
      </if>
      <if test="record.wxuserid != null">
        wxUserId = #{record.wxuserid,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionId = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.memberName != null">
        member_name = #{record.memberName,jdbcType=VARCHAR},
      </if>
      <if test="record.memberNickName != null">
        member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.avatar != null">
        avatar = #{record.avatar,jdbcType=VARCHAR},
      </if>
      <if test="record.originMemberName != null">
        origin_member_name = #{record.originMemberName,jdbcType=VARCHAR},
      </if>
      <if test="record.originMemberNickName != null">
        origin_member_nick_name = #{record.originMemberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.originAvatar != null">
        origin_avatar = #{record.originAvatar,jdbcType=VARCHAR},
      </if>
      <if test="record.memberType != null">
        member_type = #{record.memberType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.enterTime != null">
        enter_time = #{record.enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.leaveTime != null">
        leave_time = #{record.leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dsGroupId != null">
        ds_group_id = #{record.dsGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.wxGroupId != null">
        wx_group_id = #{record.wxGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.corpId != null">
        corp_id = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.businesscode != null">
        businessCode = #{record.businesscode,jdbcType=VARCHAR},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_DS_PersonalWx_Group_Member_Info
    set id = #{record.id,jdbcType=BIGINT},
      vid = #{record.vid,jdbcType=BIGINT},
      wxUserId = #{record.wxuserid,jdbcType=VARCHAR},
      unionId = #{record.unionid,jdbcType=VARCHAR},
      member_name = #{record.memberName,jdbcType=VARCHAR},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      avatar = #{record.avatar,jdbcType=VARCHAR},
      origin_member_name = #{record.originMemberName,jdbcType=VARCHAR},
      origin_member_nick_name = #{record.originMemberNickName,jdbcType=VARCHAR},
      origin_avatar = #{record.originAvatar,jdbcType=VARCHAR},
      member_type = #{record.memberType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      enter_time = #{record.enterTime,jdbcType=TIMESTAMP},
      leave_time = #{record.leaveTime,jdbcType=TIMESTAMP},
      ds_group_id = #{record.dsGroupId,jdbcType=BIGINT},
      wx_group_id = #{record.wxGroupId,jdbcType=VARCHAR},
      corp_id = #{record.corpId,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      businessCode = #{record.businesscode,jdbcType=VARCHAR},
      event_time = #{record.eventTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO">
    update Scrm_DS_PersonalWx_Group_Member_Info
    <set>
      <if test="vid != null">
        vid = #{vid,jdbcType=BIGINT},
      </if>
      <if test="wxuserid != null">
        wxUserId = #{wxuserid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionId = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="memberName != null">
        member_name = #{memberName,jdbcType=VARCHAR},
      </if>
      <if test="memberNickName != null">
        member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="originMemberName != null">
        origin_member_name = #{originMemberName,jdbcType=VARCHAR},
      </if>
      <if test="originMemberNickName != null">
        origin_member_nick_name = #{originMemberNickName,jdbcType=VARCHAR},
      </if>
      <if test="originAvatar != null">
        origin_avatar = #{originAvatar,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        member_type = #{memberType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="enterTime != null">
        enter_time = #{enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveTime != null">
        leave_time = #{leaveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dsGroupId != null">
        ds_group_id = #{dsGroupId,jdbcType=BIGINT},
      </if>
      <if test="wxGroupId != null">
        wx_group_id = #{wxGroupId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="businesscode != null">
        businessCode = #{businesscode,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO">
    update Scrm_DS_PersonalWx_Group_Member_Info
    set vid = #{vid,jdbcType=BIGINT},
      wxUserId = #{wxuserid,jdbcType=VARCHAR},
      unionId = #{unionid,jdbcType=VARCHAR},
      member_name = #{memberName,jdbcType=VARCHAR},
      member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      avatar = #{avatar,jdbcType=VARCHAR},
      origin_member_name = #{originMemberName,jdbcType=VARCHAR},
      origin_member_nick_name = #{originMemberNickName,jdbcType=VARCHAR},
      origin_avatar = #{originAvatar,jdbcType=VARCHAR},
      member_type = #{memberType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      enter_time = #{enterTime,jdbcType=TIMESTAMP},
      leave_time = #{leaveTime,jdbcType=TIMESTAMP},
      ds_group_id = #{dsGroupId,jdbcType=BIGINT},
      wx_group_id = #{wxGroupId,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      businessCode = #{businesscode,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_DS_PersonalWx_Group_Member_Info
    (vid, wxUserId, unionId, member_name, member_nick_name, avatar, origin_member_name, 
      origin_member_nick_name, origin_avatar, member_type, status, enter_time, leave_time, 
      ds_group_id, wx_group_id, corp_id, org_id, businessCode, event_time, update_time, 
      add_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.vid,jdbcType=BIGINT}, #{item.wxuserid,jdbcType=VARCHAR}, #{item.unionid,jdbcType=VARCHAR}, 
        #{item.memberName,jdbcType=VARCHAR}, #{item.memberNickName,jdbcType=VARCHAR}, #{item.avatar,jdbcType=VARCHAR}, 
        #{item.originMemberName,jdbcType=VARCHAR}, #{item.originMemberNickName,jdbcType=VARCHAR}, 
        #{item.originAvatar,jdbcType=VARCHAR}, #{item.memberType,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.enterTime,jdbcType=TIMESTAMP}, #{item.leaveTime,jdbcType=TIMESTAMP}, #{item.dsGroupId,jdbcType=BIGINT}, 
        #{item.wxGroupId,jdbcType=VARCHAR}, #{item.corpId,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT}, 
        #{item.businesscode,jdbcType=VARCHAR}, #{item.eventTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.addTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>