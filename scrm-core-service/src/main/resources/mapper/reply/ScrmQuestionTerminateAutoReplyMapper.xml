<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.reply.dao.mapper.ScrmQuestionTerminateAutoReplyMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reply_id" jdbcType="BIGINT" property="replyId" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="receiver_id" jdbcType="VARCHAR" property="receiverId" />
    <result column="session_type" jdbcType="INTEGER" property="sessionType" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="record_reason" jdbcType="INTEGER" property="recordReason" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    <result column="question_content" jdbcType="LONGVARCHAR" property="questionContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, reply_id, corp_id, user_id, receiver_id, session_type, sender_type, message_id, 
    content, send_time, record_reason, add_time, update_time, union_id
  </sql>
  <sql id="Blob_Column_List">
    question_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.scrm.core.service.reply.dao.example.ScrmQuestionTerminateAutoReplyExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from scrm_question_terminate_auto_reply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.reply.dao.example.ScrmQuestionTerminateAutoReplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_question_terminate_auto_reply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from scrm_question_terminate_auto_reply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_question_terminate_auto_reply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.reply.dao.example.ScrmQuestionTerminateAutoReplyExample">
    delete from scrm_question_terminate_auto_reply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_question_terminate_auto_reply (reply_id, corp_id, user_id, 
      receiver_id, session_type, sender_type, 
      message_id, content, send_time, 
      record_reason, add_time, update_time, 
      union_id, question_content)
    values (#{replyId,jdbcType=BIGINT}, #{corpId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{receiverId,jdbcType=VARCHAR}, #{sessionType,jdbcType=INTEGER}, #{senderType,jdbcType=INTEGER}, 
      #{messageId,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, 
      #{recordReason,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{unionId,jdbcType=VARCHAR}, #{questionContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_question_terminate_auto_reply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="replyId != null">
        reply_id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="receiverId != null">
        receiver_id,
      </if>
      <if test="sessionType != null">
        session_type,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="recordReason != null">
        record_reason,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="questionContent != null">
        question_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="replyId != null">
        #{replyId,jdbcType=BIGINT},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="receiverId != null">
        #{receiverId,jdbcType=VARCHAR},
      </if>
      <if test="sessionType != null">
        #{sessionType,jdbcType=INTEGER},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=INTEGER},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordReason != null">
        #{recordReason,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null">
        #{questionContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.reply.dao.example.ScrmQuestionTerminateAutoReplyExample" resultType="java.lang.Long">
    select count(*) from scrm_question_terminate_auto_reply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_question_terminate_auto_reply
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.replyId != null">
        reply_id = #{record.replyId,jdbcType=BIGINT},
      </if>
      <if test="record.corpId != null">
        corp_id = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverId != null">
        receiver_id = #{record.receiverId,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionType != null">
        session_type = #{record.sessionType,jdbcType=INTEGER},
      </if>
      <if test="record.senderType != null">
        sender_type = #{record.senderType,jdbcType=INTEGER},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordReason != null">
        record_reason = #{record.recordReason,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.questionContent != null">
        question_content = #{record.questionContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update scrm_question_terminate_auto_reply
    set id = #{record.id,jdbcType=BIGINT},
      reply_id = #{record.replyId,jdbcType=BIGINT},
      corp_id = #{record.corpId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      receiver_id = #{record.receiverId,jdbcType=VARCHAR},
      session_type = #{record.sessionType,jdbcType=INTEGER},
      sender_type = #{record.senderType,jdbcType=INTEGER},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      record_reason = #{record.recordReason,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      question_content = #{record.questionContent,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_question_terminate_auto_reply
    set id = #{record.id,jdbcType=BIGINT},
      reply_id = #{record.replyId,jdbcType=BIGINT},
      corp_id = #{record.corpId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      receiver_id = #{record.receiverId,jdbcType=VARCHAR},
      session_type = #{record.sessionType,jdbcType=INTEGER},
      sender_type = #{record.senderType,jdbcType=INTEGER},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      record_reason = #{record.recordReason,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      union_id = #{record.unionId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    update scrm_question_terminate_auto_reply
    <set>
      <if test="replyId != null">
        reply_id = #{replyId,jdbcType=BIGINT},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="receiverId != null">
        receiver_id = #{receiverId,jdbcType=VARCHAR},
      </if>
      <if test="sessionType != null">
        session_type = #{sessionType,jdbcType=INTEGER},
      </if>
      <if test="senderType != null">
        sender_type = #{senderType,jdbcType=INTEGER},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordReason != null">
        record_reason = #{recordReason,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null">
        question_content = #{questionContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    update scrm_question_terminate_auto_reply
    set reply_id = #{replyId,jdbcType=BIGINT},
      corp_id = #{corpId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      receiver_id = #{receiverId,jdbcType=VARCHAR},
      session_type = #{sessionType,jdbcType=INTEGER},
      sender_type = #{senderType,jdbcType=INTEGER},
      message_id = #{messageId,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      record_reason = #{recordReason,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      union_id = #{unionId,jdbcType=VARCHAR},
      question_content = #{questionContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.reply.dao.entity.ScrmQuestionTerminateAutoReply">
    update scrm_question_terminate_auto_reply
    set reply_id = #{replyId,jdbcType=BIGINT},
      corp_id = #{corpId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      receiver_id = #{receiverId,jdbcType=VARCHAR},
      session_type = #{sessionType,jdbcType=INTEGER},
      sender_type = #{senderType,jdbcType=INTEGER},
      message_id = #{messageId,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      record_reason = #{recordReason,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      union_id = #{unionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_question_terminate_auto_reply
    (reply_id, corp_id, user_id, receiver_id, session_type, sender_type, message_id, 
      content, send_time, record_reason, add_time, update_time, union_id, question_content
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.replyId,jdbcType=BIGINT}, #{item.corpId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.receiverId,jdbcType=VARCHAR}, #{item.sessionType,jdbcType=INTEGER}, #{item.senderType,jdbcType=INTEGER}, 
        #{item.messageId,jdbcType=VARCHAR}, #{item.content,jdbcType=VARCHAR}, #{item.sendTime,jdbcType=TIMESTAMP}, 
        #{item.recordReason,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.unionId,jdbcType=VARCHAR}, #{item.questionContent,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>