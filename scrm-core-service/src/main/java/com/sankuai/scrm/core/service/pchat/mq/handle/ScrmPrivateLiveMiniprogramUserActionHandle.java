package com.sankuai.scrm.core.service.pchat.mq.handle;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.scrm.core.service.pchat.mq.dto.PrivateLiveMiniprogramUserAction;
import com.sankuai.scrm.core.service.pchat.service.ScrmIdentifyUserService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@Slf4j
@Service
public class ScrmPrivateLiveMiniprogramUserActionHandle {

    @Resource
    private ScrmIdentifyUserService scrmIdentifyUserService;

    public ConsumeStatus messageHandle(MafkaMessage message) {
        try {
            String body = message.getBody().toString();
            log.info("收到消息：topic:{},messageId:{},body:{}", message.getTopic(), message.getMessageID(), body);
            PrivateLiveMiniprogramUserAction userAction = JSONObject.parseObject(body, PrivateLiveMiniprogramUserAction.class);
            scrmIdentifyUserService.process(userAction);
        } catch (Exception e) {
            log.error("ScrmPrivateLiveMiniprogramUserActionConsumer has error,msg is {}", message, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}