package com.sankuai.scrm.core.service.infrastructure.acl;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.scrm.core.service.infrastructure.util.ScrmHttpUtils;
import com.sankuai.scrm.core.service.infrastructure.vo.WxCodeUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> on 2023/1/12 8:47 PM
 **/
@Slf4j
@Service
public class WeChatUserAcl {

    private static final String getCodeUerUrl = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%token%&code=%code%";

    public WxCodeUser getUserByCode(String token, String code){
        if(StringUtils.isEmpty(token) || StringUtils.isEmpty(code)){
            return new WxCodeUser().serverFail(500, "noToken");
        }
        String url = getCodeUerUrl.replace("%token%",token).replace("%code%",code);
        WxCodeUser codeUser = ScrmHttpUtils.safeGet(url, null, null, s->{
            if(StringUtils.isEmpty(s)){
                return new WxCodeUser().serverFail(500, "emptyResult");
            }
            log.info("getUserByCode http request result is {}",s);
            return JSONObject.parseObject(s, WxCodeUser.class);
        });
        return codeUser;
    }
}
