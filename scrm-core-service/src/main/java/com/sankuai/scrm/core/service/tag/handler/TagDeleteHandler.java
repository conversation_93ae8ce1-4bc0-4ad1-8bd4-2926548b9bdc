package com.sankuai.scrm.core.service.tag.handler;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxExternalTagAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.tag.ExternalTag;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.tag.ExternalTagGroup;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.tag.WxExternalTagResponse;
import com.sankuai.scrm.core.service.infrastructure.mq.handler.AbstractCorpExEventHandler;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.bo.TagChangeBO;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class TagDeleteHandler extends AbstractCorpExEventHandler<TagChangeBO> {

    @Autowired
    private CorpWxExternalTagAcl corpWxExternalTagAcl;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private TagDomainService tagDomainService;

    @Autowired
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Override
    protected List<String> getSupportEvent() {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.getSupportEvent()");
        return Lists.newArrayList("change_external_tag");
    }

    @Override
    protected List<String> getSupportChangeType() {
        return Lists.newArrayList("delete");
    }

    @Override
    protected Class<TagChangeBO> getDataClass() {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.getDataClass()");
        return TagChangeBO.class;
    }

    @Override
    protected void handleData(TagChangeBO data) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.handleData(com.sankuai.scrm.core.service.tag.bo.TagChangeBO)");
        if (data == null || StringUtils.isEmpty(data.getCorpId()) || StringUtils.isEmpty(data.getChangeType())) {
            return;
        }
        deleteTagOrTagGroup(data);
    }

    private void deleteTagOrTagGroup(TagChangeBO data) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.deleteTagOrTagGroup(com.sankuai.scrm.core.service.tag.bo.TagChangeBO)");
        if (data == null || StringUtils.isEmpty(data.getCorpId()) || StringUtils.isEmpty(data.getTagType())) {
            return;
        }
        switch (data.getTagType()) {
            case "tag":
                deleteTag(data.getCorpId(), data.getId());
                break;
            case "tag_group":
                deleteTagGroup(data.getCorpId(), data.getId());
                break;
        }
    }

    private void deleteTag(String corpId, String tagId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.deleteTag(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(tagId)) {
            return;
        }
        String appId = appConfigRepository.getAppIdByCorpId(corpId);
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        WxExternalTagResponse response = corpWxExternalTagAcl.getWxExternalTag(corpId, null, Lists.newArrayList(tagId));
        if (response == null || !response.isSuccess() || CollectionUtils.isEmpty(response.getTagGroupList())) {
            return;
        }
        ExternalTagGroup tagGroup = response.getTagGroupList().get(0);
        if (tagGroup == null || CollectionUtils.isEmpty(tagGroup.getTagList())) {
            return;
        }
        ExternalTag tag = tagGroup.getTagList().get(0);
        if (tag == null || BooleanUtils.isNotTrue(tag.getDeleted())) {
            return;
        }
        tagDomainService.deleteTag(appId, tagId);
        long tagCount = tagDomainService.countTag(appId, tagId);
        externalContactTagDomainService.deleteTagsByTagId(appId, tagId);
        int customerCount = externalContactTagDomainService.countTagsByTagGroupId(appId, tagGroup.getTagGroupId());
        tagDomainService.updateTagGroupInfo(appId, tagGroup.getTagGroupId(), null, (int) tagCount, customerCount, null);
    }

    private void deleteTagGroup(String corpId, String tagGroupId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.handler.TagDeleteHandler.deleteTagGroup(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(tagGroupId)) {
            return;
        }
        String appId = appConfigRepository.getAppIdByCorpId(corpId);
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        tagDomainService.deleteTags(appId, tagGroupId);
        externalContactTagDomainService.deleteTagsByTagGroupId(appId, tagGroupId);
        tagDomainService.deleteTagGroup(appId, tagGroupId);
    }

}
