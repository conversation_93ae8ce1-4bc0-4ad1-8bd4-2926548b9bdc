package com.sankuai.scrm.core.service.robot.config;

import lombok.Data;

@Data
public class RobotAssignmentConfig {

    private Integer minIntervalMinute;
    private Integer maxIntervalMinute;
    private Integer startAllowedHour;
    private Integer endAllowedHour;

    public static final RobotAssignmentConfig DEFAULT_CONFIG = buildDefaultConfig();

    private static RobotAssignmentConfig buildDefaultConfig() {
        RobotAssignmentConfig config = new RobotAssignmentConfig();
        config.setMinIntervalMinute(2);
        config.setMaxIntervalMinute(10);
        config.setStartAllowedHour(9);
        config.setEndAllowedHour(20);
        return config;
    }
}
