package com.sankuai.scrm.core.service.data.statistics.dal.babymapper;

import com.sankuai.dz.srcm.data.statistics.dto.ScrmStatisticsIndicatorDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface GroupUserLogDOMapper {

    //查询在群用户人次, 按日: 新增, 这周二与上周二, 与上个月相同日期
    ScrmStatisticsIndicatorDTO countInGroupUserFrequencyForDay(@Param("corpId") String corpId,
                                                               @Param("previousDay") Date previousDay, @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                               @Param("previousDayLastWeek") Date previousDayLastWeek, @Param("startDayLastWeek") Date startDayLastWeek, @Param("endDayLastWeek") Date endDayLastWeek,
                                                               @Param("previousDayLastMonth") Date previousDayLastMonth, @Param("startDayOfLastMonth") Date startDayOfLastMonth, @Param("endDayOfLastMonth") Date endDayOfLastMonth);

    //统计按周或月自定义: 在群, 在群增量或退群群, 以及按日下的退群人次
    ScrmStatisticsIndicatorDTO countInGroupUserFrequency(@Param("corpId") String corpId, @Param("status") Integer status,
                                                         @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                         @Param("lastPeriodStart") Date lastPeriodStart, @Param("lastPeriodEnd") Date lastPeriodEnd,
                                                         @Param("beforeLastPeriodStart") Date beforeLastPeriodStart, @Param("beforeLastPeriodEnd") Date beforeLastPeriodEnd);
}
