package com.sankuai.dz.srcm.pchat.enums;

import lombok.Getter;

@Getter
public enum ApiMappingConfigEnum {

    GetGroupQRCode("/scrm/ChatRoom/GetGroupQRCode", "【异步调用】获取群二维码接口"),

    ;

    private final String code;

    private final String desc;

    ApiMappingConfigEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ApiMappingConfigEnum fromCode(String code) {
        for (ApiMappingConfigEnum enumValue : ApiMappingConfigEnum.values()) {
            if (enumValue.code.equals( code)) {
                return enumValue;
            }
        }
        return null;
    }

}
