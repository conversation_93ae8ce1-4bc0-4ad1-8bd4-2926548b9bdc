package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationExecuteLogDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationExecuteLogDO, ScrmAmProcessOrchestrationExecuteLogDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationExecuteLogDO> list);
}