package com.sankuai.scrm.core.service.faq.domain.service;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.faq.domain.entity.BizAuthorEntity;
import com.sankuai.scrm.core.service.faq.domain.entity.BizCategory;
import com.sankuai.scrm.core.service.faq.domain.entity.CategoryItemEntity;
import com.sankuai.scrm.core.service.faq.domain.vo.AuthorBizType;
import com.sankuai.scrm.core.service.faq.domain.vo.AuthorUserType;
import com.sankuai.scrm.core.service.faq.infrastructure.repository.BizAuthorRepository;
import com.sankuai.scrm.core.service.faq.infrastructure.repository.CategoryRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2022/12/9 10:12 AM
 **/
@Service
public class BizCategoryService {
    @Autowired
    private BizAuthorRepository authorRepository;
    @Autowired
    private CategoryRepository categoryRepository;

    public Long saveCategoryItem(CategoryItemEntity item){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.saveCategoryItem(com.sankuai.scrm.core.service.faq.domain.entity.CategoryItemEntity)");
        if(item.getId() == null){
            categoryRepository.insert(item);
        }else {
            categoryRepository.updateById(item);
        }
        return item.getId();
    }



    public void saveAuth(long firstCategoryId,List<String> misIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.saveAuth(long,java.util.List)");
        if(CollectionUtils.isEmpty(misIds))return ;
        List<String> old = queryCategoryAuthors(firstCategoryId);
        Collection<String> needDel = CollectionUtils.subtract(old, misIds);
        Collection<String> needAdd = CollectionUtils.subtract(misIds, old);
        deleteAuth(firstCategoryId,Lists.newArrayList(needDel));
        insertAuth(firstCategoryId,Lists.newArrayList(needAdd));
    }

    public List<String> queryCategoryAuthors(long firstCategoryId){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.queryCategoryAuthors(long)");
        List<BizAuthorEntity> list = authorRepository.queryValid(BizAuthorEntity.builder()
                                            .bizType(AuthorBizType.FAQ_CATEGORY.code)
                                            .bizId(String.valueOf(firstCategoryId))
                                            .userType(AuthorUserType.DX_MIS.code).build());
        return Optional.ofNullable(list).orElse(Lists.newArrayList()).stream()
                .map(r->r.getUserId()).collect(Collectors.toList());
    }
    public Map<Long,List<String>> batchQueryCategoryAuthors(List<Long> firstCategoryIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.batchQueryCategoryAuthors(java.util.List)");
        if(CollectionUtils.isEmpty(firstCategoryIds))return Maps.newHashMap();
        return authorRepository.queryValidUser(firstCategoryIds.stream().map(r->r.toString()).collect(Collectors.toList()),
                                               AuthorBizType.FAQ_CATEGORY.code,  AuthorUserType.DX_MIS.code)
                .entrySet()
                .stream()
                .collect(Collectors.toMap(r-> NumberUtils.toLong(r.getKey()),r->r.getValue()));
    }

    public void deleteAuth(long firstCategoryId,List<String> misIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.deleteAuth(long,java.util.List)");
        if(CollectionUtils.isEmpty(misIds))return;
        authorRepository.batchDelete(misIds.stream()
                                           .map(r->buildCategoryAuth(firstCategoryId,r))
                                           .collect(Collectors.toList()));
    }

    public void insertAuth(long firstCategoryId,List<String> misIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.insertAuth(long,java.util.List)");
        if(CollectionUtils.isEmpty(misIds))return;
        authorRepository.saveOrNot(misIds.stream()
                                           .map(r->buildCategoryAuth(firstCategoryId,r))
                                           .collect(Collectors.toList()));
    }

    private static BizAuthorEntity buildCategoryAuth(long firstCategoryId,String mis) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.buildCategoryAuth(long,java.lang.String)");
        return BizAuthorEntity.builder()
                .bizType(AuthorBizType.FAQ_CATEGORY.code)
                .bizId(String.valueOf(firstCategoryId))
                .userType(AuthorUserType.DX_MIS.code)
                .userId(mis)
                .build();
    }

    public List<BizCategory> querySortedValidAll(String appId){
        Map<Long,List<CategoryItemEntity>> firstMap = categoryRepository.queryValidByPid(Lists.newArrayList(0l),appId);
        List<CategoryItemEntity> first = firstMap.get(0L);
        if(CollectionUtils.isEmpty(first))return Lists.newArrayList();
        List<Long> firstIds = first.stream().map(r->r.getId()).collect(Collectors.toList());
        Map<Long,List<CategoryItemEntity>> secondMap = categoryRepository.queryValidByPid(firstIds,appId);

        return first.stream().map(item -> toBizCategory(item,secondMap.get(item.getId())))
                .sorted(Comparator.comparingInt(o -> o.getCategory().getRank()))
                .collect(Collectors.toList());

    }

    private BizCategory toBizCategory(CategoryItemEntity firstItem, List<CategoryItemEntity> subList){
        BizCategory bizCategory = new BizCategory();
        bizCategory.setCategory(firstItem);
        bizCategory.setSubCategories(Optional.ofNullable(subList).orElse(Lists.newArrayList()).stream()
                                          .sorted(Comparator.comparingInt(CategoryItemEntity::getRank)
                                                          .thenComparing(r->r.getName().length()))
                                          .collect(Collectors.toList()));
        return bizCategory;
    }

    public void batchDel(List<Long> categoryIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.batchDel(java.util.List)");
        if(CollectionUtils.isEmpty(categoryIds))return;
        categoryRepository.batchDelete(categoryIds);
    }

    public List<CategoryItemEntity> querySub(long firstCategoryId){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.querySub(long)");
        Map<Long,List<CategoryItemEntity>> map = categoryRepository.queryValidByPid(Lists.newArrayList(firstCategoryId));
        return map.getOrDefault(firstCategoryId,Lists.newArrayList());
    }

    public List<CategoryItemEntity> queryByIds(List<Long> ids){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.BizCategoryService.queryByIds(java.util.List)");
        if(CollectionUtils.isEmpty(ids))return Lists.newArrayList();
        return categoryRepository.queryValidByIds(ids);
    }

}
