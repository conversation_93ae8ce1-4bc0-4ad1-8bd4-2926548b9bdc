package com.sankuai.scrm.core.service.user.domain.score.data;

import com.sankuai.dz.srcm.user.enums.UserLiveViewActionType;
import com.sankuai.dz.srcm.user.score.BehaviorDTO;
import com.sankuai.dz.srcm.user.score.BehaviorQueryContext;
import com.sankuai.dz.srcm.user.score.UserTagBehaviorEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.UserAliveTimeResponse;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.request.GetUserAliveTimeRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmLiveUserViewActionInfo;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmLiveUserViewActionInfoExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmLiveUserViewActionInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 弹幕
 * <AUTHOR> ScrmLiveUserViewActionInfo 是明细表，如果同时查询多个类型，不能及时聚类，需要查询很多条，数据分布不均，可能会查询不出，分解为2个类
 * @Create On 2025/4/2 17:10
 * @Version v1.0.0
 */
@Slf4j
//@Component
@Deprecated
public class BehaviorQueryLiveUserViewActionInfoService implements BehaviorQueryService {
    @Resource
    private ScrmLiveUserViewActionInfoMapper liveUserViewActionInfoMapper;
    @Resource
    private LiveRoomRpcService liveRoomRpcService;

    @Override
    public List<BehaviorDTO> doQuery(BehaviorQueryContext<ScrmUserGrowthYimeiLiveUserTag> context) {
        ScrmLiveUserViewActionInfoExample example = new ScrmLiveUserViewActionInfoExample();
        ScrmLiveUserViewActionInfoExample.Criteria criteria = example.createCriteria();
        if (context.getStartTime() != null) {
            criteria.andActionTimeGreaterThanOrEqualTo(context.getStartTime());
        }
        if (context.getEndTime() != null) {
            criteria.andActionTimeLessThanOrEqualTo(context.getEndTime());
        }
        if (StringUtils.isNotBlank(context.getLiveId())) {
            criteria.andProjectIdEqualTo(context.getLiveId());
        }
        if (StringUtils.isNotBlank(context.getUnionId())) {
            criteria.andUnionIdEqualTo(context.getUnionId());
        }
        criteria.andActionTypeIn(Arrays.asList(UserLiveViewActionType.SEND_MESSAGE.code, UserLiveViewActionType.VIEW_LIVE.code));
        if (context.getMaxId() != null) {
            criteria.andIdLessThan(context.getMaxId());
        }
        example.setOrderByClause("id desc");
        if (context.getPageNo() != null && context.getPageSize() != null) {
            example.page(context.getPageNo(), context.getPageSize());
        } else if (context.getPageSize() != null) {
            example.limit(context.getPageSize());
        } else {
            example.limit(maxQuerySize);
        }


        List<ScrmLiveUserViewActionInfo> result = liveUserViewActionInfoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        List<ScrmLiveUserViewActionInfo> filteredLog = new ArrayList<>();
        Set<String> keywords = new HashSet<>();
        for (ScrmLiveUserViewActionInfo data : result) {
            String keyword = data.getUnionId() + data.getActionType();
            if (UserLiveViewActionType.SEND_MESSAGE.code == data.getActionType()) {
                if (data.getProductId() == null || data.getProductId() <= 0) {
                    continue;
                }
                keyword += data.getProductId();
            }

            if (keywords.contains(keyword)) {
                continue;
            }
            filteredLog.add(data);
            keywords.add(keyword);
        }

        return filteredLog.stream().map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private BehaviorDTO convert(ScrmLiveUserViewActionInfo data) {
        BehaviorDTO behaviorDTO = new BehaviorDTO();
        behaviorDTO.setUnionId(data.getUnionId());
        behaviorDTO.setUserId(data.getUserId());
        behaviorDTO.setLiveId(data.getProjectId());
        behaviorDTO.setAddTime(data.getUpdateTime());
        behaviorDTO.setReferenceId(data.getId());
        UserTagBehaviorEnum behaviorEnum;
        if (UserLiveViewActionType.SEND_MESSAGE.code == data.getActionType()) {
            behaviorEnum = UserTagBehaviorEnum.COMMENT;
            if (data.getProductId() == null || data.getProductId() <= 0) {
                return null;
            }
        } else if (UserLiveViewActionType.VIEW_LIVE.code == data.getActionType()) {
            if (!isViewLiveActionOver5Min(data)) {
                return null;
            }
            behaviorEnum = UserTagBehaviorEnum.VIEW_LIVE_TAG;
        } else {
            return null;
        }
        behaviorDTO.setBehaviorTypeEnum(behaviorEnum);
        behaviorDTO.setBehaviorType(behaviorEnum.name());

        behaviorDTO.setProductId(data.getProductId());
        behaviorDTO.setProductType(data.getProductType());
        return behaviorDTO;
    }

    // todo
    private boolean isViewLiveActionOver5Min(ScrmLiveUserViewActionInfo data) {
        return StringUtils.isNotBlank(data.getProjectId()) && StringUtils.isNotBlank(data.getUnionId()) && obtainAllViewDuration(data.getProjectId(), data.getUnionId()) > 5 * 60;
    }

    /**
     * 获取观看直播时长
     *
     * @param liveId
     * @param unionId
     */
    private long obtainAllViewDuration(String liveId, String unionId) {
        GetUserAliveTimeRequest getUserAliveTimeRequest = new GetUserAliveTimeRequest();
        getUserAliveTimeRequest.setLiveId(liveId);
        getUserAliveTimeRequest.setUnionId(unionId);
        ResponseDTO<UserAliveTimeResponse> userAliveTime = liveRoomRpcService.getUserAliveTime(getUserAliveTimeRequest);
        if (userAliveTime != null && userAliveTime.getData() != null) {
            return userAliveTime.getData().getUserAliveTime();
        }
        return 0;
    }


    @Override
    public List<UserTagBehaviorEnum> supportBehavior() {
        return Arrays.asList(UserTagBehaviorEnum.COMMENT, UserTagBehaviorEnum.VIEW_LIVE_TAG);
    }
}
