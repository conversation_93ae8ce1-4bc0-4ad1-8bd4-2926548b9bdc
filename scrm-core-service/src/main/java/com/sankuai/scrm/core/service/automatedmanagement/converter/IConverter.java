package com.sankuai.scrm.core.service.automatedmanagement.converter;

import java.util.List;

/**
 * 类型转换接口，用于不同数据类型的转换
 * 
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/16
 */
public interface IConverter<T,U> {
    /**
     * 转换DTO
     * @param resource
     * @return
     */
    T convertToDTO(U resource);
    /**
     * 转换DTO，不抛出异常，失败返回Null
     * @param resource
     * @return
     */
    T convertToDTOSafety(U resource);
    /**
     * 转换DO
     * @param resource
     * @return
     */
    U convertToDO(T resource);
    /**
     * 转换DO，不抛出异常，失败返回Null
     * @param resource
     * @return
     */
    U convertToDOSafety(T resource);

    /**
     * 批量转换DTO, 单条数据直接丢弃
     * @param resource
     * @return
     */
    List<T> convertToDTOs(List<U> resource);
    /**
     * 批量转换DTO，不抛出异常，失败返回emptyList, 单条数据直接丢弃
     * @param resource
     * @return
     */
    List<T> convertToDTOsSafety(List<U> resource);
    /**
     * 批量转换DO, 单条数据直接丢弃
     * @param resource
     * @return
     */
    List<U> convertToDOs(List<T> resource);
    /**
     * 批量转换DO，不抛出异常，失败返回emptyList, 单条数据直接丢弃
     * @param resource
     * @return
     */
    List<U> convertToDOsSafety(List<T> resource);
}
