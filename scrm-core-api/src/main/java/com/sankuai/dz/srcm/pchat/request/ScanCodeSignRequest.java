package com.sankuai.dz.srcm.pchat.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/6 19:21
 * @Version v1.0.0
 */
@Data
public class ScanCodeSignRequest implements Serializable {
    /**
     * 商家编号（必填）
     */
    private String merchant_no;

    /**
     * 协议类型（1：IPAD协议 6：云端注入版 9：PC协议版 12：Mac协议 13：安卓平板协议）（必填）
     */
    private Integer protocol_type;

    /**
     * 登录方式（true：确认方式登录、false：直接扫码登录）
     */
    private Boolean is_cache_login;

    /**
     * 机器人编号，第一次登录至开放平台的机器人，机器人编号传空。当机器人第二次登录时必传对应的机器人编号
     */
    private String robot_serial_no;

    /**
     * 扫码设备的地区编码:地级市行政区域代码
     */
    private Integer region_code;

    /**
     * 默认传false
     */
    private Boolean is_official_account_auth=false;

    /**
     * 关联编号
     */
    private String relation_serial_no;

    /**
     * 是否VIP设备（ 是：true、否：false）
     */
    private Boolean is_vip_device;
}
