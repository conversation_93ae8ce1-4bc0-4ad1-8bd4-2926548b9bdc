package com.sankuai.scrm.core.service.pchat.group.ent;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.group.dynamiccode.util.MemberStateParser;
import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeStatisticMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeStatistic;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeStatisticExample;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxGroupAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupMemberDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxUserIdDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.adapter.utils.WechatTypeUtils;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntityWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupInfoEntityExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalGroupWelcomeMsgDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionIdentificationDomainService;
import com.sankuai.scrm.core.service.pchat.domain.group.PrivateLiveGroupMemberDomainService;
import com.sankuai.scrm.core.service.pchat.enums.*;
import com.sankuai.scrm.core.service.pchat.event.GroupMemberEvent;
import com.sankuai.scrm.core.service.pchat.event.v2.GroupMemberEventV2;
import com.sankuai.scrm.core.service.pchat.service.WebcastService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.service.fe.corp.wx.thrift.AssisantInfoModel;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.WeixinInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PrivateLiveEntGroupChangeService {

    @Autowired
    private WechatTypeUtils wechatTypeUtils;

    @Autowired
    private ScrmPersonalWxGroupInfoEntityMapper personalWxGroupInfoEntityMapper;

    @Autowired
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Resource
    private WebcastService webcastService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private DsAssistantAcl dsAssistantAcl;

    @Autowired
    private CorpWxGroupAcl corpWxGroupAcl;

    @Autowired
    private MtUserCenterAclService mtUserCenterAclService;

    @Resource
    private ScrmRestrictionIdentificationDomainService scrmRestrictionIdentificationDomainService;

    @Autowired
    private GroupDynamicCodeStatisticMapper statisticMapper;

    @Autowired
    private PrivateLiveGroupMemberDomainService memberDomainService;

    @Autowired
    private PrivateLiveEntInvitationService entInvitationService;
    @Resource
    private ScrmPersonalGroupWelcomeMsgDomainService personalGroupWelcomeMsgDomainService;

    public void handleGroupDetail(String corpId, String groupId) {
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(groupId)) {
            return;
        }
        try {
            WxGroupDetail wxGroupDetail = corpWxGroupAcl.getWxGroupDetail(corpId, groupId, true);
            log.info("PrivateLiveGroupChangeService.getWxGroupDetail wxGroupDetail={}", wxGroupDetail);
            handleGroupDetail(corpId, wxGroupDetail);
        } catch (Exception e) {
            log.error("PrivateLiveGroupChangeService.handleGroupDetail exception, corpId={}, groupId={}", corpId, groupId, e);
        }
    }

    public void handleGroupDetail(String corpId, WxGroupDetail wxGroupDetail) {
        if (StringUtils.isEmpty(corpId) || wxGroupDetail == null) {
            return;
        }
        String appId = appConfigRepository.getAppIdByCorpId(corpId);
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        List<String> appIdList = wechatTypeUtils.getEnterpriseAppIdList();
        if (!appIdList.contains(appId)) {
            return;
        }
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = queryGroupInfo(appId, wxGroupDetail.getGroupId());
        if (groupInfoEntity == null) {
            return;
        }
        // 更新数据库群信息
        updateGroupInfo(appId, wxGroupDetail, groupInfoEntity);
        // 更新数据库群成员信息
        updateGroupMemberInfo(appId, wxGroupDetail, groupInfoEntity);
    }

    private ScrmPersonalWxGroupInfoEntity queryGroupInfo(String appId, String groupId) {
        ScrmPersonalWxGroupInfoEntity queryCondition = ScrmPersonalWxGroupInfoEntity.builder()
                .appId(appId)
                .groupId(groupId)
                .build();
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntityList = groupManageDomainService.queryGroupByEntity(queryCondition);
        return groupInfoEntityList.stream().findFirst().orElse(null);
    }

    private void updateGroupInfo(String appId, WxGroupDetail wxGroupDetail, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        if (StringUtils.isEmpty(appId) || wxGroupDetail == null || groupInfoEntity == null) {
            return;
        }
        groupInfoEntity.setMemberCount(Optional.ofNullable(wxGroupDetail.getMemberList()).map(List::size).orElse(0));
        groupInfoEntity.setGroupName(wxGroupDetail.getName());
        groupInfoEntity.setOwner(wxGroupDetail.getOwner());
        groupInfoEntity.setOwnerWxId(wxGroupDetail.getOwner());
        ScrmPersonalWxGroupInfoEntityWithBLOBs wxGroupInfoEntityWithBLOBs = (ScrmPersonalWxGroupInfoEntityWithBLOBs) groupInfoEntity;
        wxGroupInfoEntityWithBLOBs.setExtra(JsonUtils.toStr(wxGroupDetail));
        if (CollectionUtils.isNotEmpty(wxGroupDetail.getAdminList())) {
            List<String> adminList = wxGroupDetail.getAdminList().stream()
                    .map(WxUserIdDetail::getUserId)
                    .collect(Collectors.toList());
            wxGroupInfoEntityWithBLOBs.setAdminList(JsonUtils.toStr(adminList));
        } else {
            wxGroupInfoEntityWithBLOBs.setAdminList("");
        }
        groupManageDomainService.updateGroupWithBlobs(wxGroupInfoEntityWithBLOBs);
    }


    public boolean deleteGroup(String corpId, String groupId) {
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(groupId)) {
            return false;
        }
        String appId = appConfigRepository.getAppIdByCorpId(corpId);
        if (StringUtils.isEmpty(appId)) {
            return false;
        }
        WeChatType wechatType = wechatTypeUtils.getWechatType(appId);
        if (!WeChatType.ENTERPRISE_WECHAT.equals(wechatType)) {
            return false;
        }
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntityList = queryGroup(appId, groupId);
        if (CollectionUtils.isEmpty(groupInfoEntityList) || groupInfoEntityList.get(0) == null) {
            return false;
        }
        ScrmPersonalWxGroupInfoEntity entity = groupInfoEntityList.get(0);
        entity.setStatus(PersonalGroupStatusEnum.DISMISSED.getCode());
        int row = personalWxGroupInfoEntityMapper.updateByPrimaryKeySelective(entity);
        return row > 0;
    }

    private List<ScrmPersonalWxGroupInfoEntity> queryGroup(String appId, String groupId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(groupId)) {
            return null;
        }
        ScrmPersonalWxGroupInfoEntityExample example = new ScrmPersonalWxGroupInfoEntityExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andGroupIdEqualTo(groupId);
        return personalWxGroupInfoEntityMapper.selectByExampleWithBLOBs(example);
    }

    private void updateGroupMemberInfo(String appId, WxGroupDetail wxGroupDetail, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        if (StringUtils.isEmpty(appId) || wxGroupDetail == null || groupInfoEntity == null) {
            return;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> dbMemberList = queryGroupMemberList(appId, wxGroupDetail.getGroupId(), PersonalWxGroupMemberStatusEnum.IN_GROUP);
        Map<String, ScrmPersonalWxGroupMemberInfoEntity> dbMemberMap = dbMemberList.stream()
                .collect(Collectors.toMap(ScrmPersonalWxGroupMemberInfoEntity::getWxId, Function.identity(), (k1, k2) -> k1));
        List<WxGroupMemberDetail> wxMemberList = Optional.ofNullable(wxGroupDetail.getMemberList()).orElse(Lists.newArrayList());
        Map<String, WxGroupMemberDetail> wxMemberMap = wxMemberList.stream()
                .collect(Collectors.toMap(WxGroupMemberDetail::getGroupMemberId, Function.identity(), (k1, k2) -> k1));
        // 进群成员
        List<WxGroupMemberDetail> enterGroupMemberList = Lists.newArrayList();
        wxMemberMap.forEach((wxMemberId, wxMemberDetail) -> {
            if (dbMemberMap.get(wxMemberId) == null) {
                enterGroupMemberList.add(wxMemberDetail);
            } else {
                updateGroupMemberBaseInfo(dbMemberMap.get(wxMemberId), wxMemberDetail);
            }
        });
        // 退群成员
        List<ScrmPersonalWxGroupMemberInfoEntity> quitGroupMemberList = Lists.newArrayList();
        dbMemberMap.forEach((dbMemberId, dbMemberEntity) -> {
            if (wxMemberMap.get(dbMemberId) == null) {
                quitGroupMemberList.add(dbMemberEntity);
            }
        });
        // 处理成员入群信息
        processMemberEnter(appId, groupInfoEntity, enterGroupMemberList);
        // 处理成员离群信息
        processMemberQuit(appId, groupInfoEntity, quitGroupMemberList);
    }

    private List<ScrmPersonalWxGroupMemberInfoEntity> queryGroupMemberList(String appId, String groupId, PersonalWxGroupMemberStatusEnum memberStatus) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(groupId)) {
            return Lists.newArrayList();
        }
        ScrmPersonalWxGroupMemberInfoEntity queryCondition = ScrmPersonalWxGroupMemberInfoEntity.builder()
                .appId(appId)
                .groupId(groupId)
                .status(memberStatus != null ? memberStatus.getCode() : null)
                .deleted(false)
                .build();
        return groupManageDomainService.queryGroupMemberByEntity(queryCondition);
    }

    private void processMemberEnter(String appId, ScrmPersonalWxGroupInfoEntity groupInfoEntity, List<WxGroupMemberDetail> memberDetailList) {
        if (StringUtils.isEmpty(appId) || groupInfoEntity == null || CollectionUtils.isEmpty(memberDetailList)) {
            return;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> memberEntityList = saveOrUpdateGroupMember(appId, groupInfoEntity, memberDetailList);
        //  发送欢迎语
        personalGroupWelcomeMsgDomainService.sendWelcomeMsg(groupInfoEntity.getChatRoomWxSerialNo(),memberEntityList);
        saveInvitationRecord(groupInfoEntity.getProjectId(), memberEntityList);
        entInvitationService.addInviteRecord(appId, groupInfoEntity.getGroupId(), memberEntityList);
        // V2版本
        GroupMemberEventV2 eventV2 = new GroupMemberEventV2(memberEntityList);
        eventV2.setRobotSerialNo(groupInfoEntity.getOwner());
        eventV2.setGroupInfoEntity(groupInfoEntity);
        applicationEventPublisher.publishEvent(eventV2);
        scrmRestrictionIdentificationDomainService.identifyUser(memberEntityList);
    }

    private void processMemberQuit(String appId, ScrmPersonalWxGroupInfoEntity groupInfoEntity, List<ScrmPersonalWxGroupMemberInfoEntity> memberEntityList) {
        if (StringUtils.isEmpty(appId) || groupInfoEntity == null || CollectionUtils.isEmpty(memberEntityList)) {
            return;
        }
        // 处理用户退群
        List<Long> memberIdList = memberEntityList.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getId).collect(Collectors.toList());
        groupManageDomainService.batchUpdateGroupMemberStatus(memberIdList, PersonalWxGroupMemberStatusEnum.LEAVE_GROUP);
        memberEntityList.forEach(memberEntity -> {
            GroupMemberEvent groupMemberEvent = new GroupMemberEvent(Collections.singletonList(memberEntity));
            applicationEventPublisher.publishEvent(groupMemberEvent);
        });
        // 下面是V2版本
        GroupMemberEventV2 eventV2 = new GroupMemberEventV2(memberEntityList);
        eventV2.setRobotSerialNo(groupInfoEntity.getOwner());
        eventV2.setGroupInfoEntity(groupInfoEntity);
        applicationEventPublisher.publishEvent(eventV2);
        // 删除咨询师
        removeConsultant(groupInfoEntity, memberEntityList);
        // 更新邀请关系
        entInvitationService.updateInviteRecordList(appId, groupInfoEntity.getGroupId(), memberEntityList);
    }

    private void removeConsultant(ScrmPersonalWxGroupInfoEntity groupInfoEntity, List<ScrmPersonalWxGroupMemberInfoEntity> memberEntityList) {
        if (groupInfoEntity == null || CollectionUtils.isEmpty(memberEntityList)) {
            return;
        }
        boolean hasConsultant = memberEntityList.stream().anyMatch(entity -> PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode().equals(entity.getMemberType()));
        if (!hasConsultant) {
            return;
        }
        if (webcastService.isPersonalTrace(groupInfoEntity.getProjectId())) {
            log.info("个人溯源方式，咨询师退群，不做任何任何处理，groupId:{},projectId:{}", groupInfoEntity.getGroupId(), groupInfoEntity.getProjectId());
            return;
        }
        groupManageDomainService.removeGroupConstant(groupInfoEntity);
    }

    private List<ScrmPersonalWxGroupMemberInfoEntity> saveOrUpdateGroupMember(String appId, ScrmPersonalWxGroupInfoEntity groupInfoEntity, List<WxGroupMemberDetail> memberDetailList) {
        if (StringUtils.isEmpty(appId) || groupInfoEntity == null || CollectionUtils.isEmpty(memberDetailList)) {
            return Lists.newArrayList();
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> saveMemberEntityList = new ArrayList<>();
        List<ScrmPersonalWxGroupMemberInfoEntity> updateMemberEntityList = new ArrayList<>();
        List<ScrmPersonalWxGroupMemberInfoEntity> dbMemberEntityList = queryGroupMemberList(appId, groupInfoEntity.getGroupId(), null);
        Map<String, ScrmPersonalWxGroupMemberInfoEntity> dbMemberEntityMap = dbMemberEntityList.stream()
                .collect(Collectors.toMap(ScrmPersonalWxGroupMemberInfoEntity::getUserSerialNo, Function.identity(), (k1, k2) -> k1));
        List<AssisantInfoModel> assisantInfoModelList = dsAssistantAcl.getAllAssistantList(appId);
        Set<String> robotSet = assisantInfoModelList.stream().map(AssisantInfoModel::getUserid).collect(Collectors.toSet());
        for (WxGroupMemberDetail memberDetail : memberDetailList) {
            // 获取邀请人
            ScrmPersonalWxGroupMemberInfoEntity inviterMemberInfo = matchInviter(appId, memberDetail);
            ScrmPersonalWxGroupMemberInfoEntity entity = dbMemberEntityMap.get(memberDetail.getGroupMemberId());
            if (entity != null) {
                if (!PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode().equals(entity.getStatus())) {
                    entity.setEnterTime(memberDetail.getJoinTime());
                    entity.setTimeTrace(System.currentTimeMillis());
                }
                if (!PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode().equals(entity.getMemberType())) {
                    PersonalWxGroupMemberTypeEnum memberType = getGroupMemberType(robotSet, memberDetail, groupInfoEntity);
                    entity.setMemberType(memberType.getCode());
                }
                updateMemberEntityList.add(entity);
            } else {
                entity = new ScrmPersonalWxGroupMemberInfoEntity();
                entity.setAppId(appId);
                entity.setGroupId(groupInfoEntity.getGroupId());
                entity.setState(memberDetail.getState());
                entity.setDeleted(Boolean.FALSE);
                entity.setUserSerialNo(memberDetail.getGroupMemberId());
                PersonalWxGroupMemberTypeEnum memberType = getGroupMemberType(robotSet, memberDetail, groupInfoEntity);
                entity.setMemberType(memberType.getCode());
                entity.setWxId(memberDetail.getGroupMemberId());
                entity.setAddTime(new Date());
                entity.setEnterTime(memberDetail.getJoinTime());
                entity.setTimeTrace(System.currentTimeMillis());
                entity.setUnionId(memberDetail.getUnionId());
                saveMemberEntityList.add(entity);
            }
            String inviterWxId = inviterMemberInfo != null ? inviterMemberInfo.getWxId() : null;
            PersonalWxGroupMemberJoinTypeEnum joinType = getJoinType(appId, groupInfoEntity.getGroupId(), inviterWxId, robotSet);
            entity.setJoinType(joinType.getCode());
            entity.setStatus(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
            entity.setMemberNickName(memberDetail.getGroupNickName());
            entity.setWxNickname(memberDetail.getName());
            if (inviterMemberInfo != null) {
                entity.setInviteWxId(inviterMemberInfo.getWxId());
                entity.setInviteUserSerialNo(inviterMemberInfo.getUserSerialNo());
            }
            WeixinInfo weixinInfo = mtUserCenterAclService.getWeixinInfo(memberDetail.getUnionId(), "wxde8ac0a21135c07d");
            if (weixinInfo != null) {
                entity.setUserId(weixinInfo.getUserId());
                if (StringUtils.isNotEmpty(weixinInfo.getAvatarUrl())) {
                    entity.setAvatar("https://img.meituan.net" + weixinInfo.getAvatarUrl());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(saveMemberEntityList)) {
            groupManageDomainService.batchInsertMemberList(saveMemberEntityList);
        }
        updateMemberEntityList.forEach(entity -> {
            entity.setUpdateTime(new Date());
            groupManageDomainService.updateGroupMemberById(entity);
        });
        List<ScrmPersonalWxGroupMemberInfoEntity> allMemberEntityList = new ArrayList<>();
        allMemberEntityList.addAll(saveMemberEntityList);
        allMemberEntityList.addAll(updateMemberEntityList);
        return allMemberEntityList;
    }

    private PersonalWxGroupMemberTypeEnum getGroupMemberType(Set<String> robotIdSet, WxGroupMemberDetail memberDetail, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        if (robotIdSet.contains(memberDetail.getGroupMemberId())) {
            return PersonalWxGroupMemberTypeEnum.ROBOT;
        }
        if (StringUtils.isBlank(groupInfoEntity.getConsultantList())) {
            return PersonalWxGroupMemberTypeEnum.NORMAL;
        }
        String consultantList = groupInfoEntity.getConsultantList();
        List<ConsultantInfoDTO> consultantInfoDTOList = JsonUtils.toList(consultantList, ConsultantInfoDTO.class);
        if (CollectionUtils.isEmpty(consultantInfoDTOList)) {
            return PersonalWxGroupMemberTypeEnum.NORMAL;
        }
        boolean match = consultantInfoDTOList.stream().anyMatch(consultantInfoDTO -> consultantInfoDTO.getUnionId().equals(memberDetail.getUnionId()));
        return match ? PersonalWxGroupMemberTypeEnum.CONSULTANT : PersonalWxGroupMemberTypeEnum.NORMAL;
    }

    private void updateGroupMemberBaseInfo(ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity, WxGroupMemberDetail wxMemberDetail) {
        if (dbMemberEntity == null || wxMemberDetail == null) {
            return;
        }
        boolean needUpdate = false;
        if (!StringUtils.equals(dbMemberEntity.getWxNickname(), wxMemberDetail.getName())) {
            dbMemberEntity.setWxNickname(wxMemberDetail.getName());
            needUpdate = true;
        }
        if (!StringUtils.equals(dbMemberEntity.getMemberNickName(), wxMemberDetail.getGroupNickName())) {
            dbMemberEntity.setMemberNickName(wxMemberDetail.getGroupNickName());
            needUpdate = true;
        }
        if (needUpdate) {
            groupManageDomainService.updateGroupMemberById(dbMemberEntity);
        }
    }

    private ScrmPersonalWxGroupMemberInfoEntity matchInviter(String appId, WxGroupMemberDetail memberDetail) {
        if (StringUtils.isEmpty(appId) || memberDetail == null) {
            return null;
        }
//        if (memberDetail.getInvitor() != null && StringUtils.isNotEmpty(memberDetail.getInvitor().getUserId())) {
//            List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = memberDomainService.queryGroupMemberByUserSerialNo(appId, memberDetail.getInvitor().getUserId());
//            return memberInfoEntityList.stream().findFirst().orElse(null);
//        }
        if (StringUtils.isEmpty(memberDetail.getState())) {
            return null;
        }
        MemberStateParser.ParseResult parseResult = MemberStateParser.parse(memberDetail.getState());
        if (!parseResult.isFromDynamicCode()) {
            return null;
        }
        GroupDynamicCodeStatistic statistic = matchCodeStatistic(parseResult.getChannelId(), memberDetail);
        if (statistic == null || StringUtils.isEmpty(statistic.getInviterUnionId()) || statistic.getInviterUnionId().equals(memberDetail.getUnionId())) {
            return null;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = memberDomainService.queryGroupMemberByUnionId(appId, statistic.getInviterUnionId());
        return memberInfoEntityList.stream().findFirst().orElse(null);
    }

    private GroupDynamicCodeStatistic matchCodeStatistic(Long channelId, WxGroupMemberDetail memberDetail) {
        if (channelId == null || memberDetail == null || StringUtils.isEmpty(memberDetail.getUnionId())) {
            return null;
        }
        GroupDynamicCodeStatisticExample example = new GroupDynamicCodeStatisticExample();
        example.createCriteria()
                .andUnionIdEqualTo(memberDetail.getUnionId())
                .andChannelIdEqualTo(channelId)
                .andEventTypeEqualTo(2);
        example.setOrderByClause("add_time DESC");
        List<GroupDynamicCodeStatistic> statisticList = statisticMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(statisticList)) {
            return null;
        }
        return statisticList.stream()
                .filter(statistic -> statistic.getAddTime().compareTo(memberDetail.getJoinTime()) <= 0)
                .findFirst().orElse(null);
    }

    private PersonalWxGroupMemberJoinTypeEnum getJoinType(String appId, String groupId, String inviterWxId, Set<String> robotSet) {
        if (StringUtils.isEmpty(inviterWxId)) {
            return PersonalWxGroupMemberJoinTypeEnum.SCAN_QR;
        }
        if (robotSet.contains(inviterWxId)) {
            return PersonalWxGroupMemberJoinTypeEnum.ROBOT_PULL;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> consultantMemberList = groupManageDomainService.queryGroupConsultantMember(appId, Lists.newArrayList(groupId), PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
        Set<String> consultantWxIdSet = consultantMemberList.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getWxId).collect(Collectors.toSet());
        if (consultantWxIdSet.contains(inviterWxId)) {
            return PersonalWxGroupMemberJoinTypeEnum.CONSULTANT_PULL;
        }
        return PersonalWxGroupMemberJoinTypeEnum.PERSON_PULL_PERSON;
    }

    private void saveInvitationRecord(String liveId, List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList) {
        if (StringUtils.isEmpty(liveId) || CollectionUtils.isEmpty(memberInfoEntityList)) {
            return;
        }
        try {
            groupManageDomainService.saveInviteRecord(liveId, null, memberInfoEntityList);
        } catch (Exception e) {
            log.error("保存邀请关系异常：" + e.getMessage(), e);
        }
    }
}
