package com.sankuai.scrm.core.service.pchat.im.pike;

import com.sankuai.pike.message.sdk.listener.ListenerHolder;
import com.sankuai.scrm.core.service.pchat.im.pike.constant.PikeConstant;
import com.sankuai.scrm.core.service.pchat.im.pike.listener.PikeConnectListener;
import com.sankuai.scrm.core.service.pchat.im.pike.listener.PikeMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class PikeServer implements ApplicationRunner {

    // 连接事件监听器
    @Resource
    private PikeConnectListener connectListener;

    // 消息事件监听器
    @Resource
    private PikeMessageListener messageListener;

    public void init() {
        //设置执行监听器的线程池，可选操作，若不设置则使用RPC默认线程池
        log.info("pike 启动");
        // 若需要对连接鉴权/监听连接建立/断开的事件，可注册该监听器
        ListenerHolder.registerLister(PikeConstant.BIZ_ID, connectListener);
        ListenerHolder.registerLister(PikeConstant.BIZ_ID, messageListener);
        Executor executor = new ThreadPoolExecutor(4, 4, 0, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000));
        ListenerHolder.scheduleListenerOn(executor);

        // 以pigeon的方式发布listener
        ListenerHolder.publishListenerAsPigeonService();

    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        init();
    }
}
