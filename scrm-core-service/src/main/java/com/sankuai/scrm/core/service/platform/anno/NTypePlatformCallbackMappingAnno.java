package com.sankuai.scrm.core.service.platform.anno;

import java.lang.annotation.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 14:59
 * @Version v1.0.0
 */

@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface NTypePlatformCallbackMappingAnno {
    /**
     * 类型
     * @return
     */
    int nType() default 0;

    /**
     * 回调描述
     * @return
     */
    String desc() default "";

}
