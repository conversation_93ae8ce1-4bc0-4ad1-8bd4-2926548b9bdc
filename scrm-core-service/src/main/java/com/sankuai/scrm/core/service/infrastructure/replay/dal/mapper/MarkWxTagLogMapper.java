package com.sankuai.scrm.core.service.infrastructure.replay.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.infrastructure.replay.dal.entity.MarkWxTagLog;
import com.sankuai.scrm.core.service.infrastructure.replay.dal.example.MarkWxTagLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MarkWxTagLogMapper extends MybatisBLOBsMapper<MarkWxTagLog, MarkWxTagLogExample, Long> {
    int batchInsert(@Param("list") List<MarkWxTagLog> list);
}