package com.sankuai.scrm.core.service.basic.domain.service;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2023/1/12 2:20 PM
 **/
@Service
public class UploadWxMediaLimitService {
    @Autowired
    private UploadWxMediaAcl uploadWxMediaAcl;


    public Map<String,WechatMediaResult> batchUploadTmpMedia(List<String> mediaUrls, WxMediaType mediaType, String token){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.basic.domain.service.UploadWxMediaLimitService.batchUploadTmpMedia(java.util.List,com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType,java.lang.String)");
        //todo 限流与并行
        Map<String,WechatMediaResult> resultMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(mediaUrls) || mediaType == null)return resultMap;
        for(String url:mediaUrls){
            resultMap.put(url,uploadWxMediaAcl.uploadWxTmpMedia(url,mediaType,token,false));
        }
        return resultMap;
    }

    public WechatMediaResult uploadTmpMedia(String mediaUrl, WxMediaType mediaType, String token,boolean force){
        return uploadWxMediaAcl.uploadWxTmpMedia(mediaUrl,mediaType,token,force);
    }
}
