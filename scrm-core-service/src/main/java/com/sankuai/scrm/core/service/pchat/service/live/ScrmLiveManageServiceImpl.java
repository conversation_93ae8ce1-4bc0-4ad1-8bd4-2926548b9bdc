package com.sankuai.scrm.core.service.pchat.service.live;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeAndGroupRelationDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.GroupEntryCardDTO;
import com.sankuai.dz.srcm.pchat.dto.GroupEntryConfig;
import com.sankuai.dz.srcm.pchat.dto.GroupEntryPageDTO;
import com.sankuai.dz.srcm.pchat.service.ScrmLiveManageService;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.service.GroupDynamicCodeAndGroupRelationDataService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.service.GroupDynamicCodeInfoDataService;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveGroupEntry;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmLiveGroupEntryExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmLiveGroupEntryMapper;
import com.sankuai.scrm.core.service.pchat.domain.ScrmLiveInfoDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionIdentificationDomainService;
import com.sankuai.scrm.core.service.pchat.domain.group.PrivateLiveGroupDomainService;
import com.sankuai.scrm.core.service.pchat.domain.group.PrivateLiveGroupMemberDomainService;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.service.WebcastService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@MdpPigeonServer
public class ScrmLiveManageServiceImpl implements ScrmLiveManageService {

    @Autowired
    private ScrmLiveInfoDomainService scrmLiveInfoDomainService;

    @Resource
    private ScrmLiveGroupEntryMapper scrmLiveGroupEntryMapper;

    @Autowired
    private PrivateLiveGroupDomainService groupDomainService;

    @Autowired
    private PrivateLiveGroupMemberDomainService memberDomainService;

    @Autowired
    private ScrmRestrictionIdentificationDomainService restrictionIdentificationService;

    @Autowired
    private GroupDynamicCodeAndGroupRelationDataService dynamicCodeAndGroupRelationDataService;

    @Autowired
    private GroupDynamicCodeInfoDataService dynamicCodeInfoDataService;

    @Autowired
    private WebcastService webcastService;

    @Override
    public RemoteResponse<Boolean> bindLiveWxType(String liveId, Integer wxType) {
        if (StringUtils.isEmpty(liveId)) {
            return RemoteResponse.fail("直播id为空");
        }
        CheckPermissionUtil.checkUserPermission(liveId, AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        WeChatType weChatType = WeChatType.getWeChatType(wxType);
        if (weChatType == null) {
            return RemoteResponse.fail("无效的微信类型");
        }
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        if (liveInfo != null && liveInfo.getWxType() != null) {
            return RemoteResponse.fail("直播已经绑定过微信类型，不可重复绑定");
        }
        boolean success = scrmLiveInfoDomainService.bindLiveWxType(liveId, weChatType);
        if (!success) {
            return RemoteResponse.fail("直播绑定微信类型失败");
        }
        return RemoteResponse.success(success);
    }

    @Override
    public RemoteResponse<Integer> queryLiveWxType(String liveId) {
        if (StringUtils.isEmpty(liveId)) {
            return RemoteResponse.fail("直播id为空");
        }
        CheckPermissionUtil.checkUserPermission(liveId, AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        if (liveInfo == null || liveInfo.getWxType() == null) {
            return RemoteResponse.success(null);
        }
        WeChatType weChatType = WeChatType.getWeChatType(liveInfo.getWxType());
        if (weChatType == null) {
            return RemoteResponse.success(null);
        }
        return RemoteResponse.success(weChatType.getCode());
    }

    @Override
    public RemoteResponse<Boolean> saveGroupEntryConfig(GroupEntryConfig groupEntryConfig) {
        if (groupEntryConfig == null) {
            return RemoteResponse.fail("参数为空");
        }
        if (StringUtils.isEmpty(groupEntryConfig.getLiveId())) {
            return RemoteResponse.fail("直播id为空");
        }
        if (groupEntryConfig.getGroupEntryCard() == null) {
            return RemoteResponse.fail("入群卡片配置为空");
        }
        if (StringUtils.isEmpty(groupEntryConfig.getGroupEntryPage())) {
            return RemoteResponse.fail("入群页配置为空");
        }
        if (groupEntryConfig.getPreventRiskUser() == null) {
            return RemoteResponse.fail("是否阻止风险用户入群为空");
        }
        CheckPermissionUtil.checkUserPermission(groupEntryConfig.getLiveId(), AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(groupEntryConfig.getLiveId());
        if (liveInfo == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        WeChatType weChatType = WeChatType.getWeChatType(liveInfo.getWxType());
        if (weChatType == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        if (WeChatType.PERSONAL_WECHAT.equals(weChatType)) {
            return RemoteResponse.fail("个微不支持配置");
        }
        boolean result;
        ScrmLiveGroupEntry liveGroupEntry = queryLiveGroupEntry(groupEntryConfig.getLiveId());
        if (liveGroupEntry == null) {
            result = saveLiveGroupEntry(groupEntryConfig);
        } else {
            result = updateLiveGroupEntry(liveGroupEntry, groupEntryConfig);
        }
        if (!result) {
            return RemoteResponse.fail("保存失败");
        }
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<GroupEntryConfig> queryGroupEntryConfig(String liveId) {
        if (StringUtils.isEmpty(liveId)) {
            return RemoteResponse.fail("直播id为空");
        }
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        if (liveInfo == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        WeChatType weChatType = WeChatType.getWeChatType(liveInfo.getWxType());
        if (weChatType == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        if (WeChatType.PERSONAL_WECHAT.equals(weChatType)) {
            return RemoteResponse.fail("个微不支持配置");
        }
        CheckPermissionUtil.checkUserPermission(liveId, AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        ScrmLiveGroupEntry scrmLiveGroupEntry = queryLiveGroupEntry(liveId);
        if (scrmLiveGroupEntry == null) {
            return RemoteResponse.success(null);
        }
        GroupEntryConfig config = new GroupEntryConfig();
        config.setLiveId(liveId);
        config.setPreventRiskUser(scrmLiveGroupEntry.getBanRiskyUser());
        config.setGroupEntryCard(JsonUtils.toObject(scrmLiveGroupEntry.getEntryCard(), GroupEntryCardDTO.class));
        config.setGroupEntryPage(scrmLiveGroupEntry.getEntryPage());
        return RemoteResponse.success(config);
    }

    @Override
    public RemoteResponse<GroupEntryPageDTO> queryGroupEntryPage(String liveId, String inviterUnionId, String inviteeUnionId) {
        if (StringUtils.isEmpty(liveId)) {
            return RemoteResponse.fail("直播id不能为空");
        }
        if (StringUtils.isEmpty(inviterUnionId)) {
            return RemoteResponse.fail("邀请者的unionId不能为空");
        }
        if (StringUtils.isEmpty(inviteeUnionId)) {
            return RemoteResponse.fail("被邀请者的unionId不能为空");
        }
        ScrmLiveInfo liveInfo = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        if (liveInfo == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        WeChatType weChatType = WeChatType.getWeChatType(liveInfo.getWxType());
        if (weChatType == null) {
            return RemoteResponse.fail("直播未绑定微信类型");
        }
        if (WeChatType.PERSONAL_WECHAT.equals(weChatType)) {
            return RemoteResponse.fail("本直播为个微");
        }
        GroupEntryPageDTO groupEntryPageDTO = new GroupEntryPageDTO();
        groupEntryPageDTO.setAppId(liveInfo.getAppId());
        ScrmLiveGroupEntry liveGroupEntry = queryLiveGroupEntry(liveId);
        if (liveGroupEntry != null) {
            groupEntryPageDTO.setPoster(liveGroupEntry.getEntryPage());
            groupEntryPageDTO.setGroupEntryCard(JsonUtils.toObject(liveGroupEntry.getEntryCard(), GroupEntryCardDTO.class));
            if (BooleanUtils.isTrue(liveGroupEntry.getBanRiskyUser())) {
                boolean riskyUser = checkRiskyUser(inviteeUnionId);
                if (riskyUser) {
                    return RemoteResponse.success(groupEntryPageDTO);
                }
            }
        } else {
            GroupEntryCardDTO groupEntryCardDTO = buildDefaultGroupEntryCardDTO(liveId);
            groupEntryPageDTO.setGroupEntryCard(groupEntryCardDTO);
        }
        GroupDynamicCodeInfoDTO codeInfoDTO = queryDynCode(liveInfo.getAppId(), liveId, inviterUnionId);
        if (codeInfoDTO != null) {
            groupEntryPageDTO.setCodeId(codeInfoDTO.getId());
            groupEntryPageDTO.setQrCode(codeInfoDTO.getQrCode());
        }
        return RemoteResponse.success(groupEntryPageDTO);
    }

    private ScrmLiveGroupEntry queryLiveGroupEntry(String liveId) {
        if (StringUtils.isEmpty(liveId)) {
            return null;
        }
        ScrmLiveGroupEntryExample example = new ScrmLiveGroupEntryExample();
        example.createCriteria().andLiveIdEqualTo(liveId);
        List<ScrmLiveGroupEntry> liveGroupEntryList = scrmLiveGroupEntryMapper.selectByExample(example);
        return liveGroupEntryList.stream().findFirst().orElse(null);
    }

    private boolean saveLiveGroupEntry(GroupEntryConfig groupEntryConfig) {
        if (groupEntryConfig == null) {
            return false;
        }
        ScrmLiveGroupEntry record = ScrmLiveGroupEntry.builder()
                .liveId(groupEntryConfig.getLiveId())
                .banRiskyUser(groupEntryConfig.getPreventRiskUser())
                .entryCard(JsonUtils.toStr(groupEntryConfig.getGroupEntryCard()))
                .entryPage(groupEntryConfig.getGroupEntryPage())
                .build();
        int row = scrmLiveGroupEntryMapper.insertSelective(record);
        return row > 0;
    }

    private boolean updateLiveGroupEntry(ScrmLiveGroupEntry liveGroupEntry, GroupEntryConfig groupEntryConfig) {
        if (liveGroupEntry == null || liveGroupEntry.getLiveId() == null) {
            return false;
        }
        liveGroupEntry.setBanRiskyUser(groupEntryConfig.getPreventRiskUser());
        liveGroupEntry.setEntryCard(JsonUtils.toStr(groupEntryConfig.getGroupEntryCard()));
        liveGroupEntry.setEntryPage(groupEntryConfig.getGroupEntryPage());
        int row = scrmLiveGroupEntryMapper.updateByPrimaryKeySelective(liveGroupEntry);
        return row > 0;
    }

    private boolean checkRiskyUser(String unionId) {
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = memberDomainService.queryGroupMemberRecordByUnionId(unionId);
        if (CollectionUtils.isEmpty(memberInfoEntityList)) {
            return false;
        }
        List<String> wxIdList = memberInfoEntityList.stream()
                .map(ScrmPersonalWxGroupMemberInfoEntity::getWxId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        return restrictionIdentificationService.anyInBlacklist(wxIdList);
    }

    private GroupDynamicCodeInfoDTO queryDynCode(String appId, String liveId, String unionId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(liveId) || StringUtils.isEmpty(unionId)) {
            return null;
        }
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntityList = groupDomainService.queryGroupByLiveId(appId, liveId);
        if (CollectionUtils.isEmpty(groupInfoEntityList)) {
            return null;
        }
        List<String> groupIdList = groupInfoEntityList.stream().map(ScrmPersonalWxGroupInfoEntity::getGroupId).collect(Collectors.toList());
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = memberDomainService.queryGroupMemberByUnionIdAndGroup(appId, unionId, groupIdList);
        if (CollectionUtils.isEmpty(memberInfoEntityList)) {
            return null;
        }
        ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity = memberInfoEntityList.get(0);
        List<GroupDynamicCodeAndGroupRelationDTO> relationDTOList = dynamicCodeAndGroupRelationDataService.queryByGroupId(memberInfoEntity.getGroupId());
        if (CollectionUtils.isEmpty(relationDTOList)) {
            return null;
        }
        Long dynamicCodeId = relationDTOList.get(0).getDynamicCodeId();
        return dynamicCodeInfoDataService.queryById(dynamicCodeId);
    }

    private GroupEntryCardDTO buildDefaultGroupEntryCardDTO(String liveId) {
        GroupEntryCardDTO groupEntryCardDTO = new GroupEntryCardDTO();
        groupEntryCardDTO.setTitle("美团美播");
        groupEntryCardDTO.setDescription("邀您进群");
        if (StringUtils.isNotEmpty(liveId)) {
            LiveRoomInfo liveRoomInfo = webcastService.queryLiveRoomById(liveId);
            if (liveRoomInfo != null) {
                groupEntryCardDTO.setImage(liveRoomInfo.getCoverImage());
            }
        }
        return groupEntryCardDTO;
    }

}
