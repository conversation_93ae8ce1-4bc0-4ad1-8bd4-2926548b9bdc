package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_LiveMatchLog
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxLiveMatchLog {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: group_id
     *   说明: 对应Scrm_PersonalWx_GroupInfo表id
     */
    private Long groupId;

    /**
     *   字段: chat_room_wx_serial_no
     *   说明: 聊天室序列号
     */
    private String chatRoomWxSerialNo;

    /**
     *   字段: live_id
     *   说明: 直播项目id, 对应Scrm_PersonalWx_GroupInfo表project_id
     */
    private String liveId;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: app_id
     *   说明: app_id，用于区分业务
     */
    private String appId;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: updater
     *   说明: 更新者
     */
    private String updater;
}