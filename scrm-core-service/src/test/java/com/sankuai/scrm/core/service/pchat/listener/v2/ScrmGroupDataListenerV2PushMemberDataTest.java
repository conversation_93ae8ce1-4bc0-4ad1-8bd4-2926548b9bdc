package com.sankuai.scrm.core.service.pchat.listener.v2;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberPushDataDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberJoinTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberStatusEnum;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmGroupMemberDataPushProducer;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import com.sankuai.scrm.core.service.pchat.listener.GroupActionListenerService;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.junit.runner.RunWith.*;

public class ScrmGroupDataListenerV2PushMemberDataTest {

    @InjectMocks
    private ScrmGroupDataListenerV2 scrmGroupDataListenerV2;

    @Mock
    private ScrmGroupMemberDataPushProducer groupMemberDataPushProducer;

    @Mock
    private GroupActionListenerService groupActionListenerService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void invokePushMemberData(ScrmPersonalWxGroupMemberInfoEntity memberInfo, GroupMemberPushDataDTO dto, PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum) throws Exception {
        Method method = ScrmGroupDataListenerV2.class.getDeclaredMethod("pushMemberData", ScrmPersonalWxGroupMemberInfoEntity.class, GroupMemberPushDataDTO.class, PersonalWxGroupMemberJoinTypeEnum.class);
        method.setAccessible(true);
        method.invoke(scrmGroupDataListenerV2, memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
    }

    /**
     * 测试pushMemberData方法，正常情况
     */
    @Test
    public void testPushMemberDataNormal() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setStatus(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
        GroupMemberPushDataDTO dto = new GroupMemberPushDataDTO();
        dto.setWxId("wxId");
        dto.setMtUserId(123L);
        dto.setUnionId("unionId");
        dto.setConsultantTaskId(456L);
        dto.setLiveId("liveId");
        PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.SCAN_QR;
        // act
        invokePushMemberData(memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
        // assert
        verify(groupMemberDataPushProducer, times(1)).sendAsyncMessage(anyString());
        verify(groupActionListenerService, times(1)).pushGroupAction(eq("wxId"), any(), eq(123L), eq("unionId"), eq(456L), eq("liveId"), eq(personalWxGroupMemberJoinTypeEnum));
    }

    /**
     * 测试pushMemberData方法，memberInfo为null
     */
    @Test
    public void testPushMemberDataMemberInfoNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = null;
        GroupMemberPushDataDTO dto = new GroupMemberPushDataDTO();
        dto.setWxId("wxId");
        PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.SCAN_QR;
        // Mock behavior to throw NullPointerException
        doThrow(NullPointerException.class).when(groupActionListenerService).pushGroupAction(anyString(), any(), any(), any(), any(), any(), any());
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokePushMemberData(memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * 测试pushMemberData方法，dto为null
     */
    @Test
    public void testPushMemberDataDtoNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setStatus(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
        GroupMemberPushDataDTO dto = null;
        PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.SCAN_QR;
        // Mock behavior to throw NullPointerException
        doThrow(NullPointerException.class).when(groupMemberDataPushProducer).sendAsyncMessage(any());
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokePushMemberData(memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * 测试pushMemberData方法，personalWxGroupMemberJoinTypeEnum为null
     */
    @Test
    public void testPushMemberDataPersonalWxGroupMemberJoinTypeEnumNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setStatus(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
        GroupMemberPushDataDTO dto = new GroupMemberPushDataDTO();
        dto.setWxId("wxId");
        dto.setMtUserId(123L);
        dto.setUnionId("unionId");
        dto.setConsultantTaskId(456L);
        dto.setLiveId("liveId");
        // Mock behavior to throw NullPointerException when personalWxGroupMemberJoinTypeEnum is null
        doThrow(NullPointerException.class).when(groupActionListenerService).pushGroupAction(anyString(), any(), any(), any(), any(), any(), isNull());
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokePushMemberData(memberInfo, dto, null);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }
}
