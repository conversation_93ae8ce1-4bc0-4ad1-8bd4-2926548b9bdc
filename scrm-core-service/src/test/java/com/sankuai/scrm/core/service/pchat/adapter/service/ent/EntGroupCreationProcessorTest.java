package com.sankuai.scrm.core.service.pchat.adapter.service.ent;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupCreateRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntityWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupSetConfigWithBLOBs;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalIsBooleanStrEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupFissionStateEnum;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * 测试 EntGroupCreationProcessor 的 createGroup 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class EntGroupCreationProcessorTest {

    @InjectMocks
    private EntGroupCreationProcessor entGroupCreationProcessor;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    private EntGroupCreationProcessor processor = new EntGroupCreationProcessor();

    @Mock
    private ScrmPersonalWxGroupSetConfigWithBLOBs config;

    @Mock
    private ScrmPersonalWxGroupMsg noticeEntity;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Helper method to invoke the private chooseRobotSet method using reflection.
     */
    private Set<String> invokeChooseRobotSet(List<String> inUsedRobotList, List<String> unUsedRobotList, Map<String, Long> restGroupCountMap, Integer needGroupCount) throws Exception {
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("chooseRobotSet", List.class, List.class, Map.class, Integer.class);
        method.setAccessible(true);
        return (Set<String>) method.invoke(entGroupCreationProcessor, inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
    }

    /**
     * Helper method to invoke the private method using reflection.
     */
    private List<String> invokeGetOnlineRobotWxIdList(String appId) throws Exception {
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("getOnlineRobotWxIdList", String.class);
        method.setAccessible(true);
        try {
            return (List<String>) method.invoke(entGroupCreationProcessor, appId);
        } catch (InvocationTargetException e) {
            // Handle the InvocationTargetException by returning an empty list
            return new ArrayList<>();
        }
    }

    /**
     * Helper method to create AssistantInfo instances.
     */
    private AssistantInfo createAssistantInfo(String accountId, String name) {
        // Assuming AssistantInfo has setters for accountId and name
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAccountId(accountId);
        assistantInfo.setName(name);
        return assistantInfo;
    }

    private List<String> invokeChooseOwnerList(Set<String> robotSet, Map<String, Long> restGroupCountMap, Integer needGroupCount) throws Exception {
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("chooseOwnerList", Set.class, Map.class, Integer.class);
        method.setAccessible(true);
        return (List<String>) method.invoke(processor, robotSet, restGroupCountMap, needGroupCount);
    }

    /**
     * 测试 createGroup 方法异常场景：无闲置机器人可用
     */
    @Test(expected = RuntimeException.class)
    public void testCreateGroupNoAvailableRobot() throws Throwable {
        // arrange
        GroupCreateRequest request = mock(GroupCreateRequest.class);
        when(request.getGroupCount()).thenReturn(1);
        when(request.getGroupName()).thenReturn("TestGroup");
        when(personalWxCommonService.getCreator()).thenReturn("creator");
        when(groupManageDomainService.saveGroupSetConfig(any())).thenReturn(null);
        when(groupManageDomainService.queryGroupsByLiveId(any(), any())).thenReturn(new ArrayList<>());
        // act
        entGroupCreationProcessor.createGroup(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试 createGroup 方法边界场景：请求的群组数量为 0
     */
    @Test(expected = RuntimeException.class)
    public void testCreateGroupZeroGroupCount() throws Throwable {
        // arrange
        GroupCreateRequest request = mock(GroupCreateRequest.class);
        when(request.getGroupCount()).thenReturn(0);
        // act
        entGroupCreationProcessor.createGroup(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试当所有参数为 null 时的情况
     */
    @Test
    public void testChooseRobotSet_AllParametersNull() throws Throwable {
        // arrange
        List<String> inUsedRobotList = null;
        List<String> unUsedRobotList = null;
        Map<String, Long> restGroupCountMap = null;
        Integer needGroupCount = null;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 inUsedRobotList 为 null 时的情况
     */
    @Test
    public void testChooseRobotSet_InUsedRobotListNull() throws Throwable {
        // arrange
        List<String> inUsedRobotList = null;
        List<String> unUsedRobotList = Lists.newArrayList("robot1", "robot2");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 1L);
        restGroupCountMap.put("robot2", 2L);
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 unUsedRobotList 为 null 时的情况
     */
    @Test
    public void testChooseRobotSet_UnUsedRobotListNull() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1", "robot2");
        List<String> unUsedRobotList = null;
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 1L);
        restGroupCountMap.put("robot2", 2L);
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 restGroupCountMap 为 null 时的情况
     */
    @Test
    public void testChooseRobotSet_RestGroupCountMapNull() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1", "robot2");
        List<String> unUsedRobotList = Lists.newArrayList("robot3", "robot4");
        Map<String, Long> restGroupCountMap = null;
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 needGroupCount 为 null 时的情况
     */
    @Test
    public void testChooseRobotSet_NeedGroupCountNull() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1", "robot2");
        List<String> unUsedRobotList = Lists.newArrayList("robot3", "robot4");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 1L);
        restGroupCountMap.put("robot2", 2L);
        restGroupCountMap.put("robot3", 1L);
        restGroupCountMap.put("robot4", 1L);
        Integer needGroupCount = null;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 inUsedRobotList 和 unUsedRobotList 都为空时的情况
     */
    @Test
    public void testChooseRobotSet_BothListsEmpty() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList();
        List<String> unUsedRobotList = Lists.newArrayList();
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 restGroupCountMap 为空时的情况
     */
    @Test
    public void testChooseRobotSet_RestGroupCountMapEmpty() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1", "robot2");
        List<String> unUsedRobotList = Lists.newArrayList("robot3", "robot4");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试当 inUsedRobotList 中的机器人已经满足需求时的情况
     */
    @Test
    public void testChooseRobotSet_InUsedRobotsSufficient() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1", "robot2");
        List<String> unUsedRobotList = Lists.newArrayList("robot3", "robot4");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 2L);
        restGroupCountMap.put("robot2", 2L);
        restGroupCountMap.put("robot3", 1L);
        restGroupCountMap.put("robot4", 1L);
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet("robot1", "robot2"), result);
    }

    /**
     * 测试当需要从 unUsedRobotList 中添加机器人时的情况
     */
    @Test
    public void testChooseRobotSet_AddFromUnUsedRobots() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1");
        List<String> unUsedRobotList = Lists.newArrayList("robot2", "robot3");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 1L);
        restGroupCountMap.put("robot2", 2L);
        restGroupCountMap.put("robot3", 1L);
        Integer needGroupCount = 3;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet("robot1", "robot2"), result);
    }

    /**
     * 测试当所有机器人都不足以满足需求时的情况
     */
    @Test
    public void testChooseRobotSet_AllRobotsInsufficient() throws Throwable {
        // arrange
        List<String> inUsedRobotList = Lists.newArrayList("robot1");
        List<String> unUsedRobotList = Lists.newArrayList("robot2", "robot3");
        Map<String, Long> restGroupCountMap = Maps.newHashMap();
        restGroupCountMap.put("robot1", 1L);
        restGroupCountMap.put("robot2", 1L);
        restGroupCountMap.put("robot3", 1L);
        Integer needGroupCount = 4;
        // act
        Set<String> result = invokeChooseRobotSet(inUsedRobotList, unUsedRobotList, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(Sets.newHashSet(), result);
    }

    /**
     * 测试场景：appId 为空
     * 预期结果：返回一个空的 ArrayList
     */
    @Test
    public void testGetOnlineRobotWxIdList_appIdIsNull() throws Throwable {
        // Arrange
        String appId = null;
        List<String> expectedResult = new ArrayList<>();
        // Act
        List<String> result = invokeGetOnlineRobotWxIdList(appId);
        // Assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试场景：appId 不为空，但 dsAssistantAcl.getAssistantList(appId) 返回空列表
     * 预期结果：返回一个空的 ArrayList
     */
    @Test
    public void testGetOnlineRobotWxIdList_appIdIsNotNullButAssistantListIsEmpty() throws Throwable {
        // Arrange
        String appId = "someAppId";
        when(dsAssistantAcl.getAssistantList(appId)).thenReturn(new ArrayList<>());
        List<String> expectedResult = new ArrayList<>();
        // Act
        List<String> result = invokeGetOnlineRobotWxIdList(appId);
        // Assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试场景：appId 不为空，且 dsAssistantAcl.getAssistantList(appId) 返回非空列表
     * 预期结果：返回一个包含所有非空 accountId 的 List<String>
     */
    @Test
    public void testGetOnlineRobotWxIdList_appIdIsNotNullAndAssistantListIsNotEmpty() throws Throwable {
        // Arrange
        String appId = "someAppId";
        List<AssistantInfo> assistantList = new ArrayList<>();
        assistantList.add(createAssistantInfo("account1", "name1"));
        assistantList.add(createAssistantInfo(null, "name2"));
        assistantList.add(createAssistantInfo("account3", "name3"));
        when(dsAssistantAcl.getAssistantList(appId)).thenReturn(assistantList);
        List<String> expectedResult = Lists.newArrayList("account1", "account3");
        // Act
        List<String> result = invokeGetOnlineRobotWxIdList(appId);
        // Assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试场景：dsAssistantAcl.getAssistantList(appId) 抛出异常
     * 预期结果：返回一个空的 ArrayList
     */
    @Test
    public void testGetOnlineRobotWxIdList_dsAssistantAclThrowsException() throws Throwable {
        // Arrange
        String appId = "someAppId";
        when(dsAssistantAcl.getAssistantList(appId)).thenThrow(new RuntimeException("Mocked exception"));
        List<String> expectedResult = new ArrayList<>();
        // Act
        List<String> result = invokeGetOnlineRobotWxIdList(appId);
        // Assert
        assertEquals(expectedResult, result);
    }

    /**
     * 测试正常情况：所有参数都不为空，且 restGroupCountMap 中包含 robotSet 中的所有机器人。
     */
    @Test
    public void testChooseOwnerListNormalCase() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        robotSet.add("robot1");
        robotSet.add("robot2");
        Map<String, Long> restGroupCountMap = new HashMap<>();
        restGroupCountMap.put("robot1", 2L);
        restGroupCountMap.put("robot2", 1L);
        Integer needGroupCount = 3;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(3, result.size());
        assertEquals(Lists.newArrayList("robot1", "robot1", "robot2"), result);
    }

    /**
     * 测试边界情况：robotSet 为空。
     */
    @Test
    public void testChooseOwnerListRobotSetEmpty() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        Map<String, Long> restGroupCountMap = new HashMap<>();
        Integer needGroupCount = 3;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试边界情况：restGroupCountMap 为空。
     */
    @Test
    public void testChooseOwnerListRestGroupCountMapEmpty() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        robotSet.add("robot1");
        Map<String, Long> restGroupCountMap = new HashMap<>();
        Integer needGroupCount = 3;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试边界情况：needGroupCount 为 null。
     */
    @Test
    public void testChooseOwnerListNeedGroupCountNull() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        robotSet.add("robot1");
        Map<String, Long> restGroupCountMap = new HashMap<>();
        restGroupCountMap.put("robot1", 2L);
        Integer needGroupCount = null;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试边界情况：needGroupCount 为 0。
     */
    @Test
    public void testChooseOwnerListNeedGroupCountZero() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        robotSet.add("robot1");
        Map<String, Long> restGroupCountMap = new HashMap<>();
        restGroupCountMap.put("robot1", 2L);
        Integer needGroupCount = 0;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试异常情况：restGroupCountMap 中某些机器人的剩余群组数量为负数。
     */
    @Test
    public void testChooseOwnerListNegativeRestGroupCount() throws Throwable {
        // arrange
        Set<String> robotSet = new HashSet<>();
        robotSet.add("robot1");
        Map<String, Long> restGroupCountMap = new HashMap<>();
        restGroupCountMap.put("robot1", -1L);
        Integer needGroupCount = 3;
        // act
        List<String> result = invokeChooseOwnerList(robotSet, restGroupCountMap, needGroupCount);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试正常场景：所有输入参数都有效
     */
    @Test
    public void testInitGroupInfoEntityNormalCase() throws Throwable {
        // arrange
        when(config.getAppId()).thenReturn("appId");
        when(config.getId()).thenReturn(1L);
        when(config.getProjectId()).thenReturn("projectId");
        when(config.getIsAutoCreateNewGroup()).thenReturn(true);
        when(config.getWelcomeMsgId()).thenReturn(2L);
        when(noticeEntity.getId()).thenReturn(3L);
        String groupName = "groupName";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        ScrmPersonalWxGroupInfoEntityWithBLOBs result = (ScrmPersonalWxGroupInfoEntityWithBLOBs) method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
        // assert
        assertNotNull(result);
        assertEquals("appId", result.getAppId());
        assertNotNull(result.getFamilyCode());
        assertEquals("groupName", result.getGroupName());
        assertEquals(1L, result.getSetId().longValue());
        assertEquals("projectId", result.getProjectId());
        assertTrue(result.getIsAutoCreateNewGroup());
        assertEquals(2L, result.getWelcomeMsgId().longValue());
        assertEquals(3L, result.getGroupNotice().longValue());
        // Cast to long to resolve ambiguity
        assertEquals(PersonalGroupStatusEnum.INIT.getCode(), (long) result.getStatus());
        assertFalse(result.getDeleted());
        // Cast to long to resolve ambiguity
        assertEquals(0L, (long) result.getGroupFissionCount());
        assertEquals(PersonalIsBooleanStrEnum.FALSE.getDesc(), result.getIsFreedom());
        assertEquals(PersonalIsBooleanStrEnum.TRUE.getDesc(), result.getIsVisible());
        assertEquals(PersonalWxGroupFissionStateEnum.NOT.getCode(), result.getHadFission());
    }

    /**
     * 测试边界场景：config 为 null
     */
    @Test
    public void testInitGroupInfoEntityConfigIsNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupSetConfigWithBLOBs config = null;
        ScrmPersonalWxGroupMsg noticeEntity = mock(ScrmPersonalWxGroupMsg.class);
        String groupName = "groupName";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
            fail("Expected NullPointerException was not thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * 测试边界场景：noticeEntity 为 null
     */
    @Test
    public void testInitGroupInfoEntityNoticeEntityIsNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMsg noticeEntity = null;
        String groupName = "groupName";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
            fail("Expected NullPointerException was not thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * 测试边界场景：groupName 为 null
     */
    @Test
    public void testInitGroupInfoEntityGroupNameIsNull() throws Throwable {
        // arrange
        when(config.getAppId()).thenReturn("appId");
        when(config.getId()).thenReturn(1L);
        when(config.getProjectId()).thenReturn("projectId");
        when(config.getIsAutoCreateNewGroup()).thenReturn(true);
        when(config.getWelcomeMsgId()).thenReturn(2L);
        when(noticeEntity.getId()).thenReturn(3L);
        String groupName = null;
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        ScrmPersonalWxGroupInfoEntityWithBLOBs result = (ScrmPersonalWxGroupInfoEntityWithBLOBs) method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
        // assert
        assertNotNull(result);
        assertEquals("appId", result.getAppId());
        assertNotNull(result.getFamilyCode());
        assertNull(result.getGroupName());
        assertEquals(1L, result.getSetId().longValue());
        assertEquals("projectId", result.getProjectId());
        assertTrue(result.getIsAutoCreateNewGroup());
        assertEquals(2L, result.getWelcomeMsgId().longValue());
        assertEquals(3L, result.getGroupNotice().longValue());
        // Cast to long to resolve ambiguity
        assertEquals(PersonalGroupStatusEnum.INIT.getCode(), (long) result.getStatus());
        assertFalse(result.getDeleted());
        // Cast to long to resolve ambiguity
        assertEquals(0L, (long) result.getGroupFissionCount());
        assertEquals(PersonalIsBooleanStrEnum.FALSE.getDesc(), result.getIsFreedom());
        assertEquals(PersonalIsBooleanStrEnum.TRUE.getDesc(), result.getIsVisible());
        assertEquals(PersonalWxGroupFissionStateEnum.NOT.getCode(), result.getHadFission());
    }

    /**
     * 测试边界场景：groupName 为空字符串
     */
    @Test
    public void testInitGroupInfoEntityGroupNameIsEmpty() throws Throwable {
        // arrange
        when(config.getAppId()).thenReturn("appId");
        when(config.getId()).thenReturn(1L);
        when(config.getProjectId()).thenReturn("projectId");
        when(config.getIsAutoCreateNewGroup()).thenReturn(true);
        when(config.getWelcomeMsgId()).thenReturn(2L);
        when(noticeEntity.getId()).thenReturn(3L);
        String groupName = "";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        ScrmPersonalWxGroupInfoEntityWithBLOBs result = (ScrmPersonalWxGroupInfoEntityWithBLOBs) method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
        // assert
        assertNotNull(result);
        assertEquals("appId", result.getAppId());
        assertNotNull(result.getFamilyCode());
        assertEquals("", result.getGroupName());
        assertEquals(1L, result.getSetId().longValue());
        assertEquals("projectId", result.getProjectId());
        assertTrue(result.getIsAutoCreateNewGroup());
        assertEquals(2L, result.getWelcomeMsgId().longValue());
        assertEquals(3L, result.getGroupNotice().longValue());
        // Cast to long to resolve ambiguity
        assertEquals(PersonalGroupStatusEnum.INIT.getCode(), (long) result.getStatus());
        assertFalse(result.getDeleted());
        // Cast to long to resolve ambiguity
        assertEquals(0L, (long) result.getGroupFissionCount());
        assertEquals(PersonalIsBooleanStrEnum.FALSE.getDesc(), result.getIsFreedom());
        assertEquals(PersonalIsBooleanStrEnum.TRUE.getDesc(), result.getIsVisible());
        assertEquals(PersonalWxGroupFissionStateEnum.NOT.getCode(), result.getHadFission());
    }

    /**
     * 测试异常场景：config 的某些属性为 null
     */
    @Test
    public void testInitGroupInfoEntityConfigPropertiesAreNull() throws Throwable {
        // arrange
        when(config.getAppId()).thenReturn(null);
        when(config.getId()).thenReturn(null);
        when(config.getProjectId()).thenReturn(null);
        when(config.getIsAutoCreateNewGroup()).thenReturn(null);
        when(config.getWelcomeMsgId()).thenReturn(null);
        when(noticeEntity.getId()).thenReturn(3L);
        String groupName = "groupName";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        ScrmPersonalWxGroupInfoEntityWithBLOBs result = (ScrmPersonalWxGroupInfoEntityWithBLOBs) method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
        // assert
        assertNotNull(result);
        assertNull(result.getAppId());
        assertNotNull(result.getFamilyCode());
        assertEquals("groupName", result.getGroupName());
        assertNull(result.getSetId());
        assertNull(result.getProjectId());
        assertNull(result.getIsAutoCreateNewGroup());
        assertNull(result.getWelcomeMsgId());
        assertEquals(3L, result.getGroupNotice().longValue());
        // Cast to long to resolve ambiguity
        assertEquals(PersonalGroupStatusEnum.INIT.getCode(), (long) result.getStatus());
        assertFalse(result.getDeleted());
        // Cast to long to resolve ambiguity
        assertEquals(0L, (long) result.getGroupFissionCount());
        assertEquals(PersonalIsBooleanStrEnum.FALSE.getDesc(), result.getIsFreedom());
        assertEquals(PersonalIsBooleanStrEnum.TRUE.getDesc(), result.getIsVisible());
        assertEquals(PersonalWxGroupFissionStateEnum.NOT.getCode(), result.getHadFission());
    }

    /**
     * 测试异常场景：noticeEntity 的某些属性为 null
     */
    @Test
    public void testInitGroupInfoEntityNoticeEntityPropertiesAreNull() throws Throwable {
        // arrange
        when(config.getAppId()).thenReturn("appId");
        when(config.getId()).thenReturn(1L);
        when(config.getProjectId()).thenReturn("projectId");
        when(config.getIsAutoCreateNewGroup()).thenReturn(true);
        when(config.getWelcomeMsgId()).thenReturn(2L);
        when(noticeEntity.getId()).thenReturn(null);
        String groupName = "groupName";
        // Use reflection to invoke the private method
        Method method = EntGroupCreationProcessor.class.getDeclaredMethod("initGroupInfoEntity", ScrmPersonalWxGroupSetConfigWithBLOBs.class, ScrmPersonalWxGroupMsg.class, String.class);
        method.setAccessible(true);
        ScrmPersonalWxGroupInfoEntityWithBLOBs result = (ScrmPersonalWxGroupInfoEntityWithBLOBs) method.invoke(entGroupCreationProcessor, config, noticeEntity, groupName);
        // assert
        assertNotNull(result);
        assertEquals("appId", result.getAppId());
        assertNotNull(result.getFamilyCode());
        assertEquals("groupName", result.getGroupName());
        assertEquals(1L, result.getSetId().longValue());
        assertEquals("projectId", result.getProjectId());
        assertTrue(result.getIsAutoCreateNewGroup());
        assertEquals(2L, result.getWelcomeMsgId().longValue());
        assertNull(result.getGroupNotice());
        // Cast to long to resolve ambiguity
        assertEquals(PersonalGroupStatusEnum.INIT.getCode(), (long) result.getStatus());
        assertFalse(result.getDeleted());
        // Cast to long to resolve ambiguity
        assertEquals(0L, (long) result.getGroupFissionCount());
        assertEquals(PersonalIsBooleanStrEnum.FALSE.getDesc(), result.getIsFreedom());
        assertEquals(PersonalIsBooleanStrEnum.TRUE.getDesc(), result.getIsVisible());
        assertEquals(PersonalWxGroupFissionStateEnum.NOT.getCode(), result.getHadFission());
    }
}
