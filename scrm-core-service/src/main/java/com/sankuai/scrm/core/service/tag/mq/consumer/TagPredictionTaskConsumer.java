package com.sankuai.scrm.core.service.tag.mq.consumer;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.dal.entity.ExternalContactTag;
import com.sankuai.scrm.core.service.tag.dal.entity.PredTagRecord;
import com.sankuai.scrm.core.service.tag.dal.entity.Tag;
import com.sankuai.scrm.core.service.tag.dal.mapper.PredTagRecordMapper;
import com.sankuai.scrm.core.service.tag.domain.PredTaskDomainService;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import com.sankuai.scrm.core.service.tag.enums.TagGroupCategoryEnum;
import com.sankuai.scrm.core.service.tag.message.TagPredictionMsg;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TagPredictionTaskConsumer implements InitializingBean, DisposableBean {

    private IConsumerProcessor consumer;

    @Autowired
    private PredTagRecordMapper predTagRecordMapper;

    @Autowired
    private ContactUserDomain contactUserDomain;

    @Autowired
    private TagDomainService tagDomainService;

    @Autowired
    private PredTaskDomainService predTaskDomainService;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    public ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        log.info("TagPredictionTaskConsumer receive message:{}", message);
        try {
            String msgBody = message.getBody();
            TagPredictionMsg msg = JsonUtils.toObject(msgBody, TagPredictionMsg.class);
            if (msg == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            PredTagRecord predTagRecord = predTagRecordMapper.selectByPrimaryKey(msg.getRecordId());
            if (predTagRecord == null || BooleanUtils.isTrue(predTagRecord.getStatus()) || StringUtils.isEmpty(predTagRecord.getAppId()) ||
                    StringUtils.isEmpty(predTagRecord.getUnionId()) || StringUtils.isEmpty(predTagRecord.getTagIdList())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String appId = predTagRecord.getAppId();
            String unionId = predTagRecord.getUnionId();
            String corpId = appConfigRepository.getCorpIdByAppId(appId);
            if (StringUtils.isEmpty(corpId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 需要添加的标签
            List<String> tagIdList = JsonUtils.toList(predTagRecord.getTagIdList(), String.class);
            if (CollectionUtils.isEmpty(tagIdList)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 查询是否添加好友
            List<ContactUser> contactUsers = contactUserDomain.queryContactUsersByUnionId(corpId, unionId);
            if (CollectionUtils.isEmpty(contactUsers)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 查询现有标签
            List<Tag> channelTagList = tagDomainService.getPermittedTagList(appId, TagGroupCategoryEnum.CHANNEL);
            List<Tag> cityTagList = tagDomainService.getPermittedTagList(appId, TagGroupCategoryEnum.CITY);
            List<ExternalContactTag> externalContactTagList = tagDomainService.queryTagsByUnionId(appId, unionId);
            Set<String> channelTagIdSet = channelTagList.stream().map(Tag::getTagId).collect(Collectors.toSet());
            Set<String> cityTagIdSet = cityTagList.stream().map(Tag::getTagId).collect(Collectors.toSet());
            Set<String> needTagIdSet = Sets.newHashSet(tagIdList);
            Set<String> actualTagIdSet = externalContactTagList.stream().map(ExternalContactTag::getTagId).collect(Collectors.toSet());
            needTagIdSet.removeAll(channelTagIdSet);
            needTagIdSet.removeAll(cityTagIdSet);
            actualTagIdSet.removeAll(channelTagIdSet);
            actualTagIdSet.removeAll(cityTagIdSet);
            Set<String> addTagIdSet = Sets.difference(needTagIdSet, actualTagIdSet);
            Set<String> removeTagIdSet = Sets.difference(actualTagIdSet, needTagIdSet);
            boolean success = predTaskDomainService.editCustomerTag(corpId, contactUsers, Lists.newArrayList(addTagIdSet), Lists.newArrayList(removeTagIdSet));
            if (success){
                predTaskDomainService.updatePredRecordStatus(msg.getRecordId());
                predTaskDomainService.incrementPredTaskLogTagNum(msg.getLogId());
            }
        } catch (Exception e) {
            log.error("Tag prediction error, message:{}", message, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    @Override
    public void destroy() throws Exception {
        if (this.consumer != null) {
            this.consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.tag.prediction.task.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "scrm.tag.prediction.task");

        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }
}
