package com.sankuai.scrm.core.service.friend.channel.service;

import com.dianping.cat.Cat;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.friend.channel.dto.FriendChannelInfoDTO;
import com.sankuai.dz.srcm.friend.channel.dto.FriendChannelListDTO;
import com.sankuai.dz.srcm.friend.channel.service.FriendChannelService;
import com.sankuai.scrm.core.service.friend.channel.dal.entity.FriendChannel;
import com.sankuai.scrm.core.service.friend.channel.dal.example.FriendChannelExample;
import com.sankuai.scrm.core.service.friend.channel.dal.mapper.FriendChannelMapper;
import com.sankuai.scrm.core.service.friend.channel.domain.FriendChannelDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@MdpPigeonServer
public class FriendChannelServiceImpl implements FriendChannelService {

    @Resource
    private FriendChannelDomainService friendChannelDomainService;
    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;

    @Resource
    private FriendChannelMapper friendChannelMapper;

    @Override
    public RemoteResponse<Boolean> saveFriendChannel(String appId, String name) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.friend.channel.service.FriendChannelServiceImpl.saveFriendChannel(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(name)) {
            return RemoteResponse.fail("入参不能为空");
        }

        CorpAppConfig config = corpAppConfigRepository.getConfigByAppId(appId);
        if (ObjectUtils.isEmpty(config)) {
            log.error("FriendChannelServiceImpl.saveFriendChannel: appId={}, name={}", appId, name);
            return RemoteResponse.fail("appId输入有误");
        }
        List<FriendChannel> friendChannels = getFriendChannels(appId, name);
        if(CollectionUtils.isNotEmpty(friendChannels)){
           return RemoteResponse.fail("渠道名称重复");
        }

        boolean result = friendChannelDomainService.saveFriendChannel(appId, name);
        if (!result) {
            log.error("FriendChannelServiceImpl.saveFriendChannel:appId={}, name={}", appId, name);
            return RemoteResponse.fail("保存好友渠道未成功");
        }
        return RemoteResponse.success(true);
    }

    private List<FriendChannel> getFriendChannels(String appId, String name) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.friend.channel.service.FriendChannelServiceImpl.getFriendChannels(java.lang.String,java.lang.String)");
        FriendChannelExample channelExample = new FriendChannelExample();
        channelExample.createCriteria().andAppIdEqualTo(appId).andNameEqualTo(name).andIsDeleteEqualTo(false);
        List<FriendChannel> friendChannels = friendChannelMapper.selectByExample(channelExample);
        return friendChannels;
    }

    @Override
    public PageRemoteResponse<FriendChannelListDTO> queryFriendChannelList(String appId, Integer pageSize, Integer pageNum) {
        if (StringUtils.isBlank(appId) || pageSize == null || pageNum == null || pageSize <= 0 || pageNum <= 0) {
            return PageRemoteResponse.fail("入参不能为空");
        }
        CorpAppConfig config = corpAppConfigRepository.getConfigByAppId(appId);
        if (ObjectUtils.isEmpty(config)) {
            log.error("FriendChannelServiceImpl.queryFriendChannelList: appId={}, pageSize={}, pageNum={}", appId, pageSize, pageNum);
            return PageRemoteResponse.fail("appId输入有误");
        }
        List<FriendChannelListDTO> friendChannelListDTOS = friendChannelDomainService.queryFriendChannelList(appId, pageSize, pageNum);
        if (CollectionUtils.isEmpty(friendChannelListDTOS)) {
            log.warn("FriendChannelServiceImpl.queryFriendChannelList: 查询好友渠道列表为空, appId={}, pageSize={}, pageNum={}", appId, pageSize, pageNum);
            return PageRemoteResponse.success(Collections.emptyList(), 0, true, null);
        }
        //总数量
        long totalHit = friendChannelDomainService.queryFriendChannelListCount(appId);
        return PageRemoteResponse.success(friendChannelListDTOS, totalHit, friendChannelListDTOS.size() > totalHit, null);
    }

    @Deprecated
    @Override
    public RemoteResponse<FriendChannelInfoDTO> queryFriendChannelInfo(String appId, Long channelId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.friend.channel.service.FriendChannelServiceImpl.queryFriendChannelInfo(java.lang.String,java.lang.Long)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

}
