package com.sankuai.scrm.core.service.group.chat.service;

import com.dianping.cat.Cat;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.group.chat.service.GroupAggregateChatService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@MdpPigeonServer
public class GroupAggregateChatServiceImpl implements GroupAggregateChatService {

    @Deprecated
    @Override
    public RemoteResponse<Boolean> removeGroupMember(String appId, String userId, String groupId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.group.chat.service.GroupAggregateChatServiceImpl.removeGroupMember(java.lang.String,java.lang.String,java.lang.String)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }
}
