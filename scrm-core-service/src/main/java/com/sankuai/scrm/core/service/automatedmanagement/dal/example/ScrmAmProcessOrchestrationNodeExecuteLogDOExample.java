package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmAmProcessOrchestrationNodeExecuteLogDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmAmProcessOrchestrationNodeExecuteLogDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmAmProcessOrchestrationNodeExecuteLogDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmAmProcessOrchestrationNodeExecuteLogDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmAmProcessOrchestrationNodeExecuteLogDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNull() {
            addCriterion("process_orchestration_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNotNull() {
            addCriterion("process_orchestration_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdEqualTo(Long value) {
            addCriterion("process_orchestration_id =", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotEqualTo(Long value) {
            addCriterion("process_orchestration_id <>", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThan(Long value) {
            addCriterion("process_orchestration_id >", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id >=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThan(Long value) {
            addCriterion("process_orchestration_id <", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id <=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIn(List<Long> values) {
            addCriterion("process_orchestration_id in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotIn(List<Long> values) {
            addCriterion("process_orchestration_id not in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id not between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNull() {
            addCriterion("process_orchestration_version is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNotNull() {
            addCriterion("process_orchestration_version is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionEqualTo(String value) {
            addCriterion("process_orchestration_version =", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotEqualTo(String value) {
            addCriterion("process_orchestration_version <>", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThan(String value) {
            addCriterion("process_orchestration_version >", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version >=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThan(String value) {
            addCriterion("process_orchestration_version <", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version <=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLike(String value) {
            addCriterion("process_orchestration_version like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotLike(String value) {
            addCriterion("process_orchestration_version not like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIn(List<String> values) {
            addCriterion("process_orchestration_version in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotIn(List<String> values) {
            addCriterion("process_orchestration_version not in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionBetween(String value1, String value2) {
            addCriterion("process_orchestration_version between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotBetween(String value1, String value2) {
            addCriterion("process_orchestration_version not between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIsNull() {
            addCriterion("process_orchestration_node_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIsNotNull() {
            addCriterion("process_orchestration_node_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdEqualTo(Long value) {
            addCriterion("process_orchestration_node_id =", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotEqualTo(Long value) {
            addCriterion("process_orchestration_node_id <>", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdGreaterThan(Long value) {
            addCriterion("process_orchestration_node_id >", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_node_id >=", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdLessThan(Long value) {
            addCriterion("process_orchestration_node_id <", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdLessThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_node_id <=", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIn(List<Long> values) {
            addCriterion("process_orchestration_node_id in", values, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotIn(List<Long> values) {
            addCriterion("process_orchestration_node_id not in", values, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_node_id between", value1, value2, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_node_id not between", value1, value2, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andExecuteCountIsNull() {
            addCriterion("execute_count is null");
            return (Criteria) this;
        }

        public Criteria andExecuteCountIsNotNull() {
            addCriterion("execute_count is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteCountEqualTo(Long value) {
            addCriterion("execute_count =", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountNotEqualTo(Long value) {
            addCriterion("execute_count <>", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountGreaterThan(Long value) {
            addCriterion("execute_count >", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_count >=", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountLessThan(Long value) {
            addCriterion("execute_count <", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountLessThanOrEqualTo(Long value) {
            addCriterion("execute_count <=", value, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountIn(List<Long> values) {
            addCriterion("execute_count in", values, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountNotIn(List<Long> values) {
            addCriterion("execute_count not in", values, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountBetween(Long value1, Long value2) {
            addCriterion("execute_count between", value1, value2, "executeCount");
            return (Criteria) this;
        }

        public Criteria andExecuteCountNotBetween(Long value1, Long value2) {
            addCriterion("execute_count not between", value1, value2, "executeCount");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}