package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_ImageFeature
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxImageFeature {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: url
     *   说明: url
     */
    private String url;

    /**
     *   字段: hash_code
     *   说明: url hashcode
     */
    private Integer hashCode;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: deleted
     *   说明: 是否删除 0 未删除
     */
    private Boolean deleted;

    /**
     *   字段: compress_url
     *   说明: url
     */
    private String compressUrl;

    /**
     *   字段: feature
     *   说明: 特征值
     */
    private String feature;
}