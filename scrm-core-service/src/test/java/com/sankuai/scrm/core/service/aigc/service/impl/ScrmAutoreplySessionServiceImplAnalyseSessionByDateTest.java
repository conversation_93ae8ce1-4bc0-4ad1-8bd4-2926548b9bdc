package com.sankuai.scrm.core.service.aigc.service.impl;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.aigc.service.autoreply.response.AutoreplySessionAnalysisResponse;
import com.sankuai.scrm.core.service.aigc.service.dto.SessionAnalysisDTO;
import com.sankuai.scrm.core.service.aigc.service.dto.SessionTurnCountDTO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmAutoreplySessionServiceImplAnalyseSessionByDateTest {

    @Mock
    private ScrmAutoreplySessionServiceImpl scrmAutoreplySessionService;

    @InjectMocks
    private ScrmAutoreplySessionServiceImpl service;

    private List<AutoreplySessionAnalysisResponse.DataAnalysis> invokePrivateMethod(List<String> dates, Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap) throws Exception {
        Method method = ScrmAutoreplySessionServiceImpl.class.getDeclaredMethod("analyseSessionByDate", List.class, Map.class);
        method.setAccessible(true);
        return (List<AutoreplySessionAnalysisResponse.DataAnalysis>) method.invoke(service, dates, dateToSessionAnalysisMap);
    }

    @Test
    public void testAnalyseSessionByDate_NormalCase() throws Throwable {
        List<String> dates = Arrays.asList("2023-10-01", "2023-10-02");
        Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap = new HashMap<>();
        dateToSessionAnalysisMap.put("2023-10-01", Arrays.asList(new SessionAnalysisDTO(null, new SessionTurnCountDTO(10, 5)), new SessionAnalysisDTO(null, new SessionTurnCountDTO(20, 10))));
        dateToSessionAnalysisMap.put("2023-10-02", Arrays.asList(new SessionAnalysisDTO(null, new SessionTurnCountDTO(30, 15))));
        List<AutoreplySessionAnalysisResponse.DataAnalysis> result = invokePrivateMethod(dates, dateToSessionAnalysisMap);
        assertNotNull(result, "Result should not be null");
        assertEquals(4, result.size(), "Result size should be 4");
        assertFalse(result.get(0).getData().isEmpty(), "Data for '会话量' should not be empty");
        assertFalse(result.get(1).getData().isEmpty(), "Data for '总对话轮次' should not be empty");
        assertFalse(result.get(2).getData().isEmpty(), "Data for '平均对话轮次' should not be empty");
        assertFalse(result.get(3).getData().isEmpty(), "Data for '用户平均发言次数' should not be empty");
    }

    @Test
    public void testAnalyseSessionByDate_SingleDateWithEmptyList() throws Throwable {
        List<String> dates = Collections.singletonList("2023-10-01");
        Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap = new HashMap<>();
        dateToSessionAnalysisMap.put("2023-10-01", Collections.emptyList());
        List<AutoreplySessionAnalysisResponse.DataAnalysis> result = invokePrivateMethod(dates, dateToSessionAnalysisMap);
        assertNotNull(result, "Result should not be null");
        assertEquals(4, result.size(), "Result size should be 4");
        // Check that the data for each analysis is not empty and contains the date with a count of 0.0
        assertFalse(result.get(0).getData().isEmpty(), "Data for '会话量' should not be empty");
        assertEquals(1, result.get(0).getData().size(), "Data for '会话量' should contain one entry");
        assertEquals(0.0, result.get(0).getData().get(0).getCount(), "Count for '会话量' should be 0.0");
        assertFalse(result.get(1).getData().isEmpty(), "Data for '总对话轮次' should not be empty");
        assertEquals(1, result.get(1).getData().size(), "Data for '总对话轮次' should contain one entry");
        assertEquals(0.0, result.get(1).getData().get(0).getCount(), "Count for '总对话轮次' should be 0.0");
        assertFalse(result.get(2).getData().isEmpty(), "Data for '平均对话轮次' should not be empty");
        assertEquals(1, result.get(2).getData().size(), "Data for '平均对话轮次' should contain one entry");
        assertEquals(0.0, result.get(2).getData().get(0).getCount(), "Count for '平均对话轮次' should be 0.0");
        assertFalse(result.get(3).getData().isEmpty(), "Data for '用户平均发言次数' should not be empty");
        assertEquals(1, result.get(3).getData().size(), "Data for '用户平均发言次数' should contain one entry");
        assertEquals(0.0, result.get(3).getData().get(0).getCount(), "Count for '用户平均发言次数' should be 0.0");
    }

    @Test
    public void testAnalyseSessionByDate_EmptyDates() throws Throwable {
        List<String> dates = Collections.emptyList();
        Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap = new HashMap<>();
        List<AutoreplySessionAnalysisResponse.DataAnalysis> result = invokePrivateMethod(dates, dateToSessionAnalysisMap);
        assertNotNull(result, "Result should not be null");
        assertEquals(4, result.size(), "Result size should be 4");
        assertTrue(result.get(0).getData().isEmpty(), "Data for '会话量' should be empty");
        assertTrue(result.get(1).getData().isEmpty(), "Data for '总对话轮次' should be empty");
        assertTrue(result.get(2).getData().isEmpty(), "Data for '平均对话轮次' should be empty");
        assertTrue(result.get(3).getData().isEmpty(), "Data for '用户平均发言次数' should be empty");
    }

    @Test
    public void testAnalyseSessionByDate_NullDates() throws Throwable {
        List<String> dates = null;
        Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap = new HashMap<>();
        try {
            invokePrivateMethod(dates, dateToSessionAnalysisMap);
            org.junit.jupiter.api.Assertions.fail("Expected an Exception to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException, "Expected NullPointerException, but got: " + e.getCause().getClass().getName());
        } catch (Exception e) {
            org.junit.jupiter.api.Assertions.fail("Unexpected exception: " + e.getClass().getName());
        }
    }

    @Test
    public void testAnalyseSessionByDate_NullDateToSessionAnalysisMap() throws Throwable {
        List<String> dates = Arrays.asList("2023-10-01");
        Map<String, List<SessionAnalysisDTO>> dateToSessionAnalysisMap = null;
        try {
            invokePrivateMethod(dates, dateToSessionAnalysisMap);
            org.junit.jupiter.api.Assertions.fail("Expected an Exception to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof NullPointerException, "Expected NullPointerException, but got: " + e.getCause().getClass().getName());
        } catch (Exception e) {
            org.junit.jupiter.api.Assertions.fail("Unexpected exception: " + e.getClass().getName());
        }
    }
}
