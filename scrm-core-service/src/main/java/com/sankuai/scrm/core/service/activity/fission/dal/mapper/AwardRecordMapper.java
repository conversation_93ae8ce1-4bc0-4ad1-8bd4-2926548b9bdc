package com.sankuai.scrm.core.service.activity.fission.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord;
import com.sankuai.scrm.core.service.activity.fission.dal.example.AwardRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AwardRecordMapper extends MybatisBaseMapper<AwardRecord, AwardRecordExample, Long> {
    int batchInsert(@Param("list") List<AwardRecord> list);
}