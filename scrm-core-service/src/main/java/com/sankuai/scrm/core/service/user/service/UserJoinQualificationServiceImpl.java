package com.sankuai.scrm.core.service.user.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.user.dto.UserInGroupDynamicRequest;
import com.sankuai.dz.srcm.user.dto.UserQualificationDTO;
import com.sankuai.dz.srcm.user.service.UserJoinQualificationService;
import com.sankuai.scrm.core.service.group.dal.babymapper.MemberInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeLocalService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.dal.example.ContactUserDoExample;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/4/10 15:35
 * @Version v1.0.0
 */
@Slf4j
@MdpPigeonServer
public class UserJoinQualificationServiceImpl implements UserJoinQualificationService {
    @Resource
    private ContactUserDoMapper contactUserDoMapper;
    @Resource
    private MemberInfoEntityMapper memberInfoEntityMapper;
    @Resource
    private MtUserCenterAclService mtUserCenterAclService;

    @Resource
    private GroupDynamicCodeLocalService groupDynamicCodeLocalService;


    @Override
    public RemoteResponse<Boolean> queryUserInGroupDynamicByUnionId(UserInGroupDynamicRequest request) {
        try {
            log.info("queryUserInGroupDynamicByUnionId userInGroupDynamicRequest:{}", request);

            if (request == null) {
                log.info("queryUserInGroupDynamicByUnionId 参数异常，但是不卡控");
                return RemoteResponse.success(false);
            }

            if (request.getChannelId() == null || request.getChannelId() == 0) {
                log.info("queryUserInGroupDynamicByUnionId 渠道参数异常，但是不卡控");
                return RemoteResponse.success(false);
            }

            if (request.getCityId() == null || request.getCityId() == 0) {
                log.info("queryUserInGroupDynamicByUnionId 城市参数异常，但是不卡控");
                return RemoteResponse.success(false);
            }

            List<String> groupIdList = groupDynamicCodeLocalService.queryCoveredGroupList(request.getAppId(), request.getUnionId(), request.getCityId(), request.getChannelId());
            if (CollectionUtil.isEmpty(groupIdList)) {
                return RemoteResponse.success(false);
            }

            boolean inGroupDynamic = isInGroupDynamic(request.getUnionId(), groupIdList);
            return RemoteResponse.success(inGroupDynamic);
        } catch (Exception e) {
            log.error("queryUserInGroupDynamicByUnionId has exception ", e);
            return RemoteResponse.success(false);
        }
    }

    @Override
    public RemoteResponse<UserQualificationDTO> queryUserTypeByUnionId(String corpId, String unionId) {
        log.info("根据unionId查询用户是好友和在群内,corpId:{},unionId:{}", corpId, unionId);
        UserQualificationDTO dto = new UserQualificationDTO();
        if (StrUtil.isEmpty(corpId) || StrUtil.isEmpty(unionId)) {
            return RemoteResponse.success(dto);
        }
        dto.setFriend(isFriend(corpId, unionId));
        dto.setInGroup(isInGroup(corpId, unionId));
        return RemoteResponse.success(dto);
    }

    private boolean isFriend(String corpId, String unionId) {
        ContactUserDoExample example = new ContactUserDoExample();
        ContactUserDoExample.Criteria criteria = example.createCriteria();
        criteria.andUnionIdEqualTo(unionId);
        criteria.andCorpIdEqualTo(corpId);
        criteria.andStatusEqualTo(1);
        example.limit(1);
        List<ContactUserDo> contactUserDos = contactUserDoMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty(contactUserDos);
    }

    private boolean isInGroup(String corpId, String unionId) {
        MemberInfoEntityExample example = new MemberInfoEntityExample();
        MemberInfoEntityExample.Criteria criteria = example.createCriteria();
        criteria.andUnionIdEqualTo(unionId);
        criteria.andCorpIdEqualTo(corpId);
        criteria.andStatusEqualTo((byte) 0);
        example.limit(1);
        List<MemberInfoEntity> contactUserDos = memberInfoEntityMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty(contactUserDos);
    }

    private boolean isInGroupDynamic(String unionId, List<String> groupIdList) {
        MemberInfoEntityExample example = new MemberInfoEntityExample();
        MemberInfoEntityExample.Criteria criteria = example.createCriteria();
        criteria.andUnionIdEqualTo(unionId);
        criteria.andGroupIdIn(groupIdList);
        criteria.andStatusEqualTo((byte) 0);
        example.limit(1);
        List<MemberInfoEntity> contactUserDos = memberInfoEntityMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty(contactUserDos);
    }

    @Override
    public RemoteResponse<UserQualificationDTO> queryUserTypeByUserId(String corpId, long mtUserId) {
        if (mtUserId == 0) {
            return RemoteResponse.success(new UserQualificationDTO());
        }
        String unionIdId = mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(mtUserId, "wxde8ac0a21135c07d");
        return queryUserTypeByUnionId(corpId, unionIdId);
    }
}
