package com.sankuai.scrm.core.service.pchat.enums.activity;

import lombok.Getter;

/**
 * <AUTHOR> on 2024/06/28 14:34
 * Scrm_PersonalWx_InviteRelation表只记录邀请者和被邀请者都为普通人的记录，但是存在后续普通人被指定为咨询师的情形。此时该行记录变为无效。
 */
@Getter
public enum ActivityInviteRelationTypeEnum {
    INVALID((byte) 0, "无效"),
    VALID((byte) 1, "有效"),
    ;

    private final Byte code;

    private final String desc;

    ActivityInviteRelationTypeEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityInviteRelationTypeEnum fromCode(int code) {
        for (ActivityInviteRelationTypeEnum enumValue : ActivityInviteRelationTypeEnum.values()) {
            if (enumValue.code == code) {
                return enumValue;
            }
        }
        return null;
    }

}