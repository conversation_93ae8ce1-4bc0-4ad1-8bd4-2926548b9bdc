package com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeConfig;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeConfigExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface GroupDynamicCodeConfigMapper extends MybatisBaseMapper<GroupDynamicCodeConfig, GroupDynamicCodeConfigExample, Long> {
    int batchInsert(@Param("list") List<GroupDynamicCodeConfig> list);
}