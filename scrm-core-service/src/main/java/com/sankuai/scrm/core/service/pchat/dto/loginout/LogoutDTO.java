package com.sankuai.scrm.core.service.pchat.dto.loginout;

import lombok.Data;

/**
 * 1005
 * @Description 登出接口3.0（兼容PC）回调
 * <AUTHOR>
 * @Create On 2023/11/7 19:04
 * @Version v1.0.0
 */

@Data
public class LogoutDTO {

    /**
     * 商家业务关联流水编号
     */
    private String vcRelationSerialNo;

    /**
     * 登出原因代码 10001；10002；10003；10004；10005；10006；10007；
     */
    private String nOfflineCode;

    /**
     * 登出原因说明(10001 收到离线（移动端手动退出扫码分身、账号密码在其他地方挤登退出、被微信踢下线等） 10002 心跳离线 10003 指令退出 10004 授权在其它设备登录后退出 10005 断网了 10006 更新程序 10007 心跳过期)
     */
    private String vcOfflineReason;

}
