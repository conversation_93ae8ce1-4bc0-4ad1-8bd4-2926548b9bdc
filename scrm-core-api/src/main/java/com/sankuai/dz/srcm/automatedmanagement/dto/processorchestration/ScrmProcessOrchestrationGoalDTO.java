package com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/16
 */
@Data
public class ScrmProcessOrchestrationGoalDTO implements Serializable {
    /**************************数据库相同字段************************************************/
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: check_time
     *   说明: 目标检验时间间隔
     */
    private String checkTime;

    /**
     *   字段: check_time_unit
     *   说明: 目标检验时间间隔单位
     */
    private String checkTimeUnit;

    /**
     *   字段: status
     *   说明: 启用/未启用
     */
    private Byte status;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: goal_type
     *   说明: 1:正面目标 2:负面结果
     */
    private Byte goalType;

    /**
     *   字段: careNegativeResult
     *   说明: 是否需要收集负面结果
     */
    private Boolean careNegativeResult;

    /**
     *   字段: highLightList
     *   说明: 正面高亮结果
     */
    private List<Long> positiveResultHighlightList;

    /**
     *   字段: negativeResultHighlightList
     *   说明: 负面高亮结果
     */
    private List<Long> negativeResultHighlightList;

    /**************************自定义字段************************************************/

    List<ScrmProcessOrchestrationGoalConditionDetailDTO> goalConditionList;
}
