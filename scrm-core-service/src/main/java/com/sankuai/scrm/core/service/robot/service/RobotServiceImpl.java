package com.sankuai.scrm.core.service.robot.service;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.robot.dto.RobotInfoDTO;
import com.sankuai.dz.srcm.robot.service.RobotService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@MdpPigeonServer
@Slf4j
public class RobotServiceImpl implements RobotService {

    @Autowired
    private DsAssistantAcl dsAssistantAcl;

    @Override
    public PageRemoteResponse<RobotInfoDTO> queryAllRobotList(String appId) {
        log.info("queryAllRobotList: appId={}", appId);
        if (StringUtils.isEmpty(appId)) {
            return PageRemoteResponse.illegalArgument("AppId不能为空");
        }
        List<AssistantInfo> assistantList = dsAssistantAcl.getAssistantList(appId);
        log.info("queryAllRobotList: appId={}, assistantList={}", appId, assistantList);
        if (CollectionUtils.isEmpty(assistantList)) {
            return PageRemoteResponse.success(Lists.newArrayList(), 0, true);
        }
        List<RobotInfoDTO> robotInfoDTOList = assistantList.stream().filter(Objects::nonNull)
                .map(assistantInfo -> {
                    RobotInfoDTO robotInfoDTO = new RobotInfoDTO();
                    robotInfoDTO.setAccount(assistantInfo.getAccountId());
                    robotInfoDTO.setName(assistantInfo.getName());
                    return robotInfoDTO;
                }).collect(Collectors.toList());
        log.info("queryAllRobotList: appId={}, robotInfoDTOList={}", appId, robotInfoDTOList);
        return PageRemoteResponse.success(robotInfoDTOList, robotInfoDTOList.size(), true);
    }

}
