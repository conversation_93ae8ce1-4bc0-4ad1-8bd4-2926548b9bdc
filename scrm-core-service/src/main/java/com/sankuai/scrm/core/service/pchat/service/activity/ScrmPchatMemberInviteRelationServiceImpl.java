package com.sankuai.scrm.core.service.pchat.service.activity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteRelationInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteWxUserInfo;
import com.sankuai.dz.srcm.pchat.enums.activity.MemberInviteRelationType;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import com.sankuai.dz.srcm.pchat.request.activity.MemberInviteRelationRequest;
import com.sankuai.dz.srcm.pchat.service.activity.ScrmPchatMemberInviteRelationService;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxFirstInviteRecord;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxFirstInviteRecordDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.ScrmPersonalWxFirstInviteRecordDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/7/9 19:54
 * @Version v1.0.0
 */
@Slf4j
@MdpPigeonServer
public class ScrmPchatMemberInviteRelationServiceImpl implements ScrmPchatMemberInviteRelationService {
    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @Resource
    private ScrmPersonalWxFirstInviteRecordDomainService personalWxFirstInviteRecordDomainService;

    @Override
    public RemoteResponse<MemberInviteRelationInfoDTO> getInviteRelationByWxId(MemberInviteRelationRequest request) {
        log.info("邀请关系查询,param:{}", JsonUtils.toStr(request));
        String liveId = request.getLiveId();
        String wxId = request.getWxId();
        if (StrUtil.isBlank(liveId) || StrUtil.isBlank(wxId)) {
            return RemoteResponse.fail("传参异常");
        }
        int relationType = request.getRelationType() == null ? MemberInviteRelationType.ALL.getCode() : request.getRelationType();
        MemberInviteRelationType memberInviteRelationType = MemberInviteRelationType.fromCode(relationType);

        ScrmPersonalWxFirstInviteRecord firstInvitedRecord = personalWxFirstInviteRecordDomainService.queryTop1FirstInvitedRecord(liveId, wxId);
        if (firstInvitedRecord == null) {
            log.info("当前用户没有入群记录,projectId:{},wxId:{}", request.getLiveId(), request.getWxId());
            return RemoteResponse.success(new MemberInviteRelationInfoDTO());
        }
        log.info("当前用户第一次入群记录，firstInvitedRecordId:{}", firstInvitedRecord.getId());
        List<ScrmPersonalWxFirstInviteRecord> allRecord = parallelQueryAllRecord(liveId, memberInviteRelationType, firstInvitedRecord);
        MemberInviteRelationInfoDTO dto = buildInviteRelation(memberInviteRelationType, firstInvitedRecord, allRecord);
        return RemoteResponse.success(dto);
    }

    private MemberInviteRelationInfoDTO buildInviteRelation(MemberInviteRelationType memberInviteRelationType, ScrmPersonalWxFirstInviteRecord firstInvitedRecord, List<ScrmPersonalWxFirstInviteRecord> allRecord) {
        MemberInviteRelationInfoDTO dto = new MemberInviteRelationInfoDTO();
        if (CollectionUtil.isEmpty(allRecord)) {
            return dto;
        }
        String currWxId = firstInvitedRecord.getWxId();
        //<被邀请人，邀请人>
        Map<String, ScrmPersonalWxFirstInviteRecord> invitedMap = allRecord.stream().collect(Collectors.toMap(ScrmPersonalWxFirstInviteRecord::getWxId, Function.identity(), (u1, u2) -> u1));
        //<邀请人,<被邀请人>>
        Map<String, List<ScrmPersonalWxFirstInviteRecord>> inviteMap = allRecord.stream().collect(Collectors.groupingBy(ScrmPersonalWxFirstInviteRecord::getInviteWxId));
        if (memberInviteRelationType == MemberInviteRelationType.UP || MemberInviteRelationType.ALL == memberInviteRelationType) {
            List<ScrmPersonalWxFirstInviteRecord> upLookup = allRecord.stream().filter(r -> r.getId() < firstInvitedRecord.getId()).collect(Collectors.toList());
            fillUpInviteList(dto, currWxId, invitedMap, upLookup);
        }
        if (memberInviteRelationType == MemberInviteRelationType.DOWN || MemberInviteRelationType.ALL == memberInviteRelationType) {
            List<ScrmPersonalWxFirstInviteRecord> downLookup = allRecord.stream().filter(r -> r.getId() > firstInvitedRecord.getId()).collect(Collectors.toList());
            fillDownInviteList(dto, currWxId, inviteMap, downLookup);
        }

        return dto;
    }

    private void fillDownInviteList(MemberInviteRelationInfoDTO dto, String currWxId, Map<String, List<ScrmPersonalWxFirstInviteRecord>> inviteMap, List<ScrmPersonalWxFirstInviteRecord> downLookup) {
        if (CollectionUtil.isEmpty(downLookup)) {
            return;
        }
        List<ScrmPersonalWxFirstInviteRecordDTO> foundDownList = new ArrayList<>();
        AtomicInteger rand = new AtomicInteger(0);
        int distance = 1;//初始距离
        Set<String> foundSet = new HashSet<>();
        downLookupRecursive(distance, currWxId, inviteMap, foundDownList, rand, foundSet);
        dto.setDownInviteUserInfoList(fillData(foundDownList));
    }

    private void downLookupRecursive(int distance, String wxId, Map<String, List<ScrmPersonalWxFirstInviteRecord>> inviteMap, List<ScrmPersonalWxFirstInviteRecordDTO> downLookupRecordList, AtomicInteger rand, Set<String> foundSet) {
        if (StrUtil.isBlank(wxId)) {
            return;
        }
        if (foundSet.contains(wxId)) {
            log.info("有循环邀请，已经找到下级,wxId:{}", wxId);
            return;
        }
        foundSet.add(wxId);
        List<ScrmPersonalWxFirstInviteRecord> inviteRecords = inviteMap.get(wxId);
        if (CollectionUtil.isNotEmpty(inviteRecords)) {
            downLookupRecordList.addAll(inviteRecords.stream().map(r -> new ScrmPersonalWxFirstInviteRecordDTO(distance, r)).collect(Collectors.toList()));
            int current = rand.get();
            inviteRecords.forEach(r -> {
                downLookupRecursive(distance + 1, r.getWxId(), inviteMap, downLookupRecordList, rand, foundSet);
            });
            if (rand.get() == current) {
                rand.incrementAndGet();
            }
        }

    }

    /**
     * 填充上级关系
     *
     * @param dto
     * @param currWxId
     * @param invitedMap
     * @param upLookup
     */
    private void fillUpInviteList(MemberInviteRelationInfoDTO dto, String currWxId, Map<String, ScrmPersonalWxFirstInviteRecord> invitedMap, List<ScrmPersonalWxFirstInviteRecord> upLookup) {
        if (CollectionUtil.isEmpty(upLookup)) {
            return;
        }
        List<ScrmPersonalWxFirstInviteRecordDTO> foundUpList = new ArrayList<>();
        int distance = 1;// 初始距离为
        Set<String> foundSet = new HashSet<>();
        upLookupRecursive(distance, currWxId, invitedMap, foundUpList, foundSet);
//        Map<Long, ScrmPersonalWxFirstInviteRecord> memberIdWithRecordMap = foundUpList.stream().collect(Collectors.toMap(f -> f.getGroupMemberId(), Function.identity(), (u1, u2) -> u1));
        dto.setUpInviteUserInfoList(fillData(foundUpList));
    }

    private List<MemberInviteWxUserInfo> fillData(List<ScrmPersonalWxFirstInviteRecordDTO> foundUpList) {
        if (CollectionUtil.isEmpty(foundUpList)) {
            return new ArrayList<>();
        }
        List<Long> groupMemberIds = foundUpList.stream().map(d -> d.getRecord().getGroupMemberId()).collect(Collectors.toList());
        Map<Long, Integer> groupMemberIdDistance = foundUpList.stream().collect(Collectors.toMap(d -> d.getRecord().getGroupMemberId(), ScrmPersonalWxFirstInviteRecordDTO::getDistance, (u1, u2) -> u1));
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupManageDomainService.queryGroupMemberByIds(groupMemberIds);
        // <wxId,member>
        Map<String, ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntityMap = groupMemberInfoEntities.stream().collect(Collectors.toMap(ScrmPersonalWxGroupMemberInfoEntity::getWxId, Function.identity(), (u1, u2) -> u1));
        return groupMemberInfoEntities.stream().map(m -> {
            MemberInviteWxUserInfo info = new MemberInviteWxUserInfo();
            info.setInviteWxId(m.getInviteWxId());
            if (!groupMemberIdDistance.containsKey(m.getId())) {
                return null;
            }
            info.setDistance(groupMemberIdDistance.get(m.getId()));
            ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity = groupMemberInfoEntityMap.get(m.getInviteWxId());
            if (groupMemberInfoEntity != null) {
                if (StringUtils.isEmpty(groupMemberInfoEntity.getUnionId())) {
                    fillUnionIdByWxId(m.getInviteWxId(), info);
                } else {
                    info.setInviteMtUserId(convertObjectToPrimitive(groupMemberInfoEntity.getUserId()));
                    info.setInviteUnionId(groupMemberInfoEntity.getUnionId());
                }
            } else {
                fillUnionIdByWxId(m.getInviteWxId(), info);
            }

            info.setWxId(m.getWxId());
            info.setEnterTime(m.getEnterTime());
            info.setMtUserId(convertObjectToPrimitive(m.getUserId()));
            info.setUnionId(m.getUnionId());
            return info;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public void fillUnionIdByWxId(String wxId, MemberInviteWxUserInfo info) {
        if (StrUtil.isBlank(wxId)) {
            return;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupManageDomainService.queryGroupMemberListByWxIds(Lists.newArrayList(wxId));
        if (CollectionUtil.isEmpty(groupMemberInfoEntities)) {
            return;
        }
        Optional<ScrmPersonalWxGroupMemberInfoEntity> first = groupMemberInfoEntities.stream().filter(groupMemberInfoEntity1 -> StringUtils.isNotBlank(groupMemberInfoEntity1.getUnionId())).filter(groupMemberInfoEntity1 -> groupMemberInfoEntity1.getUserId() != null).findFirst();
        if (first.isPresent()) {
            ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity = first.get();
            info.setInviteMtUserId(convertObjectToPrimitive(groupMemberInfoEntity.getUserId()));
            info.setInviteUnionId(groupMemberInfoEntity.getUnionId());
        }
    }

    private long convertObjectToPrimitive(Long l) {
        return l == null ? 0L : l;
    }

    private void upLookupRecursive(int distance, String wxId, Map<String, ScrmPersonalWxFirstInviteRecord> invitedMap, List<ScrmPersonalWxFirstInviteRecordDTO> upLookup, Set<String> foundSet) {
        if (StrUtil.isBlank(wxId)) {
            return;
        }
        ScrmPersonalWxFirstInviteRecord self = invitedMap.get(wxId);
        if (self == null) {
            return;
        }
        if (foundSet.contains(self.getWxId())) {
            log.info("有循环邀请，已经找到上级,wxId:{},projectId:{},chatroomSerialNo:{}", self.getWxId(), self.getProjectId(), self.getChatRoomWxSerialNo());
            return;
        }
        foundSet.add(self.getWxId());
        ScrmPersonalWxFirstInviteRecord inviter = invitedMap.get(self.getInviteWxId());
        if (inviter == null) {
            if (distance > 1) {
                ScrmPersonalWxFirstInviteRecordDTO dto = new ScrmPersonalWxFirstInviteRecordDTO(distance, self);
                upLookup.add(dto);
            }
            return;
        }
        ScrmPersonalWxFirstInviteRecordDTO dto = new ScrmPersonalWxFirstInviteRecordDTO(distance, inviter);
        upLookup.add(dto);
        if (inviter.getRobotInviteWxId().equals(inviter.getWxId())) {//邀请者是根，停止找
            return;
        }
        upLookupRecursive(distance + 1, inviter.getInviteWxId(), invitedMap, upLookup, foundSet);

    }

    private List<ScrmPersonalWxFirstInviteRecord> parallelQueryAllRecord(String liveId, MemberInviteRelationType memberInviteRelationType, ScrmPersonalWxFirstInviteRecord firstInvitedRecord) {
        log.info("并行查询邀请记录");
        int pageSize = Lion.getInt(Environment.getAppName(), "query.invite.relation.page.size", 100);
        long total = personalWxFirstInviteRecordDomainService.queryFirstInviteRecordCount(liveId, firstInvitedRecord.getRobotInviteWxId(), firstInvitedRecord.getId(), memberInviteRelationType.getCode());
        if (total == 0) {
            return new ArrayList<>();
        } else if (total <= pageSize) {
            return queryRecord(PageRequest.of(1, pageSize), liveId, memberInviteRelationType, firstInvitedRecord);
        }

        int pager = (int) total / pageSize;
        if (total % pageSize != 0) {
            pager++;
        }
        List<ScrmPersonalWxFirstInviteRecord> allRecord = new ArrayList<>();
        List<CompletableFuture<List<ScrmPersonalWxFirstInviteRecord>>> futureList = new ArrayList<>();
        for (int i = 1; i <= pager; i++) {
            PageRequest pageRequest = PageRequest.of(i, pageSize);
            CompletableFuture<List<ScrmPersonalWxFirstInviteRecord>> future = CompletableFuture.supplyAsync(() ->
                            queryRecord(pageRequest, liveId, memberInviteRelationType, firstInvitedRecord)
                    , ThreadPoolUtils.queryBatchInviteRelationParallelPool);
            futureList.add(future);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<List<ScrmPersonalWxFirstInviteRecord>> future : futureList) {
            List<ScrmPersonalWxFirstInviteRecord> recordList = future.join();
            allRecord.addAll(recordList);
        }
        return allRecord;
    }

    private List<ScrmPersonalWxFirstInviteRecord> queryRecord(PageRequest pageRequest, String liveId, MemberInviteRelationType memberInviteRelationType, ScrmPersonalWxFirstInviteRecord firstInvitedRecord) {
        log.info("当前分页：{}", pageRequest.getOriginPageNo());
        List<ScrmPersonalWxFirstInviteRecord> recordList = personalWxFirstInviteRecordDomainService.queryFirstInviteRecord(liveId, firstInvitedRecord.getRobotInviteWxId(), firstInvitedRecord.getId(), memberInviteRelationType.getCode(), pageRequest);
        if (CollectionUtil.isEmpty(recordList)) {
            log.info("无用户的邀请记录");
            return recordList;
        }
        return recordList;
    }

    private List<ScrmPersonalWxFirstInviteRecord> queryAllRecord(String liveId, MemberInviteRelationType memberInviteRelationType, ScrmPersonalWxFirstInviteRecord firstInvitedRecord) {
        int totalPageNo = 100;
        PageRequest pageRequest = new PageRequest(1, 100);
        List<ScrmPersonalWxFirstInviteRecord> allRecord = new ArrayList<>();
        while (true) {
            log.info("当前分页：{}", pageRequest.getOriginPageNo());
            List<ScrmPersonalWxFirstInviteRecord> recordList = personalWxFirstInviteRecordDomainService.queryFirstInviteRecord(liveId, firstInvitedRecord.getRobotInviteWxId(), firstInvitedRecord.getId(), memberInviteRelationType.getCode(), pageRequest);
            if (CollectionUtil.isEmpty(recordList)) {
                log.info("无用户的邀请记录");
                break;
            }
            allRecord.addAll(recordList);
            if (recordList.size() < pageRequest.getPageSize()) {
                log.info("当前用户的邀请记录已查到最后，查询结束");
                break;
            }
            if (pageRequest.getOriginPageNo() >= totalPageNo) {
                log.info("当前直播下用户记录过多，防止全库查询，只能返回这么多了,{}", pageRequest.getOriginPageNo());
                break;
            }
            pageRequest.nextPage();
        }
        return allRecord;
    }
}
