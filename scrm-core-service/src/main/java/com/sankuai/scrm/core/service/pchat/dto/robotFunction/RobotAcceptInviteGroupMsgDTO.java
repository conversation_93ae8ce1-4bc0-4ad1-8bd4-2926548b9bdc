package com.sankuai.scrm.core.service.pchat.dto.robotFunction;

import lombok.Data;

/**
 * 4506
 * @Description 【直接回调】机器人收到入群邀请回调（兼容PC）
 * <AUTHOR>
 * @Create On 2023/11/6 17:39
 * @Version v1.0.0
 */
@Data
public class RobotAcceptInviteGroupMsgDTO {

    /**
     * 群名称
     */
    private String vcChatRoomName;

    /**
     * base64群名称
     */
    private String vcChatRoomBase64Name;

    /**
     * 好友的微信ID
     */
    private String vcFriendWxId;

    /**
     * 好友的编号（MD5加密的微信ID）
     */
    private String vcFriendWxIdSerialNo;

    /**
     * 群头像
     */
    private String vcHeadImgUrl;

    /**
     * 是否企微外部群（true：是）
     */
    private String IsEnterpriseChatroom;

    /**
     * 全url
     */
    private String vcFullUrl;

    /**
     * 消息描述
     */
    private String MsgDesc;
}
