package com.sankuai.scrm.core.service.aigc.swarm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR> on 2024/11/08 21:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SwarmAgentConfig {

    // 该Agent被调用时的functionName。
    // Example: transferToProductRecommendAgent
    private String calledFunctionName;
    // 该Agent被调用时的说明，将会被放在function description中。需要描述该Agent能做什么，以及什么时候该调用这个Agent。
    // Example: Get the delivery date for a customer's order. Call this whenever you need to know the delivery date, for
    // example when a customer asks 'Where is my package'.
    private String calledDescription;

    private String name;
    private String model;
    private String prompt;
    @Nullable
    private String knowledgeAppId;
    // 可调用的AgentIdentifier，需要为全英文的字符串。
    // Example: ProductRecommendAgent
    @Nullable
    private List<String> transferableAgents;
    @Nullable
    private List<SwarmFunctionConfig> functions;
    // none/auto/required
    private String toolChoice;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SwarmFunctionConfig {
        private String beanName;
        private String methodName;
        private String description;
    }

}
