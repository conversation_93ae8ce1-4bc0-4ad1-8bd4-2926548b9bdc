package com.sankuai.scrm.core.service.group.event.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class GroupMsgConsumerTest {

    private GroupMsgConsumer groupMsgConsumer;

    private IConsumerProcessor consumer;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        groupMsgConsumer = new GroupMsgConsumer();
        consumer = Mockito.mock(IConsumerProcessor.class);
        // Use reflection to set the consumer field
        Field consumerField = GroupMsgConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(groupMsgConsumer, consumer);
    }

    /**
     * 测试destroy方法，消费者为空的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // Use reflection to set the consumer field to null
        Field consumerField = GroupMsgConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(groupMsgConsumer, null);
        // act
        groupMsgConsumer.destroy();
        // assert
        verify(consumer, times(0)).close();
    }

    /**
     * 测试destroy方法，消费者不为空的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // act
        groupMsgConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
