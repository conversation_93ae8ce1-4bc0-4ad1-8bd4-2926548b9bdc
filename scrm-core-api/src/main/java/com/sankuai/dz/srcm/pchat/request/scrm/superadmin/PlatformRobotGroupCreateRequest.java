package com.sankuai.dz.srcm.pchat.request.scrm.superadmin;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.srcm.pchat.request.Request;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/11/4 11:25
 * @Version v1.0.0
 */
@Data
@TypeDoc(description = "平台号组创建")
public class PlatformRobotGroupCreateRequest extends Request {

    @FieldDoc(description = "机器人序列号")
    private List<String> robotSerialNos;

    @FieldDoc(description = "平台号机器人组名称")
    private String platformRobotGroupName;
    @FieldDoc(description = "平台号组编号")
    private String platformRobotGroupNo;

}
