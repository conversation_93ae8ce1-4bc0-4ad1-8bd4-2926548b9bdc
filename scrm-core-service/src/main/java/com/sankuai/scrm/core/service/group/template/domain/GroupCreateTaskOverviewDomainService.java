package com.sankuai.scrm.core.service.group.template.domain;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.group.creation.enums.TaskStatus;
import com.sankuai.dz.srcm.group.creation.request.TaskListQueryRequest;
import com.sankuai.scrm.core.service.group.template.dao.bo.GroupBatchCreateTaskOverview;
import com.sankuai.scrm.core.service.group.template.dao.example.GroupBatchCreateTaskOverviewExample;
import com.sankuai.scrm.core.service.group.template.dao.mapper.GroupBatchCreateTaskOverviewMapper;
import com.sankuai.scrm.core.service.group.template.enums.GroupCreateTaskType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GroupCreateTaskOverviewDomainService {

    @Autowired
    private GroupBatchCreateTaskOverviewMapper taskOverviewMapper;

    public GroupBatchCreateTaskOverview queryById(Long taskId) {
        if (taskId == null) {
            return null;
        }
        return taskOverviewMapper.selectByPrimaryKey(taskId);
    }

    public List<GroupBatchCreateTaskOverview> queryNotAllStartedTask(String appId, int offset, int limit) {
        if (StringUtils.isEmpty(appId) || offset < 0 || limit <= 0) {
            return Lists.newArrayList();
        }
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andNotStartSizeGreaterThan(0);
        example.setOffset(offset);
        example.setRows(limit);
        example.setOrderByClause("id asc");
        return taskOverviewMapper.selectByExample(example);
    }

    public List<GroupBatchCreateTaskOverview> queryTaskByType(String appId, GroupCreateTaskType taskType, int offset, int limit) {
        if (StringUtils.isEmpty(appId) || taskType == null || offset < 0 || limit <= 0) {
            return Lists.newArrayList();
        }
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andTaskTypeEqualTo(taskType.getCode());
        example.setOffset(offset);
        example.setRows(limit);
        example.setOrderByClause("id asc");
        return taskOverviewMapper.selectByExample(example);
    }

    public List<GroupBatchCreateTaskOverview> queryTask(TaskListQueryRequest request, GroupCreateTaskType taskType) {
        GroupBatchCreateTaskOverviewExample example = buildExample(request, taskType);
        if (request.getPage() != null && request.getPage() > 0 && request.getPageSize() != null && request.getPageSize() > 0) {
            example.setOffset((request.getPage() - 1) * request.getPageSize());
            example.setRows(request.getPageSize());
        }
        return taskOverviewMapper.selectByExample(example);
    }

    public long countTask(TaskListQueryRequest request, GroupCreateTaskType taskType) {
        GroupBatchCreateTaskOverviewExample example = buildExample(request, taskType);
        return taskOverviewMapper.countByExample(example);
    }

    public boolean createTaskOverview(GroupBatchCreateTaskOverview taskOverview) {
        if (taskOverview == null) {
            return false;
        }
        return taskOverviewMapper.insertSelective(taskOverview) > 0;
    }

    private GroupBatchCreateTaskOverviewExample buildExample(TaskListQueryRequest request, GroupCreateTaskType taskType) {
        if (request == null) {
            return new GroupBatchCreateTaskOverviewExample();
        }
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        GroupBatchCreateTaskOverviewExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(request.getAppId())) {
            criteria.andAppIdEqualTo(request.getAppId());
        }
        if (StringUtils.isNotEmpty(request.getTaskName())) {
            criteria.andTaskNameLike("%" + request.getTaskName() + "%");
        }
        TaskStatus taskStatus = TaskStatus.getTaskStatusByCode(request.getStatus());
        if (taskStatus != null) {
            if (TaskStatus.CREATING.equals(taskStatus)) {
                criteria.andNotStartSizeGreaterThan(0);
            } else if (TaskStatus.FINISHED.equals(taskStatus)) {
                criteria.andTaskTypeEqualTo(0);
            }
        }
        if (taskType != null) {
            criteria.andTaskTypeEqualTo(taskType.getCode());
        }
        if (request.getStartTime() != null) {
            criteria.andStartTimeGreaterThanOrEqualTo(request.getStartTime());
        }
        if (request.getEndTime() != null) {
            criteria.andEndTimeLessThanOrEqualTo(request.getEndTime());
        }
        if (StringUtils.isNotEmpty(request.getCreator())) {
            criteria.andCreatorEqualTo(request.getCreator());
        }
        example.setOrderByClause("add_time desc");
        return example;
    }

    public boolean decreaseNotStartSize(Long taskId) {
        if (taskId == null) {
            return false;
        }
        GroupBatchCreateTaskOverview overview = queryById(taskId);
        if (overview == null || overview.getNotStartSize() == null || overview.getNotStartSize() <= 0) {
            return false;
        }
        GroupBatchCreateTaskOverview record = GroupBatchCreateTaskOverview.builder()
                .notStartSize(overview.getNotStartSize() - 1)
                .build();
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        example.createCriteria()
                .andIdEqualTo(taskId)
                .andNotStartSizeEqualTo(overview.getNotStartSize());
        return taskOverviewMapper.updateByExampleSelective(record, example) > 0;
    }

    public boolean increaseSuccessSize(Long taskId) {
        if (taskId == null) {
            return false;
        }
        GroupBatchCreateTaskOverview overview = queryById(taskId);
        if (overview == null || overview.getSuccessSize() == null || overview.getTaskSize() == null
                || overview.getSuccessSize() >= overview.getTaskSize()) {
            return false;
        }
        GroupBatchCreateTaskOverview record = GroupBatchCreateTaskOverview.builder()
                .successSize(overview.getSuccessSize() + 1)
                .build();
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        example.createCriteria()
                .andIdEqualTo(taskId)
                .andSuccessSizeEqualTo(overview.getSuccessSize());
        return taskOverviewMapper.updateByExampleSelective(record, example) > 0;
    }

    public boolean increaseFailSize(Long taskId) {
        if (taskId == null) {
            return false;
        }
        GroupBatchCreateTaskOverview overview = queryById(taskId);
        if (overview == null || overview.getFailSize() == null || overview.getTaskSize() == null
                || overview.getFailSize() >= overview.getTaskSize()) {
            return false;
        }
        GroupBatchCreateTaskOverview record = GroupBatchCreateTaskOverview.builder()
                .failSize(overview.getFailSize() + 1)
                .build();
        GroupBatchCreateTaskOverviewExample example = new GroupBatchCreateTaskOverviewExample();
        example.createCriteria()
                .andIdEqualTo(taskId)
                .andFailSizeEqualTo(overview.getFailSize());
        return taskOverviewMapper.updateByExampleSelective(record, example) > 0;
    }

    public boolean retryFailSize(Long taskId) {
        if (taskId == null) {
            return false;
        }
        GroupBatchCreateTaskOverview overview = queryById(taskId);
        if (overview == null || overview.getFailSize() == null || overview.getFailSize() <= 0) {
            return false;
        }
        GroupBatchCreateTaskOverview record = GroupBatchCreateTaskOverview.builder()
                .id(taskId)
                .failSize(0)
                .notStartSize(overview.getFailSize())
                .build();
        return taskOverviewMapper.updateByPrimaryKeySelective(record) > 0;
    }
}
