package com.sankuai.scrm.core.service.open.convert;



import com.sankuai.dz.srcm.open.dto.*;
import com.sankuai.dz.srcm.open.enums.MsgTypeEnum;
import com.sankuai.dz.srcm.open.msgbody.ScrmOpenGroupMsgBody;
import com.sankuai.scrm.core.service.chat.mq.msg.GroupChatMsg;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenChattingMessage;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Data
public class ScrmOpenGroupMsgBodyConvert {


    public static ScrmOpenGroupMsgBody meiXiaoTuanGroupConvert(GroupChatMsg msg, List<AssistantInfo> assistantList, String appId) {
        if (msg == null) {
            return null;
        }

        ScrmOpenGroupMsgBody scrmOpenGroupMsgBody = new ScrmOpenGroupMsgBody();
        scrmOpenGroupMsgBody.setAppId(appId);
        scrmOpenGroupMsgBody.setGroupId(msg.getGroup().getGroupId());
        scrmOpenGroupMsgBody.setSendWxUserId(msg.getMember().getUserId());
        scrmOpenGroupMsgBody.setSenderNickName(msg.getMember().getUserName());
        long senderCount = 0;
        if (CollectionUtils.isNotEmpty(assistantList)){
            senderCount  = assistantList.stream().map(AssistantInfo::getAccountId).filter(x -> x.contains(msg.getMember().getUserId())).count();
        }
        scrmOpenGroupMsgBody.setSendWxUserType(senderCount > 0 ? "1" : "2");
        List<MessageDataDTO> msgData = new ArrayList<>();
        MessageDataDTO messageDataDTO = createMessageDataDTO(msg.getMessage());
        if (messageDataDTO != null) {
            msgData.add(messageDataDTO);
        }
        scrmOpenGroupMsgBody.setMsgData(msgData);
        scrmOpenGroupMsgBody.setDtmsgtime(formatMessageTime(msg.getMessageTime()));
        return scrmOpenGroupMsgBody;
    }



    private static MessageDataDTO createMessageDataDTO(OpenChattingMessage message) {
        MessageDataDTO messageDataDTO = new MessageDataDTO();
        switch (message.getMessageType()) {
            case "TEXT":
                messageDataDTO.setText(createTextMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.TEXT.getCode());
                break;
            case "IMAGE":
                messageDataDTO.setImage(createImageMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.IMAGE.getCode());
                break;
            case "MINI_PROGRAM":
                messageDataDTO.setMiniProgram(createMiniProgramMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.MINI_PROGRAM.getCode());
                break;
            case "H5_CARD":
                messageDataDTO.setLink(createLinkMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.NEWS.getCode());
                break;
            case "AUDIO":
                messageDataDTO.setVoice(createVoiceMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.VOICE.getCode());
                break;
            case "CHAT_HISTORY":
                messageDataDTO.setChatHistoryList(createChatHistoryMessageDTO(message.getHistoryMessages()));
                messageDataDTO.setMsgType(MsgTypeEnum.CHAT_HISTORY.getCode());
                break;
            default:
                // Handle unsupported message types if necessary
                return null;
        }
        return messageDataDTO;
    }

    private static ChatHistoryDataDTO createChatHistoryDataDTO(OpenChattingMessage message) {
        ChatHistoryDataDTO messageDataDTO = new ChatHistoryDataDTO();
        switch (message.getMessageType()) {
            case "TEXT":
                messageDataDTO.setText(createTextMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.TEXT.getCode());
                break;
            case "IMAGE":
                messageDataDTO.setImage(createImageMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.IMAGE.getCode());
                break;
            case "MINI_PROGRAM":
                messageDataDTO.setMiniProgram(createMiniProgramMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.MINI_PROGRAM.getCode());
                break;
            case "H5_CARD":
                messageDataDTO.setLink(createLinkMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.NEWS.getCode());
                break;
            case "AUDIO":
                messageDataDTO.setVoice(createVoiceMessageDTO(message));
                messageDataDTO.setMsgType(MsgTypeEnum.VOICE.getCode());
                break;
            case "CHAT_HISTORY":
                messageDataDTO.setChatHistoryList(createChatHistoryMessageDTO(message.getHistoryMessages()));
                messageDataDTO.setHistoryTitle(message.getHistoryTitle());
                messageDataDTO.setMsgType(MsgTypeEnum.CHAT_HISTORY.getCode());
                break;
            default:
                // Handle unsupported message types if necessary
                return null;
        }
        return messageDataDTO;
    }
    

    private static TextMessageDTO createTextMessageDTO(OpenChattingMessage message) {
        TextMessageDTO textMessageDTO = new TextMessageDTO();
        textMessageDTO.setContent(message.getTextContent());
        return textMessageDTO;
    }

    private static ImageMessageDTO createImageMessageDTO(OpenChattingMessage message) {
        ImageMessageDTO imageMessageDTO = new ImageMessageDTO();
        imageMessageDTO.setUrl(message.getImageUrl());
        return imageMessageDTO;
    }

    private static MiniProgramMessageDTO createMiniProgramMessageDTO(OpenChattingMessage message) {
        MiniProgramMessageDTO miniProgramMessageDTO = new MiniProgramMessageDTO();
        miniProgramMessageDTO.setAppId(message.getMiniAppId());
        miniProgramMessageDTO.setOriginAppId(message.getMiniOriginAppId());
        miniProgramMessageDTO.setTitle(message.getMiniTitle());
        miniProgramMessageDTO.setPagePath(message.getMiniPagePath());
        miniProgramMessageDTO.setAppName(message.getMiniAppName());
        return miniProgramMessageDTO;
    }

    private static LinkMessageDTO createLinkMessageDTO(OpenChattingMessage message) {
        LinkMessageDTO linkMessageDTO = new LinkMessageDTO();
        linkMessageDTO.setTitle(message.getH5Title());
        linkMessageDTO.setUrl(message.getH5LinkUrl());
        linkMessageDTO.setDescription(message.getH5Description());
        return linkMessageDTO;
    }

    private static VoiceMessageDTO createVoiceMessageDTO(OpenChattingMessage message) {
        VoiceMessageDTO voiceMessageDTO = new VoiceMessageDTO();
        voiceMessageDTO.setContent(message.getTextContent());
        return voiceMessageDTO;
    }


    public static List<ChatHistoryDataDTO> createChatHistoryMessageDTO(List<OpenChattingMessage> historyMessages) {
        List<ChatHistoryDataDTO> chatHistoryDataDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(historyMessages)) {
            return chatHistoryDataDTOS;
        }
        for (OpenChattingMessage historyMessage : historyMessages) {
            ChatHistoryDataDTO chatHistoryDataDTO = createChatHistoryDataDTO(historyMessage);
            if (chatHistoryDataDTO == null) {
                continue;
            }
            chatHistoryDataDTOS.add(chatHistoryDataDTO);
        }
        return chatHistoryDataDTOS;
    }

    private static String formatMessageTime(long messageTime) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(messageTime), ZoneId.systemDefault());
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
