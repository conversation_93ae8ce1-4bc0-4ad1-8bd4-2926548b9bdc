package com.sankuai.scrm.core.service.friend.cluecode.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.friend.cluecode.dal.entity.ClueCode;
import com.sankuai.scrm.core.service.friend.cluecode.dal.example.ClueCodeExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClueCodeMapper extends MybatisBaseMapper<ClueCode, ClueCodeExample, Long> {
    int batchInsert(@Param("list") List<ClueCode> list);
}