package com.sankuai.dz.srcm.automatedmanagement.response.productpool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryManualRefreshStatusResult implements Serializable {
    /**
     * 执行状态 负一表示执行出错 0表示未开始执行 1表示已执行 2表示重复执行 3表示正在执行
     */
    private Integer status;
}
