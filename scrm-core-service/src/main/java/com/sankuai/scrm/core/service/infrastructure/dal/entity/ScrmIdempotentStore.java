package com.sankuai.scrm.core.service.infrastructure.dal.entity;

import lombok.*;

/**
 *
 *   表名: scrm_idempotent_store
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmIdempotentStore {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: union_key
     *   说明: 唯一key
     */
    private String unionKey;

    /**
     *   字段: category
     *   说明: 类别
     */
    private String category;
}