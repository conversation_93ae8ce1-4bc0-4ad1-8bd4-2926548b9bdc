package com.sankuai.scrm.core.service.activity.fission.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.sankuai.dz.srcm.activity.fission.enums.StatusEnum;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelDTO;
import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeStatusEnum;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionActivityExample;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeChannelLocalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@CraneConfiguration
public class CleanExpireFissionActivityChannelTask {

    @Resource
    private GroupFissionActivityMapper fissionActivityMapper;

    @Resource
    private GroupDynamicCodeChannelLocalService codeChannelLocalService;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.clean.expire.fission.activity.relation.channel")
    public void cleanExpireFissionActivityChannel(){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.fission.crane.CleanExpireFissionActivityChannelTask.cleanExpireFissionActivityChannel()");

        List<GroupFissionActivity> allNeedHandlerActivityList = new ArrayList<>();

        Date thirtyDayBefore = DateUtils.addDays(new Date(),-60);
        Date twoDayBefore = DateUtils.addDays(new Date(),-2);

        GroupFissionActivityExample invalidExample = new GroupFissionActivityExample();
        invalidExample.createCriteria()
                .andStatusIn(Arrays.asList(StatusEnum.DELETED.getCode(),StatusEnum.FINISHED.getCode()))
                .andUpdateTimeBetween(thirtyDayBefore,twoDayBefore);

        List<GroupFissionActivity> invalidFissionActivityList = fissionActivityMapper.selectByExample(invalidExample);
        if(CollectionUtils.isNotEmpty(invalidFissionActivityList)){
            allNeedHandlerActivityList.addAll(invalidFissionActivityList);
        }

        GroupFissionActivityExample validAndExpireExample = new GroupFissionActivityExample();
        validAndExpireExample.createCriteria()
                .andStatusEqualTo(StatusEnum.EFFECTIvE.getCode())
                .andEndTimeBetween(thirtyDayBefore,twoDayBefore);
        List<GroupFissionActivity> validFissionActivityList = fissionActivityMapper.selectByExample(validAndExpireExample);
        if(CollectionUtils.isNotEmpty(validFissionActivityList)){
            allNeedHandlerActivityList.addAll(validFissionActivityList);
        }

        if(CollectionUtils.isEmpty(allNeedHandlerActivityList)){
            log.info("cleanExpireFissionActivityChannel has no activity need to handler");
            return;
        }

        List<Long> channelIdList = allNeedHandlerActivityList.stream().map(activity->activity.getChannelId()).collect(Collectors.toList());

        List<GroupDynamicCodeChannelDTO> dtoList = codeChannelLocalService.querySimpleChannelList(channelIdList);
        if(CollectionUtils.isEmpty(dtoList)){
            log.info("cleanExpireFissionActivityChannel has not channel need to handler");
            return;
        }

        for(GroupDynamicCodeChannelDTO dynamicCodeChannelDTO : dtoList){
            dynamicCodeChannelDTO.setStatus(DynamicCodeStatusEnum.INVALID.getCode());
            codeChannelLocalService.saveChannelFromSys(dynamicCodeChannelDTO);
        }

    }

}
