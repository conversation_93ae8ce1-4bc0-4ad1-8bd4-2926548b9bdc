package com.sankuai.scrm.core.service.pchat.service.activity;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.scrm.core.service.util.model.FileBody;
import com.sankuai.scrm.core.service.util.FileBodyBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/2 14:38
 * @Version v1.0.0
 */
@Slf4j
@Service
public class DownloadExcelDataService {
    public String download(String fileName, List<String> header, List<List<String>> body) {
        log.info("生成excel url");
        Map<String, List<List<String>>> sheetData = Maps.newHashMap();
        List<List<String>> rowData = Lists.newArrayList();
        sheetData.put("result", rowData);
        rowData.add(header);
        if (CollectionUtil.isNotEmpty(body)) {
            rowData.addAll(body);
        }
        try {
            FileBody fileBody = FileBodyBuilder.buildExcelFileBody(fileName, sheetData);
            return fileBody.getUrl();
        } catch (Exception e) {
            log.error("buildExcelFileBody fail", e);
        }

        return null;
    }

    public String download(List<String> header, List<List<String>> body) {
        return download(String.valueOf(System.currentTimeMillis()), header, body);
    }

}
