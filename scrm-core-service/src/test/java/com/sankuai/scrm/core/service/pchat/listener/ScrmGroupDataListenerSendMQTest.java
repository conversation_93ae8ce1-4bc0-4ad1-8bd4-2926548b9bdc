package com.sankuai.scrm.core.service.pchat.listener;


import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxSendMQLogDomainService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.function.Consumer;

import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class ScrmGroupDataListenerSendMQTest {

    @InjectMocks
    private ScrmGroupDataListener scrmGroupDataListener;

    @Mock(lenient = true)
    private ScrmPersonalWxSendMQLogDomainService personalWxSendMQLogDomainService;
    @Mock(lenient = true)
    private RedisStoreClient redisStoreClient;

    private final String category = "testCategory";
    private final String queryKey = "testQueryKey";
    private final String json = "{\"key\":\"value\"}";
    private final Consumer<String> consumer = mock(Consumer.class);

    @Before
    public void setUp() {
    }

    /**
     * 测试sendMQ方法，当uniquePushSwitch为false时
     */
    @Test
    public void testSendMQ_UniquePushSwitchFalse() throws Throwable {
        // arrange
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.group.push.mq.unique.switch", true)).thenReturn(false);

            // act
            ReflectionTestUtils.invokeMethod(scrmGroupDataListener, "sendMQ", category, queryKey, json, consumer, (Object) null);

            // assert
            verify(consumer).accept(json);
        }
    }

    /**
     * 测试sendMQ方法，当params为null时
     */
    @Test
    public void testSendMQ_ParamsNull() throws Throwable {
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.group.push.mq.unique.switch", true)).thenReturn(true);

            // act
            ReflectionTestUtils.invokeMethod(scrmGroupDataListener, "sendMQ", category, queryKey, json, consumer, (Object) null);

            // assert
            verify(consumer).accept(json);
        }
    }

    /**
     * 测试sendMQ方法，当params不为空时
     */
    @Test
    public void testSendMQ_ParamsNotEmpty() throws Throwable {
        // arrange
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.group.push.mq.unique.switch", true)).thenReturn(true);
            Object[] params = {"param1", "param2"};

            when(redisStoreClient.get(any(StoreKey.class))).thenReturn(null);
            // act
            ReflectionTestUtils.invokeMethod(scrmGroupDataListener, "sendMQ", category, queryKey, json, consumer, params);

            // assert
            verify(consumer).accept(json);
            // 验证idempotentSendMQ方法被调用，由于是私有方法，这里不直接验证，但通过逻辑可知必须被调用
        }
    }

    @Test
    public void testSendMQ_ParamsNotEmpty2() throws Throwable {
        // arrange
        try (MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.group.push.mq.unique.switch", true)).thenReturn(true);
            Object[] params = {"param1", "param2"};

            when(redisStoreClient.get(any(StoreKey.class))).thenReturn(1L);
            // act
            ReflectionTestUtils.invokeMethod(scrmGroupDataListener, "sendMQ", category, queryKey, json, consumer, params);

            // assert
            verify(consumer,never()).accept(json);
            // 验证idempotentSendMQ方法被调用，由于是私有方法，这里不直接验证，但通过逻辑可知必须被调用
        }
    }


}
