package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_GroupSetConfig
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxGroupSetConfig {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: appId
     */
    private String appId;

    /**
     *   字段: project_id
     *   说明: 直播项目id
     */
    private String projectId;

    /**
     *   字段: creater
     *   说明: 创建者的mis号
     */
    private String creater;

    /**
     *   字段: set_name
     *   说明: 群组名称
     */
    private String setName;

    /**
     *   字段: group_name
     *   说明: 群名称
     */
    private String groupName;

    /**
     *   字段: is_auto_create_new_group
     *   说明: 是否需要自动建群
     */
    private Boolean isAutoCreateNewGroup;

    /**
     *   字段: group_count
     *   说明: 需要创建的群数量
     */
    private Integer groupCount;

    /**
     *   字段: welcome_msg_id
     *   说明: 企微欢迎语id
     */
    private Long welcomeMsgId;

    /**
     *   字段: status
     *   说明: 自增群组状态
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: notice_msg_id
     *   说明: 个微通知id
     */
    private Long noticeMsgId;
}