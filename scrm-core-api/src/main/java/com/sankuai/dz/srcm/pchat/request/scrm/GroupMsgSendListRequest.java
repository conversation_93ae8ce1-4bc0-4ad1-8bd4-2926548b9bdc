package com.sankuai.dz.srcm.pchat.request.scrm;

import com.sankuai.dz.srcm.pchat.request.Request;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/8 19:10
 * @Version v1.0.0
 */
@Data
public class GroupMsgSendListRequest extends Request {
    private Long msgId;
    private List<Long> msgIds;
    private List<String> sendStates;

    @Override
    public boolean validParam() {
        if (msgIds == null) {
            msgIds = new ArrayList<>();
        }
        msgIds.add(msgId);
        this.assertNotEmpty(msgIds, messageNotBlank("消息id"));

        return true;
    }
}
