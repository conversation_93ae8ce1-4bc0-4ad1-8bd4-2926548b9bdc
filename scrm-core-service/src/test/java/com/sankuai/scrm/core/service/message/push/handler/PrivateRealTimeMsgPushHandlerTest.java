package com.sankuai.scrm.core.service.message.push.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushLog;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushTask;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChatType;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushSceneType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateRealTimeMsgPushHandlerTest {

    @InjectMocks
    private PrivateRealTimeMsgPushHandler handler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Test
    public void canHandle() {
        MsgPushChatType chatType = MsgPushChatType.PRIVATE;
        MsgPushSceneType sceneType = MsgPushSceneType.REALTIME_SCENE;
        boolean result = handler.canHandle(chatType, sceneType);
        assertTrue(result);
    }

    @Test
    public void fillPushChannelReceiverIdSet() {
        MsgPushTask task = new MsgPushTask();
        task.setAppId("appId");
        List<String> receiverIdList = Lists.newArrayList("s1", "s2");
        Map<String, String> dsPushMap = Maps.newHashMap();
        Map<String, String> wxPushMap = Maps.newHashMap();
        Map<String, String> unablePushMap = Maps.newHashMap();
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAccountId("accountId");
        when(dsAssistantAcl.getAssistantList("appId")).thenReturn(Lists.newArrayList(assistantInfo));
        ContactUser contactUser = new ContactUser();
        contactUser.setExternalUserId("s1");
        when(contactUserDomain.queryContactUsersByExternalUserIdListAndStaffIdList(anyString(), anyList(), anyList())).thenReturn(Lists.newArrayList(contactUser));
        handler.fillPushChannelReceiverIdSet(task, receiverIdList, dsPushMap, wxPushMap, unablePushMap);
        assertTrue(dsPushMap.containsKey("s1"));
        assertTrue(unablePushMap.containsKey("s2"));
    }
}