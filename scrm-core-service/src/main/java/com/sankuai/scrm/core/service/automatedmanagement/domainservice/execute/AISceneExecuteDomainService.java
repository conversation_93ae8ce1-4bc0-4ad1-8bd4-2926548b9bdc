package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUserAddCropTagDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.tag.service.TagServiceImpl;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/28 17:31
 * @Version v1.0.0
 */
@Component
@Slf4j
public class AISceneExecuteDomainService {

    @Autowired
    private TagServiceImpl tagService;

    @Autowired
    private CorpAppConfigRepository corpAppConfigRepository;

    @Autowired
    private ExecuteWriteDomainService executeWriteDomainService;

    @Autowired
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Autowired
    private CrowdPackUserAddCropTagDomainService crowdPackUserAddCropTagDomainService;

    @Autowired
    private ExecuteManagementService executeManagementService;

    @Autowired
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Resource
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Resource
    private ContactUserDoMapper contactUserDoMapper;

    private static final String CAT_TYPE = AISceneExecuteDomainService.class.getSimpleName();

    public StepExecuteResultDTO runRealTimeTask(Long processOrchestrationId, String wxUnionId, IntelligentFollowResultDTO aiSceneContent) throws ExecutionException, InterruptedException, TimeoutException {
//        Transaction t = Cat.newTransaction(CAT_TYPE, "runRealTimeTask");
        try {
            log.info("runRealTimeTaskAI begin processOrchestrationId:" + processOrchestrationId + " wxUnionId:" + wxUnionId + " aiSceneContent:" + JsonUtils.toStr(aiSceneContent));
            Cat.logEvent(CAT_TYPE, "runRealTimeTaskAI");
            if(processOrchestrationId <= 0L ){
                StepExecuteResultDTO resultDTO = new StepExecuteResultDTO();
                resultDTO.setSuccess(true);
                resultDTO.setProcessOrchestrationId(processOrchestrationId);
                resultDTO.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode());
                resultDTO.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getDesc());
                Cat.logEvent(CAT_TYPE, "NoNeedSendMsg");
                return resultDTO;
            }
            ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = null;
            try {
                scrmProcessOrchestrationDTO = processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId).get(5, TimeUnit.SECONDS);
                Cat.logEvent(CAT_TYPE, "queryProcessOrchestrationDetailFuture_success");
            } catch (Exception e) {
                Cat.logEvent(CAT_TYPE, "queryProcessOrchestrationDetailFuture_error");
                log.error("queryProcessOrchestrationDetailFuture error", e);
                throw e;
            }
            if (null == scrmProcessOrchestrationDTO) {
                Cat.logEvent(CAT_TYPE, "ProcessOrchestrationNotFound", "ERROR", String.valueOf(processOrchestrationId));
                StepExecuteResultDTO resultDTO = new StepExecuteResultDTO();
                resultDTO.setSuccess(false);
                resultDTO.setProcessOrchestrationId(processOrchestrationId);
                resultDTO.setProcessOrchestrationVersion(scrmProcessOrchestrationDTO.getValidVersion());
                resultDTO.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
                resultDTO.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getDesc());
                Cat.logEvent(CAT_TYPE, "ProcessOrchestrationNotFound");
                return resultDTO;
            }
            
            scrmProcessOrchestrationDTO.setAiScene(true);
            scrmProcessOrchestrationDTO.setAiSceneContent(aiSceneContent);

            ExecuteManagementDTO executeManagementDTO = executeManagementService.getExecuteMediumManagementDTO(scrmProcessOrchestrationDTO);
            Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = scrmProcessOrchestrationDTO.getNodeMediumDTO()
                    .getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(
                            ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity(), (o1, o2) -> o1));
            ScrmProcessOrchestrationNodeDTO rootNode = nodeDTOMap.get(0L);

            StepExecuteResultDTO stepExecuteResultDTO = null;
            try {
                stepExecuteResultDTO = executeWriteDomainService.executeRealTimeTask(scrmProcessOrchestrationDTO, wxUnionId, nodeDTOMap, rootNode, null, executeManagementDTO);
                Cat.logEvent(CAT_TYPE, "executeRealTimeTask_success");
            } catch (Exception e) {
                Cat.logEvent(CAT_TYPE, "executeRealTimeTask_error");
                log.error("executeRealTimeTask error",e);
                throw e;
            }
            executeWriteDomainService.secondStaffTaskAssignStep(scrmProcessOrchestrationDTO, rootNode, executeManagementDTO, null);
            executeWriteDomainService.thirdWxGroupTouchStep(scrmProcessOrchestrationDTO, executeManagementDTO, stepExecuteResultDTO);
            executeWriteDomainService.updateNodeExecuteLog(scrmProcessOrchestrationDTO, executeManagementDTO);
            stepExecuteResultDTO.setProcessOrchestrationId(processOrchestrationId);
            stepExecuteResultDTO.setUserUnionId(wxUnionId);
            log.info("runRealTimeTaskAI end result:" + JsonUtils.toStr(stepExecuteResultDTO));
            Cat.logEvent(CAT_TYPE, "runRealTimeTaskAI_success");
            return stepExecuteResultDTO;
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "runRealTimeTask_error");
            log.info("runRealTimeTask error",e);
            throw e;
        }
    }
}
