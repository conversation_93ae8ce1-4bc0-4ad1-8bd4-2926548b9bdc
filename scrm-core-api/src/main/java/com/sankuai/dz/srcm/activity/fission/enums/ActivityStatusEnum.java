package com.sankuai.dz.srcm.activity.fission.enums;

public enum ActivityStatusEnum {
    NOT_STARTED(1,"未开始"),
    ON_GOING(2,"进行中"),
    OVER(3,"已结束");

    private int code;
    private String desc;

    ActivityStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
