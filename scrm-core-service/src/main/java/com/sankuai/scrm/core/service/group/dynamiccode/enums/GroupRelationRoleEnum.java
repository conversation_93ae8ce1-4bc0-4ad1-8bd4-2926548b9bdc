package com.sankuai.scrm.core.service.group.dynamiccode.enums;

import lombok.Getter;

/**
 * 企微社群群活码：群聊在关联关系中的角色枚举
 */
@Getter
public enum GroupRelationRoleEnum {
    UNKNOWN(-1, "未知"),
    CHILD(0, "子群"),
    PARENT(1, "母群"),
    /**
     * 指的是曾经作为母群发生自增，然后被踢出群活码关联列表（从而实现无限自增）的群
     */
    FROZEN_PARENT(2, "已冻结的母群"),
    ;

    private final int code;
    private final String desc;

    GroupRelationRoleEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GroupRelationRoleEnum ofCode(Integer code) {
        GroupRelationRoleEnum result = UNKNOWN;
        if (code != null) {
            for (GroupRelationRoleEnum e : values()) {
                if (code == e.getCode()) {
                    result = e;
                    break;
                }
            }
        }
        return result;
    }
}
