package com.sankuai.dz.srcm.pchat.tanjing;

import com.sankuai.dz.srcm.pchat.anno.ApiMapping;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.dto.InvokeResultDTO;
import com.sankuai.dz.srcm.pchat.request.ConfirmScanCodeAuthorizeRequest;
import com.sankuai.dz.srcm.pchat.request.ScanCodeSignRequest;

/**
 * @Description 机器人登录登出接口
 * <AUTHOR>
 * @Create On 2023/11/6 19:19
 * @Version v1.0.0
 */
@ApiMapping
public interface RobotLoginOutService {

    /**
     * 商家机器人登录接口（3.0）
     * 同步获取好友列表数据有差异时，商家通过调用“异步获取机器人好友列表”这个接口可以获取到更准确的机器人的好友列表，通过3022好友信息列表回调；
     *
     * @param request
     * @return
     */
    @ApiMapping(path = "/scrm/robot/scan-code-sign", name = "商家机器人登录接口（3.0）")
    AsyncInvokeResultDTO scanCodeSign(ScanCodeSignRequest request);

    /**
     * 登出接口3.0（兼容PC）
     *
     * @param merchant_no     商家编号
     * @param robot_serial_no 机器人编号
     * @return
     */
    @ApiMapping(path = "/scrm/robot/sign-out", name = "登出接口3.0（兼容PC）")
    AsyncInvokeResultDTO signOut(String merchant_no, String robot_serial_no);

    /**
     * 商家确认登录授权结果接口
     * 请求商家机器人账号密码托管登录接口收到授权二维码，扫码确认后调用此接口告知开平已扫码。
     *
     * @param request
     * @return
     */
    @ApiMapping(path = "/scrm/robot/confirm-scan-code-authorize", name = "商家确认登录授权结果接口", isSync = true)
    InvokeResultDTO confirmScanCodeAuthorize(ConfirmScanCodeAuthorizeRequest request);

    /**
     * 扫码号登录提交安全验证码
     * 扫码号登录扫码成功后，若收到扫码号登录需安全验证回调，通过此接口将安全验证码提交。提交后将发起登录，对应登录结果通过登录成功回调1003或登录失败回调1004通知商家；
     *
     * @param merchant_no   商家编号
     * @param serial_no     操作编号（取本次扫码登录返回的操作编号）
     * @param validate_code 验证码
     * @return
     */
    @ApiMapping(path = "/scrm/robot/safety-verify-code-sign", name = "扫码号登录提交安全验证码", isSync = true)
    InvokeResultDTO safetyVerifyCodeSign(String serial_no, String merchant_no, String validate_code);


}
