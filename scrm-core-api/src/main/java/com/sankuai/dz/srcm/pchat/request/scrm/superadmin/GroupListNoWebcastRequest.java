package com.sankuai.dz.srcm.pchat.request.scrm.superadmin;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/25 14:17
 * @@Version v1.0.0
 */
@Data
@TypeDoc(description = "群列表")
public class GroupListNoWebcastRequest extends PageRequest {

    /**
     * 群名称
     */
    @FieldDoc(description = "群名称")
    private String groupName;


}