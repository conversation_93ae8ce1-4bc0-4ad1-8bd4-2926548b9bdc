package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> on 2024/07/18 17:01
 */
@Getter
public enum RestrictionReasonTypeEnum {
    // 手动拉入
    ADDED_BLACKLIST_MANUALLY(101, "手动拉入黑名单"),
    ADDED_WHITELIST_MANUALLY(102, "手动拉入白名单"),
    // 系统判定
    JOINING_GROUP_ABNORMAL(201, "进群数量异常"),
    FRIEND_RELATION_ABNORMAL(202, "好友关系异常"),
    // 未知状态
    UNKNOWN(404, "未知");

    private final Integer code;

    private final String desc;

    RestrictionReasonTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RestrictionReasonTypeEnum fromCode(Integer code) {
        for (RestrictionReasonTypeEnum enumValue : RestrictionReasonTypeEnum.values()) {
            if (Objects.equals(enumValue.code, code)) {
                return enumValue;
            }
        }
        return RestrictionReasonTypeEnum.UNKNOWN;
    }

}
