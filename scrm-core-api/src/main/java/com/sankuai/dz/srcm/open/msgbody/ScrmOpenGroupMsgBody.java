package com.sankuai.dz.srcm.open.msgbody;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.srcm.open.dto.MessageDataDTO;
import lombok.Data;

import java.util.List;

@Data
public class ScrmOpenGroupMsgBody {

    @FieldDoc(description = "业务方")
    private String appId;

    @FieldDoc(description = "群id")
    private String groupId;

    @FieldDoc(description = "发送人wxUserId")
    private String sendWxUserId;

    @FieldDoc(description = "发送人类型，1-机器人 2-普通用户")
    private String sendWxUserType;

    @FieldDoc(description = "发送人昵称")
    private String senderNickName;

    @FieldDoc(description = "消息内容列表")
    private List<MessageDataDTO> msgData;

    @FieldDoc(description = "消息发送时间")
    private String dtmsgtime;

}
