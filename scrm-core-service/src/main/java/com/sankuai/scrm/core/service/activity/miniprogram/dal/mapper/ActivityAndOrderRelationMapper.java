package com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.ActivityAndOrderRelation;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.example.ActivityAndOrderRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ActivityAndOrderRelationMapper extends MybatisBaseMapper<ActivityAndOrderRelation, ActivityAndOrderRelationExample, Long> {
    int batchInsert(@Param("list") List<ActivityAndOrderRelation> list);
}