package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecutePlanPackStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationExecutePlanConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackUpdateStrategyDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.AmLionConfigDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.PersonaService;
import com.sankuai.scrm.core.service.tag.dal.entity.ext.ExtExternalContactTag;
import com.sankuai.scrm.core.service.tag.dal.example.ExternalContactTagExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.ext.ExtExternalContactTagMapper;
import com.sankuai.scrm.core.service.user.dal.entity.ext.ExtScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import java.util.concurrent.TimeUnit;
import org.mockito.*;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceRunPreparedProcessOrchestrationTask1Test {

    // 后续补充详细测试用例
    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock
    private ScrmAmCrowdPackBaseInfoDOMapper scrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ScrmProcessOrchestrationExecutePlanConverter scrmProcessOrchestrationExecutePlanConverter;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private ScrmAmProcessOrchestrationExecutePlanDO planDO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ConfigDomainService configDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock
    private ExtExternalContactTagMapper externalContactTagMapper;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    private Long testPackId = 123L;

    private String testAppId = "testApp";

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogMapper;

    @Captor
    private ArgumentCaptor<ScrmAmProcessOrchestrationInfoDO> infoCaptor;

    private static final long NOW = System.currentTimeMillis();

    @BeforeEach
    void setUp() {
        planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(1L);
        planDO.setProcessOrchestrationVersion("1.0");
        planDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setPackStatus((byte) 0);
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setAppId("test-app-id");
    }

    /**
     * Tests when no prepared tasks are available
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskNoTasksAvailable() throws Throwable {
        // arrange
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, never()).queryProcessOrchestrationDetailFuture(anyLong());
    }

    /**
     * Tests successful execution of a prepared task
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskSuccess() throws Throwable {
        // arrange
        // Mock for updateCrowdPackExecuteTwoHoursLater
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList()).thenReturn(Collections.singletonList(planDO));
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }

    /**
     * Tests when crowd pack update is needed
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskWithCrowdPackUpdate() throws Throwable {
        // arrange
        // Setup for updateCrowdPackExecuteTwoHoursLater
        Calendar twoHoursLater = Calendar.getInstance();
        twoHoursLater.add(Calendar.HOUR_OF_DAY, 2);
        ScrmAmProcessOrchestrationExecutePlanDO updatePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        updatePlanDO.setId(2L);
        updatePlanDO.setProcessOrchestrationId(2L);
        updatePlanDO.setProcessOrchestrationVersion("1.0");
        updatePlanDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        updatePlanDO.setPackStatus(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_NOT_EXECUTED.getCode().byteValue());
        updatePlanDO.setTaskStartTime(new Date());
        // First call for updateCrowdPackExecuteTwoHoursLater, second for getPreparedExecuteScrmProcessOrchestrationDTO
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(updatePlanDO)).thenReturn(Collections.singletonList(planDO));
        // Setup for updateCrowdPackExecuteTwoHoursLater
        lenient().when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(infoDO);
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(10L);
        lenient().when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapDO));
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        // TEMP_PACK
        baseInfoDO.setType((byte) 1);
        lenient().when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(baseInfoDO);
        lenient().when(crowdPackUpdateLockService.getProducerValue(anyLong())).thenReturn(null);
        lenient().when(crowdPackUpdateLockService.tryProducerLock(anyLong(), anyInt())).thenReturn(true);
        lenient().doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(any(), any(), any());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }

    @Test
    public void testUpdateCrowdPackByPackIdWhenPackIsDeleted() throws Throwable {
        // arrange
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(crowdPackWriteDomainService).isDeletedTempPack(testPackId);
        verifyNoMoreInteractions(executeManagementService, scrmAmCrowdPackBaseInfoDOMapper);
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_BeginTimeBoundary() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(5L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        // Set beginTime to exactly 11 minutes from now (should not trigger transition)
        info.setBeginTime(new Date(NOW + TimeUnit.MINUTES.toMillis(11)));
        info.setEndTime(new Date(NOW + TimeUnit.DAYS.toMillis(1)));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(info);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_NoTransition() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(9L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue());
        info.setBeginTime(new Date(NOW));
        info.setEndTime(new Date(NOW + TimeUnit.DAYS.toMillis(1)));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(info);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_MessageSendingFutureEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(10L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        // 2 days in future
        info.setEndTime(new Date(NOW + TimeUnit.DAYS.toMillis(2)));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(info);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_NullId() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        // null ID will cause exception in the method
        info.setId(null);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        info.setBeginTime(new Date(NOW));
        info.setEndTime(new Date(NOW + TimeUnit.DAYS.toMillis(1)));
        // act & assert - should throw RuntimeException due to null ID
        assertThrows(RuntimeException.class, () -> executeWriteDomainService.checkRealTimeProcessTaskStatus(info), "Should throw RuntimeException when processOrchestrationId is null");
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_ValidStatusNullBeginTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(11L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        // null beginTime will cause NPE in VALID check
        info.setBeginTime(null);
        info.setEndTime(new Date(NOW + TimeUnit.DAYS.toMillis(1)));
        // act & assert - should throw NullPointerException due to null beginTime
        assertThrows(NullPointerException.class, () -> executeWriteDomainService.checkRealTimeProcessTaskStatus(info), "Should throw NullPointerException when beginTime is null in VALID status");
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_MessageSentToFinished() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(13L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        // Set endTime far enough in the past that finalCheckTime will be before now
        // 2 days ago
        info.setEndTime(new Date(NOW - TimeUnit.DAYS.toMillis(2)));
        // Return empty goal list to use default time settings (4 hours)
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(info);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(1)).updateByPrimaryKeySelective(infoCaptor.capture());
        assertEquals(ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue(), infoCaptor.getValue().getStatus(), "Status should be changed to FINISHED after goal-checking logic");
    }

    @Test
    void testCheckRealTimeProcessTaskStatus_RunningToFinished() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setId(14L);
        info.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        // Set endTime far enough in the past that finalCheckTime will be before now
        // 2 days ago
        info.setEndTime(new Date(NOW - TimeUnit.DAYS.toMillis(2)));
        when(wxInvokeLogMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // Return empty goal list to use default time settings (4 hours)
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(info);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(1)).updateByPrimaryKeySelective(infoCaptor.capture());
        assertEquals(ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue(), infoCaptor.getValue().getStatus(), "Status should be changed to FINISHED after goal-checking logic");
    }
}
