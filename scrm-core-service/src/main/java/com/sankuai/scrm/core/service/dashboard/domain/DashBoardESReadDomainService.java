package com.sankuai.scrm.core.service.dashboard.domain;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import com.sankuai.scrm.core.service.dashboard.constants.DashBoardFieldsToCountConstant;
import com.sankuai.scrm.core.service.dashboard.dal.dto.*;
import com.sankuai.scrm.core.service.dashboard.dal.enums.ActiveDataUserLogActionTypeEnum;
import com.sankuai.scrm.core.service.dashboard.dal.enums.CustomerActionResultTypeEnum;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TransactionCaliberQueryFieldEnum;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TransactionGtvQueryFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant.*;
import static com.sankuai.scrm.core.service.dashboard.dal.enums.CustomerActionResultTypeEnum.INCREMENT;
import static com.sankuai.scrm.core.service.dashboard.dal.enums.CustomerActionResultTypeEnum.LOST;

@Slf4j
@Service
public class DashBoardESReadDomainService {

    private static final String ES_CORP_ID ="corpId";
    private static final String ES_UNION_ID ="unionId";
    private static final String ES_VERSION_TIME ="versionTime";
    private static final String ES_INNER_STATION = "fromInnerStation";

    @Resource
    private PorosRestHighLevelClient porosRestHighLevelClient;

    /**
     * 分主体查询今天的增量用户日志文档，并处理成前端需要展示的数据
     */
    public UserDataDashBoardUserLogChangeDTO getUserDataDashBoardDocToday(String corpId)  {
        try {
            Map<String, Long> resultMap = new HashMap<>();
            for (Map.Entry<String, String> entry : DashBoardFieldsToCountConstant.userDataUserChangeFieldsMap.entrySet()) {
                Long count = countUserLogUserFieldChangeToday(entry.getKey(), corpId);
                resultMap.put(entry.getValue(), count);
            }
            for (Map.Entry<String, String> entry : DashBoardFieldsToCountConstant.userDataEnterSourceFieldsMap.entrySet()) {
                Long count = countUserLogSourceFieldIncrementToday(entry.getKey(), corpId);
                resultMap.put(entry.getValue(), count);
            }
            for (Map.Entry<String, String> entry : DashBoardFieldsToCountConstant.userDataLeaveSourceFieldsMap.entrySet()) {
                Long count = countUserLogSourceFieldDecrementToday(entry.getKey(), corpId);
                resultMap.put(entry.getValue(), count);
            }

            Date startOfToday = DateUtils.parseDate(LocalDate.now().toString(), "yyyy-MM-dd");
            Date startOfTomorrow = DateUtils.parseDate(LocalDate.now().plusDays(1).toString(), "yyyy-MM-dd");

            // 以下人数按用户去重
            Set<String> friendIncrement = selectUserLogDataTodayByMetricAndCorpId("isNewCorpWxFriend", true, corpId);
            Set<String> groupMemberIncrement = selectUserLogDataTodayByMetricAndCorpId("isNewGroupMember", true, corpId);
            Set<String> customerIncrement = new HashSet<>(CollectionUtils.union(friendIncrement, groupMemberIncrement));
            Set<String> customerDecrement = selectUserLogDataTodayByMetricAndCorpId("isLostCustomer", true, corpId);
            Set<String> corpWxDynamicCodeUserIncrement = selectUserLogDataTodayByMetricAndCorpId("fromCorpWxDynamicCode", INCREMENT, corpId);
            Set<String> corpWxDynamicCodeUserDecrement = selectUserLogDataTodayByMetricAndCorpId("fromCorpWxDynamicCode", LOST, corpId);
            Set<String> groupDynamicLiveCodeUserIncrement = selectUserLogDataTodayByMetricAndCorpId("fromGroupDynamicCode", INCREMENT, corpId);
            Set<String> groupDynamicCodeUserDecrement = selectUserLogDataTodayByMetricAndCorpId("fromGroupDynamicCode", LOST, corpId);
            Set<String> innerStationUserIncrement = new HashSet<>(selectInnerStationUserIncrement(corpId, startOfToday, startOfTomorrow));
            Set<String> innerStationUserDecrement = selectUserLogDataTodayByMetricAndCorpId("fromInnerStation", LOST, corpId);
            Set<String> activityUserIncrement = selectUserLogDataTodayByMetricAndCorpId("fromActivity", INCREMENT, corpId);
            Set<String> activityUserDecrement = selectUserLogDataTodayByMetricAndCorpId("fromActivity", LOST, corpId);

            // 自增用户数 = 新增客户数-企微活码来源用户数-群活码来源用户数
            Collection<String> selfUserIncrement = CollectionUtils.subtract(
                    CollectionUtils.subtract(customerIncrement, corpWxDynamicCodeUserIncrement),
                    groupDynamicLiveCodeUserIncrement
            );
            // 自增用户流失用户数 = 流失客户数-企微活码流失用户数-群活码流失用户数
            Collection<String> selfUserDecrement = CollectionUtils.subtract(
                    CollectionUtils.subtract(customerDecrement, corpWxDynamicCodeUserDecrement),
                    groupDynamicCodeUserDecrement
            );
            // 站外&置换来源用户数 = 企微活码来源用户数 + 群活码来源用户数 - 站内来源用户数 - 活动来源用户数
            Collection<String> outStationOrReplacementUserIncrement = CollectionUtils.subtract(
                    CollectionUtils.subtract(
                            CollectionUtils.union(corpWxDynamicCodeUserIncrement, groupDynamicLiveCodeUserIncrement),
                            innerStationUserIncrement
                    ), activityUserIncrement
            );
            // 站外&置换来源流失用户数 = 企微活码流失用户数 + 群活码流失用户数 - 站内来源流失用户数 - 活动来源流失用户数
            Collection<String> outStationOrReplacementUserDecrement = CollectionUtils.subtract(
                    CollectionUtils.subtract(
                            CollectionUtils.union(corpWxDynamicCodeUserDecrement, groupDynamicCodeUserDecrement),
                            innerStationUserDecrement
                    ), activityUserDecrement
            );
            // 净增量
            Long netCustomerIncrement = (long) customerIncrement.size() - customerDecrement.size();

            resultMap.put("customerIncrement", (long) customerIncrement.size());
            resultMap.put("innerStationUserIncrement", (long) innerStationUserIncrement.size());
            resultMap.put("selfUserIncrement", (long) selfUserIncrement.size());
            resultMap.put("selfUserDecrement", (long) selfUserDecrement.size());
            resultMap.put("outStationOrReplacementUserIncrement", (long) outStationOrReplacementUserIncrement.size());
            resultMap.put("outStationOrReplacementUserDecrement", (long) outStationOrReplacementUserDecrement.size());
            resultMap.put("netCustomerIncrement", netCustomerIncrement);

            UserDataDashBoardUserLogChangeDTO userDataDashBoardUserLogChangeDTO = BeanUtil.mapToBean(
                    resultMap,
                    UserDataDashBoardUserLogChangeDTO.class,
                    false,
                    null
            );
            userDataDashBoardUserLogChangeDTO.setCorpId(corpId);
            return userDataDashBoardUserLogChangeDTO;
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.getUserLogDataToday error: ", ex);
        }
        return null;
    }

    public Long countUserLogSourceFieldDecrementToday(String field, String corpId) {
        try {
            CountRequest countRequest = new CountRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 查询条件
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("versionTime", LocalDate.now().toString()))
                    .filter(QueryBuilders.termQuery(field, CustomerActionResultTypeEnum.LOST.toString()))
                    .filter(QueryBuilders.termQuery("corpId", corpId)));

            countRequest.source(searchSourceBuilder);
            CountResponse response = porosRestHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.countUserLogSourceFieldIncrementToday error: ", ex);
        }
        return null;
    }

    public Long countUserLogSourceFieldIncrementToday(String field, String corpId) {
        try {
            CountRequest countRequest = new CountRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 查询条件
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("versionTime", LocalDate.now().toString()))
                    .filter(QueryBuilders.termQuery(field, INCREMENT.toString()))
                    .filter(QueryBuilders.termQuery("corpId", corpId)));

            countRequest.source(searchSourceBuilder);
            CountResponse response = porosRestHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.countUserLogSourceFieldIncrementToday error: ", ex);
        }
        return null;
    }

    public Long countUserLogUserFieldChangeToday(String field, String corpId) {
        try {
            CountRequest countRequest = new CountRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 查询条件
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("versionTime", LocalDate.now().toString()))
                    .filter(QueryBuilders.termQuery(field, true))
                    .filter(QueryBuilders.termQuery("corpId", corpId)));

            countRequest.source(searchSourceBuilder);
            CountResponse response = porosRestHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.countUserLogFieldIncrementToday error: ", ex);
        }
        return null;
    }

    public Long countTransactionFieldData(TransactionCaliberQueryFieldEnum field, String corpId, LocalDate start, LocalDate end) {
        try {
            SearchRequest searchRequest = new SearchRequest(TRADE_DATA_DASHBOARD_ORDER_LOG);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.size(0);
            //指定count(distinct)字段
            String alias = field.getDesc() + "_count";
            CardinalityAggregationBuilder aggregationBuilder = AggregationBuilders.cardinality(alias).field("unionId");
            searchSourceBuilder.aggregation(aggregationBuilder);
            // 查询条件
            searchSourceBuilder.query(
                    QueryBuilders.boolQuery()
                            .filter(QueryBuilders.rangeQuery("versionTime").gte(start.toString()).lte(end.toString()))
                            .filter(QueryBuilders.termQuery(field.getDesc(), true))
                            .filter(QueryBuilders.termQuery("corpId", corpId))
            );
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            ParsedCardinality parsedCardinality = response.getAggregations().get(alias);
            return parsedCardinality.getValue();
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.countTransactionFieldData error: ", ex);
        }
        return null;
    }

    /**
     * 查询总订单量(宽)
     */
    public Long countTotalOrdersWidely(String corpId, LocalDate start, LocalDate end) {
        try {
            CountRequest countRequest = new CountRequest(TRADE_DATA_DASHBOARD_ORDER_LOG);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 查询条件
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .filter(QueryBuilders.rangeQuery("versionTime").gte(start.toString()).lte(end.toString()))
                    .filter(QueryBuilders.termQuery("corpId", corpId))
                    .filter(QueryBuilders.termQuery("isWideCaliber", true))
            );

            countRequest.source(searchSourceBuilder);
            CountResponse response = porosRestHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.countTotalOrders error: ", ex);
        }
        return null;
    }

    /**
     * 查询指定日期的gtv相关指标
     */
    public BigDecimal sumGtvWidely(TransactionGtvQueryFieldEnum field, String corpId, LocalDate date) {
        try {
            BigDecimal result = new BigDecimal(0);
            // 构建查询条件
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.existsQuery(field.getDesc()))
                    .filter(QueryBuilders.termQuery("corpId", corpId))
                    .filter(QueryBuilders.termQuery("versionTime", date.toString()))
                    .filter(QueryBuilders.termQuery("isWideCaliber", true));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(TRADE_DATA_DASHBOARD_ORDER_LOG);
            searchRequest.source(sourceBuilder);
            // 设置 scroll 超时时间
            searchRequest.scroll(TimeValue.timeValueMinutes(3));
            // 执行查询
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 使用 Scroll ID 获取所有数据
            String scrollId = searchResponse.getScrollId();
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                String fieldVal = (String) hit.getSourceAsMap().get(field.getDesc());
                result = result.add(new BigDecimal(fieldVal));
            }
            // 循环获取所有数据
            while (searchResponse.getHits().getHits().length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1));
                // 执行滚动查询
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 处理结果
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    String fieldVal = (String) hit.getSourceAsMap().get(field.getDesc());
                    result = result.add(new BigDecimal(fieldVal));
                }
                // 更新 scrollId
                scrollId = searchResponse.getScrollId();
            }
            // 清除滚动
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            return result;
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.sumGtv error: ", ex);
        }
        return null;
    }

    /**
     * 获取7天内指定主体用户的用户数据
     */
    public List<UserDataDashboardUserLogDoc> getUserLogDocByCorpIdAndUnionIdInSevenDays(String unionId, String corpId, LocalDate nowDate) {
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("unionId", unionId))
                    .filter(QueryBuilders.termQuery("corpId", corpId));

            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("versionTime")
                    .gte(nowDate.minusDays(6).format(DateTimeFormatter.ISO_LOCAL_DATE))
                    .lte(nowDate.minusDays(0).format(DateTimeFormatter.ISO_LOCAL_DATE));

            boolQueryBuilder.filter(rangeQueryBuilder);

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);

            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 处理搜索结果
            List<UserDataDashboardUserLogDoc> result = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                result.add(JSON.parseObject(hit.getSourceAsString(), UserDataDashboardUserLogDoc.class));
            }
            return result;
        } catch (Exception ex) {
            log.error("getUserLogDocByCorpIdAndUnionIdInSevenDays has error", ex);
        }
        return null;
    }

    /**
     * 获取7天内指定主体用户的活跃用户数据
     */
    public List<ActiveDataDashBoardUserLogDoc> getActiveUserLogDocCorpIdAndUnionIdInSevenDays(String unionId, String corpId, LocalDate nowDate) {
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("unionId", unionId))
                    .filter(QueryBuilders.termQuery("corpId", corpId));

            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("versionTime")
                    .gte(nowDate.minusDays(6).format(DateTimeFormatter.ISO_LOCAL_DATE))
                    .lte(nowDate.minusDays(0).format(DateTimeFormatter.ISO_LOCAL_DATE));

            boolQueryBuilder.filter(rangeQueryBuilder);

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);

            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(ACTIVE_DATA_DASHBOARD_USER_LOG_INDEX);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 处理搜索结果
            List<ActiveDataDashBoardUserLogDoc> result = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                result.add(JSON.parseObject(hit.getSourceAsString(), ActiveDataDashBoardUserLogDoc.class));
            }
            return result;
        } catch (Exception ex) {
            log.error("getActiveUserLogDocCorpIdAndUnionIdInSevenDays has error", ex);
        }
        return null;
    }

    public UserDataDashboardUserLogDoc getUserLogDocByVersionTimeAndCorpIdAndUnionId(Date versionTime, String corpId, String unionId) {
        String docId = UserDataDashboardUserLogDoc.concatId(versionTime, corpId, unionId);
        return getUserLogDocById(docId);
    }

    public UserDataDashboardUserLogDoc getUserLogDocById(String id) {
        try {
            GetRequest getRequest = new GetRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX, id);
            GetResponse getResponse = porosRestHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
            String sourceStr = getResponse.getSourceAsString();
            return JSON.parseObject(sourceStr, UserDataDashboardUserLogDoc.class);
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.getUserLogDocById error: ", ex);
        }
        return null;
    }

    /**
     * 活跃用户数据看板：根据指标查询用户unionid，提供当天实时数据的筛选能力
     */
    public Set<String> selectActiveUserLogDataTodayByTypeAndCorpId(ActiveDataUserLogActionTypeEnum typeEnum, String corpId) {
        try {
            Set<String> result = new HashSet<>();
            // 构建查询条件
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("actionType", typeEnum.toString()))
                    .filter(QueryBuilders.termQuery("corpId", corpId))
                    .filter(QueryBuilders.termQuery("versionTime", LocalDate.now().toString()));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);

            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(ACTIVE_DATA_DASHBOARD_USER_LOG_INDEX);
            searchRequest.source(sourceBuilder);

            // 设置 scroll 超时时间
            searchRequest.scroll(TimeValue.timeValueMinutes(5));

            // 执行查询
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 使用 Scroll ID 获取所有数据
            String scrollId = searchResponse.getScrollId();
            List<SearchHit> allHits = new ArrayList<>(Arrays.asList(searchResponse.getHits().getHits()));

            // 循环获取所有数据
            while (searchResponse.getHits().getHits().length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1));
                // 执行滚动查询
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 获取当前批次的结果并添加到 allHits 中
                allHits.addAll(Arrays.asList(searchResponse.getHits().getHits()));
                // 更新 scrollId
                scrollId = searchResponse.getScrollId();
            }

            // 解析结果
            for (SearchHit hit : allHits) {
                String unionId = (String) hit.getSourceAsMap().get("unionId");
                result.add(unionId);
            }

            // 清除滚动
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            return result;
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.selectActiveUserLogDataTodayByTypeAndCorpId error: ", ex);
        }
        return null;
    }

    /**
     * 用户数据看板：根据指标查询用户unionid，提供当天实时数据的筛选能力
     * @param metricName 指标名称
     * @param value 指标值
     */
    public Set<String> selectUserLogDataTodayByMetricAndCorpId(String metricName, Object value, String corpId) {
        try {
            Set<String> result = new HashSet<>();
            // 构建查询条件
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery(metricName, value))
                    .filter(QueryBuilders.termQuery("corpId", corpId))
                    .filter(QueryBuilders.termQuery("versionTime", LocalDate.now().toString()));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);

            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            searchRequest.source(sourceBuilder);

            // 设置 scroll 超时时间
            searchRequest.scroll(TimeValue.timeValueMinutes(5));

            // 执行查询
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 使用 Scroll ID 获取所有数据
            String scrollId = searchResponse.getScrollId();
            List<SearchHit> allHits = new ArrayList<>(Arrays.asList(searchResponse.getHits().getHits()));

            // 循环获取所有数据
            while (searchResponse.getHits().getHits().length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1));
                // 执行滚动查询
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                // 获取当前批次的结果并添加到 allHits 中
                allHits.addAll(Arrays.asList(searchResponse.getHits().getHits()));
                // 更新 scrollId
                scrollId = searchResponse.getScrollId();
            }

            // 解析结果
            for (SearchHit hit : allHits) {
                String unionId = (String) hit.getSourceAsMap().get("unionId");
                result.add(unionId);
            }

            // 清除滚动
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            return result;
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.selectUserLogDataTodayByMetricAndCorpId error: ", ex);
        }
        return null;
    }

    public Map<ActiveDataUserLogActionTypeEnum, Set<String>> selectActiveUserLogDataTodayByCorpId(String corpId) {
        try {
            Map<ActiveDataUserLogActionTypeEnum, Set<String>> result = new HashMap<>();
            for (ActiveDataUserLogActionTypeEnum typeEnum : ActiveDataUserLogActionTypeEnum.values()) {
                Set<String> set = selectActiveUserLogDataTodayByTypeAndCorpId(typeEnum, corpId);
                result.put(typeEnum, set);
            }
            return result;
        } catch (Exception ex) {
            log.error("DashBoardESReadDomainService.selectActiveUserLogDataTodayByCorpId error: ", ex);
        }
        return null;
    }

    //从es中查询站内新增用户数
    public List<String> selectInnerStationUserIncrement(String corpId, Date startOfDay, Date endOfDay) {
        List<String> unionIds = new ArrayList<>();
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L)); // 设置滚动时间

        try {
            SearchRequest searchRequest = new SearchRequest(SCRM_FLOW_MATERIAL_RELATION_LOG_INDEX);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery(ES_CORP_ID, corpId))
                    .filter(QueryBuilders.existsQuery(ES_UNION_ID))
                    .filter(QueryBuilders.existsQuery("state"))
                    .filter(QueryBuilders.rangeQuery("actionTime")
                            .gte(startOfDay.getTime())
                            .lte(endOfDay.getTime()));
            sourceBuilder.query(boolQuery);
            sourceBuilder.fetchSource(new String[]{ES_UNION_ID}, null); // 只获取unionId字段
            sourceBuilder.size(1000); // 设置每次滚动返回的文档数
            searchRequest.source(sourceBuilder);
            searchRequest.scroll(scroll);

            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    String unionId = (String) hit.getSourceAsMap().get(ES_UNION_ID);
                    if (unionId != null) {
                        unionIds.add(unionId);
                    }
                }

                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }

            // 清理滚动上下文
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            return unionIds.stream().distinct().collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("查询站内新增用户unionId列表失败", ex);
            return new ArrayList<>();
        }
    }



    // 辅助方法：将Map的值赋给EsData对象
    private EsUserDataSnapshot mapToEsUserData(Map<String, Object> sourceMap) {
        EsUserDataSnapshot data = new EsUserDataSnapshot();

        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            try {
                Field field = EsUserDataSnapshot.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(data, value);
            } catch (Exception ex) {
                log.error("mapToEsData has error", ex);
            }
        }

        return data;
    }

    // 辅助方法：将Map的值赋给EsActivateData对象
    private EsActiveDataSnapshot mapToEsActivateData(Map<String, Object> sourceMap) {
        EsActiveDataSnapshot data = new EsActiveDataSnapshot();

        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            try {
                Field field = EsActiveDataSnapshot.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(data, value);
            } catch (Exception ex) {
                log.error("mapToEsData has error", ex);
            }
        }

        return data;
    }
    private  List<SearchHit>  getSearchResponseFromEs(String indexName, List<String> corpIds, LocalDate startTime, LocalDate endTime) {
        if (corpIds == null || corpIds.isEmpty()) {
            throw new IllegalArgumentException("corpIds cannot be null or empty");
        }
        try {
            // 创建 SearchRequest 请求，指定索引名称
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 将 LocalDate 转换为字符串格式 yyyy-MM-dd
            String startDate = startTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
            String endDate = endTime.format(DateTimeFormatter.ISO_LOCAL_DATE);

            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("corpId", corpIds))
                    .filter(QueryBuilders.rangeQuery("versionTime").gte(startDate).lte(endDate));

            sourceBuilder.query(boolQuery);
            searchRequest.source(sourceBuilder);

            // 设置 scroll 超时时间
            searchRequest.scroll(TimeValue.timeValueMinutes(5));

            // 执行查询并获取响应
            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 使用 Scroll ID 获取所有数据
            String scrollId = searchResponse.getScrollId();
            List<SearchHit> allHits = new ArrayList<>(Arrays.asList(searchResponse.getHits().getHits()));

            // 循环获取所有数据
            while (searchResponse.getHits().getHits().length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1));  // 每次滚动查询都设置超时时间

                // 执行滚动查询
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);

                // 获取当前批次的结果并添加到 allHits 中
                allHits.addAll(Arrays.asList(searchResponse.getHits().getHits()));

                // 更新 scrollId
                scrollId = searchResponse.getScrollId();
            }

            // 处理 allHits 数据（此时 allHits 中包含了所有数据）

            // 最后清除滚动
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            // 返回完整的搜索响应
            return allHits;
        } catch (Exception ex) {
            // 可以记录日志或重新抛出异常
            log.error("Failed to fetch data from Elasticsearch", ex);
            return null;
        }
    }

    /**
     * 查询指定 corpId UserData在给定时间范围内的数据。
     *
     * @param indexName 索引名称
     * @param corpIds    corpId 值
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return 查询结果
     */
    public List<EsUserDataSnapshot> findUserDataByCorpIdsAndTimeRange(String indexName, List<String> corpIds, LocalDate startTime, LocalDate endTime) {
        List<EsUserDataSnapshot> result = new ArrayList<>();
        List<SearchHit> allHits = getSearchResponseFromEs(indexName, corpIds, startTime, endTime);
        if (allHits == null || allHits.isEmpty()) {
            return result;
        }
        // 解析响应结果
        for (SearchHit hit : allHits) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();
            EsUserDataSnapshot data = mapToEsUserData(sourceMap);
            result.add(data);
        }

        return result;
    }


    /**
     * 查询指定时间范围内活跃用户数据
     * @param indexName
     * @param corpIds
     * @param startTime
     * @param endTime
     * @return
     */
    public List<EsActiveDataSnapshot> findActiveDataByCorpIdsAndTimeRange(String indexName, List<String> corpIds, LocalDate startTime, LocalDate endTime) {
        List<EsActiveDataSnapshot> result = new ArrayList<>();

        List<SearchHit> allHits = getSearchResponseFromEs(indexName, corpIds, startTime, endTime);
        if (allHits == null || allHits.isEmpty()) {
            return result;
        }
        // 解析响应结果
        for (SearchHit hit : allHits) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();
            EsActiveDataSnapshot data = mapToEsActivateData(sourceMap);
            result.add(data);
        }
        return result;
    }

    //从es中查询站内流失用户数
    public List<String> selectInnerStationUserDecrement(String corpId) {
        List<String> unionIds = new ArrayList<>();
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));

        try {
            SearchRequest searchRequest = new SearchRequest(USER_DATA_DASHBOARD_USER_LOG_INDEX);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery(ES_CORP_ID, corpId))
                    .filter(QueryBuilders.termQuery(ES_INNER_STATION, CustomerActionResultTypeEnum.LOST))
                    .filter(QueryBuilders.existsQuery(ES_UNION_ID))
                    .filter(QueryBuilders.rangeQuery(ES_VERSION_TIME)
                            .gte(getYesterday())
                            .lte(getYesterday()));
            sourceBuilder.query(boolQuery);
            sourceBuilder.fetchSource(new String[]{ES_UNION_ID}, null);
            sourceBuilder.size(1000); // 每次滚动返回的文档数
            searchRequest.source(sourceBuilder);
            searchRequest.scroll(scroll);

            SearchResponse searchResponse = porosRestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    String unionId = (String) hit.getSourceAsMap().get(ES_UNION_ID);
                    if (unionId != null) {
                        unionIds.add(unionId);
                    }
                }

                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = porosRestHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }

            // 清理滚动上下文
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            porosRestHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            return unionIds.stream().distinct().collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("查询站内流失用户数失败", ex);
            return unionIds;
        }
    }



    public String getYesterday() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 使用自定义格式化器
        return yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }


    public List<EsTransactionDataSnapshot> getEsTransactionDataSnapshot(String indexName, List<String> corpIds, LocalDate startTime, LocalDate endTime) {
        List<EsTransactionDataSnapshot> result = new ArrayList<>();
        List<SearchHit> allHits = getSearchResponseFromEs(indexName, corpIds, startTime, endTime);
        if (allHits == null || allHits.isEmpty()) {
            return result;
        }
        // 解析响应结果
        for (SearchHit hit : allHits) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();
            EsTransactionDataSnapshot data = mapToEsTransactionDataSnapshot(sourceMap);
            result.add(data);
        }

        return result;
    }

    // 辅助方法：将Map的值赋给EsData对象
    private EsTransactionDataSnapshot mapToEsTransactionDataSnapshot(Map<String, Object> sourceMap) {
        EsTransactionDataSnapshot data = new EsTransactionDataSnapshot();

        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            try {
                Field field = EsTransactionDataSnapshot.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(data, value);
            } catch (Exception ex) {
                log.error("mapToEsData has error", ex);
            }
        }

        return data;
    }

    private EsNarrowlyTransactionDataSnapshot mapToEsNarrowTransactionDataSnapshot(Map<String, Object> sourceMap) {
        EsNarrowlyTransactionDataSnapshot data=new EsNarrowlyTransactionDataSnapshot();

        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            try {
                Field field = EsNarrowlyTransactionDataSnapshot.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(data, value);
            } catch (Exception ex) {
                log.error("mapToEsData has error", ex);
            }
        }
        return data;
    }

    public List<EsNarrowlyTransactionDataSnapshot> getEsTransactionDataSnapshotNarrowly(String indexName, List<String> corpIds, LocalDate startTime, LocalDate endTime) {
            List<EsNarrowlyTransactionDataSnapshot> result = new ArrayList<>();
             List<SearchHit> allHits = getSearchResponseFromEs(indexName, corpIds, startTime, endTime);
             if (allHits == null || allHits.isEmpty()) {
                return result;
             }
             for (SearchHit hit : allHits) {
                 Map<String, Object> sourceMap = hit.getSourceAsMap();
                 EsNarrowlyTransactionDataSnapshot data = mapToEsNarrowTransactionDataSnapshot(sourceMap);
                 result.add(data);
             }
             return result;
    }


}