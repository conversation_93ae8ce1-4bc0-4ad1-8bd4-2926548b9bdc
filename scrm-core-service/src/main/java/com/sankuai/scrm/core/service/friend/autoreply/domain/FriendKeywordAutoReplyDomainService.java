package com.sankuai.scrm.core.service.friend.autoreply.domain;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.friend.autoreply.dto.FriendAutoReplyContentDTO;
import com.sankuai.dz.srcm.friend.autoreply.request.FriendAutoReplyPageRequest;
import com.sankuai.dz.srcm.message.task.enums.MessageContentTypeEnum;
import com.sankuai.scrm.core.service.friend.autoreply.dal.entity.FriendAutoReplyKeyword;
import com.sankuai.scrm.core.service.friend.autoreply.dal.entity.FriendKeywordAutoReplyLog;
import com.sankuai.scrm.core.service.friend.autoreply.dal.example.FriendAutoReplyKeywordExample;
import com.sankuai.scrm.core.service.friend.autoreply.dal.mapper.FriendAutoReplyKeywordMapper;
import com.sankuai.scrm.core.service.friend.autoreply.dal.mapper.FriendKeywordAutoReplyLogMapper;
import com.sankuai.scrm.core.service.friend.autoreply.mq.entity.OpenFriendEvent;
import com.sankuai.scrm.core.service.friend.autoreply.mq.entity.PrivateChatEvent;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsFriendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.FriendSendMsgResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.FriendAssistantDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.MsgContentDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.SendFriendMsgTRequest;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.*;
import com.sankuai.service.fe.corp.ds.enums.msg.ChannelStrategyTEnum;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class FriendKeywordAutoReplyDomainService {

    @Resource
    private DsFriendAcl dsFriendAcl;
    @Resource
    private FriendAutoReplyKeywordMapper friendAutoReplyKeywordMapper;
    @Resource
    private FriendKeywordAutoReplyLogMapper friendKeywordAutoReplyLogMapper;
    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;

    private static final String APP_KEY = "baby-customer-service";

    private static final String PRIVATE_CHAT_LISTEN_CORPIDS = "baby-customer-service.operatorhelper.private.chat.listen.corpIds";

    public FriendAutoReplyKeyword queryFriendKeywordAutoReplyInfo(Long id) {
        FriendAutoReplyKeywordExample example = new FriendAutoReplyKeywordExample();
        FriendAutoReplyKeywordExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(id);
        List<FriendAutoReplyKeyword> keywordAutoReplyList = friendAutoReplyKeywordMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(keywordAutoReplyList)) {
            return null;
        }
        return keywordAutoReplyList.get(0);
    }

    public String saveFriendAutoReplyKeyword(FriendAutoReplyKeyword friendAutoReplyKeyword) {
        //是否存在冲突
        FriendAutoReplyKeywordExample exampleA = new FriendAutoReplyKeywordExample();
        FriendAutoReplyKeywordExample.Criteria criteriaA = exampleA.createCriteria();
        criteriaA.andAppIdEqualTo(friendAutoReplyKeyword.getAppId());
        criteriaA.andKeywordEqualTo(friendAutoReplyKeyword.getKeyword());
        criteriaA.andValidTimeEndGreaterThan(new Date());
        criteriaA.andOfflineEqualTo(false);
        List<FriendAutoReplyKeyword> autoRepliess = friendAutoReplyKeywordMapper.selectByExampleWithBLOBs(exampleA);
        if (CollectionUtils.isNotEmpty(autoRepliess)) {
            for (FriendAutoReplyKeyword originKeyword : autoRepliess) {
                if (friendAutoReplyKeyword.getValidTimeEnd().after(originKeyword.getValidTimeStart()) && friendAutoReplyKeyword.getValidTimeStart().before(originKeyword.getValidTimeEnd())) {
                    return "所选时间存在与生效中/待生效的重复关键词，请修改";
                }
            }
        }
        
        boolean saveResult = friendAutoReplyKeywordMapper.insertSelective(friendAutoReplyKeyword) > 0;
        if (!saveResult) {
            return "保存失败";
        }
        return null;
    }

    public String updateFriendKeywordAutoReply(FriendAutoReplyKeyword friendAutoReplyKeyword) {
        //是否存在冲突
        FriendAutoReplyKeywordExample exampleA = new FriendAutoReplyKeywordExample();
        FriendAutoReplyKeywordExample.Criteria criteriaA = exampleA.createCriteria();
        criteriaA.andAppIdEqualTo(friendAutoReplyKeyword.getAppId());
        criteriaA.andKeywordEqualTo(friendAutoReplyKeyword.getKeyword());
        criteriaA.andValidTimeEndGreaterThan(new Date());
        criteriaA.andOfflineEqualTo(false);
        List<FriendAutoReplyKeyword> autoRepliess = friendAutoReplyKeywordMapper.selectByExampleWithBLOBs(exampleA);
        if (CollectionUtils.isNotEmpty(autoRepliess)) {
            for (FriendAutoReplyKeyword originKeyword : autoRepliess) {
                if (friendAutoReplyKeyword.getValidTimeEnd().after(originKeyword.getValidTimeStart()) && friendAutoReplyKeyword.getValidTimeStart().before(originKeyword.getValidTimeEnd()) && !originKeyword.getId().equals(friendAutoReplyKeyword.getId())) {
                    return "所选时间存在与生效中/待生效的重复关键词，请修改";
                }
            }
        }

        boolean update = friendAutoReplyKeywordMapper.updateByPrimaryKeySelective(friendAutoReplyKeyword) > 0;
        if (!update) {
            return "保存失败";
        }
        return null;
    }

    public List<FriendAutoReplyKeyword> pageQueryFriendAutoReplyKeyword(FriendAutoReplyPageRequest request) {
        FriendAutoReplyKeywordExample example = new FriendAutoReplyKeywordExample();
        example.page(request.getPageNum() - 1, request.getPageSize());
        example.setOrderByClause("update_time desc");
        FriendAutoReplyKeywordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(request.getAppId());
        if (StringUtils.isNotBlank(request.getKeyword())) {
            criteria.andKeywordLike("%" + request.getKeyword() + "%");
        }
        return friendAutoReplyKeywordMapper.selectByExampleWithBLOBs(example);
    }
    public long queryAutoReplyKeywordPageCount(FriendAutoReplyPageRequest request) {
        FriendAutoReplyKeywordExample example = new FriendAutoReplyKeywordExample();
        FriendAutoReplyKeywordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(request.getAppId());
        if (StringUtils.isNotBlank(request.getKeyword())) {
            criteria.andKeywordLike("%" + request.getKeyword() + "%");
        }
        return friendAutoReplyKeywordMapper.countByExample(example);
    }

    public String offlineFriendAutoReplyKeyword(String appId, Long id) {
        FriendAutoReplyKeywordExample example = new FriendAutoReplyKeywordExample();
        FriendAutoReplyKeywordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andIdEqualTo(id);
        List<FriendAutoReplyKeyword> replyKeywords = friendAutoReplyKeywordMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(replyKeywords)) {
            return "关键词不存在";
        }
        FriendAutoReplyKeyword replyKeyword = replyKeywords.get(0);
        if (replyKeyword.getOffline()) {
            return "关键词已下线";
        }
        replyKeyword.setOffline(true);
        return friendAutoReplyKeywordMapper.updateByPrimaryKeySelective(replyKeyword) > 0 ? null : "下线失败";
    }

    public FriendAutoReplyKeyword getMatchedKeyWord(PrivateChatEvent privateChatEvent, CorpAppConfig corpAppConfig) {
        // 仅处理文本消息
        if (privateChatEvent.getMessage().getContentType() != ContentTypeTEnum.TEXT.getCode()) {
            log.info("FriendKeywordAutoReplyDomainService.getMatchedKeyWord.contenttype is not match, contenttype={}", privateChatEvent.getMessage().getContentType());
            return null;
        }

        // TODO: 重写ExtMapper
        List<FriendAutoReplyKeyword> matchedKeyWord = friendAutoReplyKeywordMapper.getMatchedKeyWord(corpAppConfig.getAppId(), privateChatEvent.getMessage().getContent());
        if (CollectionUtils.isEmpty(matchedKeyWord)) {
            return null;
        }
        log.info("FriendKeywordAutoReplyDomainService.getMatchedKeyWord.matched keyword, wxuserId={}, keyword={}", privateChatEvent.getFriend().getWwUserId(), matchedKeyWord.get(0).getKeyword());
        return matchedKeyWord.get(0);
    }

    public void replyAndRecordLog(OpenFriendEvent friend, String businessCode, FriendAutoReplyKeyword keywordInfo,
            String originMsg, Long assistantId) {
        //回复对象
        List<FriendAssistantDTO> friendAssistantDTOList = new ArrayList<>();
        FriendAssistantDTO friendAssistantDTO = new FriendAssistantDTO();
        friendAssistantDTO.setWxUserId(friend.getWwUserId());
        friendAssistantDTO.setAssistantId(assistantId);
        friendAssistantDTOList.add(friendAssistantDTO);

        //回复内容
        List<MsgContentDTO> msgContentDTOList = buildMsgContentDTOList(keywordInfo);

        SendFriendMsgTRequest request = buildSendFriendMsgTRequest(businessCode, msgContentDTOList, friendAssistantDTOList);

        log.info("FriendKeywordAutoReplyDomainService.replyAndRcordLog.sendFriendMsg.request is={}", request);
        FriendSendMsgResponse response = dsFriendAcl.sendFriendMsg(request);
        if (response == null) {
            log.error("replyAndRcordLog.sendFriendMsg has error, send wxUserId={}", friend.getWwUserId());
        }
        recordLog(JSONObject.toJSONString(request), JSONObject.toJSONString(response), keywordInfo, originMsg, friend.getWwUserId());
    }

    private void recordLog(String sendRequest, String sendResponse, FriendAutoReplyKeyword keywordInfo, String originMsg, String wxUserId) {
        FriendKeywordAutoReplyLog replyLog = new FriendKeywordAutoReplyLog();
        replyLog.setKeywordId(keywordInfo.getId());
        replyLog.setKeyword(keywordInfo.getKeyword());
        replyLog.setAppId(keywordInfo.getAppId());
        replyLog.setFriendWxUserId(wxUserId);
        replyLog.setOriginMsg(originMsg);
        replyLog.setSendRequest(sendRequest);
        replyLog.setSendResponse(sendResponse);
        friendKeywordAutoReplyLogMapper.insertSelective(replyLog);
        log.info("FriendKeywordAutoReplyDomainService.replyAndRcordLog.recordLog over, wxUserId={}, keyword={}", wxUserId, keywordInfo.getKeyword());
    }

    private SendFriendMsgTRequest buildSendFriendMsgTRequest(String businessCode, List<MsgContentDTO> msgContentDTOList, List<FriendAssistantDTO> friendAssistantDTOList) {
        if (CollectionUtils.isEmpty(friendAssistantDTOList) || CollectionUtils.isEmpty(msgContentDTOList)) {
            return null;
        }
        SendFriendMsgTRequest request = new SendFriendMsgTRequest();
        request.setBusinessCode(businessCode);
        request.setChannelStrategyTEnum(ChannelStrategyTEnum.UNSPECIFIED);
        request.setFriendAssistantDTOList(friendAssistantDTOList);
        request.setMsgContentDTOList(msgContentDTOList);

        return request;
    }

    public List<MsgContentDTO> buildMsgContentDTOList(FriendAutoReplyKeyword keywordInfo) {
        List<FriendAutoReplyContentDTO> contentList = JSONObject.parseArray(keywordInfo.getReplyObject(), FriendAutoReplyContentDTO.class);
        List<MsgContentDTO> msgContentDTOList= Lists.newArrayList();
        for (FriendAutoReplyContentDTO localDTO : contentList) {
            MsgContentDTO msgContentDTO = new MsgContentDTO();
            Integer type = localDTO.getType();
            MessageContentTypeEnum contentTypeEnum = MessageContentTypeEnum.fromCode(type);
            switch (contentTypeEnum) {
                case TEXT:
                    msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                    TextDTO textDTO = new TextDTO();
                    textDTO.setContent(localDTO.getContent());
                    msgContentDTO.setTextDTO(textDTO);
                    break;
                case PICTURE:
                    msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.IMAGE);
                    ImageDTO imageDTO = new ImageDTO();
                    imageDTO.setUrl(localDTO.getPicUrl());
                    msgContentDTO.setImageDTO(imageDTO);
                    break;
                case MIN_PROGRAM:
                    msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
                    MiniProgramDTO miniProgramDTO = new MiniProgramDTO();

                    miniProgramDTO.setDescription(localDTO.getTitleForMini());
                    miniProgramDTO.setPagePath(localDTO.getPagePathForMini());
                    miniProgramDTO.setThumbnail(localDTO.getThumbUrlForMini());
                    miniProgramDTO.setAppId(localDTO.getAppIdForMini());
                    miniProgramDTO.setOriginAppId(localDTO.getOriginAppIdForMini());

                    msgContentDTO.setMiniProgramDTO(miniProgramDTO);
                    break;
                case WEBSITE:
                    msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.LINK);
                    LinkDTO linkDTO = new LinkDTO();
                    linkDTO.setDescription(localDTO.getLinkDesc());
                    linkDTO.setThumbUrl(localDTO.getLinkImgUrl());
                    linkDTO.setTitle(localDTO.getLinkDesc());
                    linkDTO.setUrl(localDTO.getLinkUrl());
                    msgContentDTO.setLinkDTO(linkDTO);
                    break;
                case VIDEO:
                    msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.VIDEO);
                    VideoDTO videoDTO = new VideoDTO();
                    videoDTO.setUrl(localDTO.getVideoLink());
                    msgContentDTO.setVideoDTO(videoDTO);
                    break;
                default:
                    msgContentDTO = null;
                    break;
            }
            if (msgContentDTO != null) {
                msgContentDTOList.add(msgContentDTO);
            }
        }
        return msgContentDTOList;
    }

}
