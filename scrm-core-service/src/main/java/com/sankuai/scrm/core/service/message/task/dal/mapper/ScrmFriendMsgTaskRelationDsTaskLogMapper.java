package com.sankuai.scrm.core.service.message.task.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendMsgTaskRelationDsTaskLog;
import com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendMsgTaskRelationDsTaskLogExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ScrmFriendMsgTaskRelationDsTaskLogMapper extends MybatisBaseMapper<ScrmFriendMsgTaskRelationDsTaskLog, ScrmFriendMsgTaskRelationDsTaskLogExample, Long> {
    int batchInsert(@Param("list") List<ScrmFriendMsgTaskRelationDsTaskLog> list);
}