package com.sankuai.dz.srcm.user.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.user.dto.DownloadUserInfoRequest;
import com.sankuai.dz.srcm.user.dto.SCRMUserInfoPageRequest;
import com.sankuai.dz.srcm.user.dto.SCRMUserInfoPageResponse;
import com.sankuai.dz.srcm.user.dto.ScrmMemberBaseInfoDTO;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ScrmUserInfoService {
    SCRMUserInfoPageResponse<List<List<String>>> getSCRMUserPageInfoListByGroupId(SCRMUserInfoPageRequest scrmUserInfoPageRequest);
    String downloadScrmUserInfo(DownloadUserInfoRequest downloadUserInfoRequest);
}
