package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupFollower;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupFollowerExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupFollowerMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalGroupFollowDomainServiceQueryGroupFollowerTest {

    @InjectMocks
    private ScrmPersonalGroupFollowDomainService scrmPersonalGroupFollowDomainService;

    @Mock
    private ScrmPersonalWxGroupFollowerMapper personalWxGroupFollowerMapper;

    @Before
    public void setUp() {
        when(personalWxGroupFollowerMapper.selectByExample(any(ScrmPersonalWxGroupFollowerExample.class))).thenReturn(Collections.emptyList());
    }

    /**
     * 测试 chatroomSerialNo 或 robotSerialNo 为空的情况
     */
    @Test
    public void testQueryGroupFollowerWithEmptyInput() {
        List<ScrmPersonalWxGroupFollower> result = scrmPersonalGroupFollowDomainService.queryGroupFollower(null, "robotSerialNo");
        assertEquals(0, result.size());
    }

    /**
     * 测试 chatroomSerialNo 和 robotSerialNo 都不为空，但数据库中没有匹配的数据的情况
     */
    @Test
    public void testQueryGroupFollowerWithNoMatchedData() {
        List<ScrmPersonalWxGroupFollower> result = scrmPersonalGroupFollowDomainService.queryGroupFollower("chatroomSerialNo", "robotSerialNo");
        assertEquals(0, result.size());
    }

    /**
     * 测试 chatroomSerialNo 和 robotSerialNo 都不为空，数据库中有匹配的数据的情况
     */
    @Test
    public void testQueryGroupFollowerWithMatchedData() {
        when(personalWxGroupFollowerMapper.selectByExample(any(ScrmPersonalWxGroupFollowerExample.class))).thenReturn(Collections.singletonList(new ScrmPersonalWxGroupFollower()));
        List<ScrmPersonalWxGroupFollower> result = scrmPersonalGroupFollowDomainService.queryGroupFollower("chatroomSerialNo", "robotSerialNo");
        assertEquals(1, result.size());
    }
}