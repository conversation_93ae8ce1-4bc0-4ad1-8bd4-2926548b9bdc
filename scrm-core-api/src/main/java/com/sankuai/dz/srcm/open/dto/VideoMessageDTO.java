package com.sankuai.dz.srcm.open.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "视频消息")
public class VideoMessageDTO implements Serializable {
    /**
     * 视频URL
     */
    @FieldDoc(description = "视频URL")
    private String url;

    /**
     * 视频时长（秒）
     */
    @FieldDoc(description = "视频时长（秒）")
    private Integer duration;

    /**
     * 视频封面URL
     */
    @FieldDoc(description = "视频封面URL")
    private String coverUrl;
}
