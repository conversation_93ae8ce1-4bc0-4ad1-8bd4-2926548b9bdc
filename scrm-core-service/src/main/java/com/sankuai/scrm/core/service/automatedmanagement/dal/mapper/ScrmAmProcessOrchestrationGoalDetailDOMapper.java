package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationGoalDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationGoalDetailDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationGoalDetailDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationGoalDetailDO, ScrmAmProcessOrchestrationGoalDetailDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationGoalDetailDO> list);
}