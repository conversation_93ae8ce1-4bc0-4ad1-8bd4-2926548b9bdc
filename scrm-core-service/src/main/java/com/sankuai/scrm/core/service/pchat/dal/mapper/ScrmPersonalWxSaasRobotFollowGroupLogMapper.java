package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxSaasRobotFollowGroupLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxSaasRobotFollowGroupLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxSaasRobotFollowGroupLogMapper extends MybatisBaseMapper<ScrmPersonalWxSaasRobotFollowGroupLog, ScrmPersonalWxSaasRobotFollowGroupLogExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxSaasRobotFollowGroupLog> list);
}