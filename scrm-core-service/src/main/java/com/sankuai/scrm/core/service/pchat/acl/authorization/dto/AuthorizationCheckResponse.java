package com.sankuai.scrm.core.service.pchat.acl.authorization.dto;

import lombok.Data;

/**
 * 权限校验服务
 * <AUTHOR>
 * @date 2024/8/21
 */
@Data
public class AuthorizationCheckResponse<T> {

    /**
     * true - 校验通过
     * false - 校验不通过
     */
    private Boolean pass;

    /**
     * 透传数据
     */
    private T data;

    /**
     * 错误信息
     */
    private String msg;

    public boolean isPass(){
        if (null != pass && Boolean.TRUE.equals(pass)){
            return Boolean.TRUE;
        }else{
            return Boolean.FALSE;
        }
    }

    public static AuthorizationCheckResponse success(){
        AuthorizationCheckResponse res = new AuthorizationCheckResponse();
        res.setPass(Boolean.TRUE);
        return res;
    }

    public static AuthorizationCheckResponse fail(String msg){
        AuthorizationCheckResponse res = new AuthorizationCheckResponse();
        res.setPass(Boolean.FALSE);
        res.setMsg(msg);
        return res;
    }

}
