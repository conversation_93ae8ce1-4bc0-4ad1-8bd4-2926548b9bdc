package com.sankuai.scrm.core.service.pchat.group.ent;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeStatisticMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeStatistic;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeStatisticExample;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupMemberDetail;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveEntGroupChangeServiceUpdateGroupMemberBaseInfoTest {

    @InjectMocks
    private PrivateLiveEntGroupChangeService privateLiveEntGroupChangeService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private GroupDynamicCodeStatisticMapper statisticMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private GroupDynamicCodeStatistic invokeMatchCodeStatistic(Long channelId, WxGroupMemberDetail memberDetail) throws Exception {
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("matchCodeStatistic", Long.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        return (GroupDynamicCodeStatistic) method.invoke(privateLiveEntGroupChangeService, channelId, memberDetail);
    }

    /**
     * 测试场景：dbMemberEntity 为 null
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_dbMemberEntityIsNull() throws Throwable {
        // arrange
        WxGroupMemberDetail wxMemberDetail = new WxGroupMemberDetail();
        wxMemberDetail.setName("John Doe");
        wxMemberDetail.setGroupNickName("JD");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, null, wxMemberDetail);
        // assert
        verify(groupManageDomainService, never()).updateGroupMemberById(any());
    }

    /**
     * 测试场景：wxMemberDetail 为 null
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_wxMemberDetailIsNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        dbMemberEntity.setWxNickname("John Doe");
        dbMemberEntity.setMemberNickName("JD");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, dbMemberEntity, null);
        // assert
        verify(groupManageDomainService, never()).updateGroupMemberById(any());
    }

    /**
     * 测试场景：wxNickname 和 name 相等，memberNickName 和 groupNickName 相等
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_noUpdateNeeded() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        dbMemberEntity.setWxNickname("John Doe");
        dbMemberEntity.setMemberNickName("JD");
        WxGroupMemberDetail wxMemberDetail = new WxGroupMemberDetail();
        wxMemberDetail.setName("John Doe");
        wxMemberDetail.setGroupNickName("JD");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, dbMemberEntity, wxMemberDetail);
        // assert
        verify(groupManageDomainService, never()).updateGroupMemberById(any());
    }

    /**
     * 测试场景：wxNickname 和 name 不相等，memberNickName 和 groupNickName 相等
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_updateWxNickname() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        dbMemberEntity.setWxNickname("John Doe");
        dbMemberEntity.setMemberNickName("JD");
        WxGroupMemberDetail wxMemberDetail = new WxGroupMemberDetail();
        wxMemberDetail.setName("Jane Doe");
        wxMemberDetail.setGroupNickName("JD");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, dbMemberEntity, wxMemberDetail);
        // assert
        verify(groupManageDomainService, times(1)).updateGroupMemberById(dbMemberEntity);
        assertEquals("Jane Doe", dbMemberEntity.getWxNickname());
    }

    /**
     * 测试场景：wxNickname 和 name 相等，memberNickName 和 groupNickName 不相等
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_updateMemberNickName() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        dbMemberEntity.setWxNickname("John Doe");
        dbMemberEntity.setMemberNickName("JD");
        WxGroupMemberDetail wxMemberDetail = new WxGroupMemberDetail();
        wxMemberDetail.setName("John Doe");
        wxMemberDetail.setGroupNickName("Jane");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, dbMemberEntity, wxMemberDetail);
        // assert
        verify(groupManageDomainService, times(1)).updateGroupMemberById(dbMemberEntity);
        assertEquals("Jane", dbMemberEntity.getMemberNickName());
    }

    /**
     * 测试场景：wxNickname 和 name 不相等，memberNickName 和 groupNickName 不相等
     */
    @Test
    public void testUpdateGroupMemberBaseInfo_updateBoth() throws Throwable {
        // arrange
        ScrmPersonalWxGroupMemberInfoEntity dbMemberEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        dbMemberEntity.setWxNickname("John Doe");
        dbMemberEntity.setMemberNickName("JD");
        WxGroupMemberDetail wxMemberDetail = new WxGroupMemberDetail();
        wxMemberDetail.setName("Jane Doe");
        wxMemberDetail.setGroupNickName("Jane");
        // act
        Method method = PrivateLiveEntGroupChangeService.class.getDeclaredMethod("updateGroupMemberBaseInfo", ScrmPersonalWxGroupMemberInfoEntity.class, WxGroupMemberDetail.class);
        method.setAccessible(true);
        method.invoke(privateLiveEntGroupChangeService, dbMemberEntity, wxMemberDetail);
        // assert
        verify(groupManageDomainService, times(1)).updateGroupMemberById(dbMemberEntity);
        assertEquals("Jane Doe", dbMemberEntity.getWxNickname());
        assertEquals("Jane", dbMemberEntity.getMemberNickName());
    }

    /**
     * 测试场景：channelId 为 null
     */
    @Test
    public void testMatchCodeStatistic_ChannelIdIsNull() throws Throwable {
        // arrange
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        memberDetail.setUnionId("unionId");
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(null, memberDetail);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：memberDetail 为 null
     */
    @Test
    public void testMatchCodeStatistic_MemberDetailIsNull() throws Throwable {
        // arrange
        Long channelId = 123L;
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：memberDetail.getUnionId() 为 null
     */
    @Test
    public void testMatchCodeStatistic_UnionIdIsNull() throws Throwable {
        // arrange
        Long channelId = 123L;
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, memberDetail);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：memberDetail.getUnionId() 为空字符串
     */
    @Test
    public void testMatchCodeStatistic_UnionIdIsEmpty() throws Throwable {
        // arrange
        Long channelId = 123L;
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        memberDetail.setUnionId("");
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, memberDetail);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：查询结果为空列表
     */
    @Test
    public void testMatchCodeStatistic_NoMatchingRecords() throws Throwable {
        // arrange
        Long channelId = 123L;
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        memberDetail.setUnionId("unionId");
        memberDetail.setJoinTime(new Date());
        when(statisticMapper.selectByExample(any(GroupDynamicCodeStatisticExample.class))).thenReturn(Collections.emptyList());
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, memberDetail);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：查询结果非空，但所有记录的 addTime 都大于 memberDetail.getJoinTime()
     */
    @Test
    public void testMatchCodeStatistic_AllRecordsAddTimeGreaterThanJoinTime() throws Throwable {
        // arrange
        Long channelId = 123L;
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        memberDetail.setUnionId("unionId");
        memberDetail.setJoinTime(new Date(1000));
        GroupDynamicCodeStatistic statistic1 = new GroupDynamicCodeStatistic();
        statistic1.setAddTime(new Date(2000));
        GroupDynamicCodeStatistic statistic2 = new GroupDynamicCodeStatistic();
        statistic2.setAddTime(new Date(3000));
        List<GroupDynamicCodeStatistic> statisticList = Arrays.asList(statistic1, statistic2);
        when(statisticMapper.selectByExample(any(GroupDynamicCodeStatisticExample.class))).thenReturn(statisticList);
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, memberDetail);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：查询结果非空，且至少有一个记录的 addTime 小于等于 memberDetail.getJoinTime()
     */
    @Test
    public void testMatchCodeStatistic_MatchingRecordFound() throws Throwable {
        // arrange
        Long channelId = 123L;
        WxGroupMemberDetail memberDetail = new WxGroupMemberDetail();
        memberDetail.setUnionId("unionId");
        memberDetail.setJoinTime(new Date(3000));
        GroupDynamicCodeStatistic statistic1 = new GroupDynamicCodeStatistic();
        statistic1.setAddTime(new Date(2000));
        GroupDynamicCodeStatistic statistic2 = new GroupDynamicCodeStatistic();
        statistic2.setAddTime(new Date(4000));
        List<GroupDynamicCodeStatistic> statisticList = Arrays.asList(statistic1, statistic2);
        when(statisticMapper.selectByExample(any(GroupDynamicCodeStatisticExample.class))).thenReturn(statisticList);
        // act
        GroupDynamicCodeStatistic result = invokeMatchCodeStatistic(channelId, memberDetail);
        // assert
        assertEquals(statistic1, result);
    }
}
