package com.sankuai.scrm.core.service.pchat.dto.robotFunction;

import lombok.Data;

/**
 * 3002
 * @Description 【直接回调】主动添加好友结果回调接口（还未成为好友）
 * <AUTHOR>
 * @Create On 2023/11/7 10:48
 * @Version v1.0.0
 */
@Data
public class AddFriendDTO {

    /**
     * 主动添加好友的微信id（因WX机制调整，暂时这个值为空）
     */
    private String vcAddUserWxId;

    /**
     * 主动添加好友的微信编号（因WX机制调整，暂时这个值为空）
     */
    private String vcAddUserSerialNo;

    /**
     * 请求添加好友时上传的微信号/手机号/二维码地址
     */
    private String vcAccount;

    /**
     * 添加好友打招呼的内容
     */
    private String vcHelloWord;

    /**
     * 主动添加好友的昵称
     */
    private String vcNickName;

    /**
     * 主动添加好友的头像
     */
    private String vcHeadImgUrl;

    /**
     * 10 非群内添加方式 11 通过群内添加好友方式
     */
    private Integer nType;

}
