package com.sankuai.dz.srcm.activity.fission.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/4/23 15:49
 * @Version v1.0.0
 */
@Data
public class CashDistributeRequest implements Serializable {
    @FieldDoc(name = "外键流水号")
    private String foreignKey;
    @FieldDoc(name = "支付状态 1受理中 2 支付成功 3 支付失败")
    private String status;
    @FieldDoc(name = "支付状态结果信息")
    private String message;
    @FieldDoc(name = "支付状态结果代码")
    private String code;
    /**
     * 字段: id
     */
    private Long logId;

}
