package com.sankuai.scrm.core.service.aigc.friday.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> on 2024/08/26 14:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayMarketingTextGenerationTaskConfig implements Serializable {

    // 文案生成任务的优先级
    private Integer taskPriority;

    // 每个行业的Prompt配置
    private List<FridayMarketingTextGenerationAppIdConfig> appIdConfigList;

}