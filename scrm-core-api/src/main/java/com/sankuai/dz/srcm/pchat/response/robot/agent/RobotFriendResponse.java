package com.sankuai.dz.srcm.pchat.response.robot.agent;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "机器人好友信息")
public class RobotFriendResponse implements Serializable {
    /**
     * 微信昵称
     */
    @FieldDoc(description = "微信昵称")
    private String wxNickname;

    /**
     * 微信备注名称
     */
    @FieldDoc(description = "微信备注名称")
    private String wxRemarkName;

    /**
     * 微信id
     */
    @FieldDoc(description = "微信id")
    private String wxId;

    /**
     * 头像
     */
    @FieldDoc(description = "头像 url")
    private String avatar;

    /**
     * 字段: sex
     * 说明: 性别 0未知 1男 2女
     */
    @FieldDoc(description = "性别 0未知 1男 2女")
    private String sex;
    /**
     * 字段: wx_alias
     * 说明: 微信号
     */
    @FieldDoc(description = "微信号")
    private String wxAlias;

    /**
     * 标签id 多个逗号分隔
     */
    @FieldDoc(description = "标签id 多个逗号分隔")
    private String tagIds;

}
