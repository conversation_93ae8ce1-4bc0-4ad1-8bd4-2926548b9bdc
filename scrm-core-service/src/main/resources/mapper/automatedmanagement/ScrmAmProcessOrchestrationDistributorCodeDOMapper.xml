<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="process_orchestration_id" jdbcType="BIGINT" property="processOrchestrationId" />
    <result column="process_orchestration_version" jdbcType="VARCHAR" property="processOrchestrationVersion" />
    <result column="process_orchestration_node_id" jdbcType="BIGINT" property="processOrchestrationNodeId" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
    distributor_id, distributor_code, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_distributor_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_distributor_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_process_orchestration_distributor_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample">
    delete from scrm_a_m_process_orchestration_distributor_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_distributor_code (app_id, process_orchestration_id, process_orchestration_version, 
      process_orchestration_node_id, distributor_id, 
      distributor_code, add_time, update_time
      )
    values (#{appId,jdbcType=VARCHAR}, #{processOrchestrationId,jdbcType=BIGINT}, #{processOrchestrationVersion,jdbcType=VARCHAR}, 
      #{processOrchestrationNodeId,jdbcType=BIGINT}, #{distributorId,jdbcType=BIGINT}, 
      #{distributorCode,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_distributor_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id,
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version,
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_process_orchestration_distributor_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_process_orchestration_distributor_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationId != null">
        process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationVersion != null">
        process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationNodeId != null">
        process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="record.distributorId != null">
        distributor_id = #{record.distributorId,jdbcType=BIGINT},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_process_orchestration_distributor_code
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      distributor_id = #{record.distributorId,jdbcType=BIGINT},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO">
    update scrm_a_m_process_orchestration_distributor_code
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO">
    update scrm_a_m_process_orchestration_distributor_code
    set app_id = #{appId,jdbcType=VARCHAR},
      process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      distributor_id = #{distributorId,jdbcType=BIGINT},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_process_orchestration_distributor_code
    (app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
      distributor_id, distributor_code, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.processOrchestrationId,jdbcType=BIGINT}, 
        #{item.processOrchestrationVersion,jdbcType=VARCHAR}, #{item.processOrchestrationNodeId,jdbcType=BIGINT}, 
        #{item.distributorId,jdbcType=BIGINT}, #{item.distributorCode,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>