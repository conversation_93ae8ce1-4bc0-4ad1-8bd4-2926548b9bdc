package com.sankuai.scrm.core.service.pchat.dto.groupMem;

import lombok.Data;

/**
 * 4519
 * @Description 新成员入群扫码地址回调接口
 * <AUTHOR>
 * @Create On 2023/11/6 17:52
 * @Version v1.0.0
 */
@Data
public class NewFriendJoinByQRCodeDTO {
    /**
     * 群id
     */
    private String vcChatRoomId;

    /**
     * 群编号
     */
    private String vcChatRoomSerialNo;

    /**
     * 扫码入群的二维码地址
     */
    private String vcChatRoomQrcode;

    /**
     * 群成员微信id
     */
    private String vcUserWxId;

    /**
     * 群成员用户编号
     */
    private String vcUserWxSerialNo;

    /**
     * 群成员微信昵称
     */
    private String vcUserNickName;

    /**
     * 备注
     */
    private String vcRemark;

    /**
     * 时间戳
     */
    private Long nTimeStamp;
}
