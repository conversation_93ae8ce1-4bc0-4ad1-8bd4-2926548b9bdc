package com.sankuai.scrm.core.service.infrastructure.acl.ds.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum DeviceType {
    UNKNOWN(-1, "未知"),
    CLOUD_PHONE(1,"云手机"),
    CLOUD_PC(2,"云PC"),
    DOUBLE_END_HOST(3,"双端托管"),
    CLOUD_IPHONE(4,"云iphone"),
    CLOUD_ANDROID(5, "云android"),
    CLOUD_IPAD(6, "云ipad"),
    PAD(7, "pad");



    private int code;
    private String msg;

    DeviceType(int code , String msg){
        this.code=code;
        this.msg=msg;
    }

    public static DeviceType fromCode(Integer code) {
        for (DeviceType enumValue : DeviceType.values()) {
            if (Objects.equals(enumValue.code, code)) {
                return enumValue;
            }
        }
        return DeviceType.UNKNOWN;
    }
}
