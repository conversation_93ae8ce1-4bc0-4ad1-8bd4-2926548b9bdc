package com.sankuai.scrm.core.service.pchat.dto.loginout;

import lombok.Data;

/**
 * 1007
 * @Description 机器人疑似封号回调接口
 * <AUTHOR>
 * @Create On 2023/11/7 18:46
 * @Version v1.0.0
 */
@Data
public class SuspectedClosureAccountDTO {
    /**
     * 商家业务关联流水
     */
    private String vcRelationSerialNo;

    /**
     * 性别 1 男生 2 女生
     */
    private Integer nSex;

    /**
     * 微信昵称
     */
    private String vcNickName;

    /**
     * base64加密昵称
     */
    private String vcBase64NickName;

    /**
     * 用户头像
     */
    private String vcHeadImgUrl;

    /**
     * 国家
     */
    private String vcCountry;

    /**
     * 省
     */
    private String vcProvince;

    /**
     * 城市地区
     */
    private String vcCity;

    /**
     * x
     */
    private String vcBase64WhatsUp;

    /**
     * 机器人的群数量
     */
    private String nChatRoomCount;

    /**
     * 失败原因（微信回调的失败原因）
     */
    private String vcReason;

    /**
     * 时间
     */
    private String dtDateTime;

}
