package com.sankuai.scrm.core.service.pchat.dal.activity.mapper;

import com.sankuai.dz.srcm.privatelive.community.dto.CommunityUserInfoDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmPersonalWxActivityGroupMappingExtMapper  {
    List<String> queryGroupFamilyCodeByActivityId(@Param("activityId") Long activityId);

    List<CommunityUserInfoDetailDTO> queryUserInfoByUnionIds(@Param("unionIds") List<String> unionIds, @Param("groupIds") List<String> groupIds);
}