package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeGroupResultDTO;
import com.sankuai.dz.srcm.pchat.request.SendGroupChatMessagesRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.tanjing.GroupMessageService;
import com.sankuai.dz.srcm.pchat.tanjing.RobotFunctionService;
import com.sankuai.scrm.core.service.pchat.PersonalWxMiniprogramUtil;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxMsg;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.im.PChatWxImChatLogCommonService;
import com.sankuai.scrm.core.service.pchat.dto.ScrmPersonalWxMsgDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalMsgTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxMsgStateEnum;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmPersonalWxMsgTaskProducer;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import com.sankuai.scrm.core.service.pchat.thirdparty.horus.HorusVideoService;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.springframework.context.ApplicationEventPublisher;

@RunWith(MockitoJUnitRunner.class)
public class TuseMsgProcessorSendGroupMsgTest {

    @InjectMocks
    private TuseMsgProcessor tuseMsgProcessor;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private PersonalWxMiniprogramUtil personalWxMiniprogramUtil;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Mock
    private RobotFunctionService robotFunctionService;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Mock
    private ScrmPersonalWxMsgTaskProducer personalWxMsgTaskProducer;

    @Mock
    private HorusVideoService horusVideoService;

    @Mock
    private GroupMessageService groupMessageService;

    @Mock
    private PChatWxImChatLogCommonService wxImChatLogCommonService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试场景：wxMsgDTOS 为空
     */
    @Test
    public void testSendGroupMsg_EmptyWxMsgDTOS() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
        // assert
        // Since we're testing a public method that uses private methods internally,
        // we only need to verify that the method completes without exceptions
        verifyNoMoreInteractions(groupManageDomainService, personalWxMiniprogramUtil, personalWxCommonService, robotFunctionService);
    }

    /**
     * 测试场景：wxMsgDTOS 不为空
     */
    @Test
    public void testSendGroupMsg_NonEmptyWxMsgDTOS() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        ScrmPersonalWxMsg wxMsg = new ScrmPersonalWxMsg();
        GroupMsg groupMsg = new GroupMsg();
        wxMsgDTOS.add(new ScrmPersonalWxMsgDTO(wxMsg, groupMsg));
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
        // assert
        verifyNoMoreInteractions(groupManageDomainService, personalWxMiniprogramUtil, personalWxCommonService, robotFunctionService);
    }

    /**
     * 测试场景：构建延迟树时抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendGroupMsg_ExceptionInBuildWxMsgDelayTree() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Since we can't mock private methods, we'll verify the method throws an exception
        doThrow(new Exception()).when(groupManageDomainService).saveGroup(any());
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
    }

    /**
     * 测试场景：转换消息格式时抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendGroupMsg_ExceptionInWxMsgConvertToRequestFormat() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Since we can't mock private methods, we'll verify the method throws an exception
        doThrow(new Exception()).when(personalWxMiniprogramUtil).wrapXml(any());
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
    }

    /**
     * 测试场景：构建基础发送请求时抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendGroupMsg_ExceptionInBuildBaseSendRequest() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Since we can't mock private methods, we'll verify the method throws an exception
        doThrow(new Exception()).when(personalWxCommonService).getCreator();
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
    }

    /**
     * 测试场景：发送延迟消息时抛出异常
     */
    @Test(expected = Exception.class)
    public void testSendGroupMsg_ExceptionInSendDelayMsg() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Since we can't mock private methods, we'll verify the method throws an exception
        doThrow(new Exception()).when(robotFunctionService).acceptNewFriendRequest(any(), any(), any());
        // act
        tuseMsgProcessor.sendGroupMsg(appId, groupInfo, wxMsgDTOS);
    }

    /**
     * 测试正常情况下的 sendGroupMsgNow 方法
     */
    @Test
    public void testSendGroupMsgNow_NormalCase() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setChatRoomWxSerialNo("testChatRoom");
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        ScrmPersonalWxMsg wxMsg = new ScrmPersonalWxMsg();
        wxMsg.setTaskId(1L);
        GroupMsg groupMsg = new GroupMsg();
        ScrmPersonalWxMsgDTO msgDTO = new ScrmPersonalWxMsgDTO(wxMsg, groupMsg);
        wxMsgDTOS.add(msgDTO);
        AsyncInvokeGroupResultDTO asyncInvokeGroupResultDTO = new AsyncInvokeGroupResultDTO();
        asyncInvokeGroupResultDTO.setNResult(0);
        asyncInvokeGroupResultDTO.setVcSerialNo("testSerialNo");
        when(groupMessageService.sendGroupChatMessages(any(SendGroupChatMessagesRequest.class))).thenReturn(asyncInvokeGroupResultDTO);
        // act
        tuseMsgProcessor.sendGroupMsgNow(appId, groupInfo, wxMsgDTOS);
        // assert
        verify(groupMessageService, times(1)).sendGroupChatMessages(any(SendGroupChatMessagesRequest.class));
    }

    /**
     * 测试 groupInfo 为 null 的情况
     */
    @Test(expected = NullPointerException.class)
    public void testSendGroupMsgNow_GroupInfoIsNull() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = null;
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // act
        tuseMsgProcessor.sendGroupMsgNow(appId, groupInfo, wxMsgDTOS);
        // assert
        // Expecting NullPointerException
    }

    /**
     * 测试 wxMsgDTOS 为 null 的情况
     */
    @Test
    public void testSendGroupMsgNow_WxMsgDTOSIsNull() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setChatRoomWxSerialNo("testChatRoom");
        // Mock the AsyncInvokeGroupResultDTO
        AsyncInvokeGroupResultDTO asyncInvokeGroupResultDTO = new AsyncInvokeGroupResultDTO();
        asyncInvokeGroupResultDTO.setNResult(0);
        asyncInvokeGroupResultDTO.setVcSerialNo("testSerialNo");
        // act
        try {
            tuseMsgProcessor.sendGroupMsgNow(appId, groupInfo, null);
        } catch (NullPointerException e) {
            // Expected exception
        }
        // assert
        verify(groupMessageService, never()).sendGroupChatMessages(any(SendGroupChatMessagesRequest.class));
    }

    /**
     * 测试 wxMsgDTOS 为空列表的情况
     */
    @Test
    public void testSendGroupMsgNow_WxMsgDTOSIsEmpty() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // act
        tuseMsgProcessor.sendGroupMsgNow(appId, groupInfo, wxMsgDTOS);
        // assert
        verify(groupMessageService, never()).sendGroupChatMessages(any(SendGroupChatMessagesRequest.class));
    }

    /**
     * 测试场景：wxMsgDTOS 为空
     */
    @Test
    public void testSendInstantMsg_EmptyWxMsgDTOS() throws Throwable {
        // arrange
        SendGroupChatMessagesRequest request = new SendGroupChatMessagesRequest();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Use reflection to invoke the private method
        Method sendInstantMsgMethod = TuseMsgProcessor.class.getDeclaredMethod("sendInstantMsg", SendGroupChatMessagesRequest.class, List.class);
        sendInstantMsgMethod.setAccessible(true);
        sendInstantMsgMethod.invoke(tuseMsgProcessor, request, wxMsgDTOS);
        // assert
        verify(groupManageDomainService, never()).updateWxMsgList(anyList());
        verify(groupMessageService, never()).sendGroupChatMessages(any());
    }

    /**
     * 测试场景：wxMsgDTOS 为空
     */
    @Test
    public void testSendInstantMsg_wxMsgDTOSIsEmpty() throws Throwable {
        // arrange
        SendGroupChatMessagesRequest request = new SendGroupChatMessagesRequest();
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = new ArrayList<>();
        // Use reflection to invoke the private method
        Method sendInstantMsgMethod = TuseMsgProcessor.class.getDeclaredMethod("sendInstantMsg", SendGroupChatMessagesRequest.class, List.class);
        sendInstantMsgMethod.setAccessible(true);
        sendInstantMsgMethod.invoke(tuseMsgProcessor, request, wxMsgDTOS);
        // assert
        verify(groupManageDomainService, never()).updateWxMsgList(anyList());
        verify(groupMessageService, never()).sendGroupChatMessages(any());
    }
}
