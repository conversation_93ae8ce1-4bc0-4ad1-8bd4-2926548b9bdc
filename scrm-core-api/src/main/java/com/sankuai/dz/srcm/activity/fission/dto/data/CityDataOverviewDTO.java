package com.sankuai.dz.srcm.activity.fission.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CityDataOverviewDTO implements Serializable {
    private String cityName;
    private Integer cityJoinGroupTotalSize;
    private Integer cityQuitGroupSize;
    private Integer cityGrowthSize;
}
