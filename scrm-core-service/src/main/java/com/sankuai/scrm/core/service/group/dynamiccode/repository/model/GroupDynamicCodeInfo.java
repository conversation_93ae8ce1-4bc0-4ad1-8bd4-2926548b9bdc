package com.sankuai.scrm.core.service.group.dynamiccode.repository.model;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: group_dynamic_code_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GroupDynamicCodeInfo {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: channel_id
     *   说明: 渠道来源id
     */
    private Long channelId;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: status
     *   说明: 状态：0=无效，1=有效
     */
    private Integer status;

    /**
     *   字段: name
     *   说明: 群活码名称
     */
    private String name;

    /**
     *   字段: join_way_config_id
     *   说明: 企微“加入群聊”配置id
     */
    private String joinWayConfigId;

    /**
     *   字段: qr_code
     *   说明: 群活码的二维码链接
     */
    private String qrCode;

    /**
     *   字段: mt_city_id
     *   说明: 此码对应的美团前台城市id
     */
    private Long mtCityId;

    /**
     *   字段: notification_on_group_full
     *   说明: 满员提醒。0=关闭，1=开启。
     */
    private Boolean notificationOnGroupFull;

    /**
     *   字段: group_auto_increment
     *   说明: 满员自动续群。0=关闭，1=开启。
     */
    private Boolean groupAutoIncrement;

    /**
     *   字段: group_name_prefix
     *   说明: 自增群名称中的固定部分
     */
    private String groupNamePrefix;

    /**
     *   字段: group_name_initial_seq
     *   说明: 自增群名称中序号的初始值
     */
    private Integer groupNameInitialSeq;

    /**
     *   字段: sms_link
     *   说明: 短信跳转链接
     */
    private String smsLink;

    /**
     *   字段: sms_link_update_time
     *   说明: 短信跳转链接最近一次更新时间
     */
    private Date smsLinkUpdateTime;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}