package com.sankuai.scrm.core.service.activity.venue.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.dz.srcm.activity.venue.dto.VenueOverviewInfoDTO;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VenueRemoteResponse {

    private int code;

    private String message;

    private VenueOverviewInfoDTO data;

    public boolean isSuccess() {
        return code == 200;
    }
}
