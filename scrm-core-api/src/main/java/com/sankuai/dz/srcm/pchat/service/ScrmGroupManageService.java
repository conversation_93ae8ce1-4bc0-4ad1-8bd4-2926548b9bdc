package com.sankuai.dz.srcm.pchat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.request.ChangeGroupCreateOwnerRequest;
import com.sankuai.dz.srcm.pchat.request.ChangeGroupQrRequest;
import com.sankuai.dz.srcm.pchat.request.PrivateGroupInfoByConsultantTaskIdRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.*;
import com.sankuai.dz.srcm.pchat.response.scrm.*;

import java.util.List;
/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/10 17:23
 * @Version v1.0.0
 */
@InterfaceDoc(
        id = "ScrmGroupManageService",
        serviceId = "com.sankuai.medicalcosmetology.scrm.core",
        type = InterfaceDoc.InterfaceType.RESTFUL,
        displayName = "社群管理接口",
        description = "社群管理接口",
        scenarios = "社群管理接口")
public interface ScrmGroupManageService {

    /**
     * 创建群组及群
     *
     * @param request
     * @return
     */
    RemoteResponse<String> createGroupSet(GroupCreateRequest request);


    /**
     * 群组详情
     *
     * @param setId
     * @return
     */
    RemoteResponse<GroupSetDetailResponse> groupSetDetail(Long setId);

    /**
     * 群列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "个微群列表",
            description = "个微群列表",
            returnValueDescription = "个微群列表",
            parameters = {@ParamDoc(name = "request", description = "入参", paramType = ParamType.REQUEST_PARAM)},
            urls = {"/apigw/superadmin/pchat/group/list", "/apigw/saas/pchat/group/list"},
            requestMethods = {HttpMethod.GET}
    )
    PageRemoteResponse<GroupListResponse> groupList(GroupListRequest request);

    /**
     * 群编辑
     *
     * @param request
     * @return
     */
    RemoteResponse<String> groupEdit(GroupEditRequest request);

    /**
     * 群基本信息
     *
     * @param groupId
     * @return
     */
    RemoteResponse<GroupInfoResponse> groupDetail(Long groupId);

    /**
     * 群成员信息
     *
     * @param request
     * @return
     */
    PageRemoteResponse<GroupMemberInfoResponse> groupMemberList(GroupMemberListRequest request);

    /**
     * 直播维度下所有群成员
     *
     * @param request
     * @return
     */
    PageRemoteResponse<WebcastAllGroupMemberInfoResponse> webcastAllGroupMemberList(WebcastGroupMemberListRequest request);

    /**
     * 群成员信息
     *
     * @param request
     * @return
     */
    @Deprecated
    PageRemoteResponse<GroupMemberInfoResponse> memberList(GroupMemberListRequest request);


    /**
     * 群咨询师信息
     *
     * @param groupId
     * @return
     */
    PageRemoteResponse<GroupConsultantQueryResponse> groupConsultantList(Long groupId, Integer pageNo, Integer pageSize);

    /**
     * 群内裂变群列表
     *
     * @param request
     * @return
     */
    RemoteResponse<GroupSelectionResponse> groupSelection(GroupSelectionRequest request);

    /**
     * 直播下的进播率
     *
     * @param webcastId 直播id
     * @return
     */
    RemoteResponse<GroupMemberEnterLiveDataResponse> groupMemberEnterLiveData(String webcastId);

    /**
     * 群概览
     *
     * @return
     */
    RemoteResponse<GroupOverviewResponse> groupOverview(GroupOverviewRequest request);


    /**
     * 群解散
     *
     * @param groupId
     * @return
     */
    RemoteResponse<String> groupDismiss(Long groupId);

    /**
     * 群内踢人
     *
     * @return
     */
    RemoteResponse<String> groupKickOutMember(GroupKickOutMemberRequest request);

    /**
     * 群解散
     *
     * @param groupId        群id
     * @param memberSerialNo 群成员序列号
     * @return
     */
    RemoteResponse<String> transferChatRoomAdminUser(Long groupId, String memberSerialNo);


    /**
     * 群公告查询
     *
     * @param groupId
     * @return
     */
    RemoteResponse<GroupNotice> groupNoticeQuery(Long groupId);

    /**
     * 群欢迎语查询
     *
     * @param groupId
     * @return
     */
    RemoteResponse<GroupWelcomeMsg> groupWelcomeMsg(Long groupId);

    /**
     * 关联直播
     *
     * @param groupId
     * @param webcastId
     * @return
     */
    RemoteResponse<String> associateToWebcast(Long groupId, String webcastId);

    /**
     * 直播列表
     *
     * @param keyword
     * @param webcastId
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @return
     */

    PageRemoteResponse<WebcastListResponse> webcastList(String keyword, String webcastId, String startDate, String endDate, Integer pageNo, Integer pageSize);

    /**
     * 直播溯源方式
     *
     * @param webcastId
     * @return
     */
    RemoteResponse<WebcastTraceAbilityResponse> webcastListTraceAbility(String webcastId);


    /**
     * 群发消息频率提示
     *
     * @param liveId
     * @param sendType
     * @param sendTime
     * @return
     */
    RemoteResponse<List<String>> groupMsgTaskSendBeforeCheck(String liveId, String sendType, Long sendTime);

    /**
     * 群发消息
     *
     * @param request
     * @return
     */
    RemoteResponse<String> groupMsgTaskSend(GroupMsgSendRequest request);

    /**
     * 群发消息详情
     *
     * @param msgId
     * @return
     */
    RemoteResponse<GroupMsgDetailResponse> groupMsgTaskDetail(Long msgId);

    /**
     * 群发消息列表
     *
     * @param request
     * @return
     */
    PageRemoteResponse<GroupMsgListResponse> groupMsgTaskList(GroupMsgListRequest request);

    /**
     * 消息任务编辑
     *
     * @param request
     * @return
     */

    RemoteResponse<String> groupMsgTaskEdit(GroupMsgSendRequest request);

    /**
     * 消息任务取消
     *
     * @param msgId
     * @return
     */

    RemoteResponse<String> groupMsgTaskCancel(Long msgId);

    /**
     * 同步机器人信息
     *
     * @return
     */
    RemoteResponse<String> syncRobotInfo();

    /**
     * 同步机器人好友
     *
     * @param robotSerialNo
     * @return
     */

    RemoteResponse<String> syncRobotFriendInfo(String robotSerialNo);

    /**
     * 机器人添加好友
     *
     * @param robotSerialNo
     * @param account
     * @param hello
     * @return
     */
    RemoteResponse<String> robotInviteFriend(String robotSerialNo, String account, String hello, String vcQrCodeImageUrl);

    /**
     * 同步群成员
     *
     * @param robotSerialNo
     * @param chatRoomSerialNo
     * @return
     */
    RemoteResponse<String> syncGroupMember(String robotSerialNo, String chatRoomSerialNo);


    /**
     * 消息立即发送
     *
     * @param msgId
     * @return
     */
    RemoteResponse<String> groupMsgTaskInstantSend(Long msgId);

    /**
     * 查询发送的消息列表
     *
     * @param request
     * @return
     */
    RemoteResponse<List<GroupMsgSendListResponse>> queryMsgSendList(GroupMsgSendListRequest request);

    /**
     * 私发消息
     *
     * @param request
     * @return
     */
    RemoteResponse<String> privateMsgTaskSend(PrivateMsgSendRequest request);

    /**
     * 根据咨询师信息查询头像
     *
     * @param privateGroupInfoByConsultantTaskIdRequest
     * @return
     */
    RemoteResponse<GroupInfoResponse> getGroupInfoByConsultantTaskId(PrivateGroupInfoByConsultantTaskIdRequest privateGroupInfoByConsultantTaskIdRequest);

    /**
     * 手动指定咨询师
     *
     * @param request
     * @return
     */
    RemoteResponse<String> groupAppointConsultant(GroupAppointConsultantRequest request);

    /**
     * 强制指定咨询师
     * @param request
     * @return
     */
    RemoteResponse<String> forceGroupAppointConsultant(GroupAppointConsultantRequest request);


    /**
     * 手工获取群二维码
     *
     * @param groupId
     * @return
     */
    RemoteResponse<String> manualGetGroupQr(Long groupId);

    /**
     * 修改群二维码
     * @param request
     * @return
     */
    RemoteResponse<String> changeGroupQr(ChangeGroupQrRequest request);

    /**
     * 修改群主机器人
     * @param request
     * @return
     */
    RemoteResponse<String> changeMainRobt(ChangeGroupCreateOwnerRequest request);


    PageRemoteResponse<GroupRobotMemberInfoResponse> robotMemberList(GroupRobotMemberListRequest request);

    /**
     * 直播信息统计
     * @param request
     * @return
     */
    RemoteResponse<LiveGroupStatResponse> liveDataStat(LiveGroupStatRequest request);

    /**
     * 查询群内机器人列表
     * @param request
     * @return
     */
    PageRemoteResponse<GroupRobotResponse> queryGroupRobotList(GroupRobotRequest request);
}
