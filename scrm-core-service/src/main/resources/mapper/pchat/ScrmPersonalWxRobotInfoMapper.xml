<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="robot_serial_no" jdbcType="VARCHAR" property="robotSerialNo" />
    <result column="robot_wx_id" jdbcType="VARCHAR" property="robotWxId" />
    <result column="vc_ban_type" jdbcType="VARCHAR" property="vcBanType" />
    <result column="vc_reason" jdbcType="VARCHAR" property="vcReason" />
    <result column="dt_date_time" jdbcType="VARCHAR" property="dtDateTime" />
    <result column="online" jdbcType="CHAR" property="online" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="valid" jdbcType="CHAR" property="valid" />
    <result column="cluster_no" jdbcType="VARCHAR" property="clusterNo" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="platform_robot_group_no" jdbcType="VARCHAR" property="platformRobotGroupNo" />
    <result column="robot_type" jdbcType="INTEGER" property="robotType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, robot_serial_no, robot_wx_id, vc_ban_type, vc_reason, dt_date_time, online, add_time, 
    update_time, valid, cluster_no, status, platform_robot_group_no, robot_type, app_id, 
    tenant_id, service_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_RobotInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_RobotInfo
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_RobotInfo
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotInfoExample">
    delete from Scrm_PersonalWx_RobotInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_RobotInfo (robot_serial_no, robot_wx_id, vc_ban_type, 
      vc_reason, dt_date_time, online, 
      add_time, update_time, valid, 
      cluster_no, status, platform_robot_group_no, 
      robot_type, app_id, tenant_id, 
      service_type)
    values (#{robotSerialNo,jdbcType=VARCHAR}, #{robotWxId,jdbcType=VARCHAR}, #{vcBanType,jdbcType=VARCHAR}, 
      #{vcReason,jdbcType=VARCHAR}, #{dtDateTime,jdbcType=VARCHAR}, #{online,jdbcType=CHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=CHAR}, 
      #{clusterNo,jdbcType=VARCHAR}, #{status,jdbcType=CHAR}, #{platformRobotGroupNo,jdbcType=VARCHAR}, 
      #{robotType,jdbcType=INTEGER}, #{appId,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{serviceType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_RobotInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="robotSerialNo != null">
        robot_serial_no,
      </if>
      <if test="robotWxId != null">
        robot_wx_id,
      </if>
      <if test="vcBanType != null">
        vc_ban_type,
      </if>
      <if test="vcReason != null">
        vc_reason,
      </if>
      <if test="dtDateTime != null">
        dt_date_time,
      </if>
      <if test="online != null">
        online,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="clusterNo != null">
        cluster_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="platformRobotGroupNo != null">
        platform_robot_group_no,
      </if>
      <if test="robotType != null">
        robot_type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="robotSerialNo != null">
        #{robotSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="robotWxId != null">
        #{robotWxId,jdbcType=VARCHAR},
      </if>
      <if test="vcBanType != null">
        #{vcBanType,jdbcType=VARCHAR},
      </if>
      <if test="vcReason != null">
        #{vcReason,jdbcType=VARCHAR},
      </if>
      <if test="dtDateTime != null">
        #{dtDateTime,jdbcType=VARCHAR},
      </if>
      <if test="online != null">
        #{online,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=CHAR},
      </if>
      <if test="clusterNo != null">
        #{clusterNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="platformRobotGroupNo != null">
        #{platformRobotGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        #{robotType,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotInfoExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_RobotInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_RobotInfo
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.robotSerialNo != null">
        robot_serial_no = #{record.robotSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.robotWxId != null">
        robot_wx_id = #{record.robotWxId,jdbcType=VARCHAR},
      </if>
      <if test="record.vcBanType != null">
        vc_ban_type = #{record.vcBanType,jdbcType=VARCHAR},
      </if>
      <if test="record.vcReason != null">
        vc_reason = #{record.vcReason,jdbcType=VARCHAR},
      </if>
      <if test="record.dtDateTime != null">
        dt_date_time = #{record.dtDateTime,jdbcType=VARCHAR},
      </if>
      <if test="record.online != null">
        online = #{record.online,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.valid != null">
        valid = #{record.valid,jdbcType=CHAR},
      </if>
      <if test="record.clusterNo != null">
        cluster_no = #{record.clusterNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=CHAR},
      </if>
      <if test="record.platformRobotGroupNo != null">
        platform_robot_group_no = #{record.platformRobotGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="record.robotType != null">
        robot_type = #{record.robotType,jdbcType=INTEGER},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_RobotInfo
    set id = #{record.id,jdbcType=BIGINT},
      robot_serial_no = #{record.robotSerialNo,jdbcType=VARCHAR},
      robot_wx_id = #{record.robotWxId,jdbcType=VARCHAR},
      vc_ban_type = #{record.vcBanType,jdbcType=VARCHAR},
      vc_reason = #{record.vcReason,jdbcType=VARCHAR},
      dt_date_time = #{record.dtDateTime,jdbcType=VARCHAR},
      online = #{record.online,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      valid = #{record.valid,jdbcType=CHAR},
      cluster_no = #{record.clusterNo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=CHAR},
      platform_robot_group_no = #{record.platformRobotGroupNo,jdbcType=VARCHAR},
      robot_type = #{record.robotType,jdbcType=INTEGER},
      app_id = #{record.appId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      service_type = #{record.serviceType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo">
    update Scrm_PersonalWx_RobotInfo
    <set>
      <if test="robotSerialNo != null">
        robot_serial_no = #{robotSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="robotWxId != null">
        robot_wx_id = #{robotWxId,jdbcType=VARCHAR},
      </if>
      <if test="vcBanType != null">
        vc_ban_type = #{vcBanType,jdbcType=VARCHAR},
      </if>
      <if test="vcReason != null">
        vc_reason = #{vcReason,jdbcType=VARCHAR},
      </if>
      <if test="dtDateTime != null">
        dt_date_time = #{dtDateTime,jdbcType=VARCHAR},
      </if>
      <if test="online != null">
        online = #{online,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=CHAR},
      </if>
      <if test="clusterNo != null">
        cluster_no = #{clusterNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="platformRobotGroupNo != null">
        platform_robot_group_no = #{platformRobotGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="robotType != null">
        robot_type = #{robotType,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo">
    update Scrm_PersonalWx_RobotInfo
    set robot_serial_no = #{robotSerialNo,jdbcType=VARCHAR},
      robot_wx_id = #{robotWxId,jdbcType=VARCHAR},
      vc_ban_type = #{vcBanType,jdbcType=VARCHAR},
      vc_reason = #{vcReason,jdbcType=VARCHAR},
      dt_date_time = #{dtDateTime,jdbcType=VARCHAR},
      online = #{online,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=CHAR},
      cluster_no = #{clusterNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=CHAR},
      platform_robot_group_no = #{platformRobotGroupNo,jdbcType=VARCHAR},
      robot_type = #{robotType,jdbcType=INTEGER},
      app_id = #{appId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      service_type = #{serviceType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_RobotInfo
    (robot_serial_no, robot_wx_id, vc_ban_type, vc_reason, dt_date_time, online, add_time, 
      update_time, valid, cluster_no, status, platform_robot_group_no, robot_type, app_id, 
      tenant_id, service_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.robotSerialNo,jdbcType=VARCHAR}, #{item.robotWxId,jdbcType=VARCHAR}, #{item.vcBanType,jdbcType=VARCHAR}, 
        #{item.vcReason,jdbcType=VARCHAR}, #{item.dtDateTime,jdbcType=VARCHAR}, #{item.online,jdbcType=CHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.valid,jdbcType=CHAR}, 
        #{item.clusterNo,jdbcType=VARCHAR}, #{item.status,jdbcType=CHAR}, #{item.platformRobotGroupNo,jdbcType=VARCHAR}, 
        #{item.robotType,jdbcType=INTEGER}, #{item.appId,jdbcType=VARCHAR}, #{item.tenantId,jdbcType=BIGINT}, 
        #{item.serviceType,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>