package com.sankuai.scrm.core.service.flow.processor;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.poi.shopcateprop.api.dto.dpcategory.PoiCategoryInfoDTO;
import com.dianping.poi.transform.CityTransformService;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.enums.PlatformType;
import com.sankuai.scrm.core.service.infrastructure.acl.poi.PoiCategoryAclService;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserIdModel;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class PreProcessor {

    @Autowired
    private CityTransformService cityTransformService;

    @Autowired
    private PoiCategoryAclService categoryAclService;

    @Autowired
    private UserMergeQueryService.Iface userMergeQueryService;

    @Autowired
    private PoiRelationService poiRelationService;

    public void transformCity(FlowEntryWxMaterialRequest request) {
        if (request == null || request.getPlatform() == null || request.getPlatform().equals(PlatformType.MT.getCode())) {
            return;
        }
        if (request.getDpCityId() == null) {
            return;
        }
        try {
            Integer mtCityId = cityTransformService.getMtCityByDpCity(request.getDpCityId());
            request.setMtCityId(mtCityId);
        } catch (Exception e) {
            log.error("[{}][transformCity] has exception, dpCityId:{}", getClass().getSimpleName(), request.getDpCityId(), e);
        }
    }

    public void transformCategory(FlowEntryWxMaterialRequest request) {
        if (request == null || request.getShopId() == null) {
            return;
        }
        DpPoiBackCategoryDTO secondLevelCategory = categoryAclService.getBackMainCategoryPath(request.getShopId());
        if (secondLevelCategory != null) {
            if (secondLevelCategory.getCategoryId() != null) {
                request.setCategoryId((long) secondLevelCategory.getCategoryId());
            }
            request.setCategoryName(secondLevelCategory.getCategoryName());
        }
    }

    public void transformUserId(FlowEntryWxMaterialRequest request) {
        if (request == null || !(PlatformType.DP.getCode() == request.getPlatform()) || request.getDpUserId() == null) {
            return;
        }
        try {
            BindRelationResp resp = userMergeQueryService.getRealBindByDpUserId(request.getDpUserId());
            if (resp != null && resp.isSuccess() && resp.getData() != null && resp.getData().getMtUserId() != null) {
                UserIdModel model = resp.getData().getMtUserId();
                if (!model.isVirtualUser()) {
                    request.setUserId(model.getId());
                }
            }
        } catch (TException e) {
            log.error("PreProcessor.transformUserId has exception, request:{}", request, e);
        }
    }

    public void transformShopId(FlowEntryWxMaterialRequest request) {
        if (request == null) {
            return;
        }
        if (PlatformType.MT.getCode() == request.getPlatform() && request.getMtShopId() == null && request.getShopId() != null) {
            try {
                List<Long> mtShopIdList = poiRelationService.queryMtByDpIdL(request.getShopId());
                if (CollectionUtils.isNotEmpty(mtShopIdList) && mtShopIdList.get(0) != null) {
                    request.setMtShopId(mtShopIdList.get(0));
                }
            } catch (Exception e) {
                log.error("PreProcessor.transformShopId has exception", e);
            }
        }
    }


    public void transDpShopIdFormMtShopId(FlowEntryWxMaterialRequest request) {
        if (request == null) {
            return;
        }
        if (PlatformType.MT.getCode() == request.getPlatform() && request.getShopId() == null && request.getMtShopId() != null) {
            try {
                List<Long> dpShopId = poiRelationService.queryDpByMtIdL(request.getMtShopId());
                if (CollectionUtils.isNotEmpty(dpShopId) && dpShopId.get(0) != null) {
                    request.setShopId(dpShopId.get(0));
                }
            } catch (Exception e) {
                log.error("PreProcessor.transDpUserIdFormUserId has exception", e);
            }
        }
    }
}
