package com.sankuai.scrm.core.service.abtest.strategy.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserIntentionStrategyConfigDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class UserIntentionStrategyV2ConfigInitTest {

    private UserIntentionStrategyV2Config config;

    private MockedStatic<Environment> environmentMock;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<JsonUtils> jsonUtilsMock;

    private ConfigRepository configRepository;

    private static final String LION_KEY = "user_intention_strategy_v2_config";

    @BeforeEach
    void setUp() throws Exception {
        config = new UserIntentionStrategyV2Config();
        // Mock static classes
        environmentMock = mockStatic(Environment.class);
        lionMock = mockStatic(Lion.class);
        jsonUtilsMock = mockStatic(JsonUtils.class);
        // Mock dependencies
        configRepository = mock(ConfigRepository.class);
        // Setup mocks
        lionMock.when(Lion::getConfigRepository).thenReturn(configRepository);
        // Set private configRepository field via reflection
        Field configRepositoryField = UserIntentionStrategyV2Config.class.getDeclaredField("configRepository");
        configRepositoryField.setAccessible(true);
        configRepositoryField.set(config, configRepository);
    }

    @AfterEach
    void tearDown() {
        environmentMock.close();
        lionMock.close();
        jsonUtilsMock.close();
    }

    private void invokeInitMethod() throws Exception {
        Method initMethod = UserIntentionStrategyV2Config.class.getDeclaredMethod("init");
        initMethod.setAccessible(true);
        initMethod.invoke(config);
    }

    /**
     * Test successful initialization with valid configuration
     */
    @Test
    void testInitSuccess() throws Throwable {
        // arrange
        String appName = "test-app";
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        String jsonString = "{\"test\":\"value\"}";
        environmentMock.when(Environment::getAppName).thenReturn(appName);
        lionMock.when(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class)).thenReturn(configDTO);
        jsonUtilsMock.when(() -> JsonUtils.toStr(configDTO)).thenReturn(jsonString);
        // act
        invokeInitMethod();
        // assert
        lionMock.verify(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class));
        verify(configRepository).addConfigListener(LION_KEY, config);
        // Verify the DTO was set
        Field dtoField = UserIntentionStrategyV2Config.class.getDeclaredField("userIntentionStrategyConfigDTO");
        dtoField.setAccessible(true);
        assertEquals(configDTO, dtoField.get(config));
    }

    /**
     * Test initialization when Environment.getAppName() returns null
     */
    @Test
    void testInitWithNullAppName() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        String jsonString = "{\"test\":\"value\"}";
        environmentMock.when(Environment::getAppName).thenReturn(null);
        lionMock.when(() -> Lion.getBean(null, LION_KEY, UserIntentionStrategyConfigDTO.class)).thenReturn(configDTO);
        jsonUtilsMock.when(() -> JsonUtils.toStr(configDTO)).thenReturn(jsonString);
        // act
        invokeInitMethod();
        // assert
        lionMock.verify(() -> Lion.getBean(null, LION_KEY, UserIntentionStrategyConfigDTO.class));
        verify(configRepository).addConfigListener(LION_KEY, config);
    }

    /**
     * Test initialization when Lion.getBean() returns null
     */
    @Test
    void testInitWithNullConfigDTO() throws Throwable {
        // arrange
        String appName = "test-app";
        environmentMock.when(Environment::getAppName).thenReturn(appName);
        lionMock.when(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class)).thenReturn(null);
        // act
        invokeInitMethod();
        // assert
        lionMock.verify(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class));
        verify(configRepository).addConfigListener(LION_KEY, config);
        // Verify the DTO is null
        Field dtoField = UserIntentionStrategyV2Config.class.getDeclaredField("userIntentionStrategyConfigDTO");
        dtoField.setAccessible(true);
        assertNull(dtoField.get(config));
    }

    /**
     * Test initialization when adding config listener throws exception
     */
    @Test
    void testInitWithAddConfigListenerException() throws Throwable {
        // arrange
        String appName = "test-app";
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        RuntimeException listenerException = new RuntimeException("Failed to add listener");
        environmentMock.when(Environment::getAppName).thenReturn(appName);
        lionMock.when(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class)).thenReturn(configDTO);
        doThrow(listenerException).when(configRepository).addConfigListener(eq(LION_KEY), any(ConfigListener.class));
        // act & assert
        Exception exception = assertThrows(Exception.class, this::invokeInitMethod);
        assertEquals(listenerException, exception.getCause());
        // assert
        lionMock.verify(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class));
        verify(configRepository).addConfigListener(LION_KEY, config);
    }

    /**
     * Test initialization when Lion.getBean() throws exception
     */
    @Test
    void testInitWithGetBeanException() throws Throwable {
        // arrange
        String appName = "test-app";
        RuntimeException beanException = new RuntimeException("Failed to get bean");
        environmentMock.when(Environment::getAppName).thenReturn(appName);
        lionMock.when(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class)).thenThrow(beanException);
        // act & assert
        Exception exception = assertThrows(Exception.class, this::invokeInitMethod);
        assertEquals(beanException, exception.getCause());
        // assert
        lionMock.verify(() -> Lion.getBean(appName, LION_KEY, UserIntentionStrategyConfigDTO.class));
        verifyNoInteractions(configRepository);
    }
}
