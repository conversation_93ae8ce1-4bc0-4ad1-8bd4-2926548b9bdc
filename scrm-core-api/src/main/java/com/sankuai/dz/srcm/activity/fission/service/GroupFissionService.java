package com.sankuai.dz.srcm.activity.fission.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.AwardStatisticDTO;
import com.sankuai.dz.srcm.activity.fission.dto.FissionGroupMemberChangeEventDTO;
import com.sankuai.dz.srcm.activity.fission.dto.task.FissionMatchTaskDTO;
import com.sankuai.dz.srcm.activity.fission.request.AwardStatisticRequest;

public interface GroupFissionService {
    RemoteResponse<AwardStatisticDTO> queryAwardStatistic(AwardStatisticRequest request);

    RemoteResponse<Void> onGroupMemberChangeEvent(FissionGroupMemberChangeEventDTO eventDTO);

    RemoteResponse<Void> onGroupMemberChangeEvent(FissionMatchTaskDTO eventDTO);
}
