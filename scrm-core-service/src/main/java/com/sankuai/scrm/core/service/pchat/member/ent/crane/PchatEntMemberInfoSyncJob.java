package com.sankuai.scrm.core.service.pchat.member.ent.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxStaffAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxStaffInfoResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.adapter.utils.WechatTypeUtils;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxUserMemberTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxUserSexEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PchatEntMemberInfoSyncJob {

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private WechatTypeUtils wechatTypeUtils;

    @Autowired
    private CorpWxStaffAcl corpWxStaffAcl;

    @Autowired
    private ScrmPersonalWxUserDomainService wxUserDomainService;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.pchat.ent.member.sync")
    public void sync() {
        List<String> appIdList = wechatTypeUtils.getEnterpriseAppIdList();
        appIdList.forEach(this::sync);
    }

    private void sync(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        String corpId = appConfigRepository.getCorpIdByAppId(appId);
        if (StringUtils.isEmpty(corpId)) {
            return;
        }
        try {
            List<String> followUserList = corpWxStaffAcl.getFollowUserList(corpId);
            if (CollectionUtils.isEmpty(followUserList)) {
                return;
            }
            List<ScrmPersonalWxUserInfo> wxUserInfoList = followUserList.stream().map(followUser -> {
                try {
                    WxStaffInfoResponse response = corpWxStaffAcl.getStaffInfoById(corpId, followUser);
                    return buildWxUserInfo(appId, response);
                } catch (Exception e) {
                    log.error("CorpWxStaffAcl.getStaffInfoById has exception corpId={}, userId={}", corpId, followUser, e);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            wxUserDomainService.saveOrUpdateWxUserInfo(appId, wxUserInfoList);
        } catch (Exception e) {
            log.error("PchatEnterpriseMemberSyncJob.doSync exception, appId={}", appId, e);
        }
    }

    private ScrmPersonalWxUserInfo buildWxUserInfo(String appId, WxStaffInfoResponse staffInfoResponse) {
        if (StringUtils.isEmpty(appId) || staffInfoResponse == null || staffInfoResponse.getErrCode() != 0) {
            return null;
        }
        ScrmPersonalWxUserInfo info = new ScrmPersonalWxUserInfo();
        info.setWxId(staffInfoResponse.getUserId());
        info.setWxAlias(staffInfoResponse.getUserId());
        info.setCreator(PchatConfig.SYS_CREATOR);
        info.setNickname(staffInfoResponse.getStaffName());
        info.setSex(PersonalWxUserSexEnum.UNKNOWN.getCode());
        info.setAddTime(new Date());
        info.setAppId(appId);
        info.setHeadimgUrl(staffInfoResponse.getAvatar());
        info.setSerialNo(staffInfoResponse.getUserId());
        info.setExtra(JsonUtils.toStr(staffInfoResponse));
        info.setMemberType(PersonalWxUserMemberTypeEnum.ROBOT.getCode());
        return info;
    }
}