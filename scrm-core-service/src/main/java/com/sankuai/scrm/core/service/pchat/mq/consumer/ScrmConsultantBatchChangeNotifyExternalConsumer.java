package com.sankuai.scrm.core.service.pchat.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.pchat.mq.handle.ScrmConsultantBatchChangeNotifyExternalHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/07/12 14:27
 * @Version v1.0.0
 */
@Slf4j
@Service
public class ScrmConsultantBatchChangeNotifyExternalConsumer extends AbstractConsumer {

    @Resource
    private ScrmConsultantBatchChangeNotifyExternalHandle scrmConsultantBatchChangeNotifyExternalHandle;

    @Override
    protected ConsumeStatus messageHandle(MafkaMessage message, MessagetContext context) {
        return scrmConsultantBatchChangeNotifyExternalHandle.messageHandle(message);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.init("daozong", "com.sankuai.medicalcosmetology.scrm.core"
                , "scrm_consultant_batch_change_notify_consumer", "consultant_batch_change_notify");
    }

}
