package com.sankuai.dz.srcm.pchat.dto.im;

import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/01/11 14:16
 * @Version v1.0.0
 */
@Data
public class Msg extends GroupMsg {

    /**
     * 消息id
     */
    private Long msgId;
    private String vcMsgId;

    /**
     * 发送时间 时间戳
     */
    private Long sendTime;
    private Long vcSendTime;

    private Long msgSerialNo;

    /**
     * 是否引用
     */
    private Boolean isQuoteMsg=false;
    /**
     * 引用内容
     */
    private String quoteMsgContent;
    /**
     * 引用链接
     */
    private String quoteMsgHref;
    /**
     * 引用消息类型
     */
    private String quoteMsgType;
    /**
     * 引用涂色消息类型
     */
    private Integer quoteTuseMsgType;
    /**
     * 引用消息id
     */
    private Long quoteMsgId;
    /**
     * 引用消息发送者微信昵称
     */
    private String quoteWxNickname;
    private String quoteWxSerialNo;



}