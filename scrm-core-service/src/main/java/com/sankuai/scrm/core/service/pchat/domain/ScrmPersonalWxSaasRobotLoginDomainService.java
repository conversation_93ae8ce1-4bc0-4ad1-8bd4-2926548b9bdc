package com.sankuai.scrm.core.service.pchat.domain;

import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.dto.robot.SaasRobotDTO;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotLoginLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotLoginLogExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.*;
import com.sankuai.scrm.core.service.pchat.enums.LiveServiceTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalIsBooleanStrEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalRobotStatusEnum;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ScrmPersonalWxSaasRobotLoginDomainService {

    @Resource
    private ScrmPersonalWxRobotLoginLogMapper wxRobotLoginLogMapper;

    @Resource
    private ScrmPersonalWxRobotClusterMapper robotClusterMapper;

    @Resource
    private ScrmPersonalWxRobotInfoMapper robotInfoMapper;

    @Resource
    private ScrmPersonalWxCallbackMsgMapper personalWxCallbackMsgMapper;

    @Resource
    private ScrmPersonalWxUserDomainService wxUserDomainService;

    @Autowired
    private EduDaxiangSendService eduDaxiangSendService;

    @Autowired
    private RedisStoreClient redisClient;

    @Resource
    private ScrmPersonalWxUserInfoMapper personalWxUserInfoMapper;
    @Resource
    private ScrmPersonalWxRobotInfoCustomMapper personalWxRobotInfoCustomMapper;

    public boolean deleteRobot(String robotSerialNo) {
        if (StringUtils.isBlank(robotSerialNo)) {
            return false;
        }
        ScrmPersonalWxRobotInfo robotInfo = getRobotInfo(robotSerialNo);
        if (robotInfo == null) {
            log.info("deleteRobot robotSerialNo" + robotSerialNo + " not found");
            return false;
        }
        robotInfo.setValid(PersonalIsBooleanStrEnum.FALSE.getDesc());
        robotInfo.setVcReason(PersonalRobotStatusEnum.RECYCLING.getDesc());
        robotInfo.setUpdateTime(new Date());
        robotInfo.setStatus(PersonalRobotStatusEnum.RECYCLING.getCode());
        robotInfo.setClusterNo(null);
        robotInfoMapper.updateByPrimaryKey(robotInfo);
        return true;
    }

    public ScrmPersonalWxRobotInfo getRobotInfo(String robotSerialNo) {
        List<ScrmPersonalWxRobotInfo> robotInfos = wxUserDomainService.queryRobotBySerialNo(robotSerialNo);
        return CollectionUtils.isEmpty(robotInfos) ? null : robotInfos.get(0);
    }


    public ScrmPersonalWxRobotLoginLog queryHandleLogByRobot(String robotSerial) {
        try {
            ScrmPersonalWxRobotLoginLogExample example = new ScrmPersonalWxRobotLoginLogExample();
            example.createCriteria().andRobotSerialNoEqualTo(robotSerial).andHandlerIsNotNull();
            example.setOrderByClause("id desc");
            List<ScrmPersonalWxRobotLoginLog> robotLoginLogList = wxRobotLoginLogMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(robotLoginLogList)) {
                return null;
            }
            return robotLoginLogList.get(0);
        } catch (Exception e) {
            log.error("queryHandleLogByRobot has exception", e);
            return null;
        }
    }


}
