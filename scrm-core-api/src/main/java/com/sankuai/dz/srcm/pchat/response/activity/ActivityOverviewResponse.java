package com.sankuai.dz.srcm.pchat.response.activity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/18 18:17
 * @Version v1.0.0
 */
@Data
public class ActivityOverviewResponse implements Serializable {

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动状态
     */
    private String activityState;

    /**
     * 活动状态值
     */
    private String activityStateValue;
    /**
     * 获奖人数
     */
    private Long rewarderCount;

    /**
     * 关联群数
     */
    private Long groupCount;

    /**
     * 裂变率
     */
    private String fissionRate;

    /**
     * 裂变成员数
     */
    private Integer fissionMemberCount;

    /**
     * 参与成员数
     */
    private Integer memberCount;

    /**
     * 结束时间 时间戳
     */
    private Date endDate;

    public Long getRewarderCount() {
        return rewarderCount == null ? 0L : rewarderCount;
    }

    public Long getGroupCount() {
        return groupCount == null ? 0L : groupCount;
    }

    public String getFissionRate() {
        int seedMemberCount = getMemberCount() - getFissionMemberCount();
        if (seedMemberCount <= 0) {
            return "0";
        }
        BigDecimal value = BigDecimal.valueOf(getFissionMemberCount()).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(seedMemberCount), 1, RoundingMode.HALF_UP);
        return value.toString();
    }

    public Integer getFissionMemberCount() {
        return fissionMemberCount == null ? 0 : fissionMemberCount;
    }

    public Integer getMemberCount() {
        return memberCount == null ? 0 : memberCount;
    }

}