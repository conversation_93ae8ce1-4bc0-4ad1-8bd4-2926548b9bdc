package com.sankuai.scrm.core.service.user.predict.service;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.user.dto.UserHesitationResult;
import com.sankuai.dz.srcm.user.pridict.service.UserHesitationAnalysisService;
import com.sankuai.scrm.core.service.user.dal.entity.userView.BusinessScoreConfig;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserIntendVisitView;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserTradeView;
import com.sankuai.scrm.core.service.user.util.BusinessScoreConfigUtil;
import com.sankuai.scrm.core.service.user.util.UserDataFetchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

@MdpPigeonServer
@Slf4j
public class UserHesitationAnalysisServiceImpl implements UserHesitationAnalysisService {
    
    @Autowired
    private UserDataFetchUtil userDataFetchUtil;
    
    @Autowired
    private BusinessScoreConfigUtil businessScoreConfigUtil;
    
    @Override
    public UserHesitationResult analyzeUserHesitation(String userPk, String appId) {
        try {
            // 参数校验
            if (userPk == null || userPk.trim().isEmpty()) {
                log.error("用户主键不能为空: userPk={}", userPk);
                return null;
            }
            
            // 1. 通过工具类获取用户数据
            UserDataFetchUtil.UserDataResult userDataResult = userDataFetchUtil.fetchUserData(userPk, appId);
            
            if (!userDataResult.isSuccess()) {
                log.error("获取用户数据失败: userPk={}, appId={}, error={}", userPk, appId, userDataResult.getErrorMessage());
                return null;
            }
            
            UserTradeView tradeData = userDataResult.getTradeData();
            UserIntendVisitView visitData = userDataResult.getVisitData();
            UserSearchView searchData = userDataResult.getSearchData();
            
            // 2. 获取业务配置
            BusinessScoreConfig config = businessScoreConfigUtil.getBusinessConfig(appId);
            if (config == null) {
                return null;
            }
            
            if (config.getHesitationConfig() == null) {
                return null;
            }
            
            // 3. 计算各项犹豫行为特征
            Integer noRecentPurchase = calculateNoRecentPurchase(tradeData, visitData);
            Integer repeatSearch = calculateRepeatSearch(searchData, config.getHesitationConfig());
            Integer browseNoBuy = calculateBrowseNoBuy(tradeData, visitData);
            Integer priceHesitation = calculatePriceHesitation(tradeData, visitData, config.getHesitationConfig());

            // 4. 综合犹豫评分（基于配置权重）
            BigDecimal totalHesitationScore = BigDecimal.valueOf(noRecentPurchase)
                    .multiply(config.getHesitationConfig().getNoRecentPurchaseWeight() == null ? BigDecimal.valueOf(0.25) : config.getHesitationConfig().getNoRecentPurchaseWeight())
                    .add(BigDecimal.valueOf(repeatSearch)
                            .multiply(config.getHesitationConfig().getRepeatSearchWeight() == null ? BigDecimal.valueOf(0.25) : config.getHesitationConfig().getRepeatSearchWeight()))
                    .add(BigDecimal.valueOf(browseNoBuy)
                            .multiply(config.getHesitationConfig().getBrowseNoBuyWeight() == null ? BigDecimal.valueOf(0.25) : config.getHesitationConfig().getBrowseNoBuyWeight()))
                    .add(BigDecimal.valueOf(priceHesitation)
                            .multiply(config.getHesitationConfig().getPriceHesitationWeight() == null ? BigDecimal.valueOf(0.25) : config.getHesitationConfig().getPriceHesitationWeight()));

            // 保留两位小数
            totalHesitationScore = totalHesitationScore.setScale(2, RoundingMode.HALF_UP);
            //5. 组装结果
            return UserHesitationResult.builder()
                    .userPk(userPk)
                    .appId(appId)
                    .noRecentPurchase(noRecentPurchase)
                    .repeatSearch(repeatSearch)
                    .browseNoBuy(browseNoBuy)
                    .priceHesitation(priceHesitation)
                    .totalHesitationScore(totalHesitationScore)
                    .build();
                    
        } catch (Exception e) {
            log.error("分析用户犹豫行为异常: userPk={}, appId={}", userPk, appId, e);
            return null;
        }
    }

    /**
     * 近期无购买但有浏览
     */
    private int calculateNoRecentPurchase(UserTradeView tradeData, UserIntendVisitView visitData) {
        if (tradeData == null || visitData == null) {
            return 0;
        }
        
        Long trade7dCnt = Optional.ofNullable(tradeData.getTrade7dCnt()).orElse(0L);
        Long visit7dPv = Optional.ofNullable(visitData.getVisit7dPv()).orElse(0L);

        return (trade7dCnt == 0 && visit7dPv > 0) ? 1 : 0;
    }

    /**
     * 重复搜索行为（基于配置）
     */
    private int calculateRepeatSearch(UserSearchView searchData, BusinessScoreConfig.HesitationConfig config) {
        try {
            if (searchData == null || config == null) {
                return 0;
            }
            
            Long search7dCnt = Optional.ofNullable(searchData.getQv7d()).orElse(0L);
            Long search7dKeywordCnt = Optional.ofNullable(searchData.getSearchKeywordCnt7d()).orElse(0L);

            if (search7dKeywordCnt == 0) {
                return 0;
            }

            if (config.getRepeatSearchMultiplier() == null) {
                return (search7dCnt > search7dKeywordCnt * 3.0) ? 1 : 0;
            }

            return (search7dCnt > search7dKeywordCnt * config.getRepeatSearchMultiplier().doubleValue()) ? 1 : 0;
        } catch (Exception e) {
            log.error("计算重复搜索行为异常", e);
            return 0;
        }
    }

    /**
     * 浏览但未购买时间间隔
     */
    private int calculateBrowseNoBuy(UserTradeView tradeData, UserIntendVisitView visitData) {
        if (tradeData == null || visitData == null) {
            return 0;
        }
        
        Integer last2tdVisitDays = Optional.ofNullable(visitData.getLast2tdVisitDays()).orElse(Integer.MAX_VALUE);
        Integer last2tdBuySucDays = Optional.ofNullable(tradeData.getLast2tdBuySucDays()).orElse(Integer.MAX_VALUE);

        return (last2tdVisitDays < last2tdBuySucDays) ? 1 : 0;
    }

    /**
     * 商品价格犹豫（基于配置）
     */
    private int calculatePriceHesitation(UserTradeView tradeData, UserIntendVisitView visitData,
                                         BusinessScoreConfig.HesitationConfig config) {
        try {
            if (tradeData == null || visitData == null || config == null) {
                return 0;
            }
            
            BigDecimal visit7dProductAvgAmt = visitData.getVisit7dProductAvgAmt();
            if (visit7dProductAvgAmt == null) {
                return 0;
            }

            // 根据配置获取历史交易周期的数据
            BigDecimal tradeAmt;
            Long tradeCnt;

            if (config.getPriceHesitationTradePeriod() == 30) {
                tradeAmt = tradeData.getTrade30dAmt();
                tradeCnt = tradeData.getTrade30dCnt();
            } else if (config.getPriceHesitationTradePeriod() == 15) {
                tradeAmt = tradeData.getTrade15dAmt();
                tradeCnt = tradeData.getTrade15dCnt();
            } else if (config.getPriceHesitationTradePeriod() == 60) {
                tradeAmt = tradeData.getTrade60dAmt();
                tradeCnt = tradeData.getTrade60dCnt();
            } else {
                // 默认使用30天数据
                tradeAmt = tradeData.getTrade30dAmt();
                tradeCnt = tradeData.getTrade30dCnt();
            }

            if (tradeCnt == null || tradeCnt == 0 || 
                tradeAmt == null || tradeAmt.equals(BigDecimal.ZERO)) {
                return 0;
            }

            if (config.getPriceHesitationMultiplier() == null) {
                BigDecimal avgTradeAmt = tradeAmt.divide(new BigDecimal(tradeCnt), 2, RoundingMode.HALF_UP);
                BigDecimal hesitationThreshold = avgTradeAmt.multiply(BigDecimal.valueOf(2.0));
                return visit7dProductAvgAmt.compareTo(hesitationThreshold) > 0 ? 1 : 0;
            }

            BigDecimal avgTradeAmt = tradeAmt.divide(new BigDecimal(tradeCnt), 2, RoundingMode.HALF_UP);
            BigDecimal hesitationThreshold = avgTradeAmt.multiply(config.getPriceHesitationMultiplier());

            return visit7dProductAvgAmt.compareTo(hesitationThreshold) > 0 ? 1 : 0;
        } catch (Exception e) {
            log.error("计算价格犹豫行为异常", e);
            return 0;
        }
    }
}
