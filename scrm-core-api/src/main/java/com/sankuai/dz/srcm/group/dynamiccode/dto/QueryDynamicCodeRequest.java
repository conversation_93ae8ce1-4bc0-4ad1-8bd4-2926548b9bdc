package com.sankuai.dz.srcm.group.dynamiccode.dto;

import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

@Data
public class QueryDynamicCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分页查询的页码，从1开始计。
     */
    private Integer pageNo;
    /**
     * 分页查询的分页大小，0<pageSize<=50。
     */
    private Integer pageSize;
    /**
     * 业务线id
     */
    private String appId;
    /**
     * 渠道来源的id
     */
    private Long channelId;
    /**
     *   字段: scene
     *   说明: 渠道场景
     */
    private int scene;
    /**
     * 群活码名称。模糊匹配。
     */
    private String dynamicCodeName;
    /**
     * 群活码状态。 0：无效。 1：有效。 999：全部。
     *
     * @see DynamicCodeStatusEnum
     */
    private Integer dynamicCodeStatus;
    /**
     * 是否开启满员自动创建新群 0：未开启 1：已开启 999：全部
     */
    private Integer groupAutoIncrement;
    /**
     * 创建时间的范围（起始）
     */
    private Date createTimeFrom;
    /**
     * 创建时间的范围（结束）
     */
    private Date createTimeTo;
    /**
     * 是否查询群活码关联的群的信息
     */
    private Boolean includeGroupInfo;

    @Transient
    public boolean isParamIllegal() {
        return (pageNo == null || pageNo < 1)
                || (pageSize == null || pageSize < 1 || pageSize > 50)
                || StringUtils.isBlank(appId)
                || channelId == null
                || DynamicCodeStatusEnum.ofCode(dynamicCodeStatus) == DynamicCodeStatusEnum.UNKNOWN
                || (createTimeFrom != null && createTimeTo != null && createTimeFrom.after(createTimeTo));
    }
}
