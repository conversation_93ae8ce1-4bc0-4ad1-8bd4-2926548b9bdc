package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeGroupResultDTO;
import com.sankuai.dz.srcm.pchat.request.SendGroupChatMessagesRequest;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxMsgStateEnum;
import com.sankuai.dz.srcm.pchat.tanjing.GroupMessageService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import com.sankuai.scrm.core.service.pchat.dto.ScrmPersonalWxMsgDTO;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxMsg;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import com.sankuai.scrm.core.service.pchat.enums.TuseMsgTypeEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TuseMsgProcessorSendInstantMsgTest {

    @InjectMocks
    private TuseMsgProcessor tuseMsgProcessor;

    @Mock
    private GroupMessageService groupMessageService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    private SendGroupChatMessagesRequest request;

    private List<ScrmPersonalWxMsgDTO> wxMsgDTOS;

    private Method processSendingVideSeoMethod;

    @Before
    public void setUp() throws Exception {
        request = new SendGroupChatMessagesRequest();
        request.setVcMerchantNo("test_merchant");
        request.setVcRobotSerialNo("test_robot");
        request.setVcChatRoomSerialNo("test_chatroom");
        request.setVcRelaSerialNo("test_serial");
        wxMsgDTOS = new ArrayList<>();
        processSendingVideSeoMethod = TuseMsgProcessor.class.getDeclaredMethod("processSendingVideSeo", List.class);
        processSendingVideSeoMethod.setAccessible(true);
    }

    /**
     * Helper method to invoke the private sendInstantMsg method using reflection.
     */
    private void invokeSendInstantMsg(SendGroupChatMessagesRequest request, List<ScrmPersonalWxMsgDTO> wxMsgDTOS) throws Exception {
        Method sendInstantMsgMethod = TuseMsgProcessor.class.getDeclaredMethod("sendInstantMsg", SendGroupChatMessagesRequest.class, List.class);
        sendInstantMsgMethod.setAccessible(true);
        try {
            sendInstantMsgMethod.invoke(tuseMsgProcessor, request, wxMsgDTOS);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            if (cause instanceof PChatBusinessException) {
                throw (PChatBusinessException) cause;
            }
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            }
            throw new RuntimeException(cause);
        }
    }

    private SendGroupChatMessagesRequest.Data createRequestData(int msgType, Integer isHit, String content) {
        SendGroupChatMessagesRequest.Data data = new SendGroupChatMessagesRequest.Data();
        data.setNMsgType(msgType);
        data.setNIsHit(isHit);
        data.setMsgContent(content);
        data.setNMsgNum(1);
        return data;
    }

    /**
     * 测试场景：wxMsgDTOS 为空
     */
    @Test
    public void testSendInstantMsg_EmptyWxMsgDTOS() throws Throwable {
        // arrange
        wxMsgDTOS.clear();
        request.setData(new SendGroupChatMessagesRequest.Data[] {});
        // act
        invokeSendInstantMsg(request, wxMsgDTOS);
        // assert
        verify(groupMessageService, never()).sendGroupChatMessages(any());
        verify(groupManageDomainService, never()).updateWxMsgList(any());
    }
}
