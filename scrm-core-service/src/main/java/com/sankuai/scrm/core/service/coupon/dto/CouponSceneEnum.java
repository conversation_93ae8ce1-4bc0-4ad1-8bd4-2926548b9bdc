package com.sankuai.scrm.core.service.coupon.dto;

/**
 * 券场景枚举
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
public enum CouponSceneEnum {
    
    /**
     * AI智能跟进
     */
    AI_INTELLIGENT_FOLLOW("AI_INTELLIGENT_FOLLOW", "AI智能跟进"),
    
    /**
     * 流程编排
     */
    PROCESS_ORCHESTRATION("PROCESS_ORCHESTRATION", "流程编排"),
    
    /**
     * Next平台
     */
    NEXT_PLATFORM("NEXT_PLATFORM", "Next平台"),
    
    /**
     * 其他
     */
    OTHER("OTHER", "其他");
    
    private final String code;
    private final String desc;
    
    CouponSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
} 