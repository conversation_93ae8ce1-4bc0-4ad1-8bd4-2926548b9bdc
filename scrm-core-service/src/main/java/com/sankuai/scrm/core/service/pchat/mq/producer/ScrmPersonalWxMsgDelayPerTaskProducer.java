package com.sankuai.scrm.core.service.pchat.mq.producer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IDelayFutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Properties;
import java.util.function.Consumer;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/17 15:48
 * @Version v1.0.0
 */
@Service
@Slf4j
public class ScrmPersonalWxMsgDelayPerTaskProducer implements InitializingBean {
    private IProducerProcessor<?, String> producer;

    public void sendDelayTask(DelayPayload delaySendTask, long delaySec, Consumer successCallback, Consumer<Throwable> failureCallback) {
        try {
            delaySec = delaySec <= 0 ? 5 : delaySec;
            if (delaySendTask != null) {
                delaySendTask.setDelaySec(delaySec);
                sendMQDelayTask(delaySendTask.toJsonStr(), delaySec, successCallback, failureCallback);
            }
        } catch (Exception e) {
            log.error("ScrmPersonalWxMsgDelayPerTaskProducer消息发送失败：" + e.getMessage());
            if (failureCallback != null) {
                failureCallback.accept(e);
            }
        }
    }


    private void sendMQDelayTask(String msg, long delaySec, Consumer successCallback, Consumer<Throwable> failureCallback) {
        try {
            log.info("发送消息至mq");
            producer.sendAsyncDelayMessage(msg, delaySec * 1000, new IDelayFutureCallback() {
                @Override
                public void onSuccess(AsyncDelayProducerResult asyncDelayProducerResult) {
                    Cat.logMetricForCount("ScrmPersonalWxMsgDelayPerTaskProducer.sendMQDelayTask.success");
                }

                @Override
                public void onFailure(AsyncDelayProducerResult asyncDelayProducerResult) {
                    Cat.logMetricForCount("ScrmPersonalWxMsgDelayPerTaskProducer.sendMQDelayTask.fail");
                }
            });
            if (successCallback != null) {
                successCallback.accept(null);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (failureCallback != null) {
                failureCallback.accept(e);
            }
        }
    }

    private void sendMQDelayTask(String msg, long delaySec) {
        try {
            log.info("发送消息至mq");
            producer.sendAsyncDelayMessage(msg, delaySec * 1000, new IDelayFutureCallback() {
                @Override
                public void onSuccess(AsyncDelayProducerResult asyncDelayProducerResult) {
                    Cat.logMetricForCount("ScrmPersonalWxMsgDelayPerTaskProducer.sendAsyncMessage.success");
                }

                @Override
                public void onFailure(AsyncDelayProducerResult asyncDelayProducerResult) {
                    Cat.logMetricForCount("ScrmPersonalWxMsgDelayPerTaskProducer.sendAsyncMessage.fail");
                }
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildDelayProduceFactory(properties, "scrm.pchat.msg.delay.per.task");
    }

    @FunctionalInterface
    public interface Call {
        void call();
    }


}
