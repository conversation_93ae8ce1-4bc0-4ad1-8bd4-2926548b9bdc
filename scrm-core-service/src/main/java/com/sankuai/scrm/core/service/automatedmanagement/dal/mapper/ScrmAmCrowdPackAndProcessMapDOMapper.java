package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmAmCrowdPackAndProcessMapDOMapper extends MybatisBaseMapper<ScrmAmCrowdPackAndProcessMapDO, ScrmAmCrowdPackAndProcessMapDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmCrowdPackAndProcessMapDO> list);
}