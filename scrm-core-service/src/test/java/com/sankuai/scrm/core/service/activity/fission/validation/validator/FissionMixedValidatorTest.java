package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionMixedValidatorTest {

    @InjectMocks
    private FissionMixedValidator validator;

    private boolean invokePrivateMethod(String methodName, List<RewardInfoRequest> input) throws Exception {
        Method method = FissionMixedValidator.class.getDeclaredMethod(methodName, List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(validator, input);
    }

    @Test
    public void testCheckStageParticipants_EmptyList_ReturnsTrue() throws Throwable {
        List<RewardInfoRequest> emptyList = Collections.emptyList();
        boolean result = invokePrivateMethod("checkStageParticipants", emptyList);
        assertTrue(result);
    }

    @Test
    public void testCheckStageParticipants_SingleElement_ReturnsTrue() throws Throwable {
        RewardInfoRequest reward = mock(RewardInfoRequest.class);
        List<RewardInfoRequest> singleList = Collections.singletonList(reward);
        boolean result = invokePrivateMethod("checkStageParticipants", singleList);
        assertTrue(result);
    }

    @Test
    public void testCheckStageParticipants_ValidIncreasing_ReturnsTrue() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        when(reward1.getInvitationNum()).thenReturn(5);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(2);
        when(reward2.getInvitationNum()).thenReturn(10);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        when(reward3.getStage()).thenReturn(3);
        when(reward3.getInvitationNum()).thenReturn(15);
        List<RewardInfoRequest> validList = Arrays.asList(reward1, reward2, reward3);
        boolean result = invokePrivateMethod("checkStageParticipants", validList);
        assertTrue(result);
    }

    @Test
    public void testCheckStageParticipants_InvalidNotIncreasing_ReturnsFalse() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        when(reward1.getInvitationNum()).thenReturn(5);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(2);
        when(reward2.getInvitationNum()).thenReturn(3);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        when(reward3.getStage()).thenReturn(3);
        List<RewardInfoRequest> invalidList = Arrays.asList(reward1, reward2, reward3);
        boolean result = invokePrivateMethod("checkStageParticipants", invalidList);
        assertFalse(result);
    }

    @Test
    public void testCheckStageParticipants_InvalidEqualValues_ReturnsFalse() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        when(reward1.getInvitationNum()).thenReturn(5);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(2);
        when(reward2.getInvitationNum()).thenReturn(5);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        when(reward3.getStage()).thenReturn(3);
        List<RewardInfoRequest> invalidList = Arrays.asList(reward1, reward2, reward3);
        boolean result = invokePrivateMethod("checkStageParticipants", invalidList);
        assertFalse(result);
    }

    @Test
    public void testCheckStageParticipants_UnsortedButValid_ReturnsTrue() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(3);
        when(reward1.getInvitationNum()).thenReturn(15);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(1);
        when(reward2.getInvitationNum()).thenReturn(5);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        when(reward3.getStage()).thenReturn(2);
        when(reward3.getInvitationNum()).thenReturn(10);
        List<RewardInfoRequest> unsortedList = Arrays.asList(reward1, reward2, reward3);
        // Sort the list before invoking the method to ensure the method's expectations are met
        Collections.sort(unsortedList, (r1, r2) -> r1.getStage().compareTo(r2.getStage()));
        boolean result = invokePrivateMethod("checkStageParticipants", unsortedList);
        assertTrue(result);
    }

    @Test
    public void testCheckStageParticipants_WithNullElement_ThrowsException() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        List<RewardInfoRequest> listWithNull = Arrays.asList(reward1, null, reward3);
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod("checkStageParticipants", listWithNull);
        });
        // Verify the cause is NullPointerException
        assertTrue(exception.getCause() instanceof NullPointerException);
    }
}
