package com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration;

import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.group.dto.GroupListDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/16
 */
@Data
public class ScrmProcessOrchestrationDTO implements Serializable {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 任务名称
     */
    private String name;

    /**
     *   字段: process_orchestration_type
     *   说明: 任务类型(人群包更新任务/定时任务/周期任务)
     * @see  ScrmProcessOrchestrationTaskTypeEnum
     */
    private Byte processOrchestrationType;

    /**
     *   字段: cron
     *   说明: cron表达式 定时
     */
    private String cron = StringUtils.EMPTY;

    /**
     *   字段: begin_time
     *   说明: 任务有效期开始时间
     */
    private Date beginTime = new Date();

    /**
     *   字段: end_time
     *   说明: 任务有效期结束时间
     */
    private Date endTime = new Date();

    /**
     *   字段: status
     *   说明: 任务状态(启用/未启用)
     */
    private Byte status = 1;

    /**
     *   字段: valid_version
     *   说明: 目前生效流程编排版本
     */
    private String validVersion = StringUtils.EMPTY;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: creator_id
     *   说明: 创建者id
     */
    private String creatorId;

    /**
     *   字段: last_updater_id
     *   说明: 最后更新者id
     */
    private String lastUpdaterId;

    /**
     *   字段: participationRestrict
     *   说明: 外部用户参与次数 false 可重复参与 true 不可重复参与
     */
    private boolean participationRestrict = true;

    /**
     *   字段: participation_restrictions_cycle
     *   说明: 参与限制周期 单位/天
     */
    private Byte participationRestrictionsCycle = 0;

    /**
     *   字段: participation_restrictions_times
     *   说明: 参与限制周期 周期内最大次数限制
     */
    private Byte participationRestrictionsTimes = 0;

    /**
     *   字段: app_id
     *   说明: 业务id
     */
    private String appId;

    /**
     *   字段: preview_pic
     *   说明: 预览图
     */
    private String previewPic = StringUtils.EMPTY;

    /**
     *   字段: cron_comment
     *   说明: cron表达式注释,一般存储原始json 或者yyyy-MM-dd HH:mm:ss格式时间
     */
    private String cronComment = StringUtils.EMPTY;

    /**
     * 执行人员类型：1人工 2机器人
     */
    private Integer executorType = 1;

    /**
     *   字段: effective_time_start
     *   说明: 任务每日生效开始时间 格式HH:mm:ss
     */
    private String effectiveTimeStart;

    /**
     *   字段: effective_time_end
     *   说明: 任务每日生效结束时间 格式HH:mm:ss
     */
    private String effectiveTimeEnd;

    /**
     *   字段: alarmThreshold
     *   说明: 告警阈值
     */
    private String alarmthreshold;

    /**
     *   字段: alarmReceiver
     *   说明: 告警接收人
     */
    private String alarmreceiver;

    /**************************自定义字段************************************************/

    /**
     * 参与执行人列表
     */
    private List<ScrmProcessOrchestrationExecutorDTO> executorList = new ArrayList<>();

    /**
     * 实时场景id
     */
    private Long realTimeSceneId;

    /**
     * 场景类型
     * 实时场景类型：0未知 1实时感知场景
     */
    private Byte sceneType = (byte)0;

    /**
     * 人群包类型 0：全部 1：人群包客户 2：自定义筛选客户 3: 微信群
     */
    private Integer crowdPackType;

    /**
     * 人群包信息
     */
    private List<Long> crowdPackIdList;

    /**
     * 微信群id
     */
    private List<String> groupIdList;

    private List<GroupListDTO> groupInfoList;

    private ScrmCrowdPackUpdateStrategyInfoDTO crowdPackUpdateStrategyInfoDTO;

    /**
     * 人群包信息
     */
    // private List<ScrmCrowdPackDTO> crowdPackList;

    /**
     * 目标信息
     */
    private ScrmProcessOrchestrationGoalDTO goalDTO;

    /**
     * 负面结果信息
     */
    private ScrmProcessOrchestrationGoalDTO negativeGoalDTO;

    // private List<ScrmProcessOrchestrationNodeDTO> processOrchestrationNodeDTOList;

    /**
     * 节点信息媒介
     */
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    /**
     * 执行计划
     */
    private ScrmProcessOrchestrationExecutePlanDTO executePlanDTO;

    /**
     * 是否是demo场景
     */
    private boolean demoScene = false;
    /**
     * demo专用企微标签，其它场景勿用
     */
    private List<String> demoCorpTagList;

    private boolean aiScene = false;

    private IntelligentFollowResultDTO aiSceneContent;
}
