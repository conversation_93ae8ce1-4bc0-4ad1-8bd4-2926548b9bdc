package com.sankuai.scrm.core.service.external.contact.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ExternalContactBaseInfoMapper extends MybatisBaseMapper<ExternalContactBaseInfo, ExternalContactBaseInfoExample, Long> {
    int batchInsert(@Param("list") List<ExternalContactBaseInfo> list);
}