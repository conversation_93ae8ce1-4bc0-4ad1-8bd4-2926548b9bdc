package com.sankuai.scrm.core.service.pchat.adapter.service;

import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivity;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberInviteCountDTO;

import java.util.List;

public interface ActivityProcessor {

    List<GroupMemberInviteCountDTO> queryInviteCount(ScrmPersonalWxActivity activity, List<String> wxIdList, List<Long> groupKeyList);
}
