package com.sankuai.scrm.core.service.workorder.customer.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerComplaintProcessRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CustomerComplaintProcessRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CustomerComplaintProcessRecordExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CustomerComplaintProcessRecordExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CustomerComplaintProcessRecordExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdIsNull() {
            addCriterion("word_order_id is null");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdIsNotNull() {
            addCriterion("word_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdEqualTo(Long value) {
            addCriterion("word_order_id =", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdNotEqualTo(Long value) {
            addCriterion("word_order_id <>", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdGreaterThan(Long value) {
            addCriterion("word_order_id >", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("word_order_id >=", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdLessThan(Long value) {
            addCriterion("word_order_id <", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("word_order_id <=", value, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdIn(List<Long> values) {
            addCriterion("word_order_id in", values, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdNotIn(List<Long> values) {
            addCriterion("word_order_id not in", values, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdBetween(Long value1, Long value2) {
            addCriterion("word_order_id between", value1, value2, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andWordOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("word_order_id not between", value1, value2, "wordOrderId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeIsNull() {
            addCriterion("solution_type is null");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeIsNotNull() {
            addCriterion("solution_type is not null");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeEqualTo(Integer value) {
            addCriterion("solution_type =", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeNotEqualTo(Integer value) {
            addCriterion("solution_type <>", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeGreaterThan(Integer value) {
            addCriterion("solution_type >", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("solution_type >=", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeLessThan(Integer value) {
            addCriterion("solution_type <", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("solution_type <=", value, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeIn(List<Integer> values) {
            addCriterion("solution_type in", values, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeNotIn(List<Integer> values) {
            addCriterion("solution_type not in", values, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeBetween(Integer value1, Integer value2) {
            addCriterion("solution_type between", value1, value2, "solutionType");
            return (Criteria) this;
        }

        public Criteria andSolutionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("solution_type not between", value1, value2, "solutionType");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionIsNull() {
            addCriterion("revisit_solution is null");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionIsNotNull() {
            addCriterion("revisit_solution is not null");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionEqualTo(String value) {
            addCriterion("revisit_solution =", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionNotEqualTo(String value) {
            addCriterion("revisit_solution <>", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionGreaterThan(String value) {
            addCriterion("revisit_solution >", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionGreaterThanOrEqualTo(String value) {
            addCriterion("revisit_solution >=", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionLessThan(String value) {
            addCriterion("revisit_solution <", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionLessThanOrEqualTo(String value) {
            addCriterion("revisit_solution <=", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionLike(String value) {
            addCriterion("revisit_solution like", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionNotLike(String value) {
            addCriterion("revisit_solution not like", value, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionIn(List<String> values) {
            addCriterion("revisit_solution in", values, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionNotIn(List<String> values) {
            addCriterion("revisit_solution not in", values, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionBetween(String value1, String value2) {
            addCriterion("revisit_solution between", value1, value2, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andRevisitSolutionNotBetween(String value1, String value2) {
            addCriterion("revisit_solution not between", value1, value2, "revisitSolution");
            return (Criteria) this;
        }

        public Criteria andChatRecordListIsNull() {
            addCriterion("chat_record_list is null");
            return (Criteria) this;
        }

        public Criteria andChatRecordListIsNotNull() {
            addCriterion("chat_record_list is not null");
            return (Criteria) this;
        }

        public Criteria andChatRecordListEqualTo(String value) {
            addCriterion("chat_record_list =", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListNotEqualTo(String value) {
            addCriterion("chat_record_list <>", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListGreaterThan(String value) {
            addCriterion("chat_record_list >", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListGreaterThanOrEqualTo(String value) {
            addCriterion("chat_record_list >=", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListLessThan(String value) {
            addCriterion("chat_record_list <", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListLessThanOrEqualTo(String value) {
            addCriterion("chat_record_list <=", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListLike(String value) {
            addCriterion("chat_record_list like", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListNotLike(String value) {
            addCriterion("chat_record_list not like", value, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListIn(List<String> values) {
            addCriterion("chat_record_list in", values, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListNotIn(List<String> values) {
            addCriterion("chat_record_list not in", values, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListBetween(String value1, String value2) {
            addCriterion("chat_record_list between", value1, value2, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andChatRecordListNotBetween(String value1, String value2) {
            addCriterion("chat_record_list not between", value1, value2, "chatRecordList");
            return (Criteria) this;
        }

        public Criteria andImageListIsNull() {
            addCriterion("image_list is null");
            return (Criteria) this;
        }

        public Criteria andImageListIsNotNull() {
            addCriterion("image_list is not null");
            return (Criteria) this;
        }

        public Criteria andImageListEqualTo(String value) {
            addCriterion("image_list =", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListNotEqualTo(String value) {
            addCriterion("image_list <>", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListGreaterThan(String value) {
            addCriterion("image_list >", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListGreaterThanOrEqualTo(String value) {
            addCriterion("image_list >=", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListLessThan(String value) {
            addCriterion("image_list <", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListLessThanOrEqualTo(String value) {
            addCriterion("image_list <=", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListLike(String value) {
            addCriterion("image_list like", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListNotLike(String value) {
            addCriterion("image_list not like", value, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListIn(List<String> values) {
            addCriterion("image_list in", values, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListNotIn(List<String> values) {
            addCriterion("image_list not in", values, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListBetween(String value1, String value2) {
            addCriterion("image_list between", value1, value2, "imageList");
            return (Criteria) this;
        }

        public Criteria andImageListNotBetween(String value1, String value2) {
            addCriterion("image_list not between", value1, value2, "imageList");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionIsNull() {
            addCriterion("follow_solution is null");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionIsNotNull() {
            addCriterion("follow_solution is not null");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionEqualTo(String value) {
            addCriterion("follow_solution =", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionNotEqualTo(String value) {
            addCriterion("follow_solution <>", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionGreaterThan(String value) {
            addCriterion("follow_solution >", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionGreaterThanOrEqualTo(String value) {
            addCriterion("follow_solution >=", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionLessThan(String value) {
            addCriterion("follow_solution <", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionLessThanOrEqualTo(String value) {
            addCriterion("follow_solution <=", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionLike(String value) {
            addCriterion("follow_solution like", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionNotLike(String value) {
            addCriterion("follow_solution not like", value, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionIn(List<String> values) {
            addCriterion("follow_solution in", values, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionNotIn(List<String> values) {
            addCriterion("follow_solution not in", values, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionBetween(String value1, String value2) {
            addCriterion("follow_solution between", value1, value2, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFollowSolutionNotBetween(String value1, String value2) {
            addCriterion("follow_solution not between", value1, value2, "followSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionIsNull() {
            addCriterion("final_solution is null");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionIsNotNull() {
            addCriterion("final_solution is not null");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionEqualTo(String value) {
            addCriterion("final_solution =", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionNotEqualTo(String value) {
            addCriterion("final_solution <>", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionGreaterThan(String value) {
            addCriterion("final_solution >", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionGreaterThanOrEqualTo(String value) {
            addCriterion("final_solution >=", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionLessThan(String value) {
            addCriterion("final_solution <", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionLessThanOrEqualTo(String value) {
            addCriterion("final_solution <=", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionLike(String value) {
            addCriterion("final_solution like", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionNotLike(String value) {
            addCriterion("final_solution not like", value, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionIn(List<String> values) {
            addCriterion("final_solution in", values, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionNotIn(List<String> values) {
            addCriterion("final_solution not in", values, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionBetween(String value1, String value2) {
            addCriterion("final_solution between", value1, value2, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andFinalSolutionNotBetween(String value1, String value2) {
            addCriterion("final_solution not between", value1, value2, "finalSolution");
            return (Criteria) this;
        }

        public Criteria andHandlerIsNull() {
            addCriterion("handler is null");
            return (Criteria) this;
        }

        public Criteria andHandlerIsNotNull() {
            addCriterion("handler is not null");
            return (Criteria) this;
        }

        public Criteria andHandlerEqualTo(String value) {
            addCriterion("handler =", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerNotEqualTo(String value) {
            addCriterion("handler <>", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerGreaterThan(String value) {
            addCriterion("handler >", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerGreaterThanOrEqualTo(String value) {
            addCriterion("handler >=", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerLessThan(String value) {
            addCriterion("handler <", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerLessThanOrEqualTo(String value) {
            addCriterion("handler <=", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerLike(String value) {
            addCriterion("handler like", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerNotLike(String value) {
            addCriterion("handler not like", value, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerIn(List<String> values) {
            addCriterion("handler in", values, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerNotIn(List<String> values) {
            addCriterion("handler not in", values, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerBetween(String value1, String value2) {
            addCriterion("handler between", value1, value2, "handler");
            return (Criteria) this;
        }

        public Criteria andHandlerNotBetween(String value1, String value2) {
            addCriterion("handler not between", value1, value2, "handler");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdIsNull() {
            addCriterion("dx_group_id is null");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdIsNotNull() {
            addCriterion("dx_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdEqualTo(Long value) {
            addCriterion("dx_group_id =", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdNotEqualTo(Long value) {
            addCriterion("dx_group_id <>", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdGreaterThan(Long value) {
            addCriterion("dx_group_id >", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dx_group_id >=", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdLessThan(Long value) {
            addCriterion("dx_group_id <", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("dx_group_id <=", value, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdIn(List<Long> values) {
            addCriterion("dx_group_id in", values, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdNotIn(List<Long> values) {
            addCriterion("dx_group_id not in", values, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdBetween(Long value1, Long value2) {
            addCriterion("dx_group_id between", value1, value2, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andDxGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("dx_group_id not between", value1, value2, "dxGroupId");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeIsNull() {
            addCriterion("revisit_end_time is null");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeIsNotNull() {
            addCriterion("revisit_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeEqualTo(Date value) {
            addCriterion("revisit_end_time =", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeNotEqualTo(Date value) {
            addCriterion("revisit_end_time <>", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeGreaterThan(Date value) {
            addCriterion("revisit_end_time >", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("revisit_end_time >=", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeLessThan(Date value) {
            addCriterion("revisit_end_time <", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("revisit_end_time <=", value, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeIn(List<Date> values) {
            addCriterion("revisit_end_time in", values, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeNotIn(List<Date> values) {
            addCriterion("revisit_end_time not in", values, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeBetween(Date value1, Date value2) {
            addCriterion("revisit_end_time between", value1, value2, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("revisit_end_time not between", value1, value2, "revisitEndTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutIsNull() {
            addCriterion("revisit_timeout is null");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutIsNotNull() {
            addCriterion("revisit_timeout is not null");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutEqualTo(Boolean value) {
            addCriterion("revisit_timeout =", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutNotEqualTo(Boolean value) {
            addCriterion("revisit_timeout <>", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutGreaterThan(Boolean value) {
            addCriterion("revisit_timeout >", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutGreaterThanOrEqualTo(Boolean value) {
            addCriterion("revisit_timeout >=", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutLessThan(Boolean value) {
            addCriterion("revisit_timeout <", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutLessThanOrEqualTo(Boolean value) {
            addCriterion("revisit_timeout <=", value, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutIn(List<Boolean> values) {
            addCriterion("revisit_timeout in", values, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutNotIn(List<Boolean> values) {
            addCriterion("revisit_timeout not in", values, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutBetween(Boolean value1, Boolean value2) {
            addCriterion("revisit_timeout between", value1, value2, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeoutNotBetween(Boolean value1, Boolean value2) {
            addCriterion("revisit_timeout not between", value1, value2, "revisitTimeout");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIsNull() {
            addCriterion("process_time is null");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIsNotNull() {
            addCriterion("process_time is not null");
            return (Criteria) this;
        }

        public Criteria andProcessTimeEqualTo(Date value) {
            addCriterion("process_time =", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotEqualTo(Date value) {
            addCriterion("process_time <>", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeGreaterThan(Date value) {
            addCriterion("process_time >", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("process_time >=", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeLessThan(Date value) {
            addCriterion("process_time <", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeLessThanOrEqualTo(Date value) {
            addCriterion("process_time <=", value, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeIn(List<Date> values) {
            addCriterion("process_time in", values, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotIn(List<Date> values) {
            addCriterion("process_time not in", values, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeBetween(Date value1, Date value2) {
            addCriterion("process_time between", value1, value2, "processTime");
            return (Criteria) this;
        }

        public Criteria andProcessTimeNotBetween(Date value1, Date value2) {
            addCriterion("process_time not between", value1, value2, "processTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeIsNull() {
            addCriterion("revisit_time is null");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeIsNotNull() {
            addCriterion("revisit_time is not null");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeEqualTo(Date value) {
            addCriterion("revisit_time =", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeNotEqualTo(Date value) {
            addCriterion("revisit_time <>", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeGreaterThan(Date value) {
            addCriterion("revisit_time >", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("revisit_time >=", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeLessThan(Date value) {
            addCriterion("revisit_time <", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeLessThanOrEqualTo(Date value) {
            addCriterion("revisit_time <=", value, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeIn(List<Date> values) {
            addCriterion("revisit_time in", values, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeNotIn(List<Date> values) {
            addCriterion("revisit_time not in", values, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeBetween(Date value1, Date value2) {
            addCriterion("revisit_time between", value1, value2, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andRevisitTimeNotBetween(Date value1, Date value2) {
            addCriterion("revisit_time not between", value1, value2, "revisitTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeIsNull() {
            addCriterion("follow_time is null");
            return (Criteria) this;
        }

        public Criteria andFollowTimeIsNotNull() {
            addCriterion("follow_time is not null");
            return (Criteria) this;
        }

        public Criteria andFollowTimeEqualTo(Date value) {
            addCriterion("follow_time =", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeNotEqualTo(Date value) {
            addCriterion("follow_time <>", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeGreaterThan(Date value) {
            addCriterion("follow_time >", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("follow_time >=", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeLessThan(Date value) {
            addCriterion("follow_time <", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeLessThanOrEqualTo(Date value) {
            addCriterion("follow_time <=", value, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeIn(List<Date> values) {
            addCriterion("follow_time in", values, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeNotIn(List<Date> values) {
            addCriterion("follow_time not in", values, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeBetween(Date value1, Date value2) {
            addCriterion("follow_time between", value1, value2, "followTime");
            return (Criteria) this;
        }

        public Criteria andFollowTimeNotBetween(Date value1, Date value2) {
            addCriterion("follow_time not between", value1, value2, "followTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeIsNull() {
            addCriterion("solution_time is null");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeIsNotNull() {
            addCriterion("solution_time is not null");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeEqualTo(Date value) {
            addCriterion("solution_time =", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeNotEqualTo(Date value) {
            addCriterion("solution_time <>", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeGreaterThan(Date value) {
            addCriterion("solution_time >", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("solution_time >=", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeLessThan(Date value) {
            addCriterion("solution_time <", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeLessThanOrEqualTo(Date value) {
            addCriterion("solution_time <=", value, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeIn(List<Date> values) {
            addCriterion("solution_time in", values, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeNotIn(List<Date> values) {
            addCriterion("solution_time not in", values, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeBetween(Date value1, Date value2) {
            addCriterion("solution_time between", value1, value2, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andSolutionTimeNotBetween(Date value1, Date value2) {
            addCriterion("solution_time not between", value1, value2, "solutionTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeIsNull() {
            addCriterion("evaluation_time is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeIsNotNull() {
            addCriterion("evaluation_time is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeEqualTo(Date value) {
            addCriterion("evaluation_time =", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeNotEqualTo(Date value) {
            addCriterion("evaluation_time <>", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeGreaterThan(Date value) {
            addCriterion("evaluation_time >", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("evaluation_time >=", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeLessThan(Date value) {
            addCriterion("evaluation_time <", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeLessThanOrEqualTo(Date value) {
            addCriterion("evaluation_time <=", value, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeIn(List<Date> values) {
            addCriterion("evaluation_time in", values, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeNotIn(List<Date> values) {
            addCriterion("evaluation_time not in", values, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeBetween(Date value1, Date value2) {
            addCriterion("evaluation_time between", value1, value2, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andEvaluationTimeNotBetween(Date value1, Date value2) {
            addCriterion("evaluation_time not between", value1, value2, "evaluationTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNull() {
            addCriterion("finish_time is null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNotNull() {
            addCriterion("finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeEqualTo(Date value) {
            addCriterion("finish_time =", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotEqualTo(Date value) {
            addCriterion("finish_time <>", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThan(Date value) {
            addCriterion("finish_time >", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("finish_time >=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThan(Date value) {
            addCriterion("finish_time <", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("finish_time <=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIn(List<Date> values) {
            addCriterion("finish_time in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotIn(List<Date> values) {
            addCriterion("finish_time not in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeBetween(Date value1, Date value2) {
            addCriterion("finish_time between", value1, value2, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("finish_time not between", value1, value2, "finishTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}