package com.sankuai.scrm.core.service.pchat.mq.handle;

import com.sankuai.scrm.core.service.BaseMockTest;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.scrm.core.service.pchat.mq.dto.PrivateLiveMiniprogramUserAction;
import com.sankuai.scrm.core.service.pchat.service.ScrmIdentifyUserService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
public class ScrmPrivateLiveMiniprogramUserActionHandleTest extends BaseMockTest {
    @InjectMocks
    private ScrmPrivateLiveMiniprogramUserActionHandle scrmPrivateLiveMiniprogramUserActionHandle;

    @Mock
    private ScrmIdentifyUserService scrmIdentifyUserService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试messageHandle方法，当消息体可以正确解析为PrivateLiveMiniprogramUserAction对象时
     */
    @Test
    public void testMessageHandleValidMessage() {
        // arrange
        String validMessageBody = "{\"miniProgramType\":1,\"openId\":\"someOpenId\",\"unionId\":\"someUnionId\",\"mtUserId\":123,\"actionType\":2,\"liveId\":\"someLiveId\"}";
        MafkaMessage message = new MafkaMessage("topic", 0, 0, null, validMessageBody);

        // act
        ConsumeStatus result = scrmPrivateLiveMiniprogramUserActionHandle.messageHandle(message);

        // assert
        verify(scrmIdentifyUserService, times(1)).process(any(PrivateLiveMiniprogramUserAction.class));
        assertSame(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}