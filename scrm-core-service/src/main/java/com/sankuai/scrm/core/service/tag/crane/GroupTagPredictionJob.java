package com.sankuai.scrm.core.service.tag.crane;

import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.beauty.ibot.dto.DxFileResponse;
import com.dianping.beauty.ibot.tools.DxFileUploaderClient;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.tag.dto.RelationshipDTO;
import com.sankuai.dz.srcm.tag.dto.RuleConfigDTO;
import com.sankuai.dz.srcm.tag.dto.RuleDTO;
import com.sankuai.dz.srcm.tag.enums.RunSubTypeEnum;
import com.sankuai.dz.srcm.tag.enums.RunTypeEnum;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.tag.bo.GroupTagPredictionJobDTO;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupPredTagRecord;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTag;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagRuleTask;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagRuleTaskLog;
import com.sankuai.scrm.core.service.tag.dal.example.GroupPredTagRecordExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupPredTagRecordMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagRuleTaskMapper;
import com.sankuai.scrm.core.service.tag.domain.GroupTagDomainService;
import com.sankuai.scrm.core.service.tag.domain.GroupTagPredModelAlgorithmService;
import com.sankuai.scrm.core.service.tag.domain.GroupTagPredTaskDomainService;
import com.sankuai.scrm.core.service.tag.domain.GroupTagRuleTaskDomainService;
import com.sankuai.scrm.core.service.tag.enums.TagPredictionRunType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GroupTagPredictionJob {

    private static final int INTERVAL_SECONDS = 60;

    @Resource
    private GroupTagRuleTaskMapper groupTagRuleTaskMapper;
    @Resource
    private GroupPredTagRecordMapper groupPredTagRecordMapper;
    //    @Autowired
//    private TagPredictionTaskProducer producer;
    @Resource
    private GroupTagRuleTaskDomainService groupTagRuleTaskDomainService;
    @Resource
    private GroupTagPredTaskDomainService groupTagPredTaskDomainService;
    @Resource
    private GroupTagDomainService groupTagDomainService;
    @Resource
    private GroupTagPredModelAlgorithmService groupTagPredModelAlgorithmService;
    @Resource
    private CorpAppConfigRepository appConfigRepository;


    private String checkAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            return "appId is empty";
        }
        CorpAppConfig config = appConfigRepository.getConfigByAppId(appId);
        if (config == null || StringUtils.isEmpty(config.getCorpId())) {
            return "config or corpId is empty";
        }
        return config.getCorpId();
    }

    @Crane("com.sankuai.medicalcosmetology.scrm.core.group.tag.predict")
    public void predict(GroupTagPredictionJobDTO jobParam) {
        int pageNo = 0;
        int pageSize = 100;
        log.info("group tag start predict,jobParam={}", jobParam);
        jobParam = jobParam == null ? new GroupTagPredictionJobDTO() : jobParam;
        while (true) {
            PagerList<GroupTagRuleTask> pagerList = groupTagRuleTaskDomainService.queryGroupTagRuleTaskList((jobParam.getRuleTaskId() != null ? Collections.singletonList(jobParam.getRuleTaskId()) : null), pageNo, pageSize);
            if (pagerList == null || CollectionUtils.isEmpty(pagerList.getData())) {
                log.info("group tag predict rule is null,over");
                break;
            }
            List<GroupTagRuleTask> tagRuleTasks = pagerList.getData();

            GroupTagPredictionJobDTO finalJobParam = jobParam;
            tagRuleTasks.forEach(tagTask -> processPredTask(finalJobParam, tagTask));
            pageNo++;
        }
        log.info("group tag end predict");
    }


    private void processPredTask(GroupTagPredictionJobDTO jobParam, GroupTagRuleTask task) {
        if (task == null) {
            return;
        }
        log.info("processPredTask jobParam={},task={}", jobParam, task);
        RunTypeEnum runType = RunTypeEnum.getRunTypeByCode(task.getRunType());
        RunSubTypeEnum runSubType = RunSubTypeEnum.getRunTypeByCode(task.getRunSubType());
        boolean cancelCheckRunTime = Objects.equals(task.getId(), jobParam.getRuleTaskId()) && Boolean.FALSE == jobParam.isCheckRunTime();
        boolean canRun = cancelCheckRunTime || checkRunTime(task.getRunTime(), runType, runSubType, task.getRunInterval());
        if (!canRun) {
            log.info("task={} can not run", task);
            return;
        }
        log.info("processPredTask start,task={}", task);
        GroupTagRuleTaskLog groupTagRuleTaskLog = groupTagPredTaskDomainService.queryLatestGroupPredTaskLog(task.getId());
        if (groupTagRuleTaskLog != null && groupTagRuleTaskLog.getStatus() != null && groupTagRuleTaskLog.getStatus() == TagPredictionRunType.RUNNING.getCode()) {
            log.info("current task is running ,task={}", task);
            return;
        }
        // 异步执行
        CompletableFuture.runAsync(() -> {
                    // 更新任务信息
                    log.info("group async tag start predict,task={}", task);
                    groupTagRuleTaskDomainService.updatePredTaskRunInfo(task.getId(), task.getRunCount() + 1, new Date());
                    // 生成log日志
                    long logId = groupTagPredTaskDomainService.insertPredTaskLog(task.getId());
                    if (logId <= 0) {
                        return;
                    }
                    String batchId = UUID.fastUUID().toString();
                    // 判断预测结果是否生成
                    try {
                        generatePredResult(batchId, task.getId());
                    } catch (Exception e) {
                        log.error("generatePredResult error", e);
                        groupTagRuleTaskDomainService.updatePredTaskStatus(logId, 0, TagPredictionRunType.FAILURE.getCode());
                        return;
                    }
                    groupTagPredTaskDomainService.updatePredTaskLogTotal(batchId, task.getId(), task.getVersion(), logId);
                    int start = 0;
                    int limit = 100;
                    AtomicInteger tagCount = new AtomicInteger(0);
                    while (true) {
                        List<GroupPredTagRecord> predTagRecords = queryPredTagRecords(batchId, task.getId(), task.getVersion(), start, limit);
                        if (CollectionUtils.isEmpty(predTagRecords)) {
                            break;
                        }
                        predTagRecords.forEach(record -> {
                            if (StringUtils.isNotBlank(record.getGroupTagSerialNoList())) {
                                tagCount.addAndGet(record.getGroupTagSerialNoList().split(",").length);
                            }
//                            TagPredictionMsg predMsg = new TagPredictionMsg();
//                            predMsg.setLogId(logId);
//                            predMsg.setRecordId(record.getId());
//                            producer.sendPredTagMsg(predMsg);
                        });
                        start += limit;
                    }
                    groupTagRuleTaskDomainService.updatePredTaskStatus(logId, tagCount.get(), TagPredictionRunType.SUCCESS.getCode());
                    log.info("group async tag end predict,task={}", task);
                }, ThreadPoolUtils.groupTagExecutor)
                .exceptionally(ex -> {
                    log.error("Process prediction task fail", ex);
                    return null;
                });

    }

    private void generatePredResult(String batchId, Long ruleTaskId) {
        log.info("generatePredResult-start,ruleTaskId={}", ruleTaskId);
        // step1: check
        GroupTagRuleTask task = groupTagRuleTaskMapper.selectByPrimaryKey(ruleTaskId);
        if (task == null || BooleanUtils.isTrue(task.getDeleted()) || StringUtils.isBlank(task.getAppId())) {
            log.info("ruleTaskId={} is not exist", ruleTaskId);
            return;
        }
        CorpAppConfig config = appConfigRepository.getConfigByAppId(task.getAppId());
        if (config == null || StringUtils.isEmpty(config.getCorpId())) {
            log.info("config or corpId is null");
            return;
        }
        String rule = task.getRule();
        RuleConfigDTO ruleConfigDTO = JSONObject.parseObject(rule, RuleConfigDTO.class);
        if (ruleConfigDTO == null) {
            log.info("ruleConfigDTO is null");
            return;
        }
        List<RuleDTO> ruleList = ruleConfigDTO.getRuleList();
        List<RelationshipDTO> relationships = ruleConfigDTO.getRelationships();
        if (CollectionUtils.isEmpty(ruleList)) {
            log.info("ruleList is null");
            return;
        }
        if (CollectionUtils.isEmpty(relationships)) {
            log.info("relationships is null");
            return;
        }
//        Map<String, RelationshipDTO> relationshipDTOMap = relationships.stream().filter(r -> StringUtils.isNotBlank(r.getFieldName())).collect(Collectors.toMap(RelationshipDTO::getFieldName, Function.identity(), (k1, k2) -> k2));
        List<GroupTag> groupTags = groupTagDomainService.queryGroupTagByGroupSetSerialNo(task.getTagSetSerialNo());
        if (CollectionUtils.isEmpty(groupTags)) {
            log.info("群标签为控，不进行处理");
            return;
        }
        Map<String, GroupTag> groupTagMap = groupTags.stream().collect(Collectors.toMap(GroupTag::getSerialNo, Function.identity(), (k1, k2) -> k2));
        List<RuleDTO> existRuleListTag = ruleList.stream().filter(r -> groupTagMap.containsKey(r.getTagId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existRuleListTag)) {
            log.info("existRuleListTag is null");
            return;
        }
        int pageNo = 0;
        int pageSize = 500;
        // step2 : run
        long groupCount = groupTagPredTaskDomainService.countGroupInfo(config.getCorpId());
        int totalBatchCount = (int) groupCount / pageSize;
        if (groupCount % pageSize != 0) {
            totalBatchCount++;
        }
        log.info("当前所有群数据,groupCount={},taskId={},processTimes约={}", groupCount, task.getId(), totalBatchCount);
        Set<String> duplicateGroupSerialNo = new HashSet<>();
        while (true) {
            List<GroupInfoEntity> groupInfoEntities = groupTagPredTaskDomainService.queryGroupInfo(config.getCorpId(), pageNo, pageSize);
            if (CollectionUtils.isEmpty(groupInfoEntities)) {
                log.info("groupInfoEntities is null");
                break;
            }
            log.info("群数据数量,groupCount={},pageNo={}", groupInfoEntities.size(), pageNo);
            List<GroupPredTagRecord> recordList = new ArrayList<>();
            long startMills = System.currentTimeMillis();

            for (GroupInfoEntity groupInfoEntity : groupInfoEntities) {
                try {
                    if (duplicateGroupSerialNo.contains(groupInfoEntity.getGroupId())) {
                        log.info("重复的群id，groupid={},id={}", groupInfoEntity.getGroupId(), groupInfoEntity.getId());
                        continue;
                    }
                    processOneGroup(batchId, task, relationships, existRuleListTag, recordList, groupInfoEntity);
                    duplicateGroupSerialNo.add(groupInfoEntity.getGroupId());
                } catch (Exception e) {
                    log.error("processOneGroup error", e);
                }
            }
            long end = System.currentTimeMillis();
            int elapseTime = (int) (end - startMills) / 1000;
            log.info("processGroupData cost time={}s,predict remain time={}s", elapseTime, elapseTime * (totalBatchCount - pageNo - 1));
            if (CollectionUtils.isNotEmpty(recordList)) {
                int total = groupPredTagRecordMapper.batchInsert(recordList);
                log.info("batchInsert success,total={}", total);
            }
            pageNo++;
        }

        // 生成预测结果url
        String downloadUrl = generatePredResultUrl(batchId, task.getId(), task.getVersion(), task.getTagSetSerialNo());
        groupTagPredTaskDomainService.updatePredTaskResultUrl(task.getId(), downloadUrl);
        log.info("generatePredResult-end,ruleTaskId={}", ruleTaskId);
    }

    private void processOneGroup(String batchId, GroupTagRuleTask task, List<RelationshipDTO> relationshipDTOList, List<RuleDTO> existRuleListTag, List<GroupPredTagRecord> recordList, GroupInfoEntity groupInfoEntity) {
        log.info("processOneGroup group={}", groupInfoEntity);
        List<String> tagIdList = Lists.newArrayList();
        for (RuleDTO ruleDTO : existRuleListTag) {
            boolean match = groupTagPredModelAlgorithmService.preTag(ruleDTO, groupInfoEntity, relationshipDTOList);
            if (match) {
                tagIdList.add(ruleDTO.getTagId());
            }
        }
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            recordList.add(buildPredTagRecord(batchId, task, groupInfoEntity, tagIdList));
            groupTagPredTaskDomainService.bindGroupTag(task, groupInfoEntity, tagIdList);
        }
    }

    private boolean checkRunTime(String runTime, RunTypeEnum runType, RunSubTypeEnum runSubType, Integer runInterval) {
        if (StringUtils.isEmpty(runTime) || runType == null) {
            return false;
        }
        try {
            if (RunTypeEnum.SINGLE_EXECUTION.equals(runType)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(runTime);
                Date now = new Date();
                return date.getTime() >= now.getTime() && date.getTime() - now.getTime() <= INTERVAL_SECONDS * 1000;
            }
            if (RunTypeEnum.REGULAR_EXECUTION.equals(runType)) {

                if (runSubType == RunSubTypeEnum.DAY) {
                    // 每天执行
                    return checkTime(runTime);
                }
                if (runSubType == RunSubTypeEnum.WEEK) {

                    // 每周执行
                    int day = runInterval;
                    DayOfWeek dayOfWeek = DayOfWeek.of(day);
                    DayOfWeek dayOfNow = LocalDate.now().getDayOfWeek();
                    if (dayOfNow != dayOfWeek) {
                        return false;
                    }
                    return checkTime(runTime);
                }
                if (runSubType == RunSubTypeEnum.MONTH) {
                    // 每月执行
                    int dayOfMonth = runInterval;
                    int dayOfNow = LocalDate.now().getDayOfMonth();
                    if (dayOfMonth != dayOfNow) {
                        return false;
                    }
                    return checkTime(runTime);

                }
            }
        } catch (ParseException e) {
            log.error("Run time parse error, runTime:{}", runTime, e);
        }
        return false;
    }

    public String generatePredResultUrl(String batchId, Long ruleTaskId, Integer version, String groupTagSetSerialNo) {
        log.info("generatePredResultUrl,runTaskId={},version={},groupTagSetSerialNo={}", ruleTaskId, version, groupTagSetSerialNo);
        if (ruleTaskId == null || version == null || StringUtils.isEmpty(groupTagSetSerialNo)) {
            return null;
        }
        List<GroupTag> tags = groupTagDomainService.queryGroupTagByGroupSetSerialNo(groupTagSetSerialNo);
        Map<String, String> tagIdNameMap = tags.stream().collect(Collectors.toMap(GroupTag::getSerialNo, GroupTag::getTagName, (k1, k2) -> k2));
        List<List<String>> head = Lists.newArrayList();
        head.add(Lists.newArrayList("GroupId"));
        head.add(Lists.newArrayList("Group-tag"));
        String fileName = String.valueOf(System.currentTimeMillis());
        File file = createExcelFile(fileName);
        if (file == null) {
            return null;
        }
        try (ExcelWriter writer = EasyExcel.write(file).head(head).build()) {
            WriteSheet sheet = EasyExcel.writerSheet("result").build();
            int start = 0;
            int limit = 1000;
            boolean blank = true;
            while (true) {
                List<GroupPredTagRecord> predTagRecords = queryPredTagRecords(batchId, ruleTaskId, version, start, limit);
                if (CollectionUtils.isEmpty(predTagRecords)) {
                    break;
                }
                blank = false;
                List<List<String>> rowData = buildRowData(predTagRecords, tagIdNameMap);
                if (CollectionUtils.isNotEmpty(rowData)) {
                    writer.write(rowData, sheet);
                }
                start += limit;
            }
            if (blank) {
                List<List<String>> rowData = Lists.newArrayList();
                List<String> row = Lists.newArrayList("","");
                rowData.add(row);
                writer.write(rowData, sheet);
            }
        } catch (Exception e) {
            log.error("Write tag prediction result to excel fail, ruleTaskId:{}, version:{}", ruleTaskId, version, e);
        }
        String format = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        try {
            DxFileResponse fileResponse = DxFileUploaderClient.getInstance().upload(file, format, file.getName());
            return fileResponse.getUrl();
        } catch (Exception e) {
            log.error("DxFileUploaderClient upload file fail", e);
        }
        return null;
    }

    private List<GroupPredTagRecord> queryPredTagRecords(String batchId, long ruleTaskId, int ruleTaskVersion, int start, int limit) {

        GroupPredTagRecordExample example = new GroupPredTagRecordExample();
        example.createCriteria()
                .andRuleTaskIdEqualTo(ruleTaskId)
                .andRuleTaskVersionEqualTo(ruleTaskVersion)
                .andStatusEqualTo(true).andBatchIdEqualTo(batchId);
        example.setOffset(start);
        example.setRows(limit);
        return groupPredTagRecordMapper.selectByExample(example);
    }


    private GroupPredTagRecord buildPredTagRecord(String batchId, GroupTagRuleTask task, GroupInfoEntity groupInfoEntity, List<String> tagIdList) {
        GroupPredTagRecord record = new GroupPredTagRecord();
        record.setAppId(task.getAppId());
        record.setGroupId(groupInfoEntity.getGroupId());
        record.setGroupTagSetSerialNo(task.getTagSetSerialNo());
        record.setGroupTagSerialNoList(JsonUtils.toStr(tagIdList));
        record.setAddTime(new Date());
        record.setRuleTaskId(task.getId());
        record.setUpdateTime(new Date());
        record.setRuleTaskVersion(task.getVersion());
        record.setBatchId(batchId);
        record.setStatus(true);
        return record;
    }

    private boolean checkTime(String runTimeOfDay) {
        LocalTime time = LocalTime.parse(runTimeOfDay, DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime now = LocalTime.now();
        int secondOfTime = time.toSecondOfDay();
        int secondOfNow = now.toSecondOfDay();
        return secondOfTime > secondOfNow && secondOfTime - secondOfNow <= INTERVAL_SECONDS;
    }

    private File createExcelFile(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return null;
        }
        try {
            fileName = fileName.replaceAll("\\.xls$", "").replaceAll("\\.xlsx$", "");
            fileName += "-" + Calendar.getInstance().getTimeInMillis();
            File directory = new File("/opt/logs/logs/xiaoli/");
            if (!directory.exists()) {
                directory.mkdirs();
            }
            return File.createTempFile(fileName, ".xlsx", directory);
        } catch (IOException e) {
            log.error("Create excel file fail", e);
        }
        return null;
    }

    private List<List<String>> buildRowData(List<GroupPredTagRecord> predTagRecords, Map<String, String> tagIdNameMap) {
        if (CollectionUtils.isEmpty(predTagRecords) || MapUtils.isEmpty(tagIdNameMap)) {
            return Lists.newArrayList();
        }
        List<List<String>> rowData = Lists.newArrayList();
        predTagRecords.stream().filter(Objects::nonNull).forEach(record -> {
            List<String> row = Lists.newArrayList();
            row.add(String.valueOf(record.getGroupId()));
            try {
                List<String> tagIdList = JsonUtils.toList(record.getGroupTagSerialNoList(), String.class);
                List<String> tagNameList = tagIdList.stream().map(tagIdNameMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                row.add(StringUtils.join(tagNameList, ','));
                rowData.add(row);
            } catch (Exception e) {
                log.error("GroupPredTagRecord.tagIdList deserialize error, tagIdList:{}", record.getGroupTagSerialNoList(), e);
            }
        });
        return rowData;
    }
}
