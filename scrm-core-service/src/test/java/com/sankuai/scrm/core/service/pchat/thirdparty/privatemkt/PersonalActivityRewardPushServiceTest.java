package com.sankuai.scrm.core.service.pchat.thirdparty.privatemkt;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.dzmkt.privatemkt.biz.api.request.CommunityPrizeRecordByIdsRequest;
import com.sankuai.dzmkt.privatemkt.biz.api.response.CommunityPrizeRecordQueryResponse;
import com.sankuai.dzmkt.privatemkt.biz.api.service.PrizeRecordService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test for {@link PersonalActivityRewardPushService#queryPrizeRecordByRecordIds(List)}
 */
@ExtendWith(MockitoExtension.class)
public class PersonalActivityRewardPushServiceTest {

    @Mock
    private PrizeRecordService prizeRecordService;

    @InjectMocks
    private PersonalActivityRewardPushService personalActivityRewardPushService;

    /**
     * Test case for queryPrizeRecordByRecordIds when recordIds is null
     */
    @Test
    public void testQueryPrizeRecordByRecordIdsWithNullRecordIds() {
        // arrange
        List<Long> recordIds = null;
        // act
        List<CommunityPrizeRecordQueryResponse> result = personalActivityRewardPushService.queryPrizeRecordByRecordIds(recordIds);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(prizeRecordService, never()).queryCommunityPrizeRecordByIds(any());
    }

    /**
     * Test case for queryPrizeRecordByRecordIds when recordIds is empty
     */
    @Test
    public void testQueryPrizeRecordByRecordIdsWithEmptyRecordIds() {
        // arrange
        List<Long> recordIds = Collections.emptyList();
        // act
        List<CommunityPrizeRecordQueryResponse> result = personalActivityRewardPushService.queryPrizeRecordByRecordIds(recordIds);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(prizeRecordService, never()).queryCommunityPrizeRecordByIds(any());
    }

    /**
     * Test case for queryPrizeRecordByRecordIds with valid recordIds
     */
    @Test
    public void testQueryPrizeRecordByRecordIdsWithValidRecordIds() {
        // arrange
        List<Long> recordIds = Arrays.asList(1L, 2L, 3L);
        List<CommunityPrizeRecordQueryResponse> expectedResponses = new ArrayList<>();
        CommunityPrizeRecordQueryResponse response1 = new CommunityPrizeRecordQueryResponse();
        response1.setExternalRecordId(1L);
        CommunityPrizeRecordQueryResponse response2 = new CommunityPrizeRecordQueryResponse();
        response2.setExternalRecordId(2L);
        expectedResponses.add(response1);
        expectedResponses.add(response2);
        when(prizeRecordService.queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class))).thenReturn(expectedResponses);
        // act
        List<CommunityPrizeRecordQueryResponse> result = personalActivityRewardPushService.queryPrizeRecordByRecordIds(recordIds);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getExternalRecordId().longValue());
        assertEquals(2L, result.get(1).getExternalRecordId().longValue());
        // Verify the request was properly constructed
        verify(prizeRecordService).queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class));
    }

    /**
     * Test case for queryPrizeRecordByRecordIds when service returns null
     */
    @Test
    public void testQueryPrizeRecordByRecordIdsWhenServiceReturnsNull() {
        // arrange
        List<Long> recordIds = Arrays.asList(1L, 2L);
        when(prizeRecordService.queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class))).thenReturn(null);
        // act
        List<CommunityPrizeRecordQueryResponse> result = personalActivityRewardPushService.queryPrizeRecordByRecordIds(recordIds);
        // assert
        // The method doesn't handle null return from service, so it would return null
        // This test is to document the current behavior
        assertTrue(CollectionUtil.isEmpty( result));
        verify(prizeRecordService).queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class));
    }

    /**
     * Test case for queryPrizeRecordByRecordIds with single recordId
     */
    @Test
    public void testQueryPrizeRecordByRecordIdsWithSingleRecordId() {
        // arrange
        List<Long> recordIds = Collections.singletonList(1L);
        CommunityPrizeRecordQueryResponse response = new CommunityPrizeRecordQueryResponse();
        response.setExternalRecordId(1L);
        response.setPrizeRecordId(100L);
        response.setVerifyCode("ABC123");
        when(prizeRecordService.queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class))).thenReturn(Collections.singletonList(response));
        // act
        List<CommunityPrizeRecordQueryResponse> result = personalActivityRewardPushService.queryPrizeRecordByRecordIds(recordIds);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getExternalRecordId().longValue());
        assertEquals(100L, result.get(0).getPrizeRecordId().longValue());
        assertEquals("ABC123", result.get(0).getVerifyCode());
        verify(prizeRecordService).queryCommunityPrizeRecordByIds(any(CommunityPrizeRecordByIdsRequest.class));
    }
}
