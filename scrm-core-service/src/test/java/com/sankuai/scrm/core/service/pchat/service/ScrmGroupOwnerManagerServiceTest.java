package com.sankuai.scrm.core.service.pchat.service;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginAnchorInfoDTO;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.service.transfer.TransferHandler;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil.checkUserPermission;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/18 17:26
 * @Version v1.0.0
 */
public class ScrmGroupOwnerManagerServiceTest extends BaseMockTest {
    @InjectMocks
    private ScrmGroupOwnerManagerServiceImpl scrmGroupOwnerManagerService;
    @Mock
    private TransferHandler transferHandler;
    @Mock
    private RedisStoreClient redisClient;
    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;
    private final String liveId = "liveId";
    private final List<String> groupIds = Collections.singletonList("groupId");
    private final Integer transferType = 1;
    private final boolean transferAll = false;


    /**
     * 测试手动转移群主，当groupId和newOwnerMemberId都不为空时，转移成功
     */
    @Test
    public void testManualTransferGroupOwner_Success() throws Exception {
        try (MockedStatic<CheckPermissionUtil> checkPermissionUtilMockedStatic = Mockito.mockStatic(CheckPermissionUtil.class)) {
            checkPermissionUtilMockedStatic.when(() -> checkUserPermission(any(), any())).thenAnswer((Answer<Void>) invocation -> null);

            // arrange
            String groupId = "testGroupId";
            String newOwnerMemberId = "testNewOwnerMemberId";
            ScrmPersonalWxGroupInfoEntity groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
            when(groupInfoEntity.getProjectId()).thenReturn("testProjectId");
            when(transferHandler.validateAndGetGroupInfo(groupId)).thenReturn(groupInfoEntity);

            // act
            RemoteResponse<Boolean> result = scrmGroupOwnerManagerService.manualTransferGroupOwner(groupId, newOwnerMemberId);

            // assert
            assertTrue(result.getData());
            verify(transferHandler, times(1)).transferGroupOwner(groupId, newOwnerMemberId);
        }
    }


    /**
     * 创建群主转移任务成功
     */
    @Test
    public void testCreateGroupOwnerTransferTask_Success() {
        try (MockedStatic<CheckPermissionUtil> checkPermissionUtilMockedStatic = Mockito.mockStatic(CheckPermissionUtil.class)) {
            try (MockedStatic<AnchorAuthUtils> anchorAuthUtilsMockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
                LoginAnchorInfoDTO loginAnchorInfoDTO = new LoginAnchorInfoDTO();
                anchorAuthUtilsMockedStatic.when(() -> AnchorAuthUtils.loadAnchorInfo()).thenReturn(loginAnchorInfoDTO);

                checkPermissionUtilMockedStatic.when(() -> checkUserPermission(any(), any())).thenAnswer((Answer<Void>) invocation -> null);

                // arrange
                when(redisClient.setnx(any(StoreKey.class), anyInt())).thenReturn(true);
                when(groupManageDomainService.queryGroupByChatroomSerialNos(anyList())).thenReturn(Arrays.asList(new ScrmPersonalWxGroupInfoEntity()));
                when(transferHandler.hasWaitingTask(anyString())).thenReturn(false);
                when(groupManageDomainService.batchCreateGroupOwnerTransferTask(anyList())).thenReturn(1L);
                doNothing().when(transferHandler).delayProcess(anyList());

                // act
                RemoteResponse<Boolean> result = scrmGroupOwnerManagerService.createGroupOwnerTransferTask(transferType, groupIds, liveId, transferAll);

                // assert
                assertTrue(result.getData());
            }
        }
    }

}
