package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verifyNoInteractions;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationWxTouchTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetGroupMsgSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetGroupmsgSendResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.util.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for OfficialWxHandler.dealNormalOfficialWxGroupMessageV2 method.
 */
@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealNormalOfficialWxGroupMessageV2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    private static final Long PROCESS_ORCHESTRATION_ID = 1L;

    private static final String VALID_VERSION = "1.0";

    private static final Long NODE_ID = 1L;

    private static final String APP_ID = "testAppId";

    @Mock
    private WxGetGroupMsgSendResultAcl wxGetGroupMsgSendResultAcl;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private InformationGatheringService informationGatheringService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test scenario when executorIds list is empty.
     */
    @Test
    public void testDealNormalOfficialWxGroupMessageV2_EmptyExecutorIds() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = new ArrayList<>();
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, NODE_ID);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        officialWxHandler.dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS);
        verifyNoInteractions(msgUnifiedPushService);
    }

    /**
     * Test scenario when totalInvokeDetailDOS list is empty.
     */
    @Test
    public void testDealNormalOfficialWxGroupMessageV2_EmptyInvokeDetails() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, NODE_ID);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        officialWxHandler.dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS);
        verifyNoInteractions(msgUnifiedPushService);
    }

    /**
     * Helper method to create a valid ScrmProcessOrchestrationDTO
     */
    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(PROCESS_ORCHESTRATION_ID);
        dto.setValidVersion(VALID_VERSION);
        dto.setAppId(APP_ID);
        return dto;
    }

    /**
     * Helper method to create a valid ScrmAmProcessOrchestrationWxInvokeDetailDO
     */
    private ScrmAmProcessOrchestrationWxInvokeDetailDO createInvokeDetailDO() {
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationId(PROCESS_ORCHESTRATION_ID);
        invokeDetailDO.setProcessOrchestrationVersion(VALID_VERSION);
        invokeDetailDO.setProcessOrchestrationNodeId(NODE_ID);
        invokeDetailDO.setGroupId("testGroupId");
        invokeDetailDO.setExecutorId("executor1");
        return invokeDetailDO;
    }

    @Test
    void testCheckOfficialWxStatus_ExpiredLog() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        Calendar cal = Calendar.getInstance();
        // 9 h ago → expired
        cal.add(Calendar.HOUR, -9);
        log.setUpdateTime(cal.getTime());
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verify(wxInvokeLogDOMapper, times(2)).updateByPrimaryKeySelective(any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    @Test
    void testCheckOfficialWxStatus_NotWaitForSendStatus() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue());
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verifyNoInteractions(wxGetGroupMsgSendResultAcl);
    }

    @Test
    void testCheckOfficialWxStatus_EmptySendList() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        token.setAccess_token("token");
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setJobid("job");
        log.setExecutorId("exec");
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // Mock the selectByPrimaryKey call
        ScrmAmProcessOrchestrationWxInvokeLogDO currentLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        currentLog.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(currentLog);
        WxGetGroupmsgSendResultResponse resp = new WxGetGroupmsgSendResultResponse();
        resp.setSendList(Collections.emptyList());
        when(wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(any(), any(), any())).thenReturn(resp);
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verify(wxInvokeLogDOMapper).updateByPrimaryKeySelective(argThat(l -> Objects.equals(l.getStatus(), ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue())));
    }

    @Test
    void testCheckOfficialWxStatus_AllSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        token.setAccess_token("token");
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setJobid("job");
        log.setExecutorId("exec");
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // Mock the selectByPrimaryKey call
        ScrmAmProcessOrchestrationWxInvokeLogDO currentLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        currentLog.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(currentLog);
        SendListDTO s1 = new SendListDTO();
        s1.setStatus(1);
        SendListDTO s2 = new SendListDTO();
        s2.setStatus(1);
        WxGetGroupmsgSendResultResponse resp = new WxGetGroupmsgSendResultResponse();
        resp.setSendList(Arrays.asList(s1, s2));
        when(wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(any(), any(), any())).thenReturn(resp);
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verify(wxInvokeLogDOMapper).updateByPrimaryKeySelective(argThat(l -> Objects.equals(l.getStatus(), ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue())));
    }

    @Test
    void testCheckOfficialWxStatus_NotFriendStatus() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        token.setAccess_token("token");
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setJobid("job");
        log.setExecutorId("exec");
        log.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // Mock the selectByPrimaryKey call
        ScrmAmProcessOrchestrationWxInvokeLogDO currentLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        currentLog.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(currentLog);
        SendListDTO ok = new SendListDTO();
        ok.setStatus(1);
        ok.setExternalUserid("u1");
        SendListDTO notFriend = new SendListDTO();
        notFriend.setStatus(2);
        notFriend.setExternalUserid("u2");
        WxGetGroupmsgSendResultResponse resp = new WxGetGroupmsgSendResultResponse();
        resp.setSendList(Arrays.asList(ok, notFriend));
        when(wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(any(), any(), any())).thenReturn(resp);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verify(wxGetGroupMsgSendResultAcl).getGroupMsgSendResult(any(), any(), any());
    }

    @Test
    void testCheckOfficialWxStatus_PreventDisturbance() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        token.setAccess_token("token");
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setJobid("job");
        log.setExecutorId("exec");
        log.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // Mock the selectByPrimaryKey call
        ScrmAmProcessOrchestrationWxInvokeLogDO currentLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        currentLog.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(currentLog);
        SendListDTO ok = new SendListDTO();
        ok.setStatus(1);
        ok.setExternalUserid("u1");
        SendListDTO disturb = new SendListDTO();
        disturb.setStatus(3);
        disturb.setExternalUserid("u2");
        WxGetGroupmsgSendResultResponse resp = new WxGetGroupmsgSendResultResponse();
        resp.setSendList(Arrays.asList(ok, disturb));
        when(wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(any(), any(), any())).thenReturn(resp);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        verify(wxGetGroupMsgSendResultAcl).getGroupMsgSendResult(any(), any(), any());
    }

    @Test
    void testCheckOfficialWxStatus_InProgress() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        WeChatTokenResult token = new WeChatTokenResult();
        token.setAccess_token("token");
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setId(1L);
        log.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setJobid("job");
        log.setExecutorId("exec");
        // Set current time to avoid NPE
        log.setUpdateTime(new Date());
        // Mock the selectByPrimaryKey call
        ScrmAmProcessOrchestrationWxInvokeLogDO currentLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        currentLog.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(currentLog);
        SendListDTO ok = new SendListDTO();
        ok.setStatus(1);
        SendListDTO pending = new SendListDTO();
        pending.setStatus(0);
        WxGetGroupmsgSendResultResponse resp = new WxGetGroupmsgSendResultResponse();
        resp.setSendList(Arrays.asList(ok, pending));
        when(wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(any(), any(), any())).thenReturn(resp);
        // act
        officialWxHandler.checkOfficialWxStatus(processDTO, 1L, "v1", token, log);
        // assert
        // Verify that the method was called but the log was NOT updated to SEND_SUCCESS
        // because there are still in-progress messages (status = 0)
        verify(wxGetGroupMsgSendResultAcl).getGroupMsgSendResult(any(), any(), any());
        // The method should not update the log status to SEND_SUCCESS when there are pending messages
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKeySelective(argThat(l -> Objects.equals(l.getStatus(), ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue())));
    }
}
