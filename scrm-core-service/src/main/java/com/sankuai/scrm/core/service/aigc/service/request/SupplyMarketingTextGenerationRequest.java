package com.sankuai.scrm.core.service.aigc.service.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR> on 2024/08/21 14:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplyMarketingTextGenerationRequest implements Serializable {

    private String appId;

    private Long supplyProductId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SupplyMarketingTextGenerationRequest that = (SupplyMarketingTextGenerationRequest)o;
        return appId.equals(that.getAppId()) && supplyProductId.equals(that.supplyProductId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, supplyProductId);
    }

}
