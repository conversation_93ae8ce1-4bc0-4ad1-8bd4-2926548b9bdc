package com.sankuai.dz.srcm.pchat.response.activity;

import com.sankuai.dz.srcm.pchat.dto.activity.ActivityInvitationConfigDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/25 15:22
 * @Version v1.0.0
 */
@Data
public class ActivityResponse implements Serializable {
    private Long activityId;
    private ActivityDetailResponse activity;
    private ActivityRuleConfigDetailResponse rule;
    private ActivityConfigResponse config;
    private ActivityInvitationConfigDTO invitationConfig;
    private Integer wxType;
}
