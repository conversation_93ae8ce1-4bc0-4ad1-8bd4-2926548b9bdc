package com.sankuai.dz.srcm.automatedmanagement.request;

import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/18
 */
@Data
public class ExecuteProcessOrchestrationRequest implements Serializable {

    /**
     * @see ScrmProcessOrchestrationTaskTypeEnum
     */
    private Integer processOrchestrationType;

    /**
     * 人群包ID
     */
    private Long crowdPackId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排Id
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     * 业务id 必填
     */
    private String appId;

    /**
     *
     */
    private String misId;

    private String unionId;

    private List<String> unionIds;

    private String groupId;
}
