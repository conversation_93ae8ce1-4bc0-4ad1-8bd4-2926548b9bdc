package com.sankuai.scrm.core.service.data.statistics.crane;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FriendOperatorDataStatisticsTaskTest {

    private FriendOperatorDataStatisticsTask task;

    /**
     * Tests the getStatisticsDate method to ensure it returns the correct date for the previous day.
     */
    @Test
    public void testGetStatisticsDate() throws Throwable {
        // arrange
        FriendOperatorDataStatisticsTask task = new FriendOperatorDataStatisticsTask();
        // Create expected date using the same logic as the method under test
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 0);
        date = calendar.getTime();
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        LocalDateTime endOfDay = localDateTime.withHour(23).withMinute(59).withSecond(59);
        ZonedDateTime zonedDateTime = endOfDay.atZone(ZoneId.of("Asia/Shanghai"));
        Date expectedDate = Date.from(zonedDateTime.toInstant());
        // act
        Method method = FriendOperatorDataStatisticsTask.class.getDeclaredMethod("getStatisticsDate");
        method.setAccessible(true);
        Date actualDate = (Date) method.invoke(task);
        // assert
        assertEquals("The getStatisticsDate method should return the date for the current day at 23:59:59.", expectedDate, actualDate);
    }
}
