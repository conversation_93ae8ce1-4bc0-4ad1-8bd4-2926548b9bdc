package com.sankuai.scrm.core.service.user.domain;

import com.dianping.lion.client.Lion;
import com.google.common.hash.BloomFilter;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ExternalContactBaseInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagBooleanType;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/9/18
 */
public class ScrmUserTagDomainServiceTest extends BaseMockTest {

    private BloomFilter<String> bloomFilter;

    @Mock(lenient = true)
    private ContactUserMapper contactUserMapper;

    @InjectMocks
    private ScrmUserTagDomainService scrmUserTagDomainService;

    @Mock(lenient = true)
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock(lenient = true)
    private ExternalContactBaseInfoMapper externalContactBaseInfoMapper;

    @Mock(lenient = true)
    private ExtScrmUserTagMapper extScrmUserTagMapper;

    private MockedStatic<Lion> lionMock;

    @Before
    public void setUp() {
        // 使用Mockito模拟BloomFilter的行为
        bloomFilter = Mockito.mock(BloomFilter.class);
        lionMock = Mockito.mockStatic(Lion.class);
    }

    @After
    public void after() {
        lionMock.close();
    }

    /**
     * 测试unionId为空的情况
     */
    @Test
    public void testCheckUnionIdWithEmptyUnionId() {
        // arrange
        String unionId = "";

        // act
        boolean result = scrmUserTagDomainService.checkUnionId(unionId, bloomFilter);

        // assert
        Assert.assertTrue("当unionId为空时，应该返回true", result);
    }

    /**
     * 测试unionId为null的情况
     */
    @Test
    public void testCheckUnionIdWithNullUnionId() {
        // arrange
        String unionId = null;

        // act
        boolean result = scrmUserTagDomainService.checkUnionId(unionId, bloomFilter);

        // assert
        Assert.assertTrue("当unionId为null时，应该返回true", result);
    }

    /**
     * 测试BloomFilter中已经包含unionId的情况
     */
    @Test
    public void testCheckUnionIdWithUnionIdContainedInBloomFilter() {
        // arrange
        String unionId = "testUnionId";
        when(bloomFilter.mightContain(unionId)).thenReturn(true);

        // act
        boolean result = scrmUserTagDomainService.checkUnionId(unionId, bloomFilter);

        // assert
        Assert.assertTrue("当BloomFilter中已经包含unionId时，应该返回true", result);
    }

    /**
     * 测试BloomFilter中不包含unionId，且unionId非空的情况
     */
    @Test
    public void testCheckUnionIdWithUnionIdNotContainedInBloomFilter() {
        // arrange
        String unionId = "testUnionId";
        when(bloomFilter.mightContain(unionId)).thenReturn(false);

        // act
        boolean result = scrmUserTagDomainService.checkUnionId(unionId, bloomFilter);

        // assert
        Assert.assertFalse("当BloomFilter中不包含unionId，且unionId非空时，应该返回false", result);
    }

    /**
     * 测试getContactUsersCount方法，当传入有效的corpId时，应正确计算出联系人数量
     */
    @Test
    public void testGetContactUsersCountValidCorpId() {
        // arrange
        String corpId = "testCorpId";
        long expectedCount = 5L;
        when(contactUserMapper.countByExample(any(ContactUserExample.class))).thenReturn(expectedCount);

        // act
        long actualCount = scrmUserTagDomainService.getContactUsersCount(corpId);

        // assert
        assertEquals(expectedCount, actualCount);
    }

    /**
     * 测试getContactUsersCount方法，当传入的corpId为null时，应返回0，表示没有联系人
     */
    @Test
    public void testGetContactUsersCountNullCorpId() {
        // arrange
        String corpId = null;
        long expectedCount = 0L;

        // act
        long actualCount = scrmUserTagDomainService.getContactUsersCount(corpId);

        // assert
        assertEquals(expectedCount, actualCount);
    }

    /**
     * 测试getContactUsersCount方法，当传入的corpId为空字符串时，应返回0，表示没有联系人
     */
    @Test
    public void testGetContactUsersCountEmptyCorpId() {
        // arrange
        String corpId = "";
        long expectedCount = 0L;

        // act
        long actualCount = scrmUserTagDomainService.getContactUsersCount(corpId);

        // assert
        assertEquals(expectedCount, actualCount);
    }
   
    /**
     * 测试当corpId和unionId为空时的行为
     */
    @Test
    public void testHandleScrmtUserTagWhenAddContactUserWithEmptyParams() {
        // arrange
        String corpId = "";
        String unionId = "";

        // act
        scrmUserTagDomainService.handleScrmtUserTagWhenAddContactUser(corpId, unionId);

        // assert
        verify(corpAppConfigRepository, never()).getAppIdByCorpId(any());
    }

    /**
     * 测试当corpId不在兴趣列表中时的行为
     */
    @Test
    public void testHandleScrmtUserTagWhenAddContactUserWithCorpIdNotInInterestList() {
        // arrange
        String corpId = "notInterestedCorpId";
        String unionId = "unionId";
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(externalContactBaseInfoMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        lionMock.when(() -> Lion.getList(eq("com.sankuai.medicalcosmetology.scrm.core.user.tag.handle.corpIds"), eq(Long.class))).thenReturn(new ArrayList<>());
        // act
        scrmUserTagDomainService.handleScrmtUserTagWhenAddContactUser(corpId, unionId);

        // assert
        verify(extScrmUserTagMapper, never()).insertSelective(any(ScrmUserTag.class));
    }

    /**
     * 测试当成功添加好友标签时的行为
     */
    @Test
    public void testHandleScrmtUserTagWhenAddContactUserWithSuccess() {
        // arrange
        String corpId = "interestedCorpId";
        String unionId = "unionId";
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        List<ExternalContactBaseInfo> baseInfoList = new ArrayList<>();
        ExternalContactBaseInfo baseInfo = new ExternalContactBaseInfo();
        baseInfo.setMtUserId(1L);
        baseInfoList.add(baseInfo);
        when(externalContactBaseInfoMapper.selectByExample(any())).thenReturn(baseInfoList);
        when(extScrmUserTagMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        lionMock.when(() -> Lion.getList(eq("com.sankuai.medicalcosmetology.scrm.core.user.tag.handle.corpIds"), eq(Long.class))).thenReturn(new ArrayList<>());

        // act
        scrmUserTagDomainService.handleScrmtUserTagWhenAddContactUser(corpId, unionId);

        // assert
        verify(extScrmUserTagMapper, never()).insertSelective(any(ScrmUserTag.class));
    }

    /**
     * 测试当已存在好友标签且标签值为非好友时，更新标签值为好友的行为
     */
    @Test
    public void testHandleScrmtUserTagWhenAddContactUserWithExistingTag() {
        // arrange
        String corpId = "interestedCorpId";
        String unionId = "unionId";
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        List<ScrmUserTag> existingTags = new ArrayList<>();
        ScrmUserTag existingTag = new ScrmUserTag();
        existingTag.setTagValue(String.valueOf(ScrmUserTagBooleanType.FALSE.getCode()));
        existingTags.add(existingTag);
        when(extScrmUserTagMapper.selectByExample(any())).thenReturn(existingTags);
        lionMock.when(() -> Lion.getList(eq("com.sankuai.medicalcosmetology.scrm.core.user.tag.handle.corpIds"), eq(Long.class))).thenReturn(new ArrayList<>());
        // act
        scrmUserTagDomainService.handleScrmtUserTagWhenAddContactUser(corpId, unionId);

        // assert
        verify(extScrmUserTagMapper, never()).updateByPrimaryKeySelective(any(ScrmUserTag.class));
    }

}