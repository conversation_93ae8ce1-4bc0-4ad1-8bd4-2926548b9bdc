package com.sankuai.scrm.core.service.user.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/8/6 16:38
 * @Version v1.0.0
 */
@Data
public class PrivateLiveUgcIntentionProductDTO {
    /**
     * 1:直播弹幕，2:社群发言
     */
    private Integer source;

    /**
     * 说明: source为1时有值
     */
    private String unionId;

    /**
     * 说明: source为2时有值
     */
    private String wxId;

    /**
     * 字段: liveId
     */
    private String liveId;


    /**
     * 说明: source为1时弹幕id，source为2时社群发言id
     */
    private String sourceBizId;


    /**
     * 字段: text
     * 说明: 弹幕或社群发言内容
     */
    private String text;

    private List<Product> products;

    /**
     * 字段: time
     * 说明: 动作发生时间 秒级时间戳
     */
    private Long time;


    @Data
    public static class Product {
        private Long productId;
        private Integer productType;
    }

}
