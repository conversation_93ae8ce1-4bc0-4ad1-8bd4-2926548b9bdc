package com.sankuai.dz.srcm.automatedmanagement.response.activitypage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PagingQueryActivityPagetResultVO {
    //小程序封面
    private String progCover;
    //小程序标题
    private String progTitle;
    private List<Long> productIds;
    //活动页背景图
    private String activityPageImg;
    //活动页背景色
    private String backgroundColor;
    private String activityPageTitle;
    private String miniProgramOriginAppId;
    private String miniProgramAppId;
    private Long activityPageId;
    private String addTime;
}
