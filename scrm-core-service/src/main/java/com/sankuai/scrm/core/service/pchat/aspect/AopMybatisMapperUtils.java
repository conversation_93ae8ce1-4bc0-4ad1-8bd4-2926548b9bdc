package com.sankuai.scrm.core.service.pchat.aspect;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.framework.AopProxy;
import org.springframework.aop.support.AopUtils;

import java.lang.reflect.Field;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/12/13 16:34
 * @Version v1.0.0
 */
@Slf4j
public class AopMybatisMapperUtils {
    public static Class<?> getTarget(Object obj) {
        if (obj == null) {
            return null;
        }
        if (!AopUtils.isAopProxy(obj)) {
            return obj.getClass();
        }
        try {

            //判断是jdk还是cglib代理
            if (AopUtils.isJdkDynamicProxy(obj)) {
                return getJdkDynamicProxyTargetObject(obj);
            } else {
                return getCglibDynamicProxyTargetObject(obj);
            }

        } catch (Exception e) {
            log.error("AopTargetUtils.getTarget error", e);
        }
        return obj.getClass();

    }

    private static Class<?> getCglibDynamicProxyTargetObject(Object obj) throws Exception {
        Field h = obj.getClass().getDeclaredField("CGLIB$CALLBACK_0");
        h.setAccessible(true);

        Object dynamicAdvisedInterceptor = h.get(obj);
        Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        return getMapperClass(advised, dynamicAdvisedInterceptor);
    }

    private static Class<?> getJdkDynamicProxyTargetObject(Object obj) throws Exception {
        Field h = obj.getClass().getSuperclass().getDeclaredField("h");
        h.setAccessible(true);

        AopProxy aopProxy = (AopProxy) h.get(obj);
        Field advised = aopProxy.getClass().getDeclaredField("advised");
        advised.setAccessible(true);

        return getMapperClass(advised, aopProxy);

    }

    private static Class<?> getMapperClass(Field advised, Object obj) throws IllegalAccessException {
        Class<?>[] proxiedInterfaces = ((AdvisedSupport) advised.get(obj)).getProxiedInterfaces();
        for (Class<?> cls : proxiedInterfaces) {
            if (MybatisBaseMapper.class.isAssignableFrom(cls)) {
                return cls;
            }
        }
        return null;
    }
}
