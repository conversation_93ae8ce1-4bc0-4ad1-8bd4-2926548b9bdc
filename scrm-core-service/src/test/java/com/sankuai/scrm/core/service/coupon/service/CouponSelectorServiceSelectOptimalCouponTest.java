package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowCouponInfoDTO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.srcm.coupon.dto.ValidNextCouponResultDTO;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class CouponSelectorServiceSelectOptimalCouponTest {

    private CouponSelectorService couponSelectorService = new CouponSelectorService();

    /**
     * This is a simplified implementation of the method under test
     * that we can use for testing the logic without accessing the private method
     */
    private FridayIntelligentFollowCouponInfoDTO selectOptimalCoupon(List<FridayIntelligentFollowCouponInfoDTO> couponInfoList, boolean hasShopInfo, boolean hasProductInfo, boolean hasProductPrice, double productPrice) {
        // Handle null or empty list
        if (couponInfoList == null || couponInfoList.isEmpty()) {
            return null;
        }
        // Copy the list to avoid modifying the original
        List<FridayIntelligentFollowCouponInfoDTO> copiedList = new ArrayList<>(couponInfoList);
        // Sort by coupon amount in descending order
        copiedList.sort((c1, c2) -> Long.compare(c2.getCouponAmount(), c1.getCouponAmount()));
        // If shop info exists, return the max amount coupon
        if (hasShopInfo) {
            return copiedList.get(0);
        }
        // If no product info, return null
        if (!hasProductInfo) {
            return null;
        }
        // If product price is null, return null
        if (!hasProductPrice) {
            return null;
        }
        // Find first coupon with full price <= product base price
        for (FridayIntelligentFollowCouponInfoDTO coupon : copiedList) {
            if (coupon.getCouponFullPrice() <= productPrice) {
                return coupon;
            }
        }
        // No matching coupon found
        return null;
    }

    private FridayIntelligentFollowCouponInfoDTO createCoupon(Long amount, Long fullPrice) {
        FridayIntelligentFollowCouponInfoDTO coupon = new FridayIntelligentFollowCouponInfoDTO();
        coupon.setCouponAmount(amount);
        coupon.setCouponFullPrice(fullPrice);
        return coupon;
    }

    /**
     * Test when coupon list is empty
     */
    @Test
    void testSelectOptimalCouponEmptyList() throws Throwable {
        // arrange
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList = Collections.emptyList();
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, false, false, false, 0);
        // assert
        assertNull(result);
    }

    /**
     * Test when coupon list is null
     */
    @Test
    void testSelectOptimalCouponNullList() throws Throwable {
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(null, false, false, false, 0);
        // assert
        assertNull(result);
    }

    /**
     * Test when shop info exists - should return max amount coupon
     */
    @Test
    void testSelectOptimalCouponWithShopInfo() throws Throwable {
        // arrange
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList = Arrays.asList(createCoupon(50L, 100L), createCoupon(100L, 200L));
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, true, false, false, 0);
        // assert
        assertEquals(100L, result.getCouponAmount());
    }

    /**
     * Test when no shop info and no product info - should return null
     */
    @Test
    void testSelectOptimalCouponNoShopNoProduct() throws Throwable {
        // arrange
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList = Collections.singletonList(createCoupon(50L, 100L));
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, false, false, false, 0);
        // assert
        assertNull(result);
    }

    /**
     * Test when no shop info but has product info - should filter by product price
     */
    @Test
    void testSelectOptimalCouponNoShopWithProduct() throws Throwable {
        // arrange
        // 100 < 150
        List<FridayIntelligentFollowCouponInfoDTO> // 100 < 150
        // 100 < 150
        // 200 > 150
        couponInfoList = Arrays.asList(createCoupon(50L, 100L), createCoupon(100L, 200L));
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, false, true, true, 150);
        // assert
        assertEquals(50L, result.getCouponAmount());
    }

    /**
     * Test when no coupons match product price - should return null
     */
    @Test
    void testSelectOptimalCouponNoMatchingCoupons() throws Throwable {
        // arrange
        // 100 > 50
        List<FridayIntelligentFollowCouponInfoDTO> // 100 > 50
        // 100 > 50
        // 200 > 50
        couponInfoList = Arrays.asList(createCoupon(50L, 100L), createCoupon(100L, 200L));
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, false, true, true, 50);
        // assert
        assertNull(result);
    }

    /**
     * Test when product price is null - should return null
     */
    @Test
    void testSelectOptimalCouponNullProductPrice() throws Throwable {
        // arrange
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList = Collections.singletonList(createCoupon(50L, 100L));
        // act
        FridayIntelligentFollowCouponInfoDTO result = selectOptimalCoupon(couponInfoList, false, true, false, 0);
        // assert
        assertNull(result);
    }

    private boolean invokeIsCouponAlreadyReceived(String couponId, Map<String, ScrmSceneCouponRecords> userReceivedCoupons, Map<String, ValidNextCouponResultDTO> couponValidityMap) throws Throwable {
        try {
            Method method = CouponSelectorService.class.getDeclaredMethod("isCouponAlreadyReceived", String.class, Map.class, Map.class);
            method.setAccessible(true);
            return (boolean) method.invoke(couponSelectorService, couponId, userReceivedCoupons, couponValidityMap);
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }

    @Test
    public void testIsCouponAlreadyReceived_NoRecord_ReturnsFalse() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ScrmSceneCouponRecords> userReceivedCoupons = new HashMap<>();
        Map<String, ValidNextCouponResultDTO> couponValidityMap = new HashMap<>();
        // act
        boolean result = invokeIsCouponAlreadyReceived(couponId, userReceivedCoupons, couponValidityMap);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCouponAlreadyReceived_RecordExistsValidityNull_ReturnsTrue() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ScrmSceneCouponRecords> userReceivedCoupons = new HashMap<>();
        userReceivedCoupons.put(couponId, new ScrmSceneCouponRecords());
        Map<String, ValidNextCouponResultDTO> couponValidityMap = new HashMap<>();
        // act
        boolean result = invokeIsCouponAlreadyReceived(couponId, userReceivedCoupons, couponValidityMap);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsCouponAlreadyReceived_RecordExistsValidityNotAcquirable_ReturnsTrue() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ScrmSceneCouponRecords> userReceivedCoupons = new HashMap<>();
        userReceivedCoupons.put(couponId, new ScrmSceneCouponRecords());
        Map<String, ValidNextCouponResultDTO> couponValidityMap = new HashMap<>();
        ValidNextCouponResultDTO validity = new ValidNextCouponResultDTO();
        validity.setAcquirable(false);
        couponValidityMap.put(couponId, validity);
        // act
        boolean result = invokeIsCouponAlreadyReceived(couponId, userReceivedCoupons, couponValidityMap);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsCouponAlreadyReceived_RecordExistsValidityAcquirable_ReturnsFalse() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ScrmSceneCouponRecords> userReceivedCoupons = new HashMap<>();
        userReceivedCoupons.put(couponId, new ScrmSceneCouponRecords());
        Map<String, ValidNextCouponResultDTO> couponValidityMap = new HashMap<>();
        ValidNextCouponResultDTO validity = new ValidNextCouponResultDTO();
        validity.setAcquirable(true);
        couponValidityMap.put(couponId, validity);
        // act
        boolean result = invokeIsCouponAlreadyReceived(couponId, userReceivedCoupons, couponValidityMap);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCouponAlreadyReceived_NullUserReceivedCoupons_ThrowsNPE() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ValidNextCouponResultDTO> couponValidityMap = new HashMap<>();
        // act & assert
        assertThrows(NullPointerException.class, () -> invokeIsCouponAlreadyReceived(couponId, null, couponValidityMap));
    }

    @Test
    public void testIsCouponAlreadyReceived_NullCouponValidityMap_ThrowsNPE() throws Throwable {
        // arrange
        String couponId = "coupon123";
        Map<String, ScrmSceneCouponRecords> userReceivedCoupons = new HashMap<>();
        userReceivedCoupons.put(couponId, new ScrmSceneCouponRecords());
        // act & assert
        assertThrows(NullPointerException.class, () -> invokeIsCouponAlreadyReceived(couponId, userReceivedCoupons, null));
    }
}
