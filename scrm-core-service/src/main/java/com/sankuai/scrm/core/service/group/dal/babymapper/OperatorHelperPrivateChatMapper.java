package com.sankuai.scrm.core.service.group.dal.babymapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.group.dal.example.OperatorHelperPrivateChatExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OperatorHelperPrivateChatMapper extends MybatisBLOBsMapper<OperatorHelperPrivateChat, OperatorHelperPrivateChatExample, Long> {
    int batchInsert(@Param("list") List<OperatorHelperPrivateChat> list);

    List<OperatorHelperPrivateChat> pageQueryByCodition();

}