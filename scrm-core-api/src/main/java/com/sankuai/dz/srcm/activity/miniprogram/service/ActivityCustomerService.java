package com.sankuai.dz.srcm.activity.miniprogram.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.miniprogram.request.ActivityUserParticipateEventRequest;
import com.sankuai.dz.srcm.activity.miniprogram.vo.ActivityVO;
import com.sankuai.dz.srcm.activity.miniprogram.vo.CouponVO;
import com.sankuai.dz.srcm.activity.miniprogram.vo.HomePageVO;
import com.sankuai.dz.srcm.activity.miniprogram.request.ActivityPageRequest;
import com.sankuai.dz.srcm.activity.miniprogram.vo.InGroupStatusVO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.CityVO;

import java.util.List;

public interface ActivityCustomerService {

    RemoteResponse<HomePageVO> queryHomePage(String appId, Integer cityId, Long mtUserId);

    RemoteResponse<HomePageVO> queryHomePage(String appId, Integer cityId);

    RemoteResponse<ActivityVO> queryActivityPage(ActivityPageRequest request);

    RemoteResponse<List<CityVO>> queryActivityCityList(String appId);

    RemoteResponse<List<CouponVO>> drawCouponPackage(Long itemId, Integer cityId, Long shopId, Long mtUserId);
    RemoteResponse<List<CouponVO>> drawCouponPackage(Long itemId, Integer cityId, Long shopId, Long mtUserId,String token);

    RemoteResponse<List<CouponVO>> drawPlatFormCoupon(Long activityId, Long mtUserId);

    RemoteResponse<InGroupStatusVO> checkInGroup(String appId, Long mtUserId);

    /**
     * 用户参与活动打点
     */
    RemoteResponse<Boolean> logUserParticipateActivityEvent(ActivityUserParticipateEventRequest request);

    RemoteResponse<Boolean> checkNewCustomer(String appId, Long mtUserId);
}
