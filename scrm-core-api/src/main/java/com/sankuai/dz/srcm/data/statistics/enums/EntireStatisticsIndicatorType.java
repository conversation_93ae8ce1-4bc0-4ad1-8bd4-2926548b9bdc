package com.sankuai.dz.srcm.data.statistics.enums;

public enum EntireStatisticsIndicatorType {
    TOTAL_FRIEND_NUM(1, "总客户数"),
    ADD_FRIEND_NUM(2, "新增客户数"),
    LOST_FRIEND_NUM(3, "流失客户人数"),
    ACTIVITY_FRIEND_FREQUENCY(4, "活跃用户数"),
    SESSION_USER_NUM(5, "发言用户数"),
    SESSION_USER_FREQUENCY(6, "发言用户人次");


    private int code;
    private String desc;

    EntireStatisticsIndicatorType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
