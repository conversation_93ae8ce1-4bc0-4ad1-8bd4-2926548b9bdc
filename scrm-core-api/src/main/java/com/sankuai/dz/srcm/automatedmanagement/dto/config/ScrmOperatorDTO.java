package com.sankuai.dz.srcm.automatedmanagement.dto.config;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/15
 */
@Data
public class ScrmOperatorDTO implements Serializable {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: operator_name
     *   说明: 过滤条件运算符
     */
    private String operatorName;

    /**
     *   字段: operator_desc
     *   说明: 过滤条件运算符展示名称
     */
    private String operatorDesc;

    /**
     *   字段: field_type
     *   说明: 支持字段类型
     */
    private String fieldType;

    /**
     *   字段: param_num
     *   说明: 参数数量
     */
    private Byte paramNum;
}
