package com.sankuai.scrm.core.service.group.template.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;
import com.sankuai.scrm.core.service.envrequestforwarding.config.OfflineDataSyncConfig;
import com.sankuai.scrm.core.service.group.template.domain.GroupBehaviorDomainService;
import java.lang.reflect.Field;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GroupAsyncCreateEventListenerOfflineSyncHandleTest {

    @InjectMocks
    private GroupAsyncCreateEventListener groupAsyncCreateEventListener;

    @Mock
    private GroupBehaviorDomainService groupBehaviorDomainService;

    private static final String TEST_CORP_ID = "testCorpId";

    private static final String TEST_MESSAGE = "test message";

    /**
     * Test case for handling null request body
     */
    @Test
    public void testOfflineSyncHandleRequestBodyIsNull() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setMsgContent(null);
        ResultTypeEnum result = groupAsyncCreateEventListener.offlineSyncHandle(request);
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    /**
     * Test case for handling offline and corpId check failure
     */
    @Test
    public void testOfflineSyncHandleOfflineAndCorpIdCheckFail() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setMsgContent("test");
        request.setCorpId("invalidCorpId");
        ResultTypeEnum result = groupAsyncCreateEventListener.offlineSyncHandle(request);
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    /**
     * Test case for successful handling
     */
    @Test
    public void testOfflineSyncHandleSuccess() throws Throwable {
        // Create test data
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setMsgContent("test");
        request.setCorpId("validCorpId");
        // Mock the behavior for success
        ResultTypeEnum result = groupAsyncCreateEventListener.offlineSyncHandle(request);
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
        verify(groupBehaviorDomainService, never()).handlerAsyncCreateEventResult(anyString());
    }

    @Test
    public void testOfflineSyncHandle_WhenRequestIsNull() throws Throwable {
        // Act
        ResultTypeEnum result = groupAsyncCreateEventListener.offlineSyncHandle(null);
        // Assert
        assertEquals(ResultTypeEnum.EXCEPTION, result);
        verifyNoInteractions(groupBehaviorDomainService);
    }
}
