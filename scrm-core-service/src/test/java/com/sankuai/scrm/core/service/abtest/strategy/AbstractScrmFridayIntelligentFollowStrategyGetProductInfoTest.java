package com.sankuai.scrm.core.service.abtest.strategy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowContentDTO;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
public class AbstractScrmFridayIntelligentFollowStrategyGetProductInfoTest {

    @Mock
    private ScrmFridayIntelligentFollowDomainService scrmFridayIntelligentFollowDomainService;

    private TestableAbstractScrmFridayIntelligentFollowStrategy strategy;

    // Create a concrete implementation of the abstract class for testing
    private static class TestableAbstractScrmFridayIntelligentFollowStrategy extends AbstractScrmFridayIntelligentFollowStrategy {

        @Override
        public void fillABTestRecordMessageDTO(ABTestContext context, GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO) {
            // Not needed for this test
        }

        @Override
        protected IntelligentFollowResultDTO buildIntelligentFollowResultDTO(FridayIntelligentFollowResultDTO followResultDTO) {
            // Not needed for this test
            return null;
        }

        @Override
        protected void buildABTestRecordMessageDTOAndSendMessage(ABTestContext context) {
            // Not needed for this test
        }

        @Override
        protected Long getMtUserId(PlatformEnum platform, Long userId) {
            // Not needed for this test
            return null;
        }

        @Override
        protected MtPoiDTO getShopInfo(Long shopId) {
            // Not needed for this test
            return null;
        }

        @Override
        public ABTestContext execute(IntelligentFollowActionTrackDTO param, ABTestContext context) {
            // Not needed for this test
            return null;
        }

        @Override
        public String getName() {
            // Not needed for this test
            return "TestStrategy";
        }
    }

    @BeforeEach
    public void setUp() {
        strategy = new TestableAbstractScrmFridayIntelligentFollowStrategy();
        strategy.scrmFridayIntelligentFollowDomainService = scrmFridayIntelligentFollowDomainService;
    }

    /**
     * Test getProductInfo method with valid product ID and successful domain service response
     */
    @Test
    public void testGetProductInfoWithValidProductId() throws Throwable {
        // arrange
        Long productId = 123L;
        ProductInfoDTO expectedProductInfo = new ProductInfoDTO();
        expectedProductInfo.setProductId(productId);
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenReturn(expectedProductInfo);
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNotNull(result);
        assertEquals(productId, result.getProductId());
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method when domain service returns null
     */
    @Test
    public void testGetProductInfoWhenDomainServiceReturnsNull() throws Throwable {
        // arrange
        Long productId = 123L;
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenReturn(null);
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNull(result);
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method with null product ID
     */
    @Test
    public void testGetProductInfoWithNullProductId() throws Throwable {
        // arrange
        Long productId = null;
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNull(result);
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method when domain service throws exception
     */
    @Test
    public void testGetProductInfoWhenDomainServiceThrowsException() throws Throwable {
        // arrange
        Long productId = 123L;
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenThrow(new RuntimeException("Service error"));
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNull(result);
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method verifies correct DTO construction
     */
    @Test
    public void testGetProductInfoVerifyDtoConstruction() throws Throwable {
        // arrange
        Long productId = 123L;
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenAnswer(invocation -> {
            FridayIntelligentFollowResultDTO dto = invocation.getArgument(0);
            assertNotNull(dto);
            assertNotNull(dto.getContent());
            assertEquals(productId, dto.getContent().getProductId());
            ProductInfoDTO result = new ProductInfoDTO();
            result.setProductId(productId);
            return result;
        });
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNotNull(result);
        assertEquals(productId, result.getProductId());
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method with very large product ID
     */
    @Test
    public void testGetProductInfoWithVeryLargeProductId() throws Throwable {
        // arrange
        Long productId = Long.MAX_VALUE;
        ProductInfoDTO expectedProductInfo = new ProductInfoDTO();
        expectedProductInfo.setProductId(productId);
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenReturn(expectedProductInfo);
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNotNull(result);
        assertEquals(productId, result.getProductId());
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }

    /**
     * Test getProductInfo method with negative product ID
     */
    @Test
    public void testGetProductInfoWithNegativeProductId() throws Throwable {
        // arrange
        Long productId = -123L;
        ProductInfoDTO expectedProductInfo = new ProductInfoDTO();
        expectedProductInfo.setProductId(productId);
        when(scrmFridayIntelligentFollowDomainService.getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class))).thenReturn(expectedProductInfo);
        // act
        ProductInfoDTO result = strategy.getProductInfo(productId);
        // assert
        assertNotNull(result);
        assertEquals(productId, result.getProductId());
        verify(scrmFridayIntelligentFollowDomainService).getProductInfoDTO(any(FridayIntelligentFollowResultDTO.class));
    }
}
