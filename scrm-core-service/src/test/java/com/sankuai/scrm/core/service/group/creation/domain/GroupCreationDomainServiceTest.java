package com.sankuai.scrm.core.service.group.creation.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.SaveGroupDynamicCodeChannelResult;
import com.sankuai.dz.srcm.group.pattern.request.GroupTemplateInfoDTO;
import com.sankuai.dz.srcm.tag.dto.TagDTO;
import com.sankuai.scrm.core.service.group.creation.context.GroupCreationContext;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeChannelLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.service.GroupDynamicCodeChannelDataService;
import com.sankuai.scrm.core.service.group.pattern.domain.GroupTemplateDomainService;
import com.sankuai.scrm.core.service.group.set.domain.GroupSetDomainService;
import com.sankuai.scrm.core.service.group.set.domain.GroupSetRelationDomainService;
import com.sankuai.scrm.core.service.group.template.dao.bo.GroupBatchCreateTaskDetail;
import com.sankuai.scrm.core.service.group.template.dao.bo.GroupBatchCreateTaskOverview;
import com.sankuai.scrm.core.service.group.template.dao.mapper.GroupBatchCreateTaskOverviewMapper;
import com.sankuai.scrm.core.service.group.template.domain.GroupCreateTaskOverviewDomainService;
import com.sankuai.scrm.core.service.group.template.enums.GroupCreateTaskType;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.adapter.bo.PrivateLiveGroupInfoBO;
import com.sankuai.scrm.core.service.pchat.adapter.bo.PrivateLiveGroupSetBO;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupCreationDomainServiceTest {

    @InjectMocks
    private GroupCreationDomainService groupCreationDomainService;

    @Mock
    private GroupCreateTaskOverviewDomainService taskOverviewDomainService;

    @Mock
    private GroupSetDomainService groupSetDomainService;

    @Mock
    private GroupSetRelationDomainService groupSetRelationDomainService;

    @Mock
    private GroupTemplateDomainService groupTemplateDomainService;

    @Mock
    private GroupDynamicCodeLocalService groupDynamicCodeLocalService;

    @Mock
    private PrivateLiveGroupSetBO groupSetBO;

    @Mock
    private PrivateLiveGroupInfoBO groupInfoBO;

    @Mock
    private GroupDynamicCodeChannelDataService channelDataService;

    @Mock
    private GroupDynamicCodeChannelLocalService channelLocalService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(groupSetBO.getGroupInfoList()).thenReturn(Arrays.asList(groupInfoBO));
        when(groupSetBO.getAppId()).thenReturn("testAppId");
        when(groupSetBO.getLiveId()).thenReturn("testLiveId");
        // Mock appConfigRepository
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        // Mock taskOverviewDomainService
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_DetailIsNull() throws Throwable {
        groupCreationDomainService.createGroupSetAndTemplateAndCode(null, "groupId");
        verifyNoInteractions(taskOverviewDomainService, groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_GroupIdIsNull() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, null);
        verifyNoInteractions(taskOverviewDomainService, groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_TaskIdIsNull() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(null);
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "groupId");
        verifyNoInteractions(taskOverviewDomainService, groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_GroupIdIsEmpty() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "");
        verifyNoInteractions(taskOverviewDomainService, groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_OverviewIsNull() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        when(taskOverviewDomainService.queryById(detail.getTaskId())).thenReturn(null);
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "groupId");
        verify(taskOverviewDomainService).queryById(detail.getTaskId());
        verifyNoInteractions(groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test
    public void testCreateGroupSetAndTemplateAndCode_TaskTypeIsNotTagCreationGroupTask() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        GroupBatchCreateTaskOverview overview = new GroupBatchCreateTaskOverview();
        overview.setTaskType(GroupCreateTaskType.TEMPLATE_CREATION_GROUP_TASK.getCode());
        when(taskOverviewDomainService.queryById(detail.getTaskId())).thenReturn(overview);
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "groupId");
        verify(taskOverviewDomainService).queryById(detail.getTaskId());
        verifyNoInteractions(groupSetDomainService, groupSetRelationDomainService, groupTemplateDomainService, groupDynamicCodeLocalService);
    }

    @Test(expected = RuntimeException.class)
    public void testCreateGroupSetAndTemplateAndCode_Success() throws Throwable {
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        GroupBatchCreateTaskOverview overview = new GroupBatchCreateTaskOverview();
        overview.setTaskType(GroupCreateTaskType.TAG_CREATION_GROUP_TASK.getCode());
        when(taskOverviewDomainService.queryById(detail.getTaskId())).thenReturn(overview);
        when(groupTemplateDomainService.saveGroupTemplateInfo(any(GroupTemplateInfoDTO.class))).thenReturn("success");
        // Mock the createCode method to return a non-null value
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "groupId");
        verify(taskOverviewDomainService).queryById(detail.getTaskId());
        verify(groupTemplateDomainService).saveGroupTemplateInfo(any(GroupTemplateInfoDTO.class));
        verify(groupDynamicCodeLocalService).createCode(any());
        verify(groupSetDomainService).createGroupSet(anyString(), anyLong(), anyLong(), anyList());
        verify(groupSetRelationDomainService).addRelation(anyLong(), anyString());
    }

    /**
     * 测试 createPrivateLiveGroupTask 方法，getGroupDynamicCodeChannel 方法抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCreatePrivateLiveGroupTaskException1() throws Throwable {
        // arrange
        when(channelDataService.queryByAppIdAndName(anyString(), anyString(), anyList())).thenReturn(Arrays.asList());
        when(channelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(new SaveGroupDynamicCodeChannelResult(1, "error", null));
        // act
        groupCreationDomainService.createPrivateLiveGroupTask(groupSetBO);
        // assert
    }

    /**
     * 测试 createPrivateLiveGroupTask 方法，saveGroupCreateTaskOverview 方法抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCreatePrivateLiveGroupTaskException2() throws Throwable {
        // arrange
        GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
        channelDTO.setId(1L);
        when(channelDataService.queryByAppIdAndName(anyString(), anyString(), anyList())).thenReturn(Arrays.asList(channelDTO));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(taskOverviewDomainService.createTaskOverview(any(GroupBatchCreateTaskOverview.class))).thenReturn(false);
        // act
        groupCreationDomainService.createPrivateLiveGroupTask(groupSetBO);
        // assert
    }

    /**
     * 测试 createPrivateLiveGroupTask 方法，batchSaveGroupCreateTaskDetail 方法抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCreatePrivateLiveGroupTaskException3() throws Throwable {
        // arrange
        GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
        channelDTO.setId(1L);
        when(channelDataService.queryByAppIdAndName(anyString(), anyString(), anyList())).thenReturn(Arrays.asList(channelDTO));
        when(groupSetBO.getAppId()).thenReturn("appId");
        when(groupSetBO.getLiveId()).thenReturn("liveId");
        // Simulate invalid input for batchSaveGroupCreateTaskDetail
        when(groupSetBO.getGroupInfoList()).thenReturn(Arrays.asList());
        // act
        groupCreationDomainService.createPrivateLiveGroupTask(groupSetBO);
        // assert
    }

    /**
     * Test case: createPrivateLiveGroupDynCode returns null
     */
    @Test(expected = RuntimeException.class)
    public void testCreateGroupSetAndTemplateAndCode_WhenDynamicCodeNull() {
        // arrange
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        detail.setTagList("[{\"tagId\":\"1\",\"tagName\":\"test\"}]");
        GroupBatchCreateTaskOverview overview = new GroupBatchCreateTaskOverview();
        overview.setTaskType(GroupCreateTaskType.TAG_CREATION_GROUP_TASK.getCode());
        overview.setAppId("testApp");
        when(taskOverviewDomainService.queryById(anyLong())).thenReturn(overview);
        when(groupTemplateDomainService.saveGroupTemplateInfo(any())).thenReturn(null);
        // act
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "testGroupId");
    }

    /**
     * Test case: createGroupSet returns null
     */
    @Test(expected = RuntimeException.class)
    public void testCreateGroupSetAndTemplateAndCode_WhenGroupSetNull() {
        // arrange
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        detail.setTagList("[{\"tagId\":\"1\",\"tagName\":\"test\"}]");
        GroupBatchCreateTaskOverview overview = new GroupBatchCreateTaskOverview();
        overview.setTaskType(GroupCreateTaskType.TAG_CREATION_GROUP_TASK.getCode());
        overview.setAppId("testApp");
        GroupDynamicCodeInfoDTO codeInfo = new GroupDynamicCodeInfoDTO();
        codeInfo.setId(1L);
        when(taskOverviewDomainService.queryById(anyLong())).thenReturn(overview);
        when(groupTemplateDomainService.saveGroupTemplateInfo(any())).thenReturn(null);
        // act
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "testGroupId");
    }

    /**
     * Test case: addRelation returns false
     */
    @Test(expected = RuntimeException.class)
    public void testCreateGroupSetAndTemplateAndCode_WhenAddRelationFalse() {
        // arrange
        GroupBatchCreateTaskDetail detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        detail.setTagList("[{\"tagId\":\"1\",\"tagName\":\"test\"}]");
        GroupBatchCreateTaskOverview overview = new GroupBatchCreateTaskOverview();
        overview.setTaskType(GroupCreateTaskType.TAG_CREATION_GROUP_TASK.getCode());
        overview.setAppId("testApp");
        GroupDynamicCodeInfoDTO codeInfo = new GroupDynamicCodeInfoDTO();
        codeInfo.setId(1L);
        when(taskOverviewDomainService.queryById(anyLong())).thenReturn(overview);
        when(groupTemplateDomainService.saveGroupTemplateInfo(any())).thenReturn(null);
        // act
        groupCreationDomainService.createGroupSetAndTemplateAndCode(detail, "testGroupId");
    }
}
