package com.sankuai.scrm.core.service.group.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxGroupAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupMemberDetail;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/3/3 15:36
 * @Version v1.0.0
 */

@Slf4j
@Component
public class UnionIdRefreshJob {

    @Resource
    private GroupMemberDomainService groupMemberDomainService;
    @Resource
    private CorpAppConfigRepository appConfigRepository;
    @Resource
    private CorpWxGroupAcl corpWxGroupAcl;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.fill.group.member.unionId")
    public void fillGroupMemberUnionId() {
        log.info("fillGroupMemberUnionId start");
        List<CorpAppConfig> configs = appConfigRepository.getConfigs();
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        log.info("fillGroupMemberUnionId configs size:{}", configs.size());
        List<String> corpIds = configs.stream().map(CorpAppConfig::getCorpId).collect(Collectors.toList());
        corpIds.forEach(this::fillGroupMemberUnionId);
    }

    public void fillGroupMemberUnionId(String corpId) {
        log.info("fillGroupMemberUnionId: corpId={}", corpId);
        int rows = 500;
//        int offset = 0;
        Set<String> groupIds = new HashSet<>();
        Set<String> groupMemberIds = new HashSet<>();
        int minId = Lion.getInt(Environment.getAppName(), "group.member.unionId.refresh.minId", 0);
        Integer offsetDay = Lion.getInt(Environment.getAppName(), "group.member.unionId.refresh.offset.day", null);
        Date startDate = null;
        if (offsetDay != null) {
            startDate = DateUtils.addDays(new Date(), -offsetDay);
        }
        while (true) {
            log.info("fillGroupMemberUnionId,row={},minId={},corpId={}", rows, minId, corpId);
            List<MemberInfoEntity> memberInfoEntities = groupMemberDomainService.queryUnionIdIsNullRecords(corpId, minId, rows,startDate);
            if (CollectionUtils.isEmpty(memberInfoEntities)) {
                break;
            }
            minId = memberInfoEntities.get(memberInfoEntities.size() - 1).getId();
            for (MemberInfoEntity memberInfoEntity : memberInfoEntities) {
                if (memberInfoEntity.getGroupId() == null || groupMemberIds.contains(memberInfoEntity.getGroupMemberId()) || groupIds.contains(memberInfoEntity.getGroupId())) {
                    continue;
                }
                groupIds.add(memberInfoEntity.getGroupId());
                log.info("fillGroupMemberUnionId,groupId={} corpId={}", memberInfoEntity.getGroupId(), corpId);
                groupMemberIds.add(memberInfoEntity.getGroupMemberId());
                fillGroupMemberUnionId(corpId, memberInfoEntity.getGroupId());
            }
//            offset += rows;
        }
    }

    private void fillGroupMemberUnionId(String corpId, String groupId) {
        log.info("fillGroupMemberUnionId,corpId={},groupId={}", corpId, groupId);
        WxGroupDetail wxGroupDetail = corpWxGroupAcl.getWxGroupDetail(corpId, groupId, true);
        log.info("fillGroupMemberUnionId,wxGroupDetail={}", wxGroupDetail);
        if (wxGroupDetail == null) {
            return;
        }
        List<WxGroupMemberDetail> wxMemberList = wxGroupDetail.getMemberList();
        if (CollectionUtils.isEmpty(wxMemberList)) {
            return;
        }
        log.info("wxMemberList.size={}", wxMemberList.size());
        Map<String, String> memberIdWithUnionIdMap = wxMemberList.stream().filter(m -> StringUtils.isNotBlank(m.getUnionId())).collect(Collectors.toMap(WxGroupMemberDetail::getGroupMemberId, WxGroupMemberDetail::getUnionId, (u1, u2) -> u1));
        if (MapUtils.isEmpty(memberIdWithUnionIdMap)) {
            return;
        }
        List<MemberInfoEntity> dbMemberInfoEntities = groupMemberDomainService.queryMembersByGroupId(corpId, groupId);
        if (CollectionUtils.isEmpty(dbMemberInfoEntities)) {
            return;
        }
        log.info("dbMemberInfoEntities.size={}", dbMemberInfoEntities.size());
        dbMemberInfoEntities = dbMemberInfoEntities.stream().filter(member -> member.getUnionId() == null && memberIdWithUnionIdMap.containsKey(member.getGroupMemberId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dbMemberInfoEntities)) {
            return;
        }
        log.info("dbMemberInfoEntities.filter.size={}", dbMemberInfoEntities.size());

        updateGroupMemberUnionId(memberIdWithUnionIdMap, dbMemberInfoEntities);

    }

    private void updateGroupMemberUnionId(Map<String, String> memberIdWithUnionIdMap, List<MemberInfoEntity> dbMemberInfoEntities) {
        log.info("updateGroupMemberUnionId start,update size={}", dbMemberInfoEntities.size());
        AtomicInteger count = new AtomicInteger();
        dbMemberInfoEntities.forEach(memberInfoEntity -> {
            if (memberIdWithUnionIdMap.containsKey(memberInfoEntity.getGroupMemberId())) {
                memberInfoEntity.setUnionId(memberIdWithUnionIdMap.get(memberInfoEntity.getGroupMemberId()));
                groupMemberDomainService.updateGroupMemberUnionId(memberInfoEntity);
                count.getAndIncrement();
            }
        });
        log.info("updateGroupMemberUnionId end,update count={}", count);
    }

    public void fillGroupMemberUnionId(Map<String, String> groupMemberIdWithUnionIdMap) {
        try {
            log.info("fillGroupMemberUnionId start,{}", groupMemberIdWithUnionIdMap);
            if (MapUtils.isEmpty(groupMemberIdWithUnionIdMap)) {
                return;
            }
            List<String> groupMemberIds = new ArrayList<>(groupMemberIdWithUnionIdMap.keySet());
            List<MemberInfoEntity> memberInfoEntities = groupMemberDomainService.queryUnionIdIsNullRecords(groupMemberIds);
            if (CollectionUtils.isEmpty(memberInfoEntities)) {
                return;
            }
            updateGroupMemberUnionId(groupMemberIdWithUnionIdMap, memberInfoEntities);
        } catch (Exception e) {
            log.error("fillGroupMemberUnionId error", e);
        } finally {
            log.info("fillGroupMemberUnionId end");
        }
    }
}
