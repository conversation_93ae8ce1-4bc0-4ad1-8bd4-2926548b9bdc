package com.sankuai.scrm.core.service.pchat.dal.activity.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.activity.example.ScrmPersonalWxActivityRuleExample;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRule;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxActivityRuleMapper extends MybatisBaseMapper<ScrmPersonalWxActivityRule, ScrmPersonalWxActivityRuleExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxActivityRule> list);
}