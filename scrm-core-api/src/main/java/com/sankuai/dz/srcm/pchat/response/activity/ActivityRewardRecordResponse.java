package com.sankuai.dz.srcm.pchat.response.activity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/22 11:08
 * @Version v1.0.0
 */
@Data
public class ActivityRewardRecordResponse implements Serializable {
    private Long activityId;
    private String webcastId;
    /**
     * 直播名称
     */
    private String webcastName;
    /**
     * 活动名称
     */
    private String activityName;
    private Long groupId;
    /**
     * 群名称
     */
    private String groupName;
    /**
     * 聊天室微信号
     */
    private String chatRoomWxSerialNo;
    /**
     * 微信昵称
     */
    private String wxNickname;
    private String wxId;
    private Long userId;
    private String phone;
    /**
     * 群成员id
     */
    private Long groupMemberId;
    /**
     * 邀请人数
     */
    private Integer inviteCount;
    /**
     * 奖品id
     */
    private Long rewardProductId;
    /**
     * 奖品名称
     */
    private String rewardProductName;
}
