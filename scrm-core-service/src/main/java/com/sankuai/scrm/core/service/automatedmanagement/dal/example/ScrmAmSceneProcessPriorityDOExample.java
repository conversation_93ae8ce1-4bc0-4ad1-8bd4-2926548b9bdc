package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmAmSceneProcessPriorityDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmAmSceneProcessPriorityDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmAmSceneProcessPriorityDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmAmSceneProcessPriorityDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmAmSceneProcessPriorityDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNull() {
            addCriterion("scene_id is null");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNotNull() {
            addCriterion("scene_id is not null");
            return (Criteria) this;
        }

        public Criteria andSceneIdEqualTo(Long value) {
            addCriterion("scene_id =", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotEqualTo(Long value) {
            addCriterion("scene_id <>", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThan(Long value) {
            addCriterion("scene_id >", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("scene_id >=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThan(Long value) {
            addCriterion("scene_id <", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanOrEqualTo(Long value) {
            addCriterion("scene_id <=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdIn(List<Long> values) {
            addCriterion("scene_id in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotIn(List<Long> values) {
            addCriterion("scene_id not in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdBetween(Long value1, Long value2) {
            addCriterion("scene_id between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotBetween(Long value1, Long value2) {
            addCriterion("scene_id not between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNull() {
            addCriterion("process_orchestration_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNotNull() {
            addCriterion("process_orchestration_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdEqualTo(Long value) {
            addCriterion("process_orchestration_id =", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotEqualTo(Long value) {
            addCriterion("process_orchestration_id <>", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThan(Long value) {
            addCriterion("process_orchestration_id >", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id >=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThan(Long value) {
            addCriterion("process_orchestration_id <", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id <=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIn(List<Long> values) {
            addCriterion("process_orchestration_id in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotIn(List<Long> values) {
            addCriterion("process_orchestration_id not in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id not between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNull() {
            addCriterion("process_orchestration_version is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNotNull() {
            addCriterion("process_orchestration_version is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionEqualTo(String value) {
            addCriterion("process_orchestration_version =", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotEqualTo(String value) {
            addCriterion("process_orchestration_version <>", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThan(String value) {
            addCriterion("process_orchestration_version >", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version >=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThan(String value) {
            addCriterion("process_orchestration_version <", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version <=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLike(String value) {
            addCriterion("process_orchestration_version like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotLike(String value) {
            addCriterion("process_orchestration_version not like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIn(List<String> values) {
            addCriterion("process_orchestration_version in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotIn(List<String> values) {
            addCriterion("process_orchestration_version not in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionBetween(String value1, String value2) {
            addCriterion("process_orchestration_version between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotBetween(String value1, String value2) {
            addCriterion("process_orchestration_version not between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIsNull() {
            addCriterion("process_orchestration_node_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIsNotNull() {
            addCriterion("process_orchestration_node_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdEqualTo(Long value) {
            addCriterion("process_orchestration_node_id =", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotEqualTo(Long value) {
            addCriterion("process_orchestration_node_id <>", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdGreaterThan(Long value) {
            addCriterion("process_orchestration_node_id >", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_node_id >=", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdLessThan(Long value) {
            addCriterion("process_orchestration_node_id <", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdLessThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_node_id <=", value, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdIn(List<Long> values) {
            addCriterion("process_orchestration_node_id in", values, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotIn(List<Long> values) {
            addCriterion("process_orchestration_node_id not in", values, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_node_id between", value1, value2, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationNodeIdNotBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_node_id not between", value1, value2, "processOrchestrationNodeId");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidIsNull() {
            addCriterion("couponGroupId is null");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidIsNotNull() {
            addCriterion("couponGroupId is not null");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidEqualTo(String value) {
            addCriterion("couponGroupId =", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidNotEqualTo(String value) {
            addCriterion("couponGroupId <>", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidGreaterThan(String value) {
            addCriterion("couponGroupId >", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidGreaterThanOrEqualTo(String value) {
            addCriterion("couponGroupId >=", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidLessThan(String value) {
            addCriterion("couponGroupId <", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidLessThanOrEqualTo(String value) {
            addCriterion("couponGroupId <=", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidLike(String value) {
            addCriterion("couponGroupId like", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidNotLike(String value) {
            addCriterion("couponGroupId not like", value, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidIn(List<String> values) {
            addCriterion("couponGroupId in", values, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidNotIn(List<String> values) {
            addCriterion("couponGroupId not in", values, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidBetween(String value1, String value2) {
            addCriterion("couponGroupId between", value1, value2, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCoupongroupidNotBetween(String value1, String value2) {
            addCriterion("couponGroupId not between", value1, value2, "coupongroupid");
            return (Criteria) this;
        }

        public Criteria andCouponamountIsNull() {
            addCriterion("couponAmount is null");
            return (Criteria) this;
        }

        public Criteria andCouponamountIsNotNull() {
            addCriterion("couponAmount is not null");
            return (Criteria) this;
        }

        public Criteria andCouponamountEqualTo(BigDecimal value) {
            addCriterion("couponAmount =", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountNotEqualTo(BigDecimal value) {
            addCriterion("couponAmount <>", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountGreaterThan(BigDecimal value) {
            addCriterion("couponAmount >", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("couponAmount >=", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountLessThan(BigDecimal value) {
            addCriterion("couponAmount <", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("couponAmount <=", value, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountIn(List<BigDecimal> values) {
            addCriterion("couponAmount in", values, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountNotIn(List<BigDecimal> values) {
            addCriterion("couponAmount not in", values, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("couponAmount between", value1, value2, "couponamount");
            return (Criteria) this;
        }

        public Criteria andCouponamountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("couponAmount not between", value1, value2, "couponamount");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("record_time is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("record_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("record_time =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("record_time <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("record_time >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("record_time >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("record_time <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("record_time <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("record_time in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("record_time not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("record_time between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("record_time not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}