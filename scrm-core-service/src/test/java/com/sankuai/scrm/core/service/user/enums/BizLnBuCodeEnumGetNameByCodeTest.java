package com.sankuai.scrm.core.service.user.enums;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class BizLnBuCodeEnumGetNameByCodeTest {

    /**
     * 测试传入有效code返回对应name - 使用参数化测试
     */
    @ParameterizedTest
    @MethodSource("validCodeAndNameProvider")
    void testGetNameByCodeWithValidCode(Integer code, String expectedName) {
        // act
        String actualName = BizLnBuCodeEnum.getNameByCode(code);
        // assert
        assertEquals(expectedName, actualName);
    }

    private static Stream<Arguments> validCodeAndNameProvider() {
        return Stream.of(Arguments.of(-10000, "全部"), Arguments.of(1, "LifeEvent"), Arguments.of(2, "教育及母婴"), Arguments.of(3, "休娱和丽人"), Arguments.of(4, "医药健康"));
    }

    /**
     * 测试传入null返回null
     */
    @Test
    void testGetNameByCodeWithNullCode() {
        // act
        String result = BizLnBuCodeEnum.getNameByCode(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试传入不存在的code返回null
     */
    @Test
    void testGetNameByCodeWithInvalidCode() {
        // act
        String result = BizLnBuCodeEnum.getNameByCode(9999);
        // assert
        assertNull(result);
    }

    /**
     * 演示如何使用Mockito mock静态方法（虽然实际不推荐用于此类场景）
     */
    @Test
    void testGetNameByCodeWithMockedStatic() {
        try (MockedStatic<BizLnBuCodeEnum> mocked = Mockito.mockStatic(BizLnBuCodeEnum.class)) {
            // arrange
            mocked.when(() -> BizLnBuCodeEnum.getNameByCode(anyInt())).thenReturn("MockedName");
            // act
            String result = BizLnBuCodeEnum.getNameByCode(123);
            // assert
            assertEquals("MockedName", result);
        }
    }

    /**
     * 测试边界情况 - 最大/最小可能的Integer值
     */
    @Test
    void testGetNameByCodeWithExtremeValues() {
        // act & assert
        assertNull(BizLnBuCodeEnum.getNameByCode(Integer.MAX_VALUE));
        assertNull(BizLnBuCodeEnum.getNameByCode(Integer.MIN_VALUE));
    }

    @Test
    public void testGetByCodeWithNullInput() throws Throwable {
        // arrange
        Integer code = null;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null when input code is null");
    }

    @Test
    public void testGetByCodeWithValidCodeAll() throws Throwable {
        // arrange
        Integer code = -10000;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should not return null for valid code");
        assertEquals(BizLnBuCodeEnum.ALL, result, "getByCode should return ALL enum for code -10000");
        assertEquals(code, result.getCode(), "Returned enum should have the correct code");
        assertEquals("全部", result.getName(), "Returned enum should have the correct name");
    }

    @Test
    public void testGetByCodeWithValidCodeLifeEvent() throws Throwable {
        // arrange
        Integer code = 1;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should not return null for valid code");
        assertEquals(BizLnBuCodeEnum.LIFE_EVENT, result, "getByCode should return LIFE_EVENT enum for code 1");
        assertEquals(code, result.getCode(), "Returned enum should have the correct code");
        assertEquals("LifeEvent", result.getName(), "Returned enum should have the correct name");
    }

    @Test
    public void testGetByCodeWithValidCodeEducationMaternal() throws Throwable {
        // arrange
        Integer code = 2;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should not return null for valid code");
        assertEquals(BizLnBuCodeEnum.EDUCATION_MATERNAL, result, "getByCode should return EDUCATION_MATERNAL enum for code 2");
        assertEquals(code, result.getCode(), "Returned enum should have the correct code");
        assertEquals("教育及母婴", result.getName(), "Returned enum should have the correct name");
    }

    @Test
    public void testGetByCodeWithValidCodeEntertainmentBeauty() throws Throwable {
        // arrange
        Integer code = 3;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should not return null for valid code");
        assertEquals(BizLnBuCodeEnum.ENTERTAINMENT_BEAUTY, result, "getByCode should return ENTERTAINMENT_BEAUTY enum for code 3");
        assertEquals(code, result.getCode(), "Returned enum should have the correct code");
        assertEquals("休娱和丽人", result.getName(), "Returned enum should have the correct name");
    }

    @Test
    public void testGetByCodeWithValidCodeMedicalHealth() throws Throwable {
        // arrange
        Integer code = 4;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should not return null for valid code");
        assertEquals(BizLnBuCodeEnum.MEDICAL_HEALTH, result, "getByCode should return MEDICAL_HEALTH enum for code 4");
        assertEquals(code, result.getCode(), "Returned enum should have the correct code");
        assertEquals("医药健康", result.getName(), "Returned enum should have the correct name");
    }

    @Test
    public void testGetByCodeWithInvalidPositiveCode() throws Throwable {
        // arrange
        Integer code = 999;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null for invalid code 999");
    }

    @Test
    public void testGetByCodeWithInvalidNegativeCode() throws Throwable {
        // arrange
        Integer code = -999;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null for invalid code -999");
    }

    @Test
    public void testGetByCodeWithZeroCode() throws Throwable {
        // arrange
        Integer code = 0;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null for code 0");
    }

    @Test
    public void testGetByCodeWithBoundaryCodeAboveRange() throws Throwable {
        // arrange
        Integer code = 5;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null for boundary code 5");
    }

    @Test
    public void testGetByCodeWithBoundaryCodeBelowRange() throws Throwable {
        // arrange
        Integer code = -10001;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNull(result, "getByCode should return null for boundary code -10001");
    }

    @Test
    public void testGetByCodeCoversAllEnumValues() throws Throwable {
        // arrange & act & assert
        BizLnBuCodeEnum[] allValues = BizLnBuCodeEnum.values();
        assertTrue(allValues.length > 0, "Enum should have at least one value");
        for (BizLnBuCodeEnum enumValue : allValues) {
            // act
            BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(enumValue.getCode());
            // assert
            assertNotNull(result, "getByCode should return non-null for valid enum code: " + enumValue.getCode());
            assertEquals(enumValue, result, "getByCode should return the correct enum for code: " + enumValue.getCode());
            assertEquals(enumValue.getCode(), result.getCode(), "Returned enum should have matching code");
            assertEquals(enumValue.getName(), result.getName(), "Returned enum should have matching name");
        }
    }

    @Test
    public void testGetByCodeWithIntegerObjectWrapping() throws Throwable {
        // arrange
        Integer code = Integer.valueOf(1);
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should handle Integer object wrapping correctly");
        assertEquals(BizLnBuCodeEnum.LIFE_EVENT, result, "getByCode should return LIFE_EVENT for Integer.valueOf(1)");
    }

    @Test
    public void testGetByCodeIterationBehavior() throws Throwable {
        // arrange - test with the last enum value to ensure full iteration
        // MEDICAL_HEALTH is likely the last enum based on the code sequence
        Integer code = 4;
        // act
        BizLnBuCodeEnum result = BizLnBuCodeEnum.getByCode(code);
        // assert
        assertNotNull(result, "getByCode should find enum even if it's the last in iteration");
        assertEquals(BizLnBuCodeEnum.MEDICAL_HEALTH, result, "getByCode should return MEDICAL_HEALTH for code 4");
        // arrange - test with the first enum value
        // ALL is likely the first enum
        Integer firstCode = -10000;
        // act
        BizLnBuCodeEnum firstResult = BizLnBuCodeEnum.getByCode(firstCode);
        // assert
        assertNotNull(firstResult, "getByCode should find enum even if it's the first in iteration");
        assertEquals(BizLnBuCodeEnum.ALL, firstResult, "getByCode should return ALL for code -10000");
    }
}
