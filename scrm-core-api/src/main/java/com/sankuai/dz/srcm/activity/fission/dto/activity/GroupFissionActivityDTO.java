package com.sankuai.dz.srcm.activity.fission.dto.activity;

import com.sankuai.dz.srcm.activity.fission.request.CopyDynamicChannelInfo;
import com.sankuai.dz.srcm.activity.fission.request.FriendShareQrcodeInfo;
import com.sankuai.dz.srcm.activity.fission.request.ShareCardInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GroupFissionActivityDTO implements Serializable {
    private Long activityId;
    private String appId;
    private ActivityBaseInfoDTO activityBaseInfo;
    private PosterInfoDTO posterInfo;
    private ShareCardInfo shareCardInfo;
    private CopyDynamicChannelInfo copyDynamicChannelInfo;
    private List<RewardInfoDTO> rewardInfo;
    private List<PersonalGroupInfoDTO> personalGroupInfo;
}
