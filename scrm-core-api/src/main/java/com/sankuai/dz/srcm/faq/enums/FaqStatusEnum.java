package com.sankuai.dz.srcm.faq.enums;

import java.util.Arrays;

/**
 * <AUTHOR> on 2022/12/7 10:47 AM
 **/
public enum FaqStatusEnum {
    VALID(0, "已同步"),
    DELETED(1, "用户删除"),
    VERIFYING(2, "审核中"),
    AUDIT_REJECT(4, "审核驳回"),
    INIT(5, "新编辑"),
    MANUAL_AUDIT(15, "未同步"),
    ;


    public final int status;
    public final String desc;

     FaqStatusEnum(final int status, final String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static FaqStatusEnum of(Integer code) {
        if(code == null)return null;
        return Arrays.stream(FaqStatusEnum.values()).filter(r->r.status == code).findFirst().orElse(null);
    }
}
