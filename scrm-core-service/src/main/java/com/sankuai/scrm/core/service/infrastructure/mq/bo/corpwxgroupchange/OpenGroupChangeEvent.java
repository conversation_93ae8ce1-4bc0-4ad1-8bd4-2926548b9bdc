package com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/3/10 10:32
 * @Version v1.0.0
 */
@Data
public class OpenGroupChangeEvent implements Serializable {
    private Long eventTime;
    /**
     * entrust 群被托管入库
     * un_entrust 群变为非托管态，出库
     * update 群信息变化
     */
    private String dsChangeType;

    private String corpId;
    private Integer orgId;
    private String businessCode;
    /**
     * 企微微信的原始事件类型 ChangeType
     */
    private String wxEventChangeType;
    /**
     * UpdateDetail 原始的企微UpdateDetail
     */
    private String wxUpdateDetail;
    /**
     * 群信息
     */
    private OpenGroupSimpleInfo openGroupSimpleInfo;
    /**
     * 新加入的成员
     * change_member 时附加
     */
    private List<OpenGroupMemberSimpleInfo> addedMemberList;
    /**
     * 退群的成员
     * change_member 时附加
     */
    private List<OpenGroupMemberSimpleInfo> removedMemberList;
}
