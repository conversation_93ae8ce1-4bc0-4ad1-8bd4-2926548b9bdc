package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

@Getter
public enum OpTypeEnum {

    ADD("1", "添加"),
    EDIT("2", "修改"),
    DELETE("3", "删除"),

    ;

    private final String code;

    private final String desc;

    OpTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OpTypeEnum fromCode(String code) {
        for (OpTypeEnum enumValue : OpTypeEnum.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }

}
