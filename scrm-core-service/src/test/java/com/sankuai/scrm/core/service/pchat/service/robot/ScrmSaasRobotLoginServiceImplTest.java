package com.sankuai.scrm.core.service.pchat.service.robot;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.robot.SaasRobotDTO;
import com.sankuai.dz.srcm.pchat.request.robot.DeleteRobotBelongClusterRequest;
import com.sankuai.dz.srcm.pchat.request.robot.EditRobotValidStatusRequest;
import com.sankuai.dz.srcm.pchat.request.robot.GetLoginStatusRequest;
import com.sankuai.dz.srcm.pchat.request.robot.GetScanQrCodeRequest;
import com.sankuai.dz.srcm.pchat.request.robot.InputSecurityVerificationCodeRequest;
import com.sankuai.dz.srcm.pchat.request.robot.RefreshRobotQrCodeRequest;
import com.sankuai.dz.srcm.pchat.response.robot.GetScanQrCodeResponse;
import com.sankuai.dz.srcm.pchat.response.robot.ScanQrCodeStatusResponse;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginAnchorInfoDTO;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginUserInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxAccountClusterHandleLogDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxRobotLoginDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxSaasRobotLoginDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.dto.UserContextDTO;
import com.sankuai.scrm.core.service.pchat.enums.ClusterRobotType;
import com.sankuai.scrm.core.service.pchat.enums.LiveServiceTypeEnum;
import com.sankuai.scrm.core.service.pchat.service.robot.ScrmSaasRobotLoginServiceImpl;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmSaasRobotLoginServiceImplTest {

    @InjectMocks
    private ScrmSaasRobotLoginServiceImpl scrmSaasRobotLoginService;

    @Mock
    private ScrmPersonalWxUserDomainService wxUserDomainService;

    @Mock
    private ScrmRobotCommonService robotCommonService;

    private EditRobotValidStatusRequest request;

    private LoginAnchorInfoDTO anchorInfoDTO;

    private LoginUserInfo mainAnchorInfo;

    private ScrmPersonalWxRobotInfo robotInfo;

    @Mock
    private ScrmPersonalWxRobotLoginDomainService loginDomainService;

    @Mock
    private ScrmPersonalWxAccountClusterHandleLogDomainService handleLogDomainService;

    @Mock
    private ScrmPersonalWxSaasRobotLoginDomainService saasLoginDomainService;

    @Before
    public void setUp() {
        request = new EditRobotValidStatusRequest();
        request.setRobotSerialNo("robotSerialNo");
        request.setValid(1);
        anchorInfoDTO = new LoginAnchorInfoDTO();
        anchorInfoDTO.setAnchorId(1L);
        mainAnchorInfo = new LoginUserInfo();
        mainAnchorInfo.setAnchorId(1L);
        anchorInfoDTO.setMainAnchorInfo(mainAnchorInfo);
        robotInfo = new ScrmPersonalWxRobotInfo();
        robotInfo.setTenantId(1L);
    }

    private UserContextDTO createUserContextDTO() {
        return new UserContextDTO();
    }

    private LoginAnchorInfoDTO createLoginAnchorInfoDTO() {
        LoginAnchorInfoDTO loginAnchorInfoDTO = new LoginAnchorInfoDTO();
        loginAnchorInfoDTO.setAnchorId(1L);
        LoginUserInfo loginUserInfo = new LoginUserInfo();
        loginUserInfo.setUserId(1L);
        loginAnchorInfoDTO.setMainAnchorInfo(loginUserInfo);
        return loginAnchorInfoDTO;
    }

    @Test
    public void testEditotValidStatusNormal() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            when(wxUserDomainService.queryRobotBySerialNo(anyString())).thenReturn(Collections.singletonList(robotInfo));
            when(robotCommonService.editRobotValidStatus(any(), any())).thenReturn(RemoteResponse.success(true));
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.success(true);
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusException() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            when(wxUserDomainService.queryRobotBySerialNo(anyString())).thenReturn(Collections.singletonList(robotInfo));
            when(robotCommonService.editRobotValidStatus(any(), any())).thenThrow(new RuntimeException("exception"));
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("编辑失败");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusRobotNotExist() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            when(wxUserDomainService.queryRobotBySerialNo(anyString())).thenReturn(Collections.emptyList());
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("机器人不存在");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusRobotNotBelong() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            when(wxUserDomainService.queryRobotBySerialNo(anyString())).thenReturn(Collections.singletonList(robotInfo));
            robotInfo.setTenantId(2L);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("机器人不属于该商户");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusRobotSerialNoBlank() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            request.setRobotSerialNo("");
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("参数不合法");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusMainAnchorInfoNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            anchorInfoDTO.setMainAnchorInfo(null);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("未登录");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    @Test
    public void testEditRobotValidStatusAnchorInfoNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(null);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.editRobotValidStatus(request);
            RemoteResponse<Boolean> expected = RemoteResponse.fail("未登录");
            assertEquals(expected.getCode(), result.getCode());
            assertEquals(expected.getMsg(), result.getMsg());
            assertEquals(expected.getData(), result.getData());
            assertEquals(expected.isDoCrow(), result.isDoCrow());
        }
    }

    /**
     * Test case: request is null
     */
    @Test
    public void testRefreshRobotQrCode_RequestNull() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(null);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("参数不能为空", response.getMsg());
        }
    }

    /**
     * Test case: robotSerialNo is blank
     */
    @Test
    public void testRefreshRobotQrCode_BlankSerialNo() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("机器人序列号能为空", response.getMsg());
        }
    }

    /**
     * Test case: anchorInfo is null
     */
    @Test
    public void testRefreshRobotQrCode_AnchorInfoNull() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(null);
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("未登录", response.getMsg());
        }
    }

    /**
     * Test case: anchorId is null
     */
    @Test
    public void testRefreshRobotQrCode_AnchorIdNull() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("未登录", response.getMsg());
        }
    }

    /**
     * Test case: mainAnchorInfo is null
     */
    @Test
    public void testRefreshRobotQrCode_MainAnchorInfoNull() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("未登录", response.getMsg());
        }
    }

    /**
     * Test case: robot not found
     */
    @Test
    public void testRefreshRobotQrCode_RobotNotFound() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            when(wxUserDomainService.queryRobotBySerialNo(any())).thenReturn(Collections.emptyList());
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("机器人不存在", response.getMsg());
        }
    }

    /**
     * Test case: robot doesn't belong to current merchant
     */
    @Test
    public void testRefreshRobotQrCode_RobotNotBelongToMerchant() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockUserInfo.setAnchorId(1L);
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            ScrmPersonalWxRobotInfo robotInfo = new ScrmPersonalWxRobotInfo();
            robotInfo.setTenantId(2L);
            when(wxUserDomainService.queryRobotBySerialNo(any())).thenReturn(Collections.singletonList(robotInfo));
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("机器人不属于当前商户", response.getMsg());
        }
    }

    /**
     * Test case: successful refresh
     */
    @Test
    public void testRefreshRobotQrCode_Success() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockUserInfo.setAnchorId(1L);
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            ScrmPersonalWxRobotInfo robotInfo = new ScrmPersonalWxRobotInfo();
            robotInfo.setTenantId(1L);
            when(wxUserDomainService.queryRobotBySerialNo(any())).thenReturn(Collections.singletonList(robotInfo));
            when(robotCommonService.refreshRobotQrCode(any())).thenReturn(RemoteResponse.success(true));
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertTrue(response.getData());
        }
    }

    /**
     * Test case: exception occurs during execution
     */
    @Test
    public void testRefreshRobotQrCode_Exception() {
        // arrange
        try (MockedStatic<AnchorAuthUtils> anchorAuthUtils = mockStatic(AnchorAuthUtils.class)) {
            LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
            mockAnchorInfo.setAnchorId(1L);
            LoginUserInfo mockUserInfo = new LoginUserInfo();
            mockUserInfo.setAnchorId(1L);
            mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
            anchorAuthUtils.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            when(wxUserDomainService.queryRobotBySerialNo(any())).thenThrow(new RuntimeException("Test exception"));
            RefreshRobotQrCodeRequest request = new RefreshRobotQrCodeRequest();
            request.setRobotSerialNo("123");
            // act
            RemoteResponse<Boolean> response = scrmSaasRobotLoginService.refreshRobotQrCode(request);
            // assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("请求失败", response.getMsg());
        }
    }

    @Test
    public void testInputSecurityVerificationCode_AnchorInfoIsNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            InputSecurityVerificationCodeRequest request = new InputSecurityVerificationCodeRequest();
            request.setVerificationCode("123456");
            request.setSerial("serial");
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(null);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.inputSecurityVerificationCode(request);
            assertEquals("未登录", result.getMsg());
        }
    }

    @Test
    public void testInputSecurityVerificationCode_AnchorIdIsNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            InputSecurityVerificationCodeRequest request = new InputSecurityVerificationCodeRequest();
            request.setVerificationCode("123456");
            request.setSerial("serial");
            LoginAnchorInfoDTO anchorInfoDTO = new LoginAnchorInfoDTO();
            anchorInfoDTO.setAnchorId(null);
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.inputSecurityVerificationCode(request);
            assertEquals("未登录", result.getMsg());
        }
    }

    @Test
    public void testInputSecurityVerificationCode_AnchorIdIsZero() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            InputSecurityVerificationCodeRequest request = new InputSecurityVerificationCodeRequest();
            request.setVerificationCode("123456");
            request.setSerial("serial");
            LoginAnchorInfoDTO anchorInfoDTO = new LoginAnchorInfoDTO();
            anchorInfoDTO.setAnchorId(0L);
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.inputSecurityVerificationCode(request);
            assertEquals("未登录", result.getMsg());
        }
    }

    @Test
    public void testInputSecurityVerificationCode_MainAnchorInfoIsNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            InputSecurityVerificationCodeRequest request = new InputSecurityVerificationCodeRequest();
            request.setVerificationCode("123456");
            request.setSerial("serial");
            LoginAnchorInfoDTO anchorInfoDTO = new LoginAnchorInfoDTO();
            anchorInfoDTO.setAnchorId(1L);
            anchorInfoDTO.setMainAnchorInfo(null);
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.inputSecurityVerificationCode(request);
            assertEquals("未登录", result.getMsg());
        }
    }

    @Test
    public void testInputSecurityVerificationCode_Success() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            InputSecurityVerificationCodeRequest request = new InputSecurityVerificationCodeRequest();
            request.setVerificationCode("123456");
            request.setSerial("serial");
            LoginAnchorInfoDTO anchorInfoDTO = new LoginAnchorInfoDTO();
            anchorInfoDTO.setAnchorId(1L);
            LoginUserInfo mainAnchorInfo = new LoginUserInfo();
            anchorInfoDTO.setMainAnchorInfo(mainAnchorInfo);
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(anchorInfoDTO);
            when(robotCommonService.inputSecurityVerificationCode(request)).thenReturn(RemoteResponse.success(true));
            RemoteResponse<Boolean> result = scrmSaasRobotLoginService.inputSecurityVerificationCode(request);
            assertEquals(true, result.getData());
        }
    }

    @Test
    public void testGetScanQrCodeWhenAnchorIdIsNullFirstVersion() throws Throwable {
        LoginAnchorInfoDTO loginAnchorInfoDTO = createLoginAnchorInfoDTO();
        loginAnchorInfoDTO.setAnchorId(null);
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetScanQrCodeWhenLoadAnchorInfoReturnNull() throws Throwable {
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetScanQrCodeWhenMainAnchorInfoIsNull() throws Throwable {
        LoginAnchorInfoDTO loginAnchorInfoDTO = createLoginAnchorInfoDTO();
        loginAnchorInfoDTO.setMainAnchorInfo(null);
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetScanQrCodeWhenAnchorIdIsNullSecondVersion() throws Throwable {
        LoginAnchorInfoDTO loginAnchorInfoDTO = createLoginAnchorInfoDTO();
        loginAnchorInfoDTO.setAnchorId(null);
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetScanQrCodeWhenAnchorIdIsNull() throws Throwable {
        LoginAnchorInfoDTO loginAnchorInfoDTO = createLoginAnchorInfoDTO();
        loginAnchorInfoDTO.setAnchorId(null);
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetScanQrCodeWhenGetScanQrCodeThrowException() throws Throwable {
        // Assuming the setup for a failure scenario is correctly configured
        // through the test environment or indirectly through the service's behavior.
        // Mocking AnchorAuthUtils.loadAnchorInfo() to return null to simulate failure scenario
        // This is a placeholder for the actual mocking logic
        RemoteResponse<GetScanQrCodeResponse> response = scrmSaasRobotLoginService.getScanQrCode(new GetScanQrCodeRequest());
        assertEquals("未登录", response.getMsg());
    }

    @Test
    public void testGetLoginStatusWhenLoadAnchorInfoReturnNull() throws Throwable {
        GetLoginStatusRequest request = new GetLoginStatusRequest();
        try (MockedStatic<AnchorAuthUtils> mocked = mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(null);
            RemoteResponse<ScanQrCodeStatusResponse> response = scrmSaasRobotLoginService.getLoginStatus(request);
            assertEquals("未登录", response.getMsg());
        }
    }

    @Test
    public void testGetLoginStatusWhenAnchorIdIsNull() throws Throwable {
        GetLoginStatusRequest request = new GetLoginStatusRequest();
        LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
        mockAnchorInfo.setAnchorId(null);
        try (MockedStatic<AnchorAuthUtils> mocked = mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            RemoteResponse<ScanQrCodeStatusResponse> response = scrmSaasRobotLoginService.getLoginStatus(request);
            assertEquals("未登录", response.getMsg());
        }
    }

    @Test
    public void testGetLoginStatusWhenMainAnchorInfoIsNull() throws Throwable {
        GetLoginStatusRequest request = new GetLoginStatusRequest();
        LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
        mockAnchorInfo.setAnchorId(123L);
        mockAnchorInfo.setMainAnchorInfo(null);
        try (MockedStatic<AnchorAuthUtils> mocked = mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            RemoteResponse<ScanQrCodeStatusResponse> response = scrmSaasRobotLoginService.getLoginStatus(request);
            assertEquals("未登录", response.getMsg());
        }
    }

    @Test
    public void testGetLoginStatusWhenGetLoginStatusThrowsException() throws Throwable {
        GetLoginStatusRequest request = new GetLoginStatusRequest();
        LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
        mockAnchorInfo.setAnchorId(123L);
        LoginUserInfo mockUserInfo = new LoginUserInfo();
        mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
        try (MockedStatic<AnchorAuthUtils> mocked = mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            when(robotCommonService.getLoginStatus(request)).thenThrow(new RuntimeException());
            RemoteResponse<ScanQrCodeStatusResponse> response = scrmSaasRobotLoginService.getLoginStatus(request);
            assertEquals("请求失败", response.getMsg());
        }
    }

    @Test
    public void testGetLoginStatusNormal() throws Throwable {
        GetLoginStatusRequest request = new GetLoginStatusRequest();
        LoginAnchorInfoDTO mockAnchorInfo = new LoginAnchorInfoDTO();
        mockAnchorInfo.setAnchorId(123L);
        LoginUserInfo mockUserInfo = new LoginUserInfo();
        mockAnchorInfo.setMainAnchorInfo(mockUserInfo);
        try (MockedStatic<AnchorAuthUtils> mocked = mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(mockAnchorInfo);
            ScanQrCodeStatusResponse mockResponseData = new ScanQrCodeStatusResponse();
            when(robotCommonService.getLoginStatus(request)).thenReturn(RemoteResponse.success(mockResponseData));
            RemoteResponse<ScanQrCodeStatusResponse> response = scrmSaasRobotLoginService.getLoginStatus(request);
            assertNotNull(response.getData());
            assertEquals(200, response.getCode());
        }
    }

    @Test
    public void testDeleteRobotWhenAnchorInfoIsNull() throws Throwable {
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(new DeleteRobotBelongClusterRequest());
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenAnchorIdIsNull() throws Throwable {
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(new DeleteRobotBelongClusterRequest());
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenAnchorIdIsZero() throws Throwable {
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(new DeleteRobotBelongClusterRequest());
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenMainAnchorInfoIsNull() throws Throwable {
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(new DeleteRobotBelongClusterRequest());
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenRequestIsNull() throws Throwable {
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(null);
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenRobotSerialNoIsNull() throws Throwable {
        // arrange
        DeleteRobotBelongClusterRequest request = new DeleteRobotBelongClusterRequest();
        request.setRobotSerialNo(null);
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(request);
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenRobotNotExist() throws Throwable {
        // arrange
        DeleteRobotBelongClusterRequest request = new DeleteRobotBelongClusterRequest();
        request.setRobotSerialNo("123");
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(request);
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenRobotNotTuoGuan() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfo robotInfo = new ScrmPersonalWxRobotInfo();
        robotInfo.setRobotType(ClusterRobotType.PING_TAI.getCode());
        DeleteRobotBelongClusterRequest request = new DeleteRobotBelongClusterRequest();
        request.setRobotSerialNo("123");
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(request);
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotWhenRobotNotBelongToMainAnchor() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfo robotInfo = new ScrmPersonalWxRobotInfo();
        robotInfo.setRobotType(ClusterRobotType.TUO_GUAN.getCode());
        robotInfo.setTenantId(2L);
        DeleteRobotBelongClusterRequest request = new DeleteRobotBelongClusterRequest();
        request.setRobotSerialNo("123");
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(request);
        // assert
        assertEquals("未登录", result.getMsg());
    }

    @Test
    public void testDeleteRobotNormal() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfo robotInfo = new ScrmPersonalWxRobotInfo();
        robotInfo.setRobotType(ClusterRobotType.TUO_GUAN.getCode());
        robotInfo.setTenantId(1L);
        DeleteRobotBelongClusterRequest request = new DeleteRobotBelongClusterRequest();
        request.setRobotSerialNo("123");
        // act
        RemoteResponse<Boolean> result = scrmSaasRobotLoginService.deleteRobot(request);
        // assert
        assertEquals("未登录", result.getMsg());
    }
}
