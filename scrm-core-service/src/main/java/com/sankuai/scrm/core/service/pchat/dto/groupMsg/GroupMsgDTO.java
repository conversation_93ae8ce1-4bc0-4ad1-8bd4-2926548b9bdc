package com.sankuai.scrm.core.service.pchat.dto.groupMsg;

import lombok.Data;

/**
 * 5003
 *
 * @Description 群内实时消息回调
 * <AUTHOR>
 * @Create On 2023/11/6 18:00
 * @Version v1.0.0
 */
@Data
public class GroupMsgDTO extends MsgDTO {

    /**
     * 群编号
     */
    private String vcChatRoomSerialNo;

    /**
     * 群ID
     */
    private String vcChatRoomId;


    /**
     * 是否艾特所有人 (0 为群公告@所有人 1 艾特单个人或者不艾特人或@所有人)
     */
    private Integer nIsHit;


    /**
     * 被引用消息人的昵称
     */
    private String vcQuoteNickName;

    /**
     * 被引用消息人的微信ID
     */
    private String vcQuoteWxId;


}
