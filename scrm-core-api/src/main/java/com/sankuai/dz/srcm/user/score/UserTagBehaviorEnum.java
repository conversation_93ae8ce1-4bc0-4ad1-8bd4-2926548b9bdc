package com.sankuai.dz.srcm.user.score;

import com.dianping.cat.util.Pair;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 计算行为-品类 意向分的 所有行为配置
 * 添加行为：一定要添加 BehaviorMappingUserTagUtil 对应的标签关系
 * <AUTHOR>
 * @Create On 2025/4/2 16:33
 * @Version v1.0.0
 */
@Getter
public enum UserTagBehaviorEnum {
    ASK_INCREASE_PRODUCT("直播求补货", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 28), new Pair<>(7, 14)}),//done
    NO_PAY("商品提单未支付", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 25), new Pair<>(7, 12)}),//done
    MEITUAN_ACTION_SEARCH("搜索医美关键词", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 15), new Pair<>(7, 8)}),//done
    COMMENT("弹幕/社群高意向发言", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 12), new Pair<>(7, 6)}),//done
    PHONE_COMM_END_TAG("电话沟通意向高", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 10), new Pair<>(7, 5)}),//done
    ASK_EXPLAIN("直播求讲解", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 8), new Pair<>(7, 4)}),//done
    LIVE_MARKETING("直播有营销互动", UserTagBehaviorTypeEnum.COMMON, false, new Pair[]{new Pair<>(3, 8), new Pair<>(7, 4)}),//done
    HAS_IN_GROUP("进社群", UserTagBehaviorTypeEnum.COMMON, false, new Pair[]{new Pair<>(3, 8), new Pair<>(7, 4)}),//done
    VIEW_PRODUCT_TAG("商品浏览2次以上", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 6), new Pair<>(7, 3)}),//done
    VIEW_PRODUCT_CATEGORY_TAG("品类浏览2次以上", UserTagBehaviorTypeEnum.PRODUCT_TYPE, false, new Pair[]{new Pair<>(3, 6), new Pair<>(7, 3)}),//done
    VIEW_LIVE_TAG("看播≥5分钟", UserTagBehaviorTypeEnum.COMMON, false, new Pair[]{new Pair<>(3, 4), new Pair<>(7, 2)}),//done
    ;
    /**
     * 行为描述
     */
    private final String desc;
    /**
     * 用户行为意向类型：1商品品类类型 2通用类型
     */
    private final UserTagBehaviorTypeEnum type;
    /**
     * 判断是否每次行为都计入分数
     */
    private final boolean perTimes;
    /**
     * 行为分数配置
     */
    private final Pair<Integer, Integer>[] pairs;

    public static UserTagBehaviorEnum fromCode(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (UserTagBehaviorEnum config : UserTagBehaviorEnum.values()) {
            if (config.name().equalsIgnoreCase(name)) {
                return config;
            }
        }
        return null;
    }

    UserTagBehaviorEnum(String desc, UserTagBehaviorTypeEnum type, boolean perTimes, Pair<Integer, Integer>[] pairs) {
        this.desc = desc;
        this.type = type;
        this.perTimes = perTimes;
        this.pairs = pairs;
    }


}