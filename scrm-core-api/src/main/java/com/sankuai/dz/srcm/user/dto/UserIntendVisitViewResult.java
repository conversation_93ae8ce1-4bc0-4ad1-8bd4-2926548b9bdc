package com.sankuai.dz.srcm.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户意向访问视图数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserIntendVisitViewResult implements Serializable {
    
    // ==================== 核心标识字段 ====================
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务链
     */
    private Integer cChain;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 分区日期(yyyy-mm-dd)
     */
    private String partitionDate;
    
    /**
     * 统计口径维度
     */
    private String calDim;
    
    /**
     * 访问类型
     */
    private String visitType;
    
    /**
     * 用户pk 格式如 mt_xxxx dp_xxx
     */
    private String userPk;
    
    /**
     * 到综BUID（枚举值：1=LifeEvent，2=教育及母婴，3=休娱和丽人，4=医药健康）,-10000代表全部
     */
    private Integer bizlnBuCode;
    
    /**
     * 到综BUID名称
     */
    private String bizlnBuName;

    // ==================== 周期性统计数据 ====================
    
    /**
     * 周期性访问统计数据
     */
    private List<PeriodVisitStat> periodStats;

    // ==================== 最后访问相关信息 ====================
    
    /**
     * 最后访问日期
     */
    private String lastVisitDate;
    
    /**
     * 最后访问时间
     */
    private String lastVisitTime;
    
    /**
     * 最后访问日期，距离分区日期的天数
     */
    private Integer last2tdVisitDays;
    
    /**
     * 最后一天访问页面浏览量
     */
    private Long lastdayVisitPv;
    
    /**
     * 最后一天访问时长（秒）
     */
    private BigDecimal lastdayVisitSec;
    
    /**
     * 最后一天商品页面浏览量
     */
    private Long lastdayVisitProductPv;
    
    /**
     * 最后一天商品访问金额（元）
     */
    private BigDecimal lastdayVisitProductAmt;
    
    /**
     * 最后一天商品平均访问金额（元）
     */
    private BigDecimal lastdayVisitProductAvgAmt;
} 