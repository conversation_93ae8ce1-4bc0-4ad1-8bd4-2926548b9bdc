package com.sankuai.dz.srcm.activity.fission.enums;

import lombok.Getter;

@Getter
public enum PlatformType {
    PC(1, "pc"),
    H5(2, "h5, 手机浏览器"),
    ANDROID(4, "android"),
    IOS(5, "ios"),
    WX_I_VERSION(11, "微信i版"),
    APPLET(13, "小程序");

    private final int code;

    private final String desc;

    PlatformType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlatformType getPlatformTypeByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PlatformType platformType : PlatformType.values()) {
            if (code == platformType.getCode()) {
                return platformType;
            }
        }
        return null;
    }
}
