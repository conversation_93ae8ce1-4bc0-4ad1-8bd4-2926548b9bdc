package com.sankuai.scrm.core.service.tag.domain;

import com.dianping.cat.Cat;
import com.dianping.wedding.file.WedFileHelper;
import com.dianping.wedding.file.result.WedFileUrlResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.sankuai.dz.srcm.tag.dto.RelationshipDTO;
import com.sankuai.dz.srcm.tag.dto.RuleConfigDTO;
import com.sankuai.dz.srcm.tag.dto.RuleDTO;
import com.sankuai.dz.srcm.tag.dto.TagFieldDTO;
import com.sankuai.dz.srcm.tag.enums.FieldTypeEnum;
import com.sankuai.dz.srcm.tag.enums.RelationTypeEnum;
import com.sankuai.dz.srcm.tag.enums.SyncTypeEnum;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.service.MemberInfoInnerService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.bo.FieldDataBO;
import com.sankuai.scrm.core.service.tag.dal.entity.PredTagRecord;
import com.sankuai.scrm.core.service.tag.dal.entity.TagRuleTask;
import com.sankuai.scrm.core.service.tag.dal.mapper.PredTagRecordMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.TagRuleTaskMapper;
import com.sankuai.scrm.core.service.util.CsvUtils;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdInfoResp;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleDomainService {

    @Autowired
    private PredTagRecordMapper predTagRecordMapper;

    @Autowired
    private TagRuleTaskMapper tagRuleTaskMapper;

    @Autowired
    private FieldOperationDomainService fieldOperationDomainService;

    @Autowired
    private MemberInfoInnerService memberInfoInnerService;

    @Autowired
    private ContactUserDomain contactUserDomain;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private ThirdInfoService.Iface rpcUserThirdInfoService;

    public void predictTag(String appId, long ruleTaskId, int start) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.predictTag(java.lang.String,long,int)");
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        TagRuleTask tagRuleTask = tagRuleTaskMapper.selectByPrimaryKey(ruleTaskId);
        if (tagRuleTask == null || BooleanUtils.isTrue(tagRuleTask.getDeleted())) {
            return;
        }
        if (StringUtils.isEmpty(tagRuleTask.getDataUrl()) || StringUtils.isEmpty(tagRuleTask.getRule())) {
            return;
        }
        String url = convertFileKeyToUrl(tagRuleTask.getDataUrl());
        if (StringUtils.isEmpty(url)) {
            return;
        }
        URL fileUrl = CsvUtils.buildURL(url);
        if (fileUrl == null) {
            return;
        }
        try {
            String[] header = CsvUtils.reedHeader(url);
            RuleConfigDTO ruleConfig = JsonUtils.toObject(tagRuleTask.getRule(), RuleConfigDTO.class);
            if (!validateRuleConfig(Sets.newHashSet(header), ruleConfig)) {
                return;
            }
            // 开始解析数据
            try (
                    InputStream inputStream = fileUrl.openStream();
                    InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                    CSVReader reader = new CSVReaderBuilder(inputStreamReader)
                            .withCSVParser(CsvUtils.buildCSVParser())
                            .withSkipLines(start + 1)
                            .build();
            ) {
                Iterator<String[]> iterator = reader.iterator();
                List<PredTagRecord> records = Lists.newArrayList();
                while (iterator.hasNext()) {
                    String[] data = iterator.next();
                    Map<String, String> csvDataMap = convertCsvData(header, data);
                    List<String> tagIdList = matchTagList(ruleConfig, csvDataMap);
                    PredTagRecord record = buildPredTagRecord(appId, tagRuleTask, csvDataMap.get("mt_user_id"), tagIdList);
                    Optional.ofNullable(record).ifPresent(records::add);
                    start++;
                    if (records.size() == 100) {
                        predTagRecordMapper.batchInsert(records);
                        records.clear();
                        updatePredOffset(ruleTaskId, start);
                    }
                }
                if (CollectionUtils.isNotEmpty(records)) {
                    predTagRecordMapper.batchInsert(records);
                    updatePredOffset(ruleTaskId, start);
                }
            }
        } catch (Exception e) {
            log.error("Predict tag fail", e);
        }
    }

    private String convertFileKeyToUrl(String fileKey) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.convertFileKeyToUrl(java.lang.String)");
        if (StringUtils.isEmpty(fileKey) || !fileKey.endsWith(".csv")) {
            return null;
        }
        WedFileUrlResult wedFileUrlResult = WedFileHelper.getFileUrl("baby", fileKey);
        String url = wedFileUrlResult.getFileUrl();
        return StringUtils.isNotEmpty(url) ? url : null;
    }

    private boolean validateRuleConfig(Set<String> fieldSet, RuleConfigDTO ruleConfig) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.validateRuleConfig(java.util.Set,com.sankuai.dz.srcm.tag.dto.RuleConfigDTO)");
        if (CollectionUtils.isEmpty(fieldSet) || ruleConfig == null || CollectionUtils.isEmpty(ruleConfig.getRelationships()) ||
                CollectionUtils.isEmpty(ruleConfig.getRuleList())) {
            return false;
        }
        List<RelationshipDTO> relationships = ruleConfig.getRelationships();
        List<RuleDTO> ruleList = ruleConfig.getRuleList();
        for (RelationshipDTO relationship : relationships) {
            if (relationship == null || !fieldSet.contains(relationship.getFieldName())) {
                return false;
            }
            FieldTypeEnum fieldTypeEnum = FieldTypeEnum.getFieldTypeByCode(relationship.getFieldType());
            if (FieldTypeEnum.UNKNOWN.equals(fieldTypeEnum)) {
                return false;
            }
            RelationTypeEnum relationTypeEnum = RelationTypeEnum.getRelationTypeByName(relationship.getRelation());
            if (RelationTypeEnum.UNKNOWN.equals(relationTypeEnum)) {
                return false;
            }
        }
        Map<String, RelationshipDTO> relationshipMap = relationships.stream().collect(Collectors.toMap(
                RelationshipDTO::getFieldName, Function.identity(), (k1, k2) -> k2));
        for (RuleDTO ruleDTO : ruleList) {
            if (ruleDTO == null || CollectionUtils.isEmpty(ruleDTO.getTagFieldList()) ||
                    relationships.size() != ruleDTO.getTagFieldList().size()) {
                return false;
            }
            for (TagFieldDTO fieldDTO : ruleDTO.getTagFieldList()) {
                if (fieldDTO == null || relationshipMap.get(fieldDTO.getFieldName()) == null) {
                    return false;
                }
            }
        }
        return true;
    }

    private Map<String, String> convertCsvData(String[] header, String[] data) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.convertCsvData(java.lang.String[],java.lang.String[])");
        if (ArrayUtils.isEmpty(header) || ArrayUtils.isEmpty(data) || header.length != data.length) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        for (int i = 0; i < data.length; i++) {
            result.put(header[i], data[i].trim());
        }
        return result;
    }

    private boolean checkSyncType(String corpId, String unionId, SyncTypeEnum syncType) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.checkSyncType(java.lang.String,java.lang.String,com.sankuai.dz.srcm.tag.enums.SyncTypeEnum)");
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(unionId) || syncType == null) {
            return false;
        }
        switch (syncType) {
            case SYNC_WECHAT:
                return CollectionUtils.isNotEmpty(contactUserDomain.queryContactUsersByUnionId(corpId, unionId));
            case SYNC_PRIVATE_DOMAIN:
                List<ContactUser> contactUsers = contactUserDomain.queryContactUsersByUnionId(corpId, unionId);
                MemberInfoEntity memberInfo = memberInfoInnerService.queryMemberByUnionId(corpId, unionId);
                return CollectionUtils.isNotEmpty(contactUsers) || memberInfo != null;
            case SYNC_POTENTIAL_CUSTOMER:
                return true;
        }
        return false;
    }

    private List<String> matchTagList(RuleConfigDTO ruleConfig, Map<String, String> csvDataMap) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.matchTagList(com.sankuai.dz.srcm.tag.dto.RuleConfigDTO,java.util.Map)");
        if (ruleConfig == null || MapUtils.isEmpty(csvDataMap)) {
            return Lists.newArrayList();
        }
        List<String> tagIdList = Lists.newArrayList();
        Map<String, RelationshipDTO> relationshipDTOMap = ruleConfig.getRelationships().stream()
                .collect(Collectors.toMap(RelationshipDTO::getFieldName, Function.identity(), (k1, k2) -> k2));
        List<RuleDTO> ruleList = ruleConfig.getRuleList();
        for (RuleDTO ruleDTO : ruleList) {
            boolean isMatch = isMatchTag(ruleDTO, relationshipDTOMap, csvDataMap);
            if (isMatch) {
                tagIdList.add(ruleDTO.getTagId());
            }
        }
        return tagIdList;
    }

    private boolean isMatchTag(RuleDTO ruleDTO, Map<String, RelationshipDTO> relationshipMap, Map<String, String> csvDataMap) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.isMatchTag(com.sankuai.dz.srcm.tag.dto.RuleDTO,java.util.Map,java.util.Map)");
        if (ruleDTO == null || MapUtils.isEmpty(relationshipMap) || MapUtils.isEmpty(csvDataMap)) {
            return false;
        }
        List<TagFieldDTO> fieldList = ruleDTO.getTagFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return false;
        }
        for (TagFieldDTO fieldDTO : fieldList) {
            if (fieldDTO == null || StringUtils.isEmpty(fieldDTO.getFieldName()) || StringUtils.isEmpty(fieldDTO.getFieldValue())) {
                return false;
            }
            FieldDataBO fieldDataBO = convertToFieldDataBO(fieldDTO, relationshipMap, csvDataMap);
            boolean isMatch = fieldOperationDomainService.isMatch(fieldDataBO);
            if (!isMatch) {
                return false;
            }
        }
        return true;
    }


    private FieldDataBO convertToFieldDataBO(TagFieldDTO fieldDTO, Map<String, RelationshipDTO> relationshipMap, Map<String, String> csvDataMap) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.convertToFieldDataBO(com.sankuai.dz.srcm.tag.dto.TagFieldDTO,java.util.Map,java.util.Map)");
        if (fieldDTO == null || MapUtils.isEmpty(relationshipMap) || MapUtils.isEmpty(csvDataMap)) {
            return null;
        }
        FieldDataBO fieldDataBO = new FieldDataBO();
        if (relationshipMap.get(fieldDTO.getFieldName()) != null) {
            RelationshipDTO relationshipDTO = relationshipMap.get(fieldDTO.getFieldName());
            fieldDataBO.setFieldType(FieldTypeEnum.getFieldTypeByCode(relationshipDTO.getFieldType()));
            fieldDataBO.setRelationType(RelationTypeEnum.getRelationTypeByName(relationshipDTO.getRelation()));
        }
        fieldDataBO.setTargetValue(fieldDTO.getFieldValue());
        if (csvDataMap.get(fieldDTO.getFieldName()) != null) {
            fieldDataBO.setActualValue(csvDataMap.get(fieldDTO.getFieldName()));
        }
        return fieldDataBO;
    }

    private PredTagRecord buildPredTagRecord(String appId, TagRuleTask tagRuleTask, String mtUserId, List<String> tagIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.buildPredTagRecord(java.lang.String,com.sankuai.scrm.core.service.tag.dal.entity.TagRuleTask,java.lang.String,java.util.List)");
        if (StringUtils.isEmpty(appId) || tagRuleTask == null || StringUtils.isEmpty(mtUserId) || CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        String corpId = appConfigRepository.getCorpIdByAppId(appId);
        if (StringUtils.isEmpty(corpId)) {
            return null;
        }
        SyncTypeEnum syncType = SyncTypeEnum.getSyncTypeEnumByCode(tagRuleTask.getSyncType());
        if (SyncTypeEnum.UNKNOWN.equals(syncType)) {
            return null;
        }
        try {
            String unionId = queryUnionIdByMtUserId(Long.parseLong(mtUserId));
            boolean needSync = checkSyncType(corpId, unionId, syncType);
            if (!needSync) {
                return null;
            }
            return PredTagRecord.builder()
                    .mtUserId(Long.parseLong(mtUserId))
                    .unionId(unionId)
                    .appId(appId)
                    .tagGroupId(tagRuleTask.getTagGroupId())
                    .tagIdList(JsonUtils.toStr(tagIdList))
                    .status(false)
                    .ruleTaskId(tagRuleTask.getId())
                    .ruleTaskVersion(tagRuleTask.getVersion())
                    .build();
        } catch (Exception e) {
            log.error("Build prediction tag record error, appId:{}, tagRuleTask:{}, mtUserId:{}, tagIdList:{}",
                    appId, JsonUtils.toStr(tagIdList), mtUserId, JsonUtils.toStr(tagIdList), e);
        }
        return null;
    }

    private String queryUnionIdByMtUserId(long mtUserId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.queryUnionIdByMtUserId(long)");
        try {
            ThirdInfoResp thirdInfoResp = rpcUserThirdInfoService.getThirdInfoByUserId(mtUserId, "weixin", true);
            if (thirdInfoResp != null && thirdInfoResp.success && thirdInfoResp.getThirdInfo() != null &&
                    StringUtils.isNotEmpty(thirdInfoResp.getThirdInfo().getUniqueId())) {
                return thirdInfoResp.getThirdInfo().getUniqueId();
            }
        } catch (TException e) {
            log.error("Query unionId by mtUserId fail, mtUserId:{}", mtUserId, e);
        }
        return null;
    }

    private boolean updateRecordCount(long ruleTaskId, int recordCount) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.updateRecordCount(long,int)");
        TagRuleTask record = TagRuleTask.builder()
                .id(ruleTaskId)
                .recordCount(recordCount).build();
        return tagRuleTaskMapper.updateByPrimaryKeySelective(record) > 0;
    }

    public void updatePredOffset(long ruleTaskId, int predOffset) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.domain.RuleDomainService.updatePredOffset(long,int)");
        TagRuleTask record = TagRuleTask.builder()
                .id(ruleTaskId)
                .predOffset(predOffset).build();
        tagRuleTaskMapper.updateByPrimaryKeySelective(record);
    }
}
