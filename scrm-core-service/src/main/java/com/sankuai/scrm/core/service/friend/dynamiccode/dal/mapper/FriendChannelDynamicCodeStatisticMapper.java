package com.sankuai.scrm.core.service.friend.dynamiccode.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeStatistic;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.FriendChannelDynamicCodeStatisticExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FriendChannelDynamicCodeStatisticMapper extends MybatisBaseMapper<FriendChannelDynamicCodeStatistic, FriendChannelDynamicCodeStatisticExample, Long> {
    int batchInsert(@Param("list") List<FriendChannelDynamicCodeStatistic> list);
}