package com.sankuai.dz.srcm.couponIntegration.enums;

public enum CreationSceneEnum {
    UNKNOWN(-1, "未知场景"),
    MANUAL_UPLOAD(0, "手动上传"),
    REAL_TIME_TASK(1, "实时任务"),
    SCHEDULED_TASK(2, "定时/周期任务"),
    GROUP_WELCOME(3, "群欢迎语"),
    CHANNEL_QR_WELCOME(4, "渠道活码欢迎语"),
    GROUP_FRIEND_FISSION(5, "群/好友裂变活动"),
    LOTTERY_FISSION(6, "抽奖裂变活动"),
    AI_CUSTOMER_SERVICE(7, "智能客服"),;

    private final int code;
    private final String desc;

    CreationSceneEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CreationSceneEnum getByCode(int code) {
        for (CreationSceneEnum scene : values()) {
            if (scene.getCode() == code) {
                return scene;
            }
        }
        return CreationSceneEnum.UNKNOWN;
    }

    public static String getDescByCode(int code) {
        for (CreationSceneEnum scene : values()) {
            if (scene.getCode() == code) {
                return scene.getDesc();
            }
        }
        return "未知场景";
    }

    public static Integer getCodeByDesc(String desc) {
        for (CreationSceneEnum scene : values()) {
            if (scene.getDesc().equals(desc)) {
                return scene.getCode();
            }
        }
        return -1;
    }

}
