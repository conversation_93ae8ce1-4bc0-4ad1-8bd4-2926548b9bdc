package com.sankuai.scrm.core.service.tag.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.tag.enums.RunTypeEnum;
import com.sankuai.scrm.core.service.tag.dal.entity.PredTagRecord;
import com.sankuai.scrm.core.service.tag.dal.entity.TagRuleTask;
import com.sankuai.scrm.core.service.tag.dal.example.PredTagRecordExample;
import com.sankuai.scrm.core.service.tag.dal.example.TagRuleTaskExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.PredTagRecordMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.TagRuleTaskMapper;
import com.sankuai.scrm.core.service.tag.domain.PredTaskDomainService;
import com.sankuai.scrm.core.service.tag.mq.producer.TagPredictionTaskProducer;
import com.sankuai.scrm.core.service.tag.message.TagPredictionMsg;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class TagPredictionJob {

    private static final int INTERVAL_SECONDS = 60;

    @Autowired
    private TagRuleTaskMapper tagRuleTaskMapper;

    @Autowired
    private PredTagRecordMapper predTagRecordMapper;

    @Autowired
    private PredTaskDomainService predTaskDomainService;

    @Autowired
    private TagPredictionTaskProducer producer;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.tag.predict")
    public void predict() {
        int start = 0;
        int limit = 100;
        while (true) {
            List<TagRuleTask> tagRuleTasks = queryTagRuleTask(start, limit);
            if (CollectionUtils.isEmpty(tagRuleTasks)) {
                break;
            }
            tagRuleTasks.forEach(this::processPredTask);
            start += limit;
        }
    }

    private List<TagRuleTask> queryTagRuleTask(int start, int limit) {
        if (start < 0 || limit <= 0) {
            return Lists.newArrayList();
        }
        TagRuleTaskExample example = new TagRuleTaskExample();
        example.createCriteria()
                .andDeletedEqualTo(false);
        example.setOffset(start);
        example.setRows(limit);
        return tagRuleTaskMapper.selectByExample(example);
    }

    private void processPredTask(TagRuleTask task) {
        if (task == null) {
            return;
        }
        RunTypeEnum runType = RunTypeEnum.getRunTypeByCode(task.getRunType());
        boolean canRun = checkRunTime(task.getRunTime(), runType);
        if (!canRun) {
            return;
        }
        // 异步执行
        CompletableFuture.runAsync(() -> {
                    // 更新任务信息
                    predTaskDomainService.updatePredTaskRunInfo(task.getId(), task.getRunCount() + 1, new Date());
                    // 生成log日志
                    long logId = predTaskDomainService.insertPredTaskLog(task.getId());
                    if (logId <= 0) {
                        return;
                    }
                    // 判断预测结果是否生成
                    predTaskDomainService.generatePredResult(task.getId());
                    predTaskDomainService.updatePredTaskLogTotal(task.getId(), task.getVersion(), logId);
                    int start = 0;
                    int limit = 100;
                    while (true) {
                        List<PredTagRecord> predTagRecords = queryPredTagRecords(task.getAppId(), task.getId(), task.getVersion(), start, limit);
                        if (CollectionUtils.isEmpty(predTagRecords)) {
                            break;
                        }
                        predTagRecords.forEach(record -> {
                            TagPredictionMsg predMsg = new TagPredictionMsg();
                            predMsg.setLogId(logId);
                            predMsg.setRecordId(record.getId());
                            producer.sendPredTagMsg(predMsg);
                        });
                        start += limit;
                    }
                    predTaskDomainService.updatePredTaskStatus(logId, 1);
                }, ThreadPoolUtils.tagExecutor)
                .exceptionally(ex -> {
                    log.error("Process prediction task fail", ex);
                    return null;
                });
    }

    private boolean checkRunTime(String runTime, RunTypeEnum runType) {
        if (StringUtils.isEmpty(runTime) || runType == null) {
            return false;
        }
        try {
            if (RunTypeEnum.SINGLE_EXECUTION.equals(runType)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = sdf.parse(runTime);
                Date now = new Date();
                return date.getTime() >= now.getTime() && date.getTime() - now.getTime() <= INTERVAL_SECONDS * 1000;
            }
            if (RunTypeEnum.REGULAR_EXECUTION.equals(runType)) {
                String[] array = runTime.split("-");
                if (ArrayUtils.isEmpty(array)) {
                    return false;
                }
                if (array.length == 1) {
                    // 每天执行
                    return checkTime(array[0]);
                }
                if (array.length == 3) {
                    if ("month".equals(array[0])) {
                        // 每月执行
                        int dayOfMonth = NumberUtils.toInt(array[1]);
                        int dayOfNow = LocalDate.now().getDayOfMonth();
                        if (dayOfMonth != dayOfNow) {
                            return false;
                        }
                        return checkTime(array[2]);
                    }
                    if ("week".equals(array[0])) {
                        // 每周执行
                        int day = NumberUtils.toInt(array[1]);
                        DayOfWeek dayOfWeek = DayOfWeek.of(day);
                        DayOfWeek dayOfNow = LocalDate.now().getDayOfWeek();
                        if (dayOfNow != dayOfWeek) {
                            return false;
                        }
                        return checkTime(array[2]);
                    }
                }
            }
        } catch (ParseException e) {
            log.error("Run time parse error, runTime:{}", runTime, e);
        }
        return false;
    }

    private List<PredTagRecord> queryPredTagRecords(String appId, long ruleTaskId, int ruleTaskVersion, int start, int limit) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.crane.TagPredictionJob.queryPredTagRecords(java.lang.String,long,int,int,int)");
        if (StringUtils.isEmpty(appId)) {
            return Lists.newArrayList();
        }
        PredTagRecordExample example = new PredTagRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andRuleTaskIdEqualTo(ruleTaskId)
                .andRuleTaskVersionEqualTo(ruleTaskVersion)
                .andStatusEqualTo(false);
        example.setOffset(start);
        example.setRows(limit);
        return predTagRecordMapper.selectByExample(example);
    }

    private boolean checkTime(String runTimeOfDay) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.tag.crane.TagPredictionJob.checkTime(java.lang.String)");
        LocalTime time = LocalTime.parse(runTimeOfDay, DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime now = LocalTime.now();
        int secondOfTime = time.toSecondOfDay();
        int secondOfNow = now.toSecondOfDay();
        return secondOfTime >= secondOfNow && secondOfTime - secondOfNow <= INTERVAL_SECONDS;
    }
}
