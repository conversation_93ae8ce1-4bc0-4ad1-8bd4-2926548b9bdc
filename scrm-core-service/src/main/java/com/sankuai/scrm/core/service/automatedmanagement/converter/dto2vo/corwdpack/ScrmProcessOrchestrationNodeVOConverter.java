package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationConditionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.vo.*;
import com.sankuai.scrm.core.service.automatedmanagement.converter.AbstractConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/26
 */
@Component
public class ScrmProcessOrchestrationNodeVOConverter  extends AbstractConverter<ScrmProcessOrchestrationNodeMediumDTO, List<ScrmProcessOrchestrationNodeVO>> {

    private ScrmProcessOrchestrationConditionDetailVOConverter scrmProcessOrchestrationConditionVOConverter;
    private ScrmProcessOrchestrationDelayVOConverter scrmProcessOrchestrationDelayVOConverter;
    private ScrmProcessOrchestrationSplitVOConverter scrmProcessOrchestrationSplitVOConverter;
    private ScrmProcessOrchestrationActionAttachmentVOConverter scrmProcessOrchestrationActionAttachmentVOConverter;

    @Autowired
    public ScrmProcessOrchestrationNodeVOConverter(ScrmProcessOrchestrationConditionDetailVOConverter scrmProcessOrchestrationConditionVOConverter, ScrmProcessOrchestrationDelayVOConverter scrmProcessOrchestrationDelayVOConverter, ScrmProcessOrchestrationSplitVOConverter scrmProcessOrchestrationSplitVOConverter, ScrmProcessOrchestrationActionAttachmentVOConverter scrmProcessOrchestrationActionAttachmentVOConverter) {
        this.scrmProcessOrchestrationConditionVOConverter = scrmProcessOrchestrationConditionVOConverter;
        this.scrmProcessOrchestrationDelayVOConverter = scrmProcessOrchestrationDelayVOConverter;
        this.scrmProcessOrchestrationSplitVOConverter = scrmProcessOrchestrationSplitVOConverter;
        this.scrmProcessOrchestrationActionAttachmentVOConverter = scrmProcessOrchestrationActionAttachmentVOConverter;
    }

    @Override
    public ScrmProcessOrchestrationNodeMediumDTO convertToDTO(List<ScrmProcessOrchestrationNodeVO> resource) {

        if(CollectionUtils.isEmpty(resource)){
            return null;
        }

        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO= new ScrmProcessOrchestrationNodeMediumDTO();

        List<ScrmProcessOrchestrationNodeVO> processOrchestrationNodeVOList = resource;

        for (ScrmProcessOrchestrationNodeVO nodeVO : processOrchestrationNodeVOList) {
            ScrmProcessOrchestrationNodeDTO nodeDTO= new ScrmProcessOrchestrationNodeDTO();
            nodeDTO.setNodeType(nodeVO.getNodeType());
            nodeDTO.setNodeId(nodeVO.getNodeId());
            nodeDTO.setPreNodeId(nodeVO.getPreNodeId());
            nodeDTO.setChildrenNodes(nodeVO.getChildNodeIdList());
            nodeDTO.getChildrenNodes().sort(Comparator.comparing(Long::valueOf));

            nodeMediumDTO.addProcessOrchestrationNodeDTO(nodeDTO);

            if(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue() == nodeVO.getNodeType()){
                switch (nodeVO.getConditionNodeType()) {
                    //ScrmProcessOrchestrationConditionTypeEnum.CONDITION_BRANCH
                    case 1:
                        ScrmProcessOrchestrationConditionVO conditionVO = nodeVO.getConditionBranch();
                        if(conditionVO.isDefaultCondition()){
                            ScrmProcessOrchestrationConditionDetailDTO defaultConditionDetailDTO =
                                    new ScrmProcessOrchestrationConditionDetailDTO();
                            defaultConditionDetailDTO.setDefaultCondition(true);
                            defaultConditionDetailDTO.setType(ScrmProcessOrchestrationConditionTypeEnum.CONDITION_BRANCH.getValue());
                            defaultConditionDetailDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
                            nodeMediumDTO.addConditionDetailDTOList(nodeVO.getNodeId(),Collections.singletonList(defaultConditionDetailDTO));
                        } else {
                            List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList =
                                    scrmProcessOrchestrationConditionVOConverter.convertToDTOsSafety(conditionVO.getConditionDetails());
                            conditionDetailDTOList.forEach(conditionDetailDTO -> {
                                conditionDetailDTO.setType(ScrmProcessOrchestrationConditionTypeEnum.CONDITION_BRANCH.getValue());
                                conditionDetailDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
                                conditionDetailDTO.setDefaultCondition(false);
                            });
                            nodeMediumDTO.addConditionDetailDTOList(nodeVO.getNodeId(),conditionDetailDTOList);
                        }
                        break;
                    // ScrmProcessOrchestrationConditionTypeEnum.DELAY_BRANCH
                    case 2:
                        ScrmProcessOrchestrationDelayVO delayVO = nodeVO.getDelayBranch();
                        ScrmProcessOrchestrationConditionDetailDTO delayConditionDetailDTO =
                                scrmProcessOrchestrationDelayVOConverter.convertToDTO(delayVO);
                        delayConditionDetailDTO.setType(ScrmProcessOrchestrationConditionTypeEnum.DELAY_BRANCH.getValue());
                        delayConditionDetailDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
                        delayConditionDetailDTO.setDefaultCondition(false);
                        nodeMediumDTO.addConditionDetailDTOList(nodeVO.getNodeId(), Collections.singletonList(delayConditionDetailDTO));
                        break;
                    //ScrmProcessOrchestrationConditionTypeEnum.SPLIT_BRANCH
                    case 3:
                        ScrmProcessOrchestrationSplitVO splitVO = nodeVO.getSplitBranch();
                        ScrmProcessOrchestrationConditionDetailDTO splitConditionDetailDTO =
                                scrmProcessOrchestrationSplitVOConverter.convertToDTO(splitVO);
                        splitConditionDetailDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
                        splitConditionDetailDTO.setDefaultCondition(false);
                        splitConditionDetailDTO.setType(ScrmProcessOrchestrationConditionTypeEnum.SPLIT_BRANCH.getValue());
                        nodeMediumDTO.addConditionDetailDTOList(nodeVO.getNodeId(), Collections.singletonList(splitConditionDetailDTO));
                        break;
                    case 0:
                    default:
                        break;
                }
            }

            if(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue() == nodeVO.getNodeType()){
                ScrmProcessOrchestrationActionVO actionVO = nodeVO.getActionVO();
                buildActionDTO(nodeVO, actionVO, nodeMediumDTO);
            }

            if(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_AUTO_ACTION.getValue() == nodeVO.getNodeType()){
                ScrmProcessOrchestrationActionVO actionVO = nodeVO.getAutomatedActionVO();
                buildActionDTO(nodeVO, actionVO, nodeMediumDTO);
            }
        }

        return nodeMediumDTO;
    }

    private void buildActionDTO(ScrmProcessOrchestrationNodeVO nodeVO, ScrmProcessOrchestrationActionVO actionVO, ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO) {
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionType(nodeVO.getActionNodeType().byteValue());
        if(null != actionVO){
            actionDTO.setActionId(actionVO.getActionId());
            actionDTO.setActionSubType(actionVO.getActionSubType());
            actionDTO.setContentType(actionVO.getContentType());
        } else {
            actionDTO.setActionId(0);
            actionDTO.setActionSubType(0);
            actionDTO.setContentType(0);
        }
        actionDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
        nodeMediumDTO.addActionDTO(nodeVO.getNodeId(), actionDTO);

        List<ScrmProcessOrchestrationActionContentVO> contentList = null != actionVO ? actionVO.getContentList() : new ArrayList<>();
        // 流程编排动作附件信息 key = nodeId + "-" + actionId + "-" + contentId
        Map<String,List<ScrmProcessOrchestrationActionAttachmentDTO>> actionAttachmentMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(contentList)){
            List<ScrmProcessOrchestrationActionContentDTO> contentDTOList = new ArrayList<>();
            for (ScrmProcessOrchestrationActionContentVO contentVO : contentList) {
                ScrmProcessOrchestrationActionContentDTO contentDTO = getScrmProcessOrchestrationActionContentDTO(nodeVO, actionVO, contentVO);
                contentDTOList.add(contentDTO);

                List<ScrmProcessOrchestrationActionAttachmentVO> actionAttachmentVOS = contentVO.getAttachmentList();
                if(CollectionUtils.isNotEmpty(actionAttachmentVOS)){
                    List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = getScrmProcessOrchestrationActionAttachmentDTOS(nodeVO, actionVO, contentVO, actionAttachmentVOS);
                    actionAttachmentMap.put(nodeVO.getNodeId() + "-" + actionDTO.getActionId() + "-" + contentVO.getContentId(), actionAttachmentDTOList);
                }
            }
            nodeMediumDTO.addActionContentDTOList(actionDTO, contentDTOList);
            nodeMediumDTO.getActionAttachmentMap().putAll(actionAttachmentMap);
        }
    }

    @Override
    public List<ScrmProcessOrchestrationNodeVO> convertToDO(ScrmProcessOrchestrationNodeMediumDTO resource) {
        if(resource == null){
            return new ArrayList<>();
        }
        // 流程编排条件信息 key = nodeId
        Map<Long, List<ScrmProcessOrchestrationConditionDetailDTO>> conditionMap = resource.getConditionMap();

        // 流程编排动作信息 key = nodeId
        Map<Long, ScrmProcessOrchestrationActionDTO> actionMap = resource.getActionMap();

        // 流程编排动作内容信息 key = nodeId + "-" + actionId
        Map<String, List<ScrmProcessOrchestrationActionContentDTO>> actionContentMap = resource.getActionContentMap();

        // 流程编排动作附件信息 key = nodeId + "-" + actionId + "-" + contentId
        Map<String, List<ScrmProcessOrchestrationActionAttachmentDTO>> actionAttachmentMap = resource.getActionAttachmentMap();

        Map<Long, ScrmAmProcessOrchestrationNodeExecuteLogDTO> executeLogMap = resource.getNodeExecuteLogMap();

        Map<Long, ScrmAmProcessOrchestrationBranchExeLogDTO> branchStatisticsMap = resource.getBranchStatisticsMap();

        // 流程编排节点信息 key = nodeId
        Map<Long, ScrmProcessOrchestrationNodeVO> nodeMap = new HashMap<>();

        List<ScrmProcessOrchestrationNodeVO> processOrchestrationNodeList = new ArrayList<>();
        for (ScrmProcessOrchestrationNodeDTO nodeDTO : resource.getProcessOrchestrationNodeDTOList()) {
            ScrmProcessOrchestrationNodeVO nodeVO = new ScrmProcessOrchestrationNodeVO();
            nodeVO.setNodeId(nodeDTO.getNodeId());
            nodeVO.setPreNodeId(nodeDTO.getPreNodeId());
            nodeVO.setChildNodeIdList(new ArrayList<>());
            nodeVO.setNodeType(nodeDTO.getNodeType());

            if (ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue() == nodeDTO.getNodeType()) {
                /**
                 * 更新条件节点
                 */
                updateConditionsOfNode(conditionMap, nodeDTO, nodeVO);
            } else if (ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue() == (nodeDTO.getNodeType())
                    || ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_AUTO_ACTION.getValue() == nodeDTO.getNodeType()) {
                updateActionNode(actionMap, actionContentMap, actionAttachmentMap, nodeDTO, nodeVO);
            }

            ScrmAmProcessOrchestrationNodeExecuteLogDTO executeLogDTO =  executeLogMap.get(nodeDTO.getNodeId());
            if(null != executeLogDTO){
                nodeVO.setBranchStatisticsVO(new ScrmProcessOrchestrationBranchStatisticsVO());
                nodeVO.getBranchStatisticsVO().setUserCount(executeLogDTO.getExecuteCount());
            }

            ScrmAmProcessOrchestrationBranchExeLogDTO branchExeLogDTO = branchStatisticsMap.get(nodeDTO.getNodeId());
            if(null != nodeVO.getBranchStatisticsVO() && null != branchExeLogDTO){
                nodeVO.getBranchStatisticsVO().setClickCount(branchExeLogDTO.getClickCount());
                nodeVO.getBranchStatisticsVO().setOrderCount(branchExeLogDTO.getOrderCount());
                nodeVO.getBranchStatisticsVO().setQuitGroupCount(branchExeLogDTO.getQuitGroupCount());
                nodeVO.getBranchStatisticsVO().setDeleteFriendCount(branchExeLogDTO.getDeleteFriendCount());
                nodeVO.getBranchStatisticsVO().setLossCount(branchExeLogDTO.getLossCount());
            }

            nodeMap.put(nodeDTO.getNodeId(), nodeVO);
            processOrchestrationNodeList.add(nodeVO);
        }

        processOrchestrationNodeList.sort(Comparator.comparingLong(ScrmProcessOrchestrationNodeVO::getNodeId));
        long totalExecuteCount = 0;
        for (ScrmProcessOrchestrationNodeVO scrmProcessOrchestrationNodeVO : processOrchestrationNodeList) {
            if (scrmProcessOrchestrationNodeVO.getPreNodeId() < 0) {
                continue;
            }
            if (null != scrmProcessOrchestrationNodeVO.getBranchStatisticsVO() && scrmProcessOrchestrationNodeVO.getNodeId() == 0) {
                totalExecuteCount = scrmProcessOrchestrationNodeVO.getBranchStatisticsVO().getUserCount();
            }
            nodeMap.get(scrmProcessOrchestrationNodeVO.getPreNodeId()).getChildNodeIdList().add(scrmProcessOrchestrationNodeVO.getNodeId());
        }
        long finalTotalExecuteCount = totalExecuteCount;
        processOrchestrationNodeList.forEach(o->{
            if (null == o.getBranchStatisticsVO()) {
                return;
            }

            if(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue() == o.getNodeType()){
                ScrmProcessOrchestrationNodeVO currentNode = o;
                while (currentNode.getPreNodeId() >= 0) {
                    currentNode = nodeMap.get(currentNode.getPreNodeId());
                    if (null == currentNode.getBranchStatisticsVO()) {
                        continue;
                    }
                    ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO = currentNode.getBranchStatisticsVO();
                    if(false == branchStatisticsVO.isLeafNode()){
                        break;
                    }
                    branchStatisticsVO.setLeafNode(false);
                }
            } else {
                o.getBranchStatisticsVO().setLeafNode(false);
            }


            if (finalTotalExecuteCount == 0) {
                o.getBranchStatisticsVO().setUserRate("-");
            } else {
                BigDecimal userRate = new BigDecimal(o.getBranchStatisticsVO().getUserCount()).divide(new BigDecimal(finalTotalExecuteCount), 2, RoundingMode.HALF_UP);
                o.getBranchStatisticsVO().setUserRate(userRate.toPlainString());
            }
        });
        return processOrchestrationNodeList;
    }

    private void updateActionNode(Map<Long, ScrmProcessOrchestrationActionDTO> actionMap, Map<String, List<ScrmProcessOrchestrationActionContentDTO>> actionContentMap, Map<String, List<ScrmProcessOrchestrationActionAttachmentDTO>> actionAttachmentMap, ScrmProcessOrchestrationNodeDTO nodeDTO, ScrmProcessOrchestrationNodeVO nodeVO) {
        ScrmProcessOrchestrationActionDTO actionDTO = actionMap.get(nodeDTO.getNodeId());
        if (actionDTO == null) {
            return;
        }

        Integer actionType = actionDTO.getActionType().intValue();
        nodeVO.setConditionNodeType(ScrmProcessOrchestrationConditionTypeEnum.UNKNOWN.getValue());

        ScrmProcessOrchestrationActionVO actionVO = new ScrmProcessOrchestrationActionVO();
        actionVO.setActionType(actionType);
        actionVO.setActionSubType(actionDTO.getActionSubType());
        actionVO.setContentType(actionDTO.getContentType());
        actionVO.setActionId(actionDTO.getActionId());

        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = actionContentMap.get(nodeDTO.getNodeId() + "-" + actionDTO.getActionId());
        if(CollectionUtils.isNotEmpty(contentDTOS)){
            contentDTOS.sort(Comparator.comparingInt(ScrmProcessOrchestrationActionContentDTO::getContentId));

            switch (actionType) {
                // ScrmProcessOrchestrationActionTypeEnum.CROWD_GROUP_TOUCH
                case 1:
                    // ScrmProcessOrchestrationActionTypeEnum.STAFF_TASK_ASSIGN
                case 2:
                    // ScrmProcessOrchestrationActionTypeEnum.STAFF_FRIEND_CIRCLE_TOUCH
                case 3:
                    // ScrmProcessOrchestrationActionTypeEnum.CUSTOMIZE
                case 7:
                    // ScrmProcessOrchestrationActionTypeEnum.CROWD_GROUP_TOUCH_IN_GROUP
                    actionVO.setContentList(new ArrayList<>());
                    nodeVO.setActionNodeType(actionType);
                    for (ScrmProcessOrchestrationActionContentDTO contentDTO : contentDTOS) {
                        ScrmProcessOrchestrationActionContentVO contentVO = new ScrmProcessOrchestrationActionContentVO();
                        contentVO.setContentId(contentDTO.getContentId());
                        contentVO.setContent(contentDTO.getContent());
                        contentVO.setContentType(contentDTO.getContentType());
                        contentVO.setAttachmentList(scrmProcessOrchestrationActionAttachmentVOConverter
                                                            .convertToDOsSafety(actionAttachmentMap.get(nodeDTO.getNodeId() + "-" + actionDTO.getActionId() + "-" + contentDTO.getContentId())));
                        actionVO.getContentList().add(contentVO);
                    }
                    nodeVO.setActionVO(actionVO);
                    break;
                // ScrmProcessOrchestrationActionTypeEnum.AUTO_EDIT_CROWD_TAG
                case 4:
                // ScrmProcessOrchestrationActionTypeEnum.AUTO_EDIT_CROWD_ATTR
                case 5:
                // ScrmProcessOrchestrationActionTypeEnum.AUTO_EDIT_CROWD_STAGE
                case 6:
                // ScrmProcessOrchestrationActionTypeEnum.COUPON_DISTRIBUTION
                case 8:
                    actionVO.setContentList(new ArrayList<>());
                    nodeVO.setActionNodeType(actionType);
                    for (ScrmProcessOrchestrationActionContentDTO contentDTO : contentDTOS) {
                        ScrmProcessOrchestrationActionContentVO contentVO = new ScrmProcessOrchestrationActionContentVO();
                        contentVO.setContentId(contentDTO.getContentId());
                        contentVO.setContent(contentDTO.getContent());
                        contentVO.setContentType(contentDTO.getContentType());
                        contentVO.setAttachmentList(scrmProcessOrchestrationActionAttachmentVOConverter
                                                            .convertToDOsSafety(actionAttachmentMap.get(nodeDTO.getNodeId() + "-" + actionDTO.getActionId() + "-" + contentDTO.getContentId())));
                        actionVO.getContentList().add(contentVO);
                    }
                    nodeVO.setAutomatedActionVO(actionVO);
                    break;
                default:
                    break;
            }
        }
    }

    private void updateConditionsOfNode(Map<Long, List<ScrmProcessOrchestrationConditionDetailDTO>> conditionMap, ScrmProcessOrchestrationNodeDTO nodeDTO, ScrmProcessOrchestrationNodeVO nodeVO) {
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = conditionMap.get(nodeDTO.getNodeId());
        if (CollectionUtils.isEmpty(conditionDetailDTOList)) {
            return;
        }
        ScrmProcessOrchestrationConditionDetailDTO firstCondition = conditionDetailDTOList.get(0);
        Integer conditionType = firstCondition.getType();
        nodeVO.setConditionNodeType(conditionType);

        switch (conditionType) {
            //ScrmProcessOrchestrationConditionTypeEnum.CONDITION_BRANCH
            case 1:
                ScrmProcessOrchestrationConditionVO conditionVO = new ScrmProcessOrchestrationConditionVO();
                conditionVO.setConditionDetails(scrmProcessOrchestrationConditionVOConverter.convertToDOsSafety(conditionDetailDTOList));
                if (conditionDetailDTOList.size() == 1 && conditionDetailDTOList.get(0).getDefaultCondition()) {
                    conditionVO.setDefaultCondition(true);
                }
                nodeVO.setConditionBranch(conditionVO);
                break;
            // ScrmProcessOrchestrationConditionTypeEnum.DELAY_BRANCH
            case 2:
                ScrmProcessOrchestrationDelayVO delayVO = scrmProcessOrchestrationDelayVOConverter.convertToDO(firstCondition);
                nodeVO.setDelayBranch(delayVO);
                break;
            //ScrmProcessOrchestrationConditionTypeEnum.SPLIT_BRANCH
            case 3:
                ScrmProcessOrchestrationSplitVO splitVO = scrmProcessOrchestrationSplitVOConverter.convertToDO(firstCondition);
                nodeVO.setSplitBranch(splitVO);
                break;
            case 0:
            default:
                break;
        }
    }

    @NotNull
    private ScrmProcessOrchestrationActionContentDTO getScrmProcessOrchestrationActionContentDTO(ScrmProcessOrchestrationNodeVO nodeVO, ScrmProcessOrchestrationActionVO actionVO, ScrmProcessOrchestrationActionContentVO contentVO) {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContentId(contentVO.getContentId());
        contentDTO.setContent(contentVO.getContent());
        contentDTO.setContentType(contentVO.getContentType());
        contentDTO.setActionId(actionVO.getActionId());
        contentDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
        return contentDTO;
    }

    @NotNull
    private List<ScrmProcessOrchestrationActionAttachmentDTO> getScrmProcessOrchestrationActionAttachmentDTOS(ScrmProcessOrchestrationNodeVO nodeVO, ScrmProcessOrchestrationActionVO actionVO, ScrmProcessOrchestrationActionContentVO contentVO, List<ScrmProcessOrchestrationActionAttachmentVO> actionAttachmentVOS) {
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = new ArrayList<>();
        for (ScrmProcessOrchestrationActionAttachmentVO actionAttachmentVO : actionAttachmentVOS) {
            ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = scrmProcessOrchestrationActionAttachmentVOConverter.convertToDTO(actionAttachmentVO);
            actionAttachmentDTO.setProcessOrchestrationNodeId(nodeVO.getNodeId());
            actionAttachmentDTO.setActionId(actionVO.getActionId());
            actionAttachmentDTO.setContentId(contentVO.getContentId());
            actionAttachmentDTOList.add(actionAttachmentDTO);
        }
        return actionAttachmentDTOList;
    }
}
