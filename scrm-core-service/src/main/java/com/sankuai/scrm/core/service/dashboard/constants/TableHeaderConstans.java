package com.sankuai.scrm.core.service.dashboard.constants;

import lombok.Data;

@Data
public class TableHeaderConstans {
    public static final String CORP_NAME="主体";
    public static final String CORP_WX_CUSTOMER_COUNT="企微客户数";
    public static final String CORP_WX_FRIEND_COUNT="企微好友数";
    public static final String IN_GROUP_USERS_COUNT="群内用户数";
    public static final String CUSTOMER_INCREMENT="新增客户数";
    public static final String FRIEND_INCREMENT="新增好友数";
    public static final String IN_GROUP_USER_INCREMENT="新增群内用户数";
    public static final String INNER_STATION_USER_INCREMENT="站内来源用户数";
    public static final String ACTIVITY_USER_INCREMENT="活动来源用户数";
    public static final String OUT_STATION_OR_REPLACEMENT_USER_INCREMENT="站外&置换来源用户数";
    public static final String SELF_USER_INCREMENT="自增量用户数";
    public static final String CORP_WX_DYNAMIC_CODE_USER_INCREMENT="企微活码来源用户数";
    public static final String GROUP_DYNAMIC_LIVE_CODE_USER_INCREMENT="群活码来源用户数";
    public static final String CUSTOMER_DECREMENT="流失客户数";
    public static final String FRIEND_DECREMENT="流失好友数";
    public static final String IN_GROUP_USER_DECREMENT="流失群内用户数";
    public static final String INNER_STATION_USER_DECREMENT="站内来源流失用户数";
    public static final String ACTIVITY_USER_DECREMENT="活动来源流失用户数";
    public static final String OUT_STATION_OR_REPLACEMENT_USER_DECREMENT="站外&置换来源流失用户数";
    public static final String SELF_USER_DECREMENT="自增量流失用户数";
    public static final String CORP_WX_DYNAMIC_CODE_USER_DECREMENT="企微活码流失用户数";
    public static final String GROUP_DYNAMIC_CODE_USER_DECREMENT="群活码流失用户数";
    public static final String NET_CUSTOMER_INCREMENT="净增用户数";
    public static final String WEEKLY_USER_RETENTION_RATE="7日留存率";
    public static final String MONTHLY_USER_RETENTION_RATE="30日留存率";
    public static final String IN_GROUP_SENDER_COUNT="群内发言";
    public static final String PRIVATE_SENDER_COUNT="私聊发言";
    public static final String SENDER_COUNT="合计发言";
    public static final String FISSION_PARTICIPANT_COUNT="参加裂变用户";
    public static final String DRAW_PARTICIPANT_COUNT="参加抽奖用户";
    public static final String CONTENT_RADAR_PARTICIPANT_COUNT="参加内容雷达用户";
    public static final String PARTICIPANT_COUNT="合计参加活动";
    public static final String CLICK_PRODUCTS_NOTIFICATION_COUNT="商品页UV";
    public static final String CLICK_ACTIVITY_PAGE_NOTIFICATION_COUNT="活动页UV";
    public static final String CLICK_COUPON_COUNT="优惠券UV";
    public static final String CLICK_CONTENT_PAGE_NOTIFICATION_COUNT="点击企微内推送内容页";
    public static final String CLICK_CONTENT_COUNT="合计UV";
    public static final String TRANSACTION_USER_COUNT_WIDELY="交易用户数（宽）";
    public static final String TRANSACTION_USER_COUNT_NARROWLY="交易用户数（窄）";
    public static final String TRANSACTION_ORDER_COUNT="交易订单量";
    public static final String GTV="gtv";
    public static final String ACTUAL_GTV="实付GTV";
    public static final String WRITE_OFF_GTV="核销GTV";
    public static final String ACTUAL_CHECK_GTV="实付验证GTV";
}
