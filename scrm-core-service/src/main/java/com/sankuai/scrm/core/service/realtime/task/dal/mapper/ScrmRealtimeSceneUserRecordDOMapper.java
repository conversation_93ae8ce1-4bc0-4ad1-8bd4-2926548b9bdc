package com.sankuai.scrm.core.service.realtime.task.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmRealtimeSceneUserRecordDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmRealtimeSceneUserRecordDOMapper extends MybatisBaseMapper<ScrmRealtimeSceneUserRecordDO, ScrmRealtimeSceneUserRecordDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmRealtimeSceneUserRecordDO> list);
}