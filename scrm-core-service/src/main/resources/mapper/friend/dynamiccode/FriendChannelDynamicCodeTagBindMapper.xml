<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.friend.dynamiccode.dal.mapper.FriendChannelDynamicCodeTagBindMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeTagBind">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="dynamic_code_id" jdbcType="BIGINT" property="dynamicCodeId" />
    <result column="tag_group_id" jdbcType="VARCHAR" property="tagGroupId" />
    <result column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, dynamic_code_id, tag_group_id, tag_id, is_delete, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.FriendChannelDynamicCodeTagBindExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from friend_channel_dynamic_code_tag_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from friend_channel_dynamic_code_tag_bind
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from friend_channel_dynamic_code_tag_bind
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.FriendChannelDynamicCodeTagBindExample">
    delete from friend_channel_dynamic_code_tag_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeTagBind">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into friend_channel_dynamic_code_tag_bind (app_id, dynamic_code_id, tag_group_id, 
      tag_id, is_delete, add_time, 
      update_time)
    values (#{appId,jdbcType=VARCHAR}, #{dynamicCodeId,jdbcType=BIGINT}, #{tagGroupId,jdbcType=VARCHAR}, 
      #{tagId,jdbcType=VARCHAR}, #{isDelete,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeTagBind">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into friend_channel_dynamic_code_tag_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="dynamicCodeId != null">
        dynamic_code_id,
      </if>
      <if test="tagGroupId != null">
        tag_group_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="dynamicCodeId != null">
        #{dynamicCodeId,jdbcType=BIGINT},
      </if>
      <if test="tagGroupId != null">
        #{tagGroupId,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.FriendChannelDynamicCodeTagBindExample" resultType="java.lang.Long">
    select count(*) from friend_channel_dynamic_code_tag_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update friend_channel_dynamic_code_tag_bind
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.dynamicCodeId != null">
        dynamic_code_id = #{record.dynamicCodeId,jdbcType=BIGINT},
      </if>
      <if test="record.tagGroupId != null">
        tag_group_id = #{record.tagGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update friend_channel_dynamic_code_tag_bind
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      dynamic_code_id = #{record.dynamicCodeId,jdbcType=BIGINT},
      tag_group_id = #{record.tagGroupId,jdbcType=VARCHAR},
      tag_id = #{record.tagId,jdbcType=VARCHAR},
      is_delete = #{record.isDelete,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeTagBind">
    update friend_channel_dynamic_code_tag_bind
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="dynamicCodeId != null">
        dynamic_code_id = #{dynamicCodeId,jdbcType=BIGINT},
      </if>
      <if test="tagGroupId != null">
        tag_group_id = #{tagGroupId,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCodeTagBind">
    update friend_channel_dynamic_code_tag_bind
    set app_id = #{appId,jdbcType=VARCHAR},
      dynamic_code_id = #{dynamicCodeId,jdbcType=BIGINT},
      tag_group_id = #{tagGroupId,jdbcType=VARCHAR},
      tag_id = #{tagId,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into friend_channel_dynamic_code_tag_bind
    (app_id, dynamic_code_id, tag_group_id, tag_id, is_delete, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.dynamicCodeId,jdbcType=BIGINT}, #{item.tagGroupId,jdbcType=VARCHAR}, 
        #{item.tagId,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=BIT}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      insert into friend_channel_dynamic_code_tag_bind
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.appId != null">
          app_id,
        </if>
        <if test="item.dynamicCodeId != null">
          dynamic_code_id,
        </if>
        <if test="item.tagGroupId != null">
          tag_group_id,
        </if>
        <if test="item.tagId != null">
          tag_id,
        </if>
        <if test="item.isDelete != null">
          is_delete,
        </if>
        <if test="item.addTime != null">
          add_time,
        </if>
        <if test="item.updateTime != null">
          update_time,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.appId != null">
          #{item.appId,jdbcType=VARCHAR},
        </if>
        <if test="item.dynamicCodeId != null">
          #{item.dynamicCodeId,jdbcType=BIGINT},
        </if>
        <if test="item.tagGroupId != null">
          #{item.tagGroupId,jdbcType=VARCHAR},
        </if>
        <if test="item.tagId != null">
          #{item.tagId,jdbcType=VARCHAR},
        </if>
        <if test="item.isDelete != null">
          #{item.isDelete,jdbcType=BIT},
        </if>
        <if test="item.addTime != null">
          #{item.addTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>