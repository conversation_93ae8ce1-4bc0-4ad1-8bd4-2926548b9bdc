package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CrowdPackPersonaRedisServiceTest {

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private CrowdPackPersonaRedisService crowdPackPersonaRedisService;

    private final long testPackId = 123L;
    private final String todayDate = LocalDate.now().toString();
    private final String yesterdayDate = LocalDate.now().minusDays(1).toString();


    /**
     * 测试设置空用户ID集合
     */
    @Test
    public void testSetTodayPersonaUserIdSet_EmptySet() {
        // arrange
        Set<Long> emptySet = new HashSet<>();

        // act
        boolean result = crowdPackPersonaRedisService.setTodayPersonaUserIdSet(testPackId, emptySet);

        // assert
        assertFalse(result);
        verify(redisClient, never()).set(any(StoreKey.class), anyString(), anyInt());
    }

    /**
     * 测试设置null用户ID集合
     */
    @Test
    public void testSetTodayPersonaUserIdSet_NullSet() {
        // act
        boolean result = crowdPackPersonaRedisService.setTodayPersonaUserIdSet(testPackId, null);

        // assert
        assertFalse(result);
        verify(redisClient, never()).set(any(StoreKey.class), anyString(), anyInt());
    }

    /**
     * 测试获取昨日用户ID集合 - 有效数据
     */
    @Test
    public void testGetYesterdayPersonaUserIdSet_WithValidData() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        StoreKey partitionKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_0");

        when(redisClient.get(batchCountKey)).thenReturn("1");
        when(redisClient.get(partitionKey)).thenReturn("[1001,1002,1003]");

        // act
        Set<Long> result = crowdPackPersonaRedisService.getYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertEquals(3, result.size());
        assertTrue(result.containsAll(Arrays.asList(1001L, 1002L, 1003L)));
        verify(redisClient).get(batchCountKey);
        verify(redisClient).get(partitionKey);
    }

    /**
     * 测试获取昨日用户ID集合 - 多批次数据
     */
    @Test
    public void testGetYesterdayPersonaUserIdSet_WithMultipleBatches() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        StoreKey partitionKey0 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_0");
        StoreKey partitionKey1 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_1");

        when(redisClient.get(batchCountKey)).thenReturn("2");
        when(redisClient.get(partitionKey0)).thenReturn("[1001,1002]");
        when(redisClient.get(partitionKey1)).thenReturn("[1003,1004]");

        // act
        Set<Long> result = crowdPackPersonaRedisService.getYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertEquals(4, result.size());
        assertTrue(result.containsAll(Arrays.asList(1001L, 1002L, 1003L, 1004L)));
        verify(redisClient).get(batchCountKey);
        verify(redisClient).get(partitionKey0);
        verify(redisClient).get(partitionKey1);
    }

    /**
     * 测试获取昨日用户ID集合 - 无批次信息
     */
    @Test
    public void testGetYesterdayPersonaUserIdSet_NoBatchInfo() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        when(redisClient.get(batchCountKey)).thenReturn(null);

        // act
        Set<Long> result = crowdPackPersonaRedisService.getYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(redisClient).get(batchCountKey);
    }

    /**
     * 测试获取昨日用户ID集合 - 空批次信息
     */
    @Test
    public void testGetYesterdayPersonaUserIdSet_EmptyBatchInfo() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        when(redisClient.get(batchCountKey)).thenReturn("");

        // act
        Set<Long> result = crowdPackPersonaRedisService.getYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(redisClient).get(batchCountKey);
    }

    /**
     * 测试获取昨日用户ID集合 - 部分分片数据为空
     */
    @Test
    public void testGetYesterdayPersonaUserIdSet_WithPartialEmptyData() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        StoreKey partitionKey0 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_0");
        StoreKey partitionKey1 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_1");

        when(redisClient.get(batchCountKey)).thenReturn("2");
        when(redisClient.get(partitionKey0)).thenReturn("[1001,1002]");
        when(redisClient.get(partitionKey1)).thenReturn(null); // 第二个分片为空

        // act
        Set<Long> result = crowdPackPersonaRedisService.getYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertEquals(2, result.size());
        assertTrue(result.containsAll(Arrays.asList(1001L, 1002L)));
        verify(redisClient).get(batchCountKey);
        verify(redisClient).get(partitionKey0);
        verify(redisClient).get(partitionKey1);
    }

    /**
     * 测试检查今日用户是否存在
     */
    @Test
    public void testIsTodayPersonaUserExist_Exists() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, todayDate, "batch_info");
        when(redisClient.exists(batchCountKey)).thenReturn(true);

        // act
        boolean result = crowdPackPersonaRedisService.isTodayPersonaUserExist(testPackId);

        // assert
        assertTrue(result);
        verify(redisClient).exists(batchCountKey);
    }

    /**
     * 测试检查今日用户是否存在 - 不存在
     */
    @Test
    public void testIsTodayPersonaUserExist_NotExists() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, todayDate, "batch_info");
        when(redisClient.exists(batchCountKey)).thenReturn(false);

        // act
        boolean result = crowdPackPersonaRedisService.isTodayPersonaUserExist(testPackId);

        // assert
        assertFalse(result);
        verify(redisClient).exists(batchCountKey);
    }

    /**
     * 测试删除昨日用户ID集合 - 成功
     */
    @Test
    public void testDeleteYesterdayPersonaUserIdSet_Success() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        StoreKey partitionKey0 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_0");
        StoreKey partitionKey1 = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_1");

        when(redisClient.get(batchCountKey)).thenReturn("2");

        // act
        boolean result = crowdPackPersonaRedisService.deleteYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertTrue(result);
        verify(redisClient).get(batchCountKey);
        verify(redisClient).delete(partitionKey0);
        verify(redisClient).delete(partitionKey1);
        verify(redisClient).delete(batchCountKey);
    }

    /**
     * 测试删除昨日用户ID集合 - 无批次信息
     */
    @Test
    public void testDeleteYesterdayPersonaUserIdSet_NoBatchInfo() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, yesterdayDate, "batch_info");
        when(redisClient.get(batchCountKey)).thenReturn(null);

        // act
        boolean result = crowdPackPersonaRedisService.deleteYesterdayPersonaUserIdSet(testPackId);

        // assert
        assertTrue(result);
        verify(redisClient).get(batchCountKey);
        verify(redisClient).delete(batchCountKey);
        verify(redisClient, times(1)).delete(any(StoreKey.class));
    }

    /**
     * 测试获取今日用户ID集合
     */
    @Test
    public void testGetTodayPersonaUserIdSet() {
        // arrange
        StoreKey batchCountKey = new StoreKey("scrm_persona_user_id_category", testPackId, todayDate, "batch_info");
        StoreKey partitionKey = new StoreKey("scrm_persona_user_id_category", testPackId, todayDate, "batch_0");

        when(redisClient.get(batchCountKey)).thenReturn("1");
        when(redisClient.get(partitionKey)).thenReturn("[1001,1002,1003]");

        // act
        Set<Long> result = crowdPackPersonaRedisService.getTodayPersonaUserIdSet(testPackId);

        // assert
        assertEquals(3, result.size());
        assertTrue(result.containsAll(Arrays.asList(1001L, 1002L, 1003L)));
        verify(redisClient).get(batchCountKey);
        verify(redisClient).get(partitionKey);
    }
}
