package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ext.ScrmAmCrowdPackDetailCountDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackDetailInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackDetailInfoDOMapper;

import java.util.List;

public interface ExtScrmAmCrowdPackDetailInfoDOMapper extends ScrmAmCrowdPackDetailInfoDOMapper {

    /**
     * 根据条件查询明细数量
     * @param example
     * @return
     */
    List<ScrmAmCrowdPackDetailCountDO> batchCountByExample(ScrmAmCrowdPackDetailInfoDOExample example);

    List<Long> getPackDetailInfoByPackId(ScrmAmCrowdPackDetailInfoDOExample example);
    Long findMinIdByExample(ScrmAmCrowdPackDetailInfoDOExample example);
    Long findMaxIdByExample(ScrmAmCrowdPackDetailInfoDOExample example);
}