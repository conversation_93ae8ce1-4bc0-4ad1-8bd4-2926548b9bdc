package com.sankuai.scrm.core.service.pchat.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.scrm.core.service.pchat.adapter.utils.WechatTypeUtils;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveInfo;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmLiveInfoExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmLiveInfoMapper;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmLiveInfoDomainServiceTest {

    @InjectMocks
    private ScrmLiveInfoDomainService scrmLiveInfoDomainService;

    @Mock
    private ScrmLiveInfoMapper liveInfoMapper;

    @Mock
    private WechatTypeUtils wechatTypeUtils;

    private String liveId = "testLiveId";

    private WeChatType weChatType = WeChatType.PERSONAL_WECHAT;

    @Before
    public void setUp() {
        when(wechatTypeUtils.getPersonalAppIdList()).thenReturn(Arrays.asList("appId1", "appId2"));
    }

    /**
     * 测试 liveId 为空的情况
     */
    @Test
    public void testQueryLiveInfoLiveIdIsNull() throws Throwable {
        // arrange
        String liveId = null;
        // act
        ScrmLiveInfo result = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 liveId 不为空，但数据库中没有对应的 ScrmLiveInfo 的情况
     */
    @Test
    public void testQueryLiveInfoLiveIdIsNotNullButNoData() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        when(liveInfoMapper.selectByExample(any(ScrmLiveInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmLiveInfo result = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 liveId 不为空，数据库中有对应的 ScrmLiveInfo 的情况
     */
    @Test
    public void testQueryLiveInfoLiveIdIsNotNullAndHasData() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        ScrmLiveInfo expected = new ScrmLiveInfo();
        when(liveInfoMapper.selectByExample(any(ScrmLiveInfoExample.class))).thenReturn(Collections.singletonList(expected));
        // act
        ScrmLiveInfo result = scrmLiveInfoDomainService.queryLiveInfo(liveId);
        // assert
        assertSame(expected, result);
    }

    /**
     * 测试 liveIdList 为空的情况
     */
    @Test
    public void testBatchQueryLiveInfoListEmptyList() throws Throwable {
        // arrange
        List<String> liveIdList = Collections.emptyList();
        // act
        List<ScrmLiveInfo> result = scrmLiveInfoDomainService.batchQueryLiveInfoList(liveIdList);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 liveIdList 不为空，但数据库中没有对应的 ScrmLiveInfo 的情况
     */
    @Test
    public void testBatchQueryLiveInfoListNoData() throws Throwable {
        // arrange
        List<String> liveIdList = Arrays.asList("live1", "live2");
        when(liveInfoMapper.selectByExample(any(ScrmLiveInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmLiveInfo> result = scrmLiveInfoDomainService.batchQueryLiveInfoList(liveIdList);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 liveIdList 不为空，数据库中有对应的 ScrmLiveInfo 的情况
     */
    @Test
    public void testBatchQueryLiveInfoListWithData() throws Throwable {
        // arrange
        List<String> liveIdList = Arrays.asList("live1", "live2");
        ScrmLiveInfo liveInfo1 = new ScrmLiveInfo();
        liveInfo1.setLiveId("live1");
        ScrmLiveInfo liveInfo2 = new ScrmLiveInfo();
        liveInfo2.setLiveId("live2");
        when(liveInfoMapper.selectByExample(any(ScrmLiveInfoExample.class))).thenReturn(Arrays.asList(liveInfo1, liveInfo2));
        // act
        List<ScrmLiveInfo> result = scrmLiveInfoDomainService.batchQueryLiveInfoList(liveIdList);
        // assert
        assertEquals(2, result.size());
        assertEquals("live1", result.get(0).getLiveId());
        assertEquals("live2", result.get(1).getLiveId());
    }

    @Test
    public void testBindLiveWxTypeWithEmptyLiveId() {
        assertFalse(scrmLiveInfoDomainService.bindLiveWxType(null, weChatType));
    }

    @Test
    public void testBindLiveWxTypeWithEmptyWeChatType() {
        assertFalse(scrmLiveInfoDomainService.bindLiveWxType(liveId, null));
    }

    @Test
    public void testBindLiveWxTypeWithEmptyAppIdList() {
        when(wechatTypeUtils.getPersonalAppIdList()).thenReturn(Collections.emptyList());
        assertFalse(scrmLiveInfoDomainService.bindLiveWxType(liveId, weChatType));
    }

    @Test
    public void testBindLiveWxTypeWithNonEmptyAppIdList() {
        when(liveInfoMapper.insert(any(ScrmLiveInfo.class))).thenReturn(1);
        assertTrue(scrmLiveInfoDomainService.bindLiveWxType(liveId, weChatType));
    }

    @Test
    public void testBindLiveWxTypeWithInsertFailed() {
        when(liveInfoMapper.insert(any(ScrmLiveInfo.class))).thenReturn(0);
        assertFalse(scrmLiveInfoDomainService.bindLiveWxType(liveId, weChatType));
    }
}
