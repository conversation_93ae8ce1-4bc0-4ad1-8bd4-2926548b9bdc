package com.sankuai.scrm.core.service.faq.domain.service;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.faq.enums.FaqStatusEnum;
import com.sankuai.scrm.core.service.faq.domain.entity.AnswerEntity;
import com.sankuai.scrm.core.service.faq.domain.entity.FaqEntity;
import com.sankuai.scrm.core.service.faq.domain.entity.QuestionEntity;
import com.sankuai.scrm.core.service.faq.infrastructure.repository.StoreModelRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2022/12/7 4:54 PM
 **/
@Slf4j
@Service
public class FaqAggregateCommand implements FaqCommandIface {
    @Autowired
    private StoreModelRepository storeModelRepository;
    @Autowired
    private FaqKeywordService keywordService;

    @Override
    public Long addFaq(FaqEntity faq) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.addFaq(com.sankuai.scrm.core.service.faq.domain.entity.FaqEntity)");
        if(faq == null || faq.getQuestion()== null || faq.getQuestion().getId()!=null)return null;
        QuestionEntity question = faq.getQuestion();
        List<AnswerEntity> answers = faq.getAnswerList();
        if(question.getStatus() == null)question.setStatus(FaqStatusEnum.INIT.status);
        Long questionId = storeModelRepository.addQuestion(question);
        if(questionId == null)return null;
        if(CollectionUtils.isNotEmpty(answers)){
            answers.forEach(r->{
                r.setQuestionIds(Lists.newArrayList(questionId));
                if(r.getStatus() == null)r.setStatus(FaqStatusEnum.INIT.status);
            });
        }
        storeModelRepository.addAnswers(answers);
        return questionId;
    }

    @Override
    public boolean updateQuestion(QuestionEntity question) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.updateQuestion(com.sankuai.scrm.core.service.faq.domain.entity.QuestionEntity)");
        if(question == null || question.getId() == null){
            throw new IllegalArgumentException("id 不能为空");
        }
        return storeModelRepository.updateQuestionById(question);
    }

    @Override
    public boolean deleteQuestion(Long id) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.deleteQuestion(java.lang.Long)");
        if(id == null)return false;
        List<QuestionEntity> list = storeModelRepository.queryQuestionById(Lists.newArrayList(id));
        if(CollectionUtils.isEmpty(list)){
            throw new IllegalArgumentException("无此问题");
        }
        QuestionEntity question = list.get(0);
        if(question.getStatus() == FaqStatusEnum.DELETED.status){
            throw new IllegalArgumentException("问题已删除");
        }
        QuestionEntity updateEntity = new QuestionEntity();
        updateEntity.setId(id);
        updateEntity.setStatus(FaqStatusEnum.DELETED.status);
        boolean update =  updateQuestion(question);
        keywordService.processKeywordWhenOff(question);
        return update;
    }

    @Override
    public boolean deleteFaq(Long id) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.deleteFaq(java.lang.Long)");
        List<QuestionEntity> list = storeModelRepository.queryQuestionById(Lists.newArrayList(id));
        if(CollectionUtils.isEmpty(list)){
            Cat.logEvent("DeleteFaq","nofaq-"+ id);
            return false;
        }
        QuestionEntity question = list.get(0);
        List<AnswerEntity> answerList = storeModelRepository.queryMax100AnswerList(Lists.newArrayList(id),Lists.newArrayList(FaqStatusEnum.INIT.status, FaqStatusEnum.VALID.status, FaqStatusEnum.MANUAL_AUDIT.status, FaqStatusEnum.VERIFYING.status));
        if(question.getStatus() != FaqStatusEnum.DELETED.status){
            QuestionEntity updateEntity = new QuestionEntity();
            updateEntity.setId(id);
            updateEntity.setStatus(FaqStatusEnum.DELETED.status);
            updateQuestion(updateEntity);
            keywordService.processKeywordWhenOff(question);
        }
        List<Long> needDelAnswers = answerList.stream().filter(r->r.getStatus()!=FaqStatusEnum.DELETED.status).map(r->r.getId()).collect(Collectors.toList());
        List<List<Long>> lists = Lists.partition(needDelAnswers,50);
        lists.forEach(l->deleteAnswer(needDelAnswers));
        return true;
    }

    @Override
    public boolean updateAnswer(List<AnswerEntity> answers) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.updateAnswer(java.util.List)");
        if(CollectionUtils.isEmpty(answers))return true;
        boolean canUpdate = answers.stream().allMatch(r->r.getId()!=null);
        if(!canUpdate){
            throw new IllegalArgumentException("id 不能为空");
        }
        return storeModelRepository.updateAnswersById(answers);
    }

    @Override
    public boolean addAnswer(List<AnswerEntity> answers) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.addAnswer(java.util.List)");
        if(CollectionUtils.isEmpty(answers))return true;
        return storeModelRepository.addAnswers(answers);
    }

    @Override
    public boolean deleteAnswer(List<Long> ids) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.deleteAnswer(java.util.List)");
        return updateAnswerStatus(ids,FaqStatusEnum.DELETED.status);
    }

    @Override
    public boolean updateAnswerStatus(List<Long> ids, int status) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.updateAnswerStatus(java.util.List,int)");
        if(CollectionUtils.isEmpty(ids))return true;
        Cat.logEvent("Faq-updateAnswerStatus",ids.toString()+status);
        List<AnswerEntity> answerList = ids.stream().map(r->{
            AnswerEntity entity = new AnswerEntity();
            entity.setId(r);
            entity.setStatus(status);
            return entity;
        }).collect(Collectors.toList());
        return storeModelRepository.updateAnswersById(answerList);
    }

    @Override
    public boolean publishAnswer(List<Long> ids) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.publishAnswer(java.util.List)");
        if(CollectionUtils.isEmpty(ids))return true;
        List<AnswerEntity> nowAnswers = storeModelRepository.queryAnswerById(ids);
        boolean canPublish = nowAnswers.stream().allMatch(r-> FaqStatusEnum.MANUAL_AUDIT.status == r.getStatus() || FaqStatusEnum.VALID.status == r.getStatus() || FaqStatusEnum.VERIFYING.status == r.getStatus());
        if(!canPublish){
            throw new IllegalArgumentException("当前状态不允许发布");
        }
        return updateAnswerStatus(ids,FaqStatusEnum.VALID.status);
    }

    @Override
    public boolean publishQuestion(List<Long> ids) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.publishQuestion(java.util.List)");
        List<QuestionEntity> list = storeModelRepository.queryQuestionById(ids);
        boolean allAudited = list.stream().allMatch(r->r.getStatus() == FaqStatusEnum.MANUAL_AUDIT.status||r.getStatus() == FaqStatusEnum.VERIFYING.status);
        if(!allAudited){
            throw new IllegalArgumentException("当前状态不允许发布");
        }
        for(Long id:ids){
            QuestionEntity entity = new QuestionEntity();
            entity.setId(id);
            entity.setStatus(FaqStatusEnum.VALID.status);
            updateQuestion(entity);
        }
        //更新关键词相关数据
        for(QuestionEntity entity:list){
            keywordService.processKeywordWhenPublish(entity);
        }
        return true;
    }

    @Override
    public void asyncDelQuestion(String appId,Long categoryId, List<Long> subCategoryId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.domain.service.FaqAggregateCommand.asyncDelQuestion(java.lang.String,java.lang.Long,java.util.List)");
        if(CollectionUtils.isEmpty(subCategoryId) && categoryId==null)throw new IllegalArgumentException("param error");
        storeModelRepository.asyncDelQuestion(categoryId,subCategoryId);
        keywordService.submitRefreshTask(appId,20);
    }
}
