package com.sankuai.scrm.core.service.group.service.impl;

import com.sankuai.dz.srcm.group.enums.GroupTagOperatorTypeEnum;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagMapping;
import com.sankuai.scrm.core.service.tag.domain.GroupTagDomainService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupInfoServiceImplObtainGroupHasTagTest {

    @InjectMocks
    private GroupInfoServiceImpl groupInfoService;

    @Mock
    private GroupTagDomainService groupTagDomainService;

    private List<String> tagSerialNos;

    @Before
    public void setUp() {
        tagSerialNos = Arrays.asList("tag1", "tag2");
    }

    private GroupTagMapping createGroupTagMapping(String groupId, String groupTagSerialNo) {
        GroupTagMapping groupTagMapping = new GroupTagMapping();
        groupTagMapping.setGroupId(groupId);
        groupTagMapping.setGroupTagSerialNo(groupTagSerialNo);
        return groupTagMapping;
    }

    @Test
    public void testObtainGroupHasTagWithIntersectionAndMatch() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(createGroupTagMapping("group1", "tag1"), createGroupTagMapping("group1", "tag2"), createGroupTagMapping("group2", "tag1"));
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.INTERSECTION);
        assertEquals(Arrays.asList("group1"), result);
    }

    @Test
    public void testObtainGroupHasTagWithIntersectionComplementAndNoMatch() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(
                createGroupTagMapping("group1", "tag1"),
                createGroupTagMapping("group2", "tag2"),
                createGroupTagMapping("group1", "tag2")
        );
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<GroupTagMapping> allGroupTagMapping = new ArrayList<>();
        allGroupTagMapping.addAll(groupTagMappings);
        when(groupTagDomainService.queryGroupTagMappingExcludeGroupTagSetSerialNo("AppId", new ArrayList<>())).thenReturn(allGroupTagMapping);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.INTERSECTION_COMPLEMENT);
        assertThat(result, containsInAnyOrder("group2"));
    }

    @Test
    public void testObtainGroupHasTagWithUnionComplementAndMatch() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(createGroupTagMapping("group1", "tag1"), createGroupTagMapping("group2", "tag2"));
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<GroupTagMapping> allGroupTagMapping = new ArrayList<>();
        allGroupTagMapping.add(createGroupTagMapping("group3", "tag3"));
        allGroupTagMapping.add(createGroupTagMapping("group4", "tag3"));
        when(groupTagDomainService.queryGroupTagMappingExcludeGroupTagSetSerialNo(anyString(), anyList())).thenReturn(allGroupTagMapping);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.UNION_COMPLEMENT);
        assertEquals(Arrays.asList("group3","group4"), result);
    }

    @Test
    public void testObtainGroupHasTagWithEmptyTagSerialNos() throws Throwable {
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", Collections.emptyList(), GroupTagOperatorTypeEnum.INTERSECTION);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testObtainGroupHasTagWithIntersectionAndNoMatch() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(createGroupTagMapping("group1", "tag1"), createGroupTagMapping("group2", "tag2"));
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.INTERSECTION);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testObtainGroupHasTagWithUnion() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(createGroupTagMapping("group1", "tag1"), createGroupTagMapping("group2", "tag2"));
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.UNION);
        assertThat(result, containsInAnyOrder("group1", "group2"));
    }

    @Test
    public void testObtainGroupHasTagWithUnionComplementAndNoMatch() throws Throwable {
        List<GroupTagMapping> groupTagMappings = Arrays.asList(createGroupTagMapping("group1", "tag1"), createGroupTagMapping("group2", "tag2"));
        when(groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(tagSerialNos)).thenReturn(groupTagMappings);
        List<String> result = groupInfoService.obtainGroupHasTag("AppId", tagSerialNos, GroupTagOperatorTypeEnum.UNION_COMPLEMENT);
        assertEquals(Collections.emptyList(), result);
    }
}
