package com.sankuai.scrm.core.service.user.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import java.lang.reflect.Method;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import java.io.IOException;
import java.util.HashMap;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.xcontent.XContentType;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.lang.reflect.Field;
import org.mockito.MockedStatic;

@ExtendWith(MockitoExtension.class)
class ScrmUserSearchViewConsumerTest {

    @InjectMocks
    private ScrmUserSearchViewConsumer scrmUserSearchViewConsumer;

    @Mock
    private PorosRestHighLevelClient porosRestHighLevelClient;

    /**
     * 测试构建ES文档 - 正常情况所有字段都有值
     */
    @Test
    void testBuildESDocumentWithAllFields() throws Throwable {
        // arrange
        UserSearchView userSearchView = createFullyPopulatedUserSearchView();
        Method method = getPrivateMethod("buildESDocument", UserSearchView.class);
        // act
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(scrmUserSearchViewConsumer, userSearchView);
        // assert
        assertNotNull(result);
        assertEquals(66, result.size());
        verifyAllFieldsMappedCorrectly(result, userSearchView);
    }

    /**
     * 测试构建ES文档 - 输入为null的情况
     */
    @Test
    void testBuildESDocumentWithNullInput() throws Throwable {
        // arrange
        Method method = getPrivateMethod("buildESDocument", UserSearchView.class);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> method.invoke(scrmUserSearchViewConsumer, (Object) null));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * 测试构建ES文档 - 部分字段为null的情况
     */
    @Test
    void testBuildESDocumentWithPartialFields() throws Throwable {
        // arrange
        UserSearchView userSearchView = new UserSearchView();
        userSearchView.setUserId(12345L);
        userSearchView.setAppId("testApp");
        userSearchView.setQv1d(100L);
        Method method = getPrivateMethod("buildESDocument", UserSearchView.class);
        // act
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(scrmUserSearchViewConsumer, userSearchView);
        // assert
        assertNotNull(result);
        assertEquals(66, result.size());
        assertEquals(12345L, result.get("userId"));
        assertEquals("testApp", result.get("appId"));
        assertEquals(100L, result.get("qv1d"));
        assertNull(result.get("partitionDate"));
        assertNull(result.get("calDim"));
    }

    /**
     * 测试构建ES文档 - 所有字段都为null的情况
     */
    @Test
    void testBuildESDocumentWithAllNullFields() throws Throwable {
        // arrange
        UserSearchView userSearchView = new UserSearchView();
        Method method = getPrivateMethod("buildESDocument", UserSearchView.class);
        // act
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(scrmUserSearchViewConsumer, userSearchView);
        // assert
        assertNotNull(result);
        assertEquals(66, result.size());
        assertTrue(result.values().stream().allMatch(v -> v == null));
    }

    // 辅助方法：通过反射获取私有方法
    private Method getPrivateMethod(String methodName, Class<?>... parameterTypes) throws Exception {
        Method method = ScrmUserSearchViewConsumer.class.getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method;
    }

    // 辅助方法：创建完全填充的UserSearchView对象
    private UserSearchView createFullyPopulatedUserSearchView() {
        UserSearchView view = new UserSearchView();
        view.setPartitionDate("2023-01-01");
        view.setCalDim("day");
        view.setBizlnBuCode(1);
        view.setSourceInfo("web");
        view.setIsDirect(1);
        view.setItemType("product");
        view.setCChain(1);
        view.setUserId(12345L);
        // 1天
        view.setSearchKeywordCnt1d(10L);
        view.setClickQv1d(5L);
        view.setQv1d(20L);
        view.setClickItemCnt1d(3L);
        view.setViewItemCnt1d(15L);
        // 3天
        view.setSearchKeywordCnt3d(30L);
        view.setClickQv3d(15L);
        view.setQv3d(60L);
        view.setClickItemCnt3d(9L);
        view.setViewItemCnt3d(45L);
        // 5天
        view.setSearchKeywordCnt5d(50L);
        view.setClickQv5d(25L);
        view.setQv5d(100L);
        view.setClickItemCnt5d(15L);
        view.setViewItemCnt5d(75L);
        // 7天
        view.setSearchKeywordCnt7d(70L);
        view.setClickQv7d(35L);
        view.setQv7d(140L);
        view.setClickItemCnt7d(21L);
        view.setViewItemCnt7d(105L);
        // 15天
        view.setSearchKeywordCnt15d(150L);
        view.setClickQv15d(75L);
        view.setQv15d(300L);
        view.setClickItemCnt15d(45L);
        view.setViewItemCnt15d(225L);
        // 30天
        view.setSearchKeywordCnt30d(300L);
        view.setClickQv30d(150L);
        view.setQv30d(600L);
        view.setClickItemCnt30d(90L);
        view.setViewItemCnt30d(450L);
        // 60天
        view.setSearchKeywordCnt60d(600L);
        view.setClickQv60d(300L);
        view.setQv60d(1200L);
        view.setClickItemCnt60d(180L);
        view.setViewItemCnt60d(900L);
        // 90天
        view.setSearchKeywordCnt90d(900L);
        view.setClickQv90d(450L);
        view.setQv90d(1800L);
        view.setClickItemCnt90d(270L);
        view.setViewItemCnt90d(1350L);
        // 180天
        view.setSearchKeywordCnt180d(1800L);
        view.setClickQv180d(900L);
        view.setQv180d(3600L);
        view.setClickItemCnt180d(540L);
        view.setViewItemCnt180d(2700L);
        // 360天
        view.setSearchKeywordCnt360d(3600L);
        view.setClickQv360d(1800L);
        view.setQv360d(7200L);
        view.setClickItemCnt360d(1080L);
        view.setViewItemCnt360d(5400L);
        // 最后搜索信息
        view.setLastSearchDate("2023-01-01");
        view.setLastSearchToNowDays(0);
        view.setLastdaySearchKeywordCnt(10L);
        view.setLastdayClickQv(5L);
        view.setLastdayQv(20L);
        view.setLastdayClickItemCnt(3L);
        view.setLastdayViewItemCnt(15L);
        // 业务线信息
        view.setAppId("testApp");
        return view;
    }

    // 辅助方法：验证所有字段映射正确
    private void verifyAllFieldsMappedCorrectly(Map<String, Object> result, UserSearchView expected) {
        assertEquals(expected.getPartitionDate(), result.get("partitionDate"));
        assertEquals(expected.getCalDim(), result.get("calDim"));
        assertEquals(expected.getBizlnBuCode(), result.get("bizlnBuCode"));
        assertEquals(expected.getSourceInfo(), result.get("sourceInfo"));
        assertEquals(expected.getIsDirect(), result.get("isDirect"));
        assertEquals(expected.getItemType(), result.get("itemType"));
        assertEquals(expected.getCChain(), result.get("cChain"));
        assertEquals(expected.getUserId(), result.get("userId"));
        // spot check
        assertEquals(expected.getSearchKeywordCnt1d(), result.get("searchKeywordCnt1d"));
        assertEquals(expected.getClickQv1d(), result.get("clickQv1d"));
        assertEquals(expected.getQv1d(), result.get("qv1d"));
        assertEquals(expected.getClickItemCnt1d(), result.get("clickItemCnt1d"));
        assertEquals(expected.getViewItemCnt1d(), result.get("viewItemCnt1d"));
        assertEquals(expected.getSearchKeywordCnt360d(), result.get("searchKeywordCnt360d"));
        assertEquals(expected.getClickQv360d(), result.get("clickQv360d"));
        assertEquals(expected.getQv360d(), result.get("qv360d"));
        assertEquals(expected.getClickItemCnt360d(), result.get("clickItemCnt360d"));
        assertEquals(expected.getViewItemCnt360d(), result.get("viewItemCnt360d"));
        assertEquals(expected.getLastSearchDate(), result.get("lastSearchDate"));
        assertEquals(expected.getLastSearchToNowDays(), result.get("lastSearchToNowDays"));
        assertEquals(expected.getLastdaySearchKeywordCnt(), result.get("lastdaySearchKeywordCnt"));
        assertEquals(expected.getLastdayClickQv(), result.get("lastdayClickQv"));
        assertEquals(expected.getLastdayQv(), result.get("lastdayQv"));
        assertEquals(expected.getLastdayClickItemCnt(), result.get("lastdayClickItemCnt"));
        assertEquals(expected.getLastdayViewItemCnt(), result.get("lastdayViewItemCnt"));
        assertEquals(expected.getAppId(), result.get("appId"));
    }

    private void invokeInsertToES(UserSearchView userSearchView) throws Exception {
        Method method = ScrmUserSearchViewConsumer.class.getDeclaredMethod("insertToES", UserSearchView.class);
        method.setAccessible(true);
        method.invoke(scrmUserSearchViewConsumer, userSearchView);
    }

    @Test
    public void testInsertToES_NullInput() throws Throwable {
        // arrange - no setup needed for null case
        // act
        invokeInsertToES(null);
        // assert
        verifyNoInteractions(porosRestHighLevelClient);
    }

    @Test
    public void testInsertToES_SuccessfulInsertionDianping() throws Throwable {
        // arrange
        UserSearchView userSearchView = createTestUserSearchView();
        // dp platform
        userSearchView.setCChain(0);
        IndexResponse mockResponse = mock(IndexResponse.class);
        when(porosRestHighLevelClient.index(any(IndexRequest.class), any(RequestOptions.class))).thenReturn(mockResponse);
        // act
        invokeInsertToES(userSearchView);
        // assert
        verify(porosRestHighLevelClient).index(any(IndexRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test
    public void testInsertToES_SuccessfulInsertionMeituan() throws Throwable {
        // arrange
        UserSearchView userSearchView = createTestUserSearchView();
        // mt platform
        userSearchView.setCChain(1);
        IndexResponse mockResponse = mock(IndexResponse.class);
        when(porosRestHighLevelClient.index(any(IndexRequest.class), any(RequestOptions.class))).thenReturn(mockResponse);
        // act
        invokeInsertToES(userSearchView);
        // assert
        verify(porosRestHighLevelClient).index(any(IndexRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test
    public void testInsertToES_IOExceptionDuringInsert() throws Throwable {
        // arrange
        UserSearchView userSearchView = createTestUserSearchView();
        when(porosRestHighLevelClient.index(any(IndexRequest.class), any(RequestOptions.class))).thenThrow(new IOException("ES connection failed"));
        // act & assert
        org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> {
            invokeInsertToES(userSearchView);
        });
        verify(porosRestHighLevelClient).index(any(IndexRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test
    public void testInsertToES_MinimalFields() throws Throwable {
        // arrange
        UserSearchView userSearchView = new UserSearchView();
        // Set all required fields that are accessed in generateDocumentId() and buildESDocument()
        userSearchView.setUserId(123L);
        userSearchView.setAppId("testApp");
        userSearchView.setBizlnBuCode(1);
        // Must be set to avoid NPE in generateDocumentId()
        userSearchView.setCChain(1);
        // Required for ES document
        userSearchView.setPartitionDate("20230101");
        IndexResponse mockResponse = mock(IndexResponse.class);
        when(porosRestHighLevelClient.index(any(IndexRequest.class), any(RequestOptions.class))).thenReturn(mockResponse);
        // act
        invokeInsertToES(userSearchView);
        // assert
        verify(porosRestHighLevelClient).index(any(IndexRequest.class), eq(RequestOptions.DEFAULT));
    }

    private UserSearchView createTestUserSearchView() {
        UserSearchView view = new UserSearchView();
        // Required fields
        view.setPartitionDate("20230101");
        view.setCalDim("day");
        view.setBizlnBuCode(1);
        view.setSourceInfo("test");
        view.setIsDirect(1);
        view.setItemType("product");
        view.setCChain(1);
        view.setUserId(123L);
        view.setAppId("testApp");
        // Optional metrics fields
        view.setSearchKeywordCnt1d(10L);
        view.setClickQv1d(5L);
        view.setQv1d(20L);
        view.setSearchKeywordCnt3d(30L);
        view.setClickQv3d(15L);
        view.setQv3d(60L);
        view.setSearchKeywordCnt5d(50L);
        view.setClickQv5d(25L);
        view.setQv5d(100L);
        return view;
    }

    @Test
    public void testDestroyWhenConsumerNotNull() throws Throwable {
        // arrange
        IConsumerProcessor mockConsumerProcessor = mock(IConsumerProcessor.class);
        // Use reflection to set the static field
        Field consumerField = ScrmUserSearchViewConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        Object originalValue = consumerField.get(null);
        consumerField.set(null, mockConsumerProcessor);
        try {
            // act
            scrmUserSearchViewConsumer.destroy();
            // assert
            verify(mockConsumerProcessor, times(1)).close();
        } finally {
            // Reset the static field to its original value
            consumerField.set(null, originalValue);
        }
    }

    @Test
    public void testDestroyWhenConsumerNull() throws Throwable {
        // arrange
        // Use reflection to set the static field to null
        Field consumerField = ScrmUserSearchViewConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        Object originalValue = consumerField.get(null);
        consumerField.set(null, null);
        try {
            // act
            // Assert that no exception is thrown when consumer is null
            assertDoesNotThrow(() -> scrmUserSearchViewConsumer.destroy());
            // Create a spy to verify that no interactions happen when consumer is null
            IConsumerProcessor spyConsumer = spy(IConsumerProcessor.class);
            // We don't actually set the spy as the consumer, we just verify no interactions
            // Verify that no interactions would happen with any consumer
            verifyNoInteractions(spyConsumer);
        } finally {
            // Reset the static field to its original value
            consumerField.set(null, originalValue);
        }
    }

    @Test
    public void testDestroyWhenCloseThrowsException() throws Throwable {
        // arrange
        IConsumerProcessor mockConsumerProcessor = mock(IConsumerProcessor.class);
        Exception expectedException = new Exception("Test exception");
        doThrow(expectedException).when(mockConsumerProcessor).close();
        // Use reflection to set the static field
        Field consumerField = ScrmUserSearchViewConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        Object originalValue = consumerField.get(null);
        consumerField.set(null, mockConsumerProcessor);
        try {
            // act & assert
            assertThrows(Exception.class, () -> scrmUserSearchViewConsumer.destroy());
            verify(mockConsumerProcessor, times(1)).close();
        } finally {
            // Reset the static field to its original value
            consumerField.set(null, originalValue);
        }
    }
}
