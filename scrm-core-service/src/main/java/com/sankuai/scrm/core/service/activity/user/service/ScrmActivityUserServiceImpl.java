package com.sankuai.scrm.core.service.activity.user.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.activity.user.request.ScrmActivityUserInfoRequest;
import com.sankuai.dz.srcm.activity.user.request.ScrmActivityUserQueryRequest;
import com.sankuai.dz.srcm.activity.user.request.ScrmActivityUserVerifyRequest;
import com.sankuai.dz.srcm.activity.user.response.ScrmActivityUserInfoResponse;
import com.sankuai.dz.srcm.activity.user.response.UserInfoResponse;
import com.sankuai.dz.srcm.activity.user.service.ScrmActivityUserService;
import com.sankuai.scrm.core.service.activity.user.domain.ScrmActivityUserDomainService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

@Slf4j
@MdpPigeonServer
public class ScrmActivityUserServiceImpl implements ScrmActivityUserService {
    @Resource
    private ScrmActivityUserDomainService scrmActivityUserDomainService;
    @Resource
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;
    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;

    @Override
    public RemoteResponse<Boolean> verifyScrmActivityUser(ScrmActivityUserVerifyRequest request) {
        try {
            if (isInvalidRequest(request)) {
                log.error("Invalid request: {}", request);
                return RemoteResponse.fail("Invalid request");
            }
            String corpId = corpAppConfigRepository.getCorpIdByAppId(request.getAppId());
            if (StringUtils.isBlank(corpId)) {
                return RemoteResponse.fail("appId输入有误");
            }
            log.info("verifyScrmActivityUser: request={}", request);
            boolean verifyResult = scrmActivityUserDomainService.verifyScrmActivityUser(request.getAppId(), request.getUnionId(), corpId, request.getChannelCodeId());
            log.info("verifyScrmActivityUser: verifyResult={}", request);
            return RemoteResponse.success(verifyResult);
        } catch (Exception e) {
            log.error("verifyScrmActivityUser: 系统异常", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<UserInfoResponse> queryUserInfo(ScrmActivityUserQueryRequest request) {
        try {
            if (StringUtils.isBlank(request.getAppId()) || StringUtils.isBlank(request.getUnionId())) {
                log.error("Invalid request: appId={}, unionId={}", request.getAppId(), request.getUnionId());
                return RemoteResponse.fail("Invalid request");
            }
            log.info("queryUserInfo: appId={}, unionId={}", request.getAppId(), request.getUnionId());
            ExternalContactBaseInfo info = externalContactBaseInfoDomainService.queryBaseInfoByUnionId(request.getAppId(), request.getUnionId());
            log.info("queryUserInfo: userInfo={}, request={}", info, request);
            if (ObjectUtils.isEmpty(info)) {
                return RemoteResponse.success(new UserInfoResponse());
            }

            UserInfoResponse response = UserInfoResponse.builder()
                    .userName(info.getName())
                    .avatar(info.getAvatar())
                    .imageUrl(scrmActivityUserDomainService.getConfigForImg())
                    .build();
            return RemoteResponse.success(response);
        } catch (Exception e) {
            log.error("queryUserInfo: 系统异常", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<Boolean> saveScrmActivityUserInfo(ScrmActivityUserInfoRequest request) {
        try {
            log.info("saveScrmActivityUserInfo: request={}", request);
            String checkResult = checkSaveUserInfoParams(request);
            if (StringUtils.isNotBlank(checkResult)) {
                return RemoteResponse.fail(checkResult);
            }
            String corpId = corpAppConfigRepository.getCorpIdByAppId(request.getAppId());
            if (StringUtils.isBlank(corpId)) {
                return RemoteResponse.fail("appId输入有误");
            }

            String saveResult = scrmActivityUserDomainService.saveScrmActivityUserInfo(request, corpId);
            if (StringUtils.isNotBlank(saveResult)) {
                return RemoteResponse.fail(saveResult);
            }
            return RemoteResponse.success(true);
        } catch (Exception e) {
            log.error("saveScrmActivityUserInfo: 系统异常", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<ScrmActivityUserInfoResponse> queryScrmActivityUserInfo(ScrmActivityUserQueryRequest request) {
        try {
            log.info("queryScrmActivityUserInfo: request={}", request);
            if (ObjectUtils.isEmpty(request)) {
                return RemoteResponse.fail("请求参数为空");
            }
            if (StringUtils.isBlank(request.getUnionId())) {
                return RemoteResponse.fail("unionId为空");
            }
            if (StringUtils.isBlank(request.getAppId())) {
                return RemoteResponse.fail("appId为空");
            }
            String corpId = corpAppConfigRepository.getCorpIdByAppId(request.getAppId());
            if (StringUtils.isBlank(corpId)) {
                return RemoteResponse.fail("appId输入有误");
            }

            ScrmActivityUserInfoResponse response = scrmActivityUserDomainService.queryScrmActivityUserInfo(request);
            log.info("queryScrmActivityUserInfo: response={}", response);
            if (ObjectUtils.isEmpty(response)) {
                return RemoteResponse.success(new ScrmActivityUserInfoResponse());
            }
            return RemoteResponse.success(response);
        } catch (Exception e) {
            log.info("queryScrmActivityUserInfo: 系统异常", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    private String checkSaveUserInfoParams(ScrmActivityUserInfoRequest request) {
        if (ObjectUtils.isEmpty(request)) {
            return "请求参数为空";
        }
        if (StringUtils.isBlank(request.getAppId())) {
            return "appId不能为空";
        }
        if (request.getChannelCodeId() == null || request.getChannelCodeId() < 1) {
            return "channelCodeId有误";
        }

        if (StringUtils.isBlank(request.getUnionId())) {
            return "unionId不能为空";
        }
        if (StringUtils.isBlank(request.getProvinceName())) {
            return "省份不能为空";
        }
        if (StringUtils.isBlank(request.getCityName())) {
            return "城市不能为空";
        }
        if (StringUtils.isBlank(request.getDetailAddress())) {
            return "详细地址不能为空";
        }
        if (StringUtils.isBlank(request.getReceiver())) {
            return "收件人不能为空";
        }
        if (StringUtils.isBlank(request.getMobileNo())) {
            return "手机号不能为空";
        }


        return null;
    }


    private boolean isInvalidRequest(ScrmActivityUserVerifyRequest request) {
        return ObjectUtils.isEmpty(request) || StringUtils.isAnyBlank(request.getAppId(), request.getUnionId()) || request.getChannelCodeId() == null || request.getChannelCodeId() < 1;
    }
}
