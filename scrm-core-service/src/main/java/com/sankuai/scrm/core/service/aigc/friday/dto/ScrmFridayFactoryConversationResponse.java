package com.sankuai.scrm.core.service.aigc.friday.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2024/11/06 20:03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScrmFridayFactoryConversationResponse {

    private Integer code;
    private String message;
    private ConversationResult data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConversationResult {
        private String conversationId;
        private String messageId;
        private String requestId;
        private List<TextContent> contents;
        private Map<String, Object> debugInfo;
        private Map<String, Object> extendInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TextContent {
        private String type;
        private String text;
    }

}
