package com.sankuai.scrm.core.service.user.domain.intention;

import com.sankuai.dz.srcm.user.enums.PrivateSphereUserTagEnum;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberInviteCountDTO;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthYimeiLiveUserTagMapper;
import com.sankuai.scrm.core.service.user.domain.PrivateUserDataHandleDomainService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateUserDataHandleDomainServiceTest {

    @Mock
    private ScrmPersonalWxGroupManageDomainService wxGroupManageDomainService;
    @Mock
    private ScrmUserGrowthYimeiLiveUserTagMapper userGrowthYimeiLiveUserTagMapper;

    @InjectMocks
    private PrivateUserDataHandleDomainService privateUserDataHandleDomainService;

    private List<ScrmUserGrowthYimeiLiveUserTag> userTagList;
    private String wxId = "wxId";
    private Long userId = 1L;
    private String unionId = "unionId";
    private Long consultantTaskId = 1L;
    private String projectId = "projectId";

    @Before
    public void setUp() {
        userTagList = new ArrayList<>();
    }

    /**
     * 测试用户标签列表为空
     */
    @Test
    public void testComputeUserIntentionTagEmptyUserTagList() {
        // arrange
        // act
        privateUserDataHandleDomainService.computeUserIntentionTag(userTagList, wxId, userId, unionId, consultantTaskId, projectId);
        // assert
        verify(wxGroupManageDomainService, never()).queryGroupMemberInviteWxCountByUnionId(any(), any(), any(), anyLong());
    }

    /**
     * 测试用户有邀请用户，意向标签应为中等
     */
    @Test
    public void testComputeUserIntentionTagHasInviteUser() {
        // arrange
        GroupMemberInviteCountDTO inviteCountDTO = new GroupMemberInviteCountDTO();
        inviteCountDTO.setInviteCount(1);
        List<GroupMemberInviteCountDTO> inviteCountDTOList = new ArrayList<>();
        inviteCountDTOList.add(inviteCountDTO);
        when(wxGroupManageDomainService.queryGroupMemberInviteWxCountByUnionId(any(), any(), any(), anyLong())).thenReturn(inviteCountDTOList);

        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setTagId(PrivateSphereUserTagEnum.HAS_IN_GROUP.getTagId());
        userTagList.add(tag);

        // act
        privateUserDataHandleDomainService.computeUserIntentionTag(userTagList, wxId, userId, unionId, consultantTaskId, projectId);

        // assert
        verify(userGrowthYimeiLiveUserTagMapper, times(1)).insert(any(ScrmUserGrowthYimeiLiveUserTag.class));
    }

    /**
     * 测试用户无邀请用户，意向标签应为低
     */
    @Test
    public void testComputeUserIntentionTagNoInviteUser() {
        // arrange
        when(wxGroupManageDomainService.queryGroupMemberInviteWxCountByUnionId(any(), any(), any(), anyLong())).thenReturn(new ArrayList<>());

        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setTagId(PrivateSphereUserTagEnum.HAS_IN_GROUP.getTagId());
        userTagList.add(tag);

        // act
        privateUserDataHandleDomainService.computeUserIntentionTag(userTagList, wxId, userId, unionId, consultantTaskId, projectId);

        // assert
        verify(userGrowthYimeiLiveUserTagMapper, times(1)).insert(any(ScrmUserGrowthYimeiLiveUserTag.class));
    }

    /**
     * 测试用户标签列表中包含高意向标签，意向标签不变
     */
    @Test
    public void testComputeUserIntentionTagWithHighIntentionTag() {
        // arrange
        ScrmUserGrowthYimeiLiveUserTag highIntentionTag = new ScrmUserGrowthYimeiLiveUserTag();
        highIntentionTag.setTagId(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagId());
        userTagList.add(highIntentionTag);

        // act
        privateUserDataHandleDomainService.computeUserIntentionTag(userTagList, wxId, userId, unionId, consultantTaskId, projectId);

        // assert
        verify(userGrowthYimeiLiveUserTagMapper, never()).insert(any(ScrmUserGrowthYimeiLiveUserTag.class));
    }
}