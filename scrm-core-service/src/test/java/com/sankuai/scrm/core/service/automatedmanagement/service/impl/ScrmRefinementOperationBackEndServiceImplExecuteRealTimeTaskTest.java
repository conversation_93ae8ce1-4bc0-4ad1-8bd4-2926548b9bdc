package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ExecuteResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.AISceneExecuteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;

@ExtendWith(MockitoExtension.class)
class ScrmRefinementOperationBackEndServiceImplExecuteRealTimeTaskTest {

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private Transaction transaction;

    @InjectMocks
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    private static final Long VALID_SCENE_ID = 1L;

    private static final String VALID_WX_UNION_ID = "valid_union_id";

    private static final String VALID_POI_ID = "valid_poi_id";

    private static final Long VALID_PROCESS_ID = 100L;

    private static final String VALID_VERSION = "1.0";

    @Mock
    private AISceneExecuteDomainService aiSceneExecuteDomainService;

    private final Long validSceneId = 123L;

    private final String validWxUnionId = "wx123";

    private final IntelligentFollowResultDTO validAiSceneContent = new IntelligentFollowResultDTO();

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;


    /**
     * Test successful execution with valid inputs
     */
    @Test
    void testExecuteRealTimeTaskSuccess() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(true);
            stepResult.setCode(0);
            stepResult.setMsg("Success");
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertTrue(result.isSuccess());
            assertTrue(result.isNeedExecute());
            assertEquals(0, result.getResultCode());
            assertEquals(VALID_PROCESS_ID, result.getProcessOrchestrationId());
            assertEquals(VALID_VERSION, result.getProcessOrchestrationVersion());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test empty wxUnionId returns UNION_ID_IS_NULL error
     */
    @Test
    void testExecuteRealTimeTaskEmptyUnionId() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(false);
            stepResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getCode());
            stepResult.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getDesc());
            when(executeWriteDomainService.executeRealTimeTask(any(), eq(""), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, "", VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getCode(), result.getResultCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getDesc(), result.getMessage());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test null wxUnionId returns UNION_ID_IS_NULL error
     */
    @Test
    void testExecuteRealTimeTaskNullUnionId() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(false);
            stepResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getCode());
            stepResult.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getDesc());
            when(executeWriteDomainService.executeRealTimeTask(any(), eq(null), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, null, VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getCode(), result.getResultCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getDesc(), result.getMessage());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test when no matching scene/process found returns NOT_IN_TIME error
     */
    @Test
    void testExecuteRealTimeTaskNoMatchingScene() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(false);
            stepResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode());
            stepResult.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc());
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode(), result.getResultCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc(), result.getMessage());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test when process not in valid status returns NOT_IN_TIME error
     */
    @Test
    void testExecuteRealTimeTaskInvalidProcessStatus() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(false);
            stepResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode());
            stepResult.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc());
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode(), result.getResultCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc(), result.getMessage());
            assertEquals(VALID_PROCESS_ID, result.getProcessOrchestrationId());
            assertEquals(VALID_VERSION, result.getProcessOrchestrationVersion());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test when not in effective time returns NOT_IN_EFFECTIVE_TIME error
     */
    @Test
    void testExecuteRealTimeTaskNotInEffectiveTime() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(false);
            stepResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_EFFECTIVE_TIME.getCode());
            stepResult.setMsg(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_EFFECTIVE_TIME.getDesc());
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_EFFECTIVE_TIME.getCode(), result.getResultCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_EFFECTIVE_TIME.getDesc(), result.getMessage());
            assertEquals(VALID_PROCESS_ID, result.getProcessOrchestrationId());
            assertEquals(VALID_VERSION, result.getProcessOrchestrationVersion());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test exception handling returns FAIL error
     */
    @Test
    void testExecuteRealTimeTaskException() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            RuntimeException exception = new RuntimeException("Test exception");
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenThrow(exception);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertFalse(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.FAIL.getCode(), result.getResultCode());
            assertTrue(result.getMessage().contains("sceneId: " + VALID_SCENE_ID));
            assertEquals(VALID_WX_UNION_ID, result.getUserUnionId());
            verify(transaction).setStatus(exception);
            verify(transaction).complete();
        }
    }

    /**
     * Test when step result code is positive sets needExecute to false
     */
    @Test
    void testExecuteRealTimeTaskPositiveCodeSetsNeedExecuteFalse() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(true);
            // Positive code
            stepResult.setCode(1);
            stepResult.setMsg("Success");
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertTrue(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(1, result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test when step result code is negative sets needExecute to true
     */
    @Test
    void testExecuteRealTimeTaskNegativeCodeSetsNeedExecuteTrue() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(true);
            // Negative code
            stepResult.setCode(-1);
            stepResult.setMsg("Success");
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertTrue(result.isSuccess());
            assertTrue(result.isNeedExecute());
            assertEquals(-1, result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    /**
     * Test when step result code is null sets needExecute to false
     */
    @Test
    void testExecuteRealTimeTaskNullCodeSetsNeedExecuteFalse() throws Throwable {
        // arrange
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
            stepResult.setSuccess(true);
            // Null code
            stepResult.setCode(null);
            stepResult.setMsg("Success");
            stepResult.setProcessOrchestrationId(VALID_PROCESS_ID);
            stepResult.setProcessOrchestrationVersion(VALID_VERSION);
            when(executeWriteDomainService.executeRealTimeTask(any(), any(), any())).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeRealTimeTask(VALID_SCENE_ID, VALID_WX_UNION_ID, VALID_POI_ID);
            // assert
            assertTrue(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertNull(result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    @Test
    public void testExecuteAIRealTimeTaskSuccessWithNeedExecuteTrue() throws Throwable {
        // arrange
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        // code <= 0
        stepResult.setCode(-1);
        stepResult.setMsg("success");
        stepResult.setProcessOrchestrationId(456L);
        stepResult.setProcessOrchestrationVersion("v1");
        stepResult.setProcessOrchestrationNodeId(789L);
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            when(aiSceneExecuteDomainService.runRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent)).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeAIRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent);
            // assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertTrue(result.isNeedExecute());
            assertEquals(-1, result.getResultCode());
            assertEquals("success", result.getMessage());
            assertEquals(456L, result.getProcessOrchestrationId());
            assertEquals("v1", result.getProcessOrchestrationVersion());
            assertEquals(789L, result.getProcessOrchestrationNodeId());
            assertEquals(validWxUnionId, result.getUserUnionId());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    @Test
    public void testExecuteAIRealTimeTaskSuccessWithNeedExecuteFalse() throws Throwable {
        // arrange
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        // code > 0
        stepResult.setCode(1);
        stepResult.setMsg("success");
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            when(aiSceneExecuteDomainService.runRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent)).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeAIRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent);
            // assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertEquals(1, result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    @Test
    public void testExecuteAIRealTimeTaskSuccessWithNullCode() throws Throwable {
        // arrange
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        // null code
        stepResult.setCode(null);
        stepResult.setMsg("success");
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            when(aiSceneExecuteDomainService.runRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent)).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeAIRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent);
            // assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertFalse(result.isNeedExecute());
            assertNull(result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    @Test
    public void testExecuteAIRealTimeTaskWithException() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("test exception");
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            when(aiSceneExecuteDomainService.runRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent)).thenThrow(expectedException);
            // We need to use ReflectionTestUtils to set the log field since it's a static final field
            // For this test, we'll just verify the behavior without checking the log
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeAIRealTimeTask(validSceneId, validWxUnionId, validAiSceneContent);
            // assert
            assertNull(result);
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
            // We can't easily verify the log.error call since it's using a static logger
            // In a real test, we might use PowerMock or similar to verify static method calls
        }
    }

    @Test
    public void testExecuteAIRealTimeTaskWithZeroSceneId() throws Throwable {
        // arrange
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        stepResult.setCode(0);
        stepResult.setMsg("success");
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            catMock.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            when(aiSceneExecuteDomainService.runRealTimeTask(0L, validWxUnionId, validAiSceneContent)).thenReturn(stepResult);
            // act
            ExecuteResultDTO result = scrmRefinementOperationBackEndService.executeAIRealTimeTask(0L, validWxUnionId, validAiSceneContent);
            // assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            // code = 0
            assertTrue(result.isNeedExecute());
            assertEquals(0, result.getResultCode());
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        }
    }

    @BeforeEach
    public void setUp() {
        // Mock Cat.newTransaction to return our mocked transaction
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, we'll handle it differently in each test
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackSuccess() throws Throwable {
        // arrange
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
            verify(transaction).setStatus(Transaction.SUCCESS);
            verify(transaction).complete();
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackWithException() throws Throwable {
        // arrange
        Exception expectedException = new RuntimeException("Test exception");
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doThrow(expectedException).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
            verify(transaction).setStatus(expectedException);
            verify(transaction).complete();
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doThrow(expectedException).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackTransactionCompletionOnException() throws Throwable {
        // arrange
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doThrow(new RuntimeException()).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(transaction).complete();
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doThrow(new RuntimeException()).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackTransactionCompletionOnSuccess() throws Throwable {
        // arrange
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(transaction).complete();
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackTransactionStatusSuccess() throws Throwable {
        // arrange
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(transaction).setStatus(Transaction.SUCCESS);
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doNothing().when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }

    @Test
    public void testAutoDeleteTimeOutTempPackTransactionStatusOnError() throws Throwable {
        // arrange
        Exception expectedException = new RuntimeException("Test exception");
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
            doThrow(expectedException).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            // act
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            // assert
            verify(transaction).setStatus(expectedException);
        } catch (UnsupportedOperationException e) {
            // If mockStatic is not supported, verify only the service call
            doThrow(expectedException).when(crowdPackWriteDomainService).deleteTimeoutTempPack();
            scrmRefinementOperationBackEndService.autoDeleteTimeOutTempPack();
            verify(crowdPackWriteDomainService).deleteTimeoutTempPack();
        }
    }
}
