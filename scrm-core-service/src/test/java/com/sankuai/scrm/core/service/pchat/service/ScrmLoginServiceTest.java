package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginAnchorInfoDTO;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmLoginServiceTest {

    private ScrmLoginService scrmLoginService = new ScrmLoginService();

    @Mock
    private AnchorAuthUtils anchorAuthUtils;

    /**
     * Tests the getCreator method when AnchorAuthUtils.getMainAnchorId() returns a non-null value.
     */
    @Test
    public void testGetCreatorWhenMainAnchorIdIsNotNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            // arrange
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            String result = scrmLoginService.getCreator();
            // assert
            assertEquals("1", result);
        }
    }

    /**
     * Tests the getCreator method when AnchorAuthUtils.getMainAnchorId() returns a null value.
     */
    @Test
    public void testGetCreatorWhenMainAnchorIdIsNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            // arrange
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(null);
            // act
            String result = scrmLoginService.getCreator();
            // assert
            assertEquals("", result);
        }
    }

    /**
     * 测试 getLoginMisId 方法，当 AnchorAuthUtils.loadAnchorInfo() 返回 null 时
     */
    @Test
    public void testGetLoginMisIdWhenLoadAnchorInfoReturnNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            // arrange
            ScrmLoginService scrmLoginService = new ScrmLoginService();
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(null);
            // act
            String result = scrmLoginService.getLoginMisId();
            // assert
            assertEquals("", result);
        }
    }

    /**
     * 测试 getLoginMisId 方法，当 AnchorAuthUtils.loadAnchorInfo() 返回非 null 的 LoginAnchorInfoDTO 对象时
     */
    @Test
    public void testGetLoginMisIdWhenLoadAnchorInfoReturnNonNull() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mockedStatic = mockStatic(AnchorAuthUtils.class)) {
            // arrange
            ScrmLoginService scrmLoginService = new ScrmLoginService();
            LoginAnchorInfoDTO loginAnchorInfoDTO = new LoginAnchorInfoDTO();
            loginAnchorInfoDTO.setSourceBizId("test");
            mockedStatic.when(AnchorAuthUtils::loadAnchorInfo).thenReturn(loginAnchorInfoDTO);
            // act
            String result = scrmLoginService.getLoginMisId();
            // assert
            assertEquals("test", result);
        }
    }
}
