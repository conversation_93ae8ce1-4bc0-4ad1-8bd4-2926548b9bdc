package com.sankuai.scrm.core.service.abtest.service;

import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.enums.SplitType;
import lombok.Data;
import lombok.Builder;

/**
 * AB测试处理请求参数包装类
 * @param <P> 策略执行参数类型
 */
@Data
@Builder
public class ABTestHandleRequest<P> {
    
    /**
     * 用户ID，用于用户分流和随机数生成
     */
    private Long userId;
    
    /**
     * 分流类型：RANGE(区间分流)、PERCENTAGE(百分比分流)、DOT_CARE_USER_ID(不关心用户ID分流)
     */
    private SplitType splitType;
    
    /**
     * 应用ID，用于获取对应的AB测试配置
     */
    private String appId;
    
    /**
     * 策略执行参数，传递给具体策略实现
     */
    private P param;
    
    /**
     * AB测试上下文，包含执行过程中的状态和结果信息
     */
    private ABTestContext context;
    
    /**
     * 策略名称，如果指定则直接执行该策略，否则根据分流规则选择策略
     */
    private String strategyName;

    /**
     * AB测试状态
     * 当前AB测试的执行状态，用于标识测试的进行阶段
     * strategyName不为空时，需要同步传abTestStatus
     */
    private Integer abTestStatus;
} 