package com.sankuai.scrm.core.service.flow.config;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class BaseFlowEntryConfigTest {

    private BaseFlowEntryConfig baseFlowEntryConfig;

    @Before
    public void setUp() {
        baseFlowEntryConfig = new BaseFlowEntryConfig();
    }

    /**
     * 测试设置version为null的情况
     */
    @Test
    public void testSetVersionNull() {
        // arrange
        Integer version = null;

        // act
        baseFlowEntryConfig.setVersion(version);

        // assert
        assertNull("版本号应为null", baseFlowEntryConfig.getVersion());
    }

    /**
     * 测试设置version为正数的情况
     */
    @Test
    public void testSetVersionPositive() {
        // arrange
        Integer version = 1;

        // act
        baseFlowEntryConfig.setVersion(version);

        // assert
        assertNotNull("版本号不应为null", baseFlowEntryConfig.getVersion());
        assertEquals("版本号应为1", Integer.valueOf(1), baseFlowEntryConfig.getVersion());
    }

    /**
     * 测试设置version为负数的情况
     */
    @Test
    public void testSetVersionNegative() {
        // arrange
        Integer version = -1;

        // act
        baseFlowEntryConfig.setVersion(version);

        // assert
        assertEquals("版本号应为-1", Integer.valueOf(-1), baseFlowEntryConfig.getVersion());
    }

    /**
     * 测试设置version为0的情况
     */
    @Test
    public void testSetVersionZero() {
        // arrange
        Integer version = 0;

        // act
        baseFlowEntryConfig.setVersion(version);

        // assert
        assertEquals("版本号应为0", Integer.valueOf(0), baseFlowEntryConfig.getVersion());
    }
}
