package com.sankuai.dz.srcm.reply.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ScrmQuestionReplyDTO implements Serializable {

    private long questionId;

    private String externalUserId;

    private String externalUserName;

    private String externalUserAvatar;

    private String realHandlerUserId;

    private String realHandlerAvatar;

    private String realHandlerName;

    private String groupId;

    private String groupName;

    private int scene;

    private int status;

    private int isOvertime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date askTime;

    private List<MsgContentDTO> askList;

    private MsgContentDTO replyContent;

}
