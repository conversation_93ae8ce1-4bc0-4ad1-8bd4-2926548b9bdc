package com.sankuai.dz.srcm.pchat.dto.im;

import lombok.Data;

import java.util.Date;

@Data
public class SessionRecordInfoDTO {

    /**
     * 主键
     */
    private Long id;

    private String robotWxSerialNo;

    private String friendWxSerialNo;

    /**
     * 群编号
     */
    private String chatRoomSerialNo;
    /**
     * 字段: receive_wx_serial_no
     * 说明: 接收微信号 msg_type=1为群编号 2时个人微信号 3时未群编号 4为机器人微信号
     */
    private String receiveWxSerialNo;

    /**
     * 发送者在用户表中主键
     */
    private Long senderId;
    /**
     * 发送者昵称
     */
    private String senderNickName;
    /**
     * 发送者微信序列号
     */
    private String senderWxSerialNo;
    /**
     * 发送者头像
     */
    private String senderAvatar;
    /**
     * 发送者微信id
     */
    private String senderWxId;
    /**
     * 消息内容
     */
    private String content;
    /**
     *   字段: extra_data
     *   说明: 用于存储其他格外数据
     */
    private String extraData;

    /**
     * 消息类型：1000 系统消息 2001 文字 2002 图片 2003 语音 2004 视频 2005 链接 2006 名片 2007 表情图片 2010 文件 2013 小程序 2017 视频号消息 2019 视频号直播间 2020 视频号名片 2024 聊天记录消息 2025 笔记类型
     */
    private String sendMsgType;

    /**
     *  '1 机器人群发消息 2 机器人私发消息 3 群接收消息 4 机器人接收私发消息',
     */
    private String msgType;

    /**
     * 消息id
     */
    private String vcMsgId;

    /**
     * 发送时间 时间戳
     */
    private String dtmsgtime;

    private Date addTime;

}
