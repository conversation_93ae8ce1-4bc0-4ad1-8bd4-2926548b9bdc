package com.sankuai.scrm.core.service.friend.dynamiccode.domain;

import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.dianping.squirrel.client.StoreKey;
import com.sankuai.scrm.core.service.friend.dynamiccode.mq.dto.CheckAddFriendMessage;
import com.sankuai.scrm.core.service.infrastructure.acl.StoreCacheAcl;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;


@Slf4j
@Service
public class FriendScanCodeDomainService {

    @Resource
    private EduDaxiangSendService eduDaxiangSendService;

    @Resource
    private StoreCacheAcl storeCacheAcl;


    private static final String OLD_USER_CACHE_CATEGORY = "scan_added_old_user";
    private static final String NEW_USER_CACHE_CATEGORY = "scan_added_new_user";


    /**
     * 发送未添加好友提醒
     */
    public void sendNotAddFriendReminder(CheckAddFriendMessage message) {
        try {

            String reminderMsg = String.format("微信新用户\"%s\"扫描渠道%s\"%s\"，未添加好友",
                    message.getUserName(), message.getChannelId(), message.getFriendCodeName());
            // 发送大象消息
            eduDaxiangSendService.sendTextMsg(reminderMsg, Collections.singletonList(message.getCreator()));
        } catch (Exception e) {
            log.error("发送未添加好友提醒异常", e);
        }
    }

    /**
     * 发送添加好友提醒
     */
    public void sendAddFriendReminder(CheckAddFriendMessage message) {
        try {
            String reminderMsg = String.format("微信新用户\"%s\"扫描渠道%s\"%s\"，好友添加成功",
                    message.getUserName(), message.getChannelId(), message.getFriendCodeName());
            // 发送大象消息
            eduDaxiangSendService.sendTextMsg(reminderMsg, Collections.singletonList(message.getCreator()));
        } catch (Exception e) {
            log.error("发送添加好友提醒异常", e);
        }
    }

    /**
     * 检查老用户缓存，避免重复处理
     */
    public boolean isOlderUserAddedCache(String flowId, String unionId) {
        if (StringUtils.isEmpty(flowId) || StringUtils.isEmpty(unionId) ) {
            return false;
        }
        try {
            Boolean cacheResult = storeCacheAcl.getFromCache(OLD_USER_CACHE_CATEGORY, flowId, unionId);
            if (cacheResult != null) {
                return cacheResult;
            }
            // 设置缓存，防止重复处理
            StoreKey storeKey = new StoreKey(OLD_USER_CACHE_CATEGORY, flowId, unionId);
            storeCacheAcl.setCache(storeKey, true);
        } catch (Exception e) {
            log.error("缓存操作异常: flowId={}, unionId={}", flowId, unionId, e);
            return false;
        }
        return false;
    }

    /**
     * 检查新用户是否已经处理过
     */
    public boolean isNewUserAddedCache(String flowId, String unionId) {
        if (StringUtils.isEmpty(flowId) || StringUtils.isEmpty(unionId) ) {
            return false;
        }

        try {
            Boolean cacheResult = storeCacheAcl.getFromCache(NEW_USER_CACHE_CATEGORY, flowId, unionId);
            if (cacheResult != null) {
                return cacheResult;
            }
            StoreKey storeKey = new StoreKey(NEW_USER_CACHE_CATEGORY, flowId, unionId);
            storeCacheAcl.setCache(storeKey, true);
        } catch (Exception e) {
            log.error("缓存操作异常: flowId={}, unionId={}", flowId, unionId, e);
            return false;
        }

        return false;
    }


}

