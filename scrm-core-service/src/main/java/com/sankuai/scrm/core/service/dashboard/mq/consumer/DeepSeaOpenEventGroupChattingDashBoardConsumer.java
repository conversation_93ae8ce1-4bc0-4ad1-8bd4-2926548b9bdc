package com.sankuai.scrm.core.service.dashboard.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Properties;

@Component
@Slf4j
public class DeepSeaOpenEventGroupChattingDashBoardConsumer implements InitializingBean, DisposableBean {

    @Resource
    private DashBoardDomainService dashBoardDomainService;

    private static IConsumerProcessor consumer;

    public ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        try {
            String body = message.getBody();
            dashBoardDomainService.handleGroupChattingMsg(body);
        } catch (Exception e) {
            log.error("DeepSeaOpenEventGroupChattingDashBoardConsumer has error, msg is {}", message, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "pingtai");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "deepsea.open.event.group.chatting.scrm.dashboard.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "deepsea.open.event.group.chatting");

        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }
}
