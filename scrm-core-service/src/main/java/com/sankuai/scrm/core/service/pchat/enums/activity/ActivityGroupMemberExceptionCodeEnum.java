package com.sankuai.scrm.core.service.pchat.enums.activity;

import lombok.Getter;

@Getter
public enum ActivityGroupMemberExceptionCodeEnum {
    NORMAL(0, "正常"),
    NOT_IN_GROUP(1, "您还未加入活动群，请联系给您分享活动的朋友邀您入群～"),
    CONSULTANT(2, "您的身份为咨询师，不能参与裂变活动"),
    ACTIVITY_FINISHED(3, "裂变活动已结束"),
    EXIT_GROUP(4, "您已退出活动群，无法查看活动页面，退群不影响奖品发放"),
    ;

    private final int code;

    private final String desc;

    ActivityGroupMemberExceptionCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityGroupMemberExceptionCodeEnum fromCode(int code) {
        for (ActivityGroupMemberExceptionCodeEnum enumValue : ActivityGroupMemberExceptionCodeEnum.values()) {
            if (enumValue.code == code) {
                return enumValue;
            }
        }
        return null;
    }

}
