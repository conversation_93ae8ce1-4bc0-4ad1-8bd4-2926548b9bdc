package com.sankuai.scrm.core.service.pchat.adapter.service.ent;

import com.sankuai.dz.srcm.pchat.request.scrm.GroupEditRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.adapter.bo.GroupNoticeEditionBO;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.adapter.service.GroupEditionProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.pchat.group.GroupCommonService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
@PrivateLiveProcessor(wechatType = WeChatType.ENTERPRISE_WECHAT)
public class EntGroupEditionProcessor implements GroupEditionProcessor {

    @Resource
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Autowired
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Autowired
    private DsAssistantAcl dsAssistantAcl;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private GroupCommonService groupCommonService;

    @Resource
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Override
    public void editGroup(GroupEditRequest request) {
        groupCommonService.supplementConsultantData(request.getConsultantList());
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = groupManageDomainService.queryGroupById(request.getGroupId());
        if (groupInfoEntity == null) {
            throw new RuntimeException("群不存在");
        }
        handleGroupName(request, groupInfoEntity);
        handleGroupNotice(request, groupInfoEntity);
        handleGroupWelcomeMsg(request, groupInfoEntity);
        updateGroupInfo(request, groupInfoEntity);
    }

    @Override
    public void setGroupName(ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        boolean success = dsCorpGroupAclService.updateGroupName(groupInfoEntity.getGroupId(), groupInfoEntity.getGroupName(), getMobile(groupInfoEntity.getAppId(), groupInfoEntity.getOwnerWxId()));
        if (!success) {
            throw new PChatBusinessException("微信群名称修改失败");
        }
    }

    @Override
    public void handleGroupNotice(GroupNoticeEditionBO noticeEditionBO) {
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = noticeEditionBO.getGroupInfoEntity();
        dsCorpGroupAclService.setGroupNotifyMsg(groupInfoEntity.getGroupId(), noticeEditionBO.getContent());
    }

    private String getMobile(String appId, String userId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(userId)) {
            return null;
        }
        CorpAppConfig config = appConfigRepository.getConfigByAppId(appId);
        if (config == null || StringUtils.isEmpty(config.getCorpId())) {
            return null;
        }
        return dsAssistantAcl.getAssistantMobileByUserId(userId, config.getCorpId(), config.getOrgId());
    }

    private void handleGroupName(GroupEditRequest request, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        if (groupInfoEntity.getGroupName().equals(request.getGroupName())) {
            return;
        }
        boolean existDuplicateGroupName = personalWxCommonService.existsDuplicateGroupName(groupInfoEntity.getAppId(), request.getGroupName());
        if (existDuplicateGroupName) {
            throw new RuntimeException("存在重名微信群名");
        }
        groupInfoEntity.setGroupName(request.getGroupName());
        setGroupName(groupInfoEntity);
    }

    private void handleGroupNotice(GroupEditRequest request, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        PersonalGroupStatusEnum personalGroupStatusEnum = PersonalGroupStatusEnum.fromCode(groupInfoEntity.getStatus());
        if (PersonalGroupStatusEnum.SUCCESS != personalGroupStatusEnum) {
            log.info("未生效的群不发送公告");
            return;
        }
        ScrmPersonalWxGroupMsg oldGroupMsg = null;
        if (groupInfoEntity.getGroupNotice() != null) {
            oldGroupMsg = groupManageDomainService.queryGroupMsgById(groupInfoEntity.getGroupNotice());
        }
        String noticeContent = null;
        if (request.getGroupNotice() != null) {
            noticeContent = request.getGroupNotice().getContent();
        }
        if (StringUtils.isEmpty(noticeContent) && oldGroupMsg == null) {
            return;
        } else if (StringUtils.isEmpty(noticeContent) && oldGroupMsg != null) {
            dsCorpGroupAclService.setGroupNotifyMsg(groupInfoEntity.getGroupId(), "");
            groupInfoEntity.setGroupNotice(0L);
        } else if (StringUtils.isNotEmpty(noticeContent) && oldGroupMsg == null) {
            dsCorpGroupAclService.setGroupNotifyMsg(groupInfoEntity.getGroupId(), noticeContent);
            ScrmPersonalWxGroupMsg record = ScrmPersonalWxGroupMsg.builder()
                    .creator(personalWxCommonService.getCreator())
                    .content(noticeContent)
                    .appId(groupInfoEntity.getAppId())
                    .build();
            groupManageDomainService.saveGroupMsg(record);
        } else if (StringUtils.isNotEmpty(noticeContent) && oldGroupMsg != null) {
            if (!noticeContent.equals(oldGroupMsg.getContent())) {
                dsCorpGroupAclService.setGroupNotifyMsg(groupInfoEntity.getGroupId(), noticeContent);
                oldGroupMsg.setContent(noticeContent);
                groupManageDomainService.updateGroupMsgById(oldGroupMsg);
            }
        }
    }

    private void handleGroupWelcomeMsg(GroupEditRequest request, ScrmPersonalWxGroupInfoEntity entity) {
        Long oldWelcomeMsgId = entity.getWelcomeMsgId();
        Long newWelcomeMsgId = request.getGroupWelcomeMsg().getWelcomeMsgId();
        if (newWelcomeMsgId == null) {
            if (oldWelcomeMsgId != null) {
                entity.setWelcomeMsgId(0L);
//                dsCorpGroupAclService.setGroupWelcomeMsg(-1, entity.getGroupId(), getMobile(entity.getAppId(), entity.getOwnerWxId()));
            }
        } else {
            if (oldWelcomeMsgId == null) {
                entity.setWelcomeMsgId(newWelcomeMsgId);
//                dsCorpGroupAclService.setGroupWelcomeMsg(newWelcomeMsgId, entity.getGroupId(), getMobile(entity.getAppId(), entity.getOwnerWxId()));
            } else {
                if (!newWelcomeMsgId.equals(oldWelcomeMsgId)) {
                    entity.setWelcomeMsgId(newWelcomeMsgId);
//                    dsCorpGroupAclService.setGroupWelcomeMsg(newWelcomeMsgId, entity.getGroupId(), getMobile(entity.getAppId(), entity.getOwnerWxId()));
                }
            }
        }
    }

    private void updateGroupInfo(GroupEditRequest request, ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        groupInfoEntity.setGroupName(request.getGroupName());
        groupInfoEntity.setIsAutoCreateNewGroup(request.getIsAutoCreateNewGroup());
        groupInfoEntity.setConsultantList(JsonUtils.toStr(request.getConsultantList()));
        groupInfoEntity.setProjectId(request.getWebcastId());
        groupManageDomainService.updateGroup(groupInfoEntity);
        groupManageDomainService.saveGroupConsultantMapping(groupInfoEntity);
    }
}
