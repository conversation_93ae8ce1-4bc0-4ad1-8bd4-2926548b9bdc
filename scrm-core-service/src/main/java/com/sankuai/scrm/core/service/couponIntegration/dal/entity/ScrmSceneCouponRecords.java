package com.sankuai.scrm.core.service.couponIntegration.dal.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_Scene_Coupon_Records
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmSceneCouponRecords {
    /**
     *   字段: id
     *   说明: 主键ID
     */
    private Long id;

    /**
     *   字段: couponGroupId
     *   说明: 券批次id
     */
    private String coupongroupid;

    /**
     *   字段: unifiedCouponId
     *   说明: 优惠券id
     */
    private String unifiedcouponid;

    /**
     *   字段: userId
     *   说明: 用户id
     */
    private Long userid;

    /**
     *   字段: unionId
     *   说明: unionId
     */
    private String unionid;

    /**
     *   字段: appId
     *   说明: appId，用于区分业务
     */
    private String appid;

    /**
     *   字段: distributorCode
     *   说明: 分销码
     */
    private String distributorcode;

    /**
     *   字段: couponAmount
     */
    private BigDecimal couponamount;

    /**
     *   字段: redirectLink
     *   说明: 跳转链接
     */
    private String redirectlink;

    /**
     *   字段: priceLimit
     *   说明: 最小使用金额 金额限制
     */
    private BigDecimal pricelimit;

    /**
     *   字段: endTime
     *   说明: 结束时间（请注意，如果是发美团红包，结束时间可能不准，请勿使用）
     */
    private Date endtime;

    /**
     *   字段: beginTime
     *   说明: 券开始时间
     */
    private Date begintime;

    /**
     *   字段: couponGroupName
     *   说明: 抵用券名称
     */
    private String coupongroupname;

    /**
     *   字段: useCouponTime
     *   说明: 用券时间
     */
    private Date usecoupontime;

    /**
     *   字段: sceneDetail
     *   说明: 场景明细
     */
    private String scenedetail;

    /**
     *   字段: sceneType
     *   说明: 0手动上传 1实时任务 2定时/周期任务 3群欢迎语 4渠道活码欢迎语 5群/好友裂变活动 6抽奖裂变活动 7智能客服
     */
    private Integer scenetype;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: statisticStatus
     *   说明: 状态：1-统计中、2-暂停统计、3-已删除、-1-未知状态
     */
    private Integer statisticstatus;

    /**
     *   字段: client_type
     *   说明: 点评侧-0 美团侧-1
     */
    private Byte clientType;

    /**
     *   字段: orderid
     */
    private String orderid;
}