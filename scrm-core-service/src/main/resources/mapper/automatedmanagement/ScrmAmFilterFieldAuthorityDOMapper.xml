<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmFilterFieldAuthorityDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmFilterFieldAuthorityDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="filter_field_id" jdbcType="BIGINT" property="filterFieldId" />
    <result column="accessible" jdbcType="TINYINT" property="accessible" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, filter_field_id, accessible, app_id, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmFilterFieldAuthorityDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_filter_field_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_filter_field_authority
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_filter_field_authority
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmFilterFieldAuthorityDOExample">
    delete from scrm_a_m_filter_field_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmFilterFieldAuthorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_filter_field_authority (filter_field_id, accessible, app_id, 
      update_time)
    values (#{filterFieldId,jdbcType=BIGINT}, #{accessible,jdbcType=TINYINT}, #{appId,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmFilterFieldAuthorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_filter_field_authority
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="filterFieldId != null">
        filter_field_id,
      </if>
      <if test="accessible != null">
        accessible,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="filterFieldId != null">
        #{filterFieldId,jdbcType=BIGINT},
      </if>
      <if test="accessible != null">
        #{accessible,jdbcType=TINYINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmFilterFieldAuthorityDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_filter_field_authority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_filter_field_authority
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.filterFieldId != null">
        filter_field_id = #{record.filterFieldId,jdbcType=BIGINT},
      </if>
      <if test="record.accessible != null">
        accessible = #{record.accessible,jdbcType=TINYINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_filter_field_authority
    set id = #{record.id,jdbcType=BIGINT},
      filter_field_id = #{record.filterFieldId,jdbcType=BIGINT},
      accessible = #{record.accessible,jdbcType=TINYINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmFilterFieldAuthorityDO">
    update scrm_a_m_filter_field_authority
    <set>
      <if test="filterFieldId != null">
        filter_field_id = #{filterFieldId,jdbcType=BIGINT},
      </if>
      <if test="accessible != null">
        accessible = #{accessible,jdbcType=TINYINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmFilterFieldAuthorityDO">
    update scrm_a_m_filter_field_authority
    set filter_field_id = #{filterFieldId,jdbcType=BIGINT},
      accessible = #{accessible,jdbcType=TINYINT},
      app_id = #{appId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_filter_field_authority
    (filter_field_id, accessible, app_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.filterFieldId,jdbcType=BIGINT}, #{item.accessible,jdbcType=TINYINT}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>