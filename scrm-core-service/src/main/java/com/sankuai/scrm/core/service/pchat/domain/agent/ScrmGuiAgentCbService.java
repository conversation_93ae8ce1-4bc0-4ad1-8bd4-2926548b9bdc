package com.sankuai.scrm.core.service.pchat.domain.agent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dz.srcm.pchat.dto.FriendList;
import com.sankuai.dz.srcm.pchat.enums.PchatBizIdEnum;
import com.sankuai.dz.srcm.pchat.tanjing.RobotInfoService;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxFriendsCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfoCommon;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.domain.common.RobotBizCommonDomainService;
import com.sankuai.scrm.core.service.pchat.domain.common.ScrmPersonalWxUserCommonDomainService;
import com.sankuai.scrm.core.service.pchat.dto.CallbackDTO;
import com.sankuai.scrm.core.service.pchat.dto.loginout.ClosureAccountDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotFunction.AcceptNewFriendDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotFunction.DeleteContactDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotFunction.FriendChangeInfoDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotFunction.NewFriendDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotInfo.FriendListDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalIsBooleanStrEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxUserSexEnum;
import com.sankuai.scrm.core.service.pchat.enums.im.ImRobotOnlineStateType;
import com.sankuai.scrm.core.service.pchat.notify.ClairvoyanceService;
import com.sankuai.scrm.core.service.pchat.notify.NotifyPayload;
import com.sankuai.scrm.core.service.pchat.notify.NotifyTypeEnum;
import com.sankuai.scrm.core.service.pchat.utils.Base64Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description agent相关的接口回调
 * <AUTHOR>
 * @Create On 2025/7/21 14:39
 * @Version v1.0.0
 */
@Slf4j
@Component
public class ScrmGuiAgentCbService {
    @Resource
    private ScrmPersonalWxUserCommonDomainService wxUserCommonDomainService;
    @Resource
    private RobotInfoService robotInfoService;
    @Resource
    private RobotBizCommonDomainService robotBizCommonDomainService;
    @Resource
    private ClairvoyanceService clairvoyanceService;
    @Resource
    private ScrmPersonalWxUserDomainService personalWxUserDomainService;
    @MdpConfig("scrm.wx.gui_agent.robot.intercept.switch:true")
    private boolean isIntercept;
    // 1005 登出 1003 登录 3022 刷新机器人 2001 机器人标签 1001 机器人二维码登录成功 1010机器人封号 1004 登录失败 3015 好友信息变动回调 3005加好友回调
    private final Set<String> ignoreTypes = new HashSet<>(Arrays.asList("1005", "1003", "3022", "2001", "1001", "1010", "1004", "3015", "3005", "3011", "3004"));

    public boolean intercept(String nType, String strContext) {
        try {
            return doIntercept(nType, strContext);
        } catch (Exception e) {
            log.error("ScrmGuiAgentDomainService.intercept error, strContext:{}", strContext, e);
            return false;
        }
    }

    private boolean doIntercept(String nType, String strContext) {
        if (!isIntercept) {
            return false;
        }
        List<String> ignoreList = Lion.getList(Environment.getAppName(), "scrm.wx.gui_agent.robot.intercept.ignore.type", String.class, new ArrayList<>(ignoreTypes));
        if (ignoreList.contains(nType)) {
            return false;
        }
        JSONObject jsonObject = JSONObject.parseObject(strContext);
        if (jsonObject == null) {
            return false;
        }
        String vcRobotSerialNo = jsonObject.getString("vcRobotSerialNo");
        List<ScrmPersonalWxRobotInfo> robotInfos = personalWxUserDomainService.queryRobotBySerialNo(vcRobotSerialNo);
        if (CollectionUtils.isNotEmpty(robotInfos)) {// 如果原机器人库里面有的，不拦截
            return false;
        }
        List<ScrmPersonalWxRobotInfoCommon> scrmPersonalWxRobotInfoCommon = wxUserCommonDomainService.queryRobotInfos(vcRobotSerialNo);

        if (CollectionUtils.isEmpty(scrmPersonalWxRobotInfoCommon)) {
            return false;
        }

        // todo others
        return true;
    }

    /**
     * 新增机器好友
     *
     * @param callbackDTO
     */
    public void do3022(CallbackDTO<FriendListDTO> callbackDTO) {
        try {

            log.info("ScrmGuiAgentCbService.do3022 收到微信回调");
            FriendListDTO data = callbackDTO.getData();
            if (data == null) {
                return;
            }
            FriendList[] friendList = data.getFriendList();
            if (friendList == null) {
                return;
            }
            ScrmPersonalWxRobotInfoCommon scrmPersonalWxRobotInfoCommon = wxUserCommonDomainService.queryRobotInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (scrmPersonalWxRobotInfoCommon == null) {
                return;
            }
            ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfoCommon = wxUserCommonDomainService.queryUserInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (scrmPersonalWxUserInfoCommon == null) {
                return;
            }
            List<ScrmPersonalWxUserInfoCommon> newUserInfo = new ArrayList<>();
            Map<String, ScrmPersonalWxFriendsCommon> friendInfoMap = new HashMap<>();
            for (FriendList list : friendList) {
                newUserInfo.add(robotBizCommonDomainService.convertToRobotFriendWxUserInfo(list, PchatBizIdEnum.GUI_AGENT.getCode()));
                ScrmPersonalWxFriendsCommon common = robotBizCommonDomainService.convertToRobotFriend(list, PchatBizIdEnum.GUI_AGENT.getCode());
                friendInfoMap.put(list.getVcFriendSerialNo(), common);
            }
            List<ScrmPersonalWxUserInfoCommon> friendUserInfo = wxUserCommonDomainService.saveOrUpdateWxUserInfo(newUserInfo);
            wxUserCommonDomainService.saveOrUpdateWxFriends(callbackDTO.getVcRobotSerialNo(), friendUserInfo, friendInfoMap);
        } catch (Exception e) {
            log.error("ScrmGuiAgentCbService.do3022 微信回调异常", e);
        }
    }


    public void do3011(CallbackDTO<AcceptNewFriendDTO> callbackDTO) {
        try {

            log.info("ScrmGuiAgentCbService.do3011 收到微信回调");
            AcceptNewFriendDTO data = callbackDTO.getData();
            if (data == null) {
                return;
            }
            ScrmPersonalWxRobotInfoCommon scrmPersonalWxRobotInfoCommon = wxUserCommonDomainService.queryRobotInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (scrmPersonalWxRobotInfoCommon == null) {
                return;
            }
            ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfoCommon = wxUserCommonDomainService.queryUserInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (scrmPersonalWxUserInfoCommon == null) {
                return;
            }
            List<ScrmPersonalWxUserInfoCommon> newUserInfo = new ArrayList<>();
            newUserInfo.add(robotBizCommonDomainService.convertToRobotFriendWxUserInfo(data, PchatBizIdEnum.GUI_AGENT.getCode()));
            List<ScrmPersonalWxUserInfoCommon> friendUserInfo = wxUserCommonDomainService.saveOrUpdateWxUserInfo(newUserInfo);
            Map<String, ScrmPersonalWxFriendsCommon> friendsCommonMap = new HashMap<>();
            ScrmPersonalWxFriendsCommon friendsCommon = new ScrmPersonalWxFriendsCommon();
//            friendsCommon.setRemarkName(data.);
            friendsCommonMap.put(data.getVcFriendSerialNo(), friendsCommon);
            wxUserCommonDomainService.saveOrUpdateWxFriends(callbackDTO.getVcRobotSerialNo(), friendUserInfo, friendsCommonMap);
        } catch (Exception e) {
            log.error("ScrmGuiAgentCbService.do3022 微信回调异常", e);
        }
    }


    public void do3005(CallbackDTO<List<NewFriendDTO>> callbackDTO) {
        try {
            ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfoCommon = wxUserCommonDomainService.queryUserInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (scrmPersonalWxUserInfoCommon == null) {
                return;
            }
            Map<String, ScrmPersonalWxFriendsCommon> friendsCommonMap = new HashMap<>();
            List<ScrmPersonalWxUserInfoCommon> newUserList = callbackDTO.getData().stream().map(d -> {
                ScrmPersonalWxUserInfoCommon userInfo = buildWxUserInfo(d);
                ScrmPersonalWxFriendsCommon friends = buildFriend(d);
                friendsCommonMap.put(d.getVcFriendSerialNo(), friends);
                return userInfo;

            }).collect(Collectors.toList());
            newUserList = wxUserCommonDomainService.saveOrUpdateWxUserInfo(newUserList);
            wxUserCommonDomainService.saveOrUpdateWxFriends(callbackDTO.getVcRobotSerialNo(), newUserList, friendsCommonMap);
        } catch (Exception e) {
            log.error("ScrmGuiAgentCbService.do3005 微信回调异常", e);
        }
    }


    @NotNull
    private static ScrmPersonalWxFriendsCommon buildFriend(NewFriendDTO d) {
        ScrmPersonalWxFriendsCommon friends = new ScrmPersonalWxFriendsCommon();
        friends.setExtra(JSONObject.toJSONString(d));
        friends.setRemarkName(Base64Util.decode(d.getVcBase64RemarkName(), d.getVcRemarkName()));
        friends.setAddTime(new Date());
        friends.setUpdateTime(new Date());
        friends.setTagIds(d.getVcTagIds());
        return friends;
    }

    @NotNull
    private static ScrmPersonalWxUserInfoCommon buildWxUserInfo(NewFriendDTO d) {
        ScrmPersonalWxUserInfoCommon userInfo = new ScrmPersonalWxUserInfoCommon();
        userInfo.setWxId(d.getVcFriendWxId());
        userInfo.setCreator(PchatConfig.SYS_CREATOR);
        userInfo.setNickname(Base64Util.decode(d.getVcBase64NickName(), d.getVcNickName()));
        userInfo.setSex(PersonalWxUserSexEnum.fromCode(d.getNSex()).getCode());
        userInfo.setHeadimgUrl(d.getVcHeadImgUrl());
        userInfo.setSerialNo(d.getVcFriendSerialNo());
        userInfo.setExtra(JSONObject.toJSONString(d));
        userInfo.setBizId(PchatBizIdEnum.GUI_AGENT.getCode());
        userInfo.setAddTime(new Date());
        userInfo.setUpdateTime(new Date());
        userInfo.setWxAlias(StrUtil.blankToDefault(d.getVcWxAlias(), null));
        return userInfo;
    }


    public void do1005(String vcRobotSerialNo) {
        try {
            if (StrUtil.isBlank(vcRobotSerialNo)) {
                return;
            }
            List<ScrmPersonalWxRobotInfoCommon> robotInfos = wxUserCommonDomainService.queryRobotInfos(vcRobotSerialNo, PchatBizIdEnum.GUI_AGENT.getCode());
            robotInfos.forEach(r -> {
                r.setOnline(ImRobotOnlineStateType.DIS_CONNECT.getCode());
                wxUserCommonDomainService.updateRobotInfo(r);
            });
        } catch (Exception e) {
            log.error("do1005 error", e);
        }
    }

    public void do1010(CallbackDTO<ClosureAccountDTO> callbackDTO) {
        try {
            List<ScrmPersonalWxRobotInfoCommon> robotInfos = wxUserCommonDomainService.queryRobotInfos(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (CollectionUtil.isEmpty(robotInfos)) {
                return;
            }
            robotInfos.forEach(robot -> {
                ClosureAccountDTO data = callbackDTO.getData();
                robot.setVcBanType(data.getVcBanType());
                robot.setVcReason(data.getVcReason());
                robot.setDtDateTime(data.getDtDateTime());
                robot.setValid(PersonalIsBooleanStrEnum.FALSE.getDesc());
                wxUserCommonDomainService.updateRobotInfo(robot);
                clairvoyanceService.sendEvent(NotifyTypeEnum.ROBOT_STATE, NotifyPayload.format("机器人人被封号,限制类型：{},限制原因：{},机器人编号:{}", robot.getVcBanType(), robot.getVcReason(), robot.getRobotSerialNo()));

            });
        } catch (Exception e) {
            log.error("do1010 error", e);
        }
    }

    public void do3015(CallbackDTO<List<FriendChangeInfoDTO>> callbackDTO) {
        try {
            List<FriendChangeInfoDTO> data = callbackDTO.getData();
            if (CollectionUtil.isEmpty(data)) {
                return;
            }
            ScrmPersonalWxUserInfoCommon robotInfo = wxUserCommonDomainService.queryUserInfo(callbackDTO.getVcRobotSerialNo(), PchatBizIdEnum.GUI_AGENT.getCode());
            if (robotInfo == null) {
                return;
            }
            List<List<FriendChangeInfoDTO>> partition = Lists.partition(data, 50);
            partition.forEach(d -> subBatchDo3015(robotInfo, d));
        } catch (Exception e) {
            log.error("do3015 error", e);
        }

    }

    public void subBatchDo3015(String robotSerialNo, List<FriendChangeInfoDTO> data) {
        ScrmPersonalWxUserInfoCommon robotInfo = wxUserCommonDomainService.queryUserInfo(robotSerialNo, PchatBizIdEnum.GUI_AGENT.getCode());
        if (robotInfo == null) {
            return;
        }
        subBatchDo3015(robotInfo, data);
    }

    public void subBatchDo3015(ScrmPersonalWxUserInfoCommon robotInfo, List<FriendChangeInfoDTO> data) {
        List<String> userSerialNos = data.stream().map(FriendChangeInfoDTO::getVcFriendSerialNo).collect(Collectors.toList());
        Map<String, FriendChangeInfoDTO> changeInfoDTOMap = data.stream().collect(Collectors.toMap(FriendChangeInfoDTO::getVcFriendSerialNo, Function.identity(), (u1, u2) -> u1));
        List<ScrmPersonalWxUserInfoCommon> userInfos = wxUserCommonDomainService.queryUserInfos(userSerialNos, PchatBizIdEnum.GUI_AGENT.getCode());
        if (CollectionUtil.isEmpty(userInfos)) {
            return;
        }
        Map<Long, FriendChangeInfoDTO> friendListDTOMap = new HashMap<>();
        userInfos.forEach(wx -> {
            FriendChangeInfoDTO friendChangeInfoDTO = changeInfoDTOMap.get(wx.getSerialNo());
            wx.setSex(PersonalWxUserSexEnum.fromCode(friendChangeInfoDTO.getNSex()).getCode());
            wx.setNickname(Base64Util.decode(friendChangeInfoDTO.getVcBase64NickName(), friendChangeInfoDTO.getVcNickName()));
            wx.setHeadimgUrl(friendChangeInfoDTO.getVcHeadImgUrl());
            friendListDTOMap.put(wx.getId(), friendChangeInfoDTO);
        });
        wxUserCommonDomainService.updateWxUserInfoById(userInfos);

        List<ScrmPersonalWxFriendsCommon> scrmPersonalWxFriendsCommons = wxUserCommonDomainService.queryWxFriendRelationByUserIdAndFriendId(robotInfo.getId(), new ArrayList<>(friendListDTOMap.keySet()), PchatBizIdEnum.GUI_AGENT.getCode());
        scrmPersonalWxFriendsCommons.forEach(s -> {
            FriendChangeInfoDTO friendChangeInfoDTO = friendListDTOMap.get(s.getFriendId());
            s.setRemarkName(Base64Util.decode(friendChangeInfoDTO.getVcBase64RemarkName(), friendChangeInfoDTO.getVcRemarkName()));
            s.setTagIds(friendChangeInfoDTO.getVcTagIds());
            wxUserCommonDomainService.updateFriend(s);
        });
    }

    public void do3004(CallbackDTO<DeleteContactDTO> callbackDTO) {
        try {
            DeleteContactDTO data = callbackDTO.getData();
            String robotSerialNo = callbackDTO.getVcRobotSerialNo();
            String vcContactSerialNo = data.getVcContactSerialNo();
            ScrmPersonalWxUserInfoCommon robotInfo = wxUserCommonDomainService.queryUserInfo(robotSerialNo, PchatBizIdEnum.GUI_AGENT.getCode());
            ScrmPersonalWxUserInfoCommon friendUser = wxUserCommonDomainService.queryUserInfo(vcContactSerialNo, PchatBizIdEnum.GUI_AGENT.getCode());

            if (robotInfo == null || friendUser == null) {
                return;
            }
            List<ScrmPersonalWxFriendsCommon> scrmPersonalWxFriends = wxUserCommonDomainService.queryWxFriendRelationByUserIdAndFriendId(robotInfo.getId(), Arrays.asList(friendUser.getId()), PchatBizIdEnum.GUI_AGENT.getCode());
            if (CollectionUtil.isNotEmpty(scrmPersonalWxFriends)) {
                scrmPersonalWxFriends.forEach(wxUserCommonDomainService::deleteFriend);
            }
            scrmPersonalWxFriends = wxUserCommonDomainService.queryWxFriendRelationByUserIdAndFriendId(friendUser.getId(), Arrays.asList(robotInfo.getId()), PchatBizIdEnum.GUI_AGENT.getCode());
            if (CollectionUtil.isNotEmpty(scrmPersonalWxFriends)) {
                scrmPersonalWxFriends.forEach(wxUserCommonDomainService::deleteFriend);
            }
        } catch (Exception e) {
            log.error("do3004 error", e);
        }
    }
}
