package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/26 15:53
 * @Version v1.0.0
 */
@Getter
public enum PersonalGroupTaskExecuteResultEnum {
    SUCCESS("1", "全部成功"),
    RUNNING("2", "执行中"),
    PART_FAILURE("3", "部分失败"),
    ALL_FAILURE("4", "全部失败"),


    ;

    private final String code;

    private final String desc;

    PersonalGroupTaskExecuteResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
