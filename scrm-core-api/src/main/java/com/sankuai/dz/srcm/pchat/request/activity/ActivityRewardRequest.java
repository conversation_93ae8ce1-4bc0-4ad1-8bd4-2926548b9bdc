package com.sankuai.dz.srcm.pchat.request.activity;

import com.sankuai.dz.srcm.pchat.request.PageRequest;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/7/3 15:50
 * @Version v1.0.0
 */
@Data
public class ActivityRewardRequest extends PageRequest {
    private Long activityId;
    private String webcastId;

    @Override
    public boolean validParam() {
        assertTrue(activityId == null || activityId < 1L || StringUtils.isEmpty(webcastId), "活动id和直播id必传");
        return true;
    }
}
