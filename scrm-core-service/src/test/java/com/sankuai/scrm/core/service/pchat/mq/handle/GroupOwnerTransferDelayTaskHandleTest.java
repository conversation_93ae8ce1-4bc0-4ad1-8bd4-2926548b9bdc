package com.sankuai.scrm.core.service.pchat.mq.handle;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.pchat.service.transfer.Transfer2ConsultantTask;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
public class GroupOwnerTransferDelayTaskHandleTest extends BaseMockTest {

    @Mock
    private Transfer2ConsultantTask transfer2ConsultantTask;

    @InjectMocks
    private GroupOwnerTransferDelayTaskHandle groupOwnerTransferDelayTaskHandle;

    private MafkaMessage<String> message;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        message = new MafkaMessage<>("topic", 0, 0L, null, "{\"taskId\":\"123\"}");
    }

    /**
     * 测试消息处理成功的场景
     */
    @Test
    public void testMessageHandleSuccess() {
        // arrange

        // act
        ConsumeStatus result = groupOwnerTransferDelayTaskHandle.messageHandle(message);

        // assert
        assert result == ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 测试消息格式不正确导致处理失败的场景
     */
    @Test
    public void testMessageHandleInvalidFormat() {
        // arrange
        MafkaMessage<String> invalidMessage = new MafkaMessage<>("topic", 0, 0L, null, "Invalid JSON");

        // act
        ConsumeStatus result = groupOwnerTransferDelayTaskHandle.messageHandle(invalidMessage);

        // assert
        Mockito.verify(transfer2ConsultantTask, Mockito.never()).execute(any(Long.class));
        assert result == ConsumeStatus.CONSUME_SUCCESS;
    }

}