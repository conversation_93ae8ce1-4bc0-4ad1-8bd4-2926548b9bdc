package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.request.SendGroupChatMessagesRequest;
import com.sankuai.dz.srcm.pchat.request.SendPrivateChatMessagesRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.tanjing.RobotFunctionService;
import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.MsgProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxMsgTask;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.ScrmPersonalWxMsgDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalMsgTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxLogBusinessRelateTableEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxLogBusinessTypeEnum;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.pchat.mq.dto.DelayMsgTask;
import com.sankuai.scrm.core.service.pchat.thirdparty.horus.HorusVideoService;
import com.sankuai.scrm.core.service.util.SwitchUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.springframework.context.ApplicationEventPublisher;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxSendMessageServiceProcessSendingVideSeoTest {

    @InjectMocks
    private ScrmPersonalWxSendMessageService scrmPersonalWxSendMessageService;

    @Mock
    private HorusVideoService horusVideoService;

    private List<ScrmPersonalWxMsgDTO> msgEntityList;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private PrivateLiveAdapterRouter adapterRouter;

    @Mock
    private MsgProcessor msgProcessor;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Mock
    private RobotFunctionService robotFunctionService;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Before
    public void setUp() {
        msgEntityList = new ArrayList<>();
    }

    /**
     * 测试场景：msgEntityList 为空
     */
    @Test
    public void testProcessSendingVideSeo_EmptyList() throws Throwable {
        // arrange
        // act
        scrmPersonalWxSendMessageService.processSendingVideSeo(msgEntityList);
        // assert
        assertTrue(msgEntityList.isEmpty());
    }

    /**
     * 测试场景：msgEntityList 包含多个 ScrmPersonalWxMsgDTO 对象，其中所有对象的 sendMsgType 为 VIDEO 但 msgContent 不为空
     */
    @Test
    public void testProcessSendingVideSeo_AllVideoWithNonEmptyContent() throws Throwable {
        // arrange
        ScrmPersonalWxMsg wxMsg1 = new ScrmPersonalWxMsg();
        wxMsg1.setSendMsgType(PersonalMsgTypeEnum.VIDEO.getCode());
        GroupMsg groupMsg1 = new GroupMsg();
        groupMsg1.setMsgContent("Video content");
        ScrmPersonalWxMsg wxMsg2 = new ScrmPersonalWxMsg();
        wxMsg2.setSendMsgType(PersonalMsgTypeEnum.VIDEO.getCode());
        GroupMsg groupMsg2 = new GroupMsg();
        groupMsg2.setMsgContent("Another video content");
        msgEntityList.add(new ScrmPersonalWxMsgDTO(wxMsg1, groupMsg1));
        msgEntityList.add(new ScrmPersonalWxMsgDTO(wxMsg2, groupMsg2));
        // act
        scrmPersonalWxSendMessageService.processSendingVideSeo(msgEntityList);
        // assert
        assertEquals(2, msgEntityList.size());
        verify(horusVideoService, never()).process(anyString(), anyLong(), anyString());
    }

    /**
     * 测试场景：msgEntityList 包含多个 ScrmPersonalWxMsgDTO 对象，其中部分对象的 sendMsgType 不是 VIDEO
     */
    @Test
    public void testProcessSendingVideSeo_SomeNonVideo() throws Throwable {
        // arrange
        ScrmPersonalWxMsg wxMsg1 = new ScrmPersonalWxMsg();
        wxMsg1.setSendMsgType(PersonalMsgTypeEnum.TEXT.getCode());
        GroupMsg groupMsg1 = new GroupMsg();
        groupMsg1.setMsgContent("Text content");
        ScrmPersonalWxMsg wxMsg2 = new ScrmPersonalWxMsg();
        wxMsg2.setSendMsgType(PersonalMsgTypeEnum.IMAGE.getCode());
        GroupMsg groupMsg2 = new GroupMsg();
        groupMsg2.setMsgContent("Image content");
        msgEntityList.add(new ScrmPersonalWxMsgDTO(wxMsg1, groupMsg1));
        msgEntityList.add(new ScrmPersonalWxMsgDTO(wxMsg2, groupMsg2));
        // act
        scrmPersonalWxSendMessageService.processSendingVideSeo(msgEntityList);
        // assert
        assertEquals(2, msgEntityList.size());
        verify(horusVideoService, never()).process(anyString(), anyLong(), anyString());
    }

    /**
     * 测试场景：msgEntityList 包含 null 元素
     */
    @Test
    public void testProcessSendingVideSeo_ContainsNullElement() throws Throwable {
        // arrange
        ScrmPersonalWxMsg wxMsg = new ScrmPersonalWxMsg();
        wxMsg.setSendMsgType(PersonalMsgTypeEnum.TEXT.getCode());
        GroupMsg groupMsg = new GroupMsg();
        groupMsg.setMsgContent("Text content");
        msgEntityList.add(new ScrmPersonalWxMsgDTO(wxMsg, groupMsg));
        msgEntityList.add(new ScrmPersonalWxMsgDTO(new ScrmPersonalWxMsg(), new GroupMsg()));
        // act
        scrmPersonalWxSendMessageService.processSendingVideSeo(msgEntityList);
        // assert
        assertEquals(2, msgEntityList.size());
        verify(horusVideoService, never()).process(anyString(), anyLong(), anyString());
    }

    /**
     * 测试场景：msgEntityList 包含 ScrmPersonalWxMsgDTO 对象，但其 wxMsg 或 groupMsg 为 null
     */
    @Test
    public void testProcessSendingVideSeo_NullWxMsgOrGroupMsg() throws Throwable {
        // arrange
        ScrmPersonalWxMsg wxMsg = new ScrmPersonalWxMsg();
        wxMsg.setSendMsgType(PersonalMsgTypeEnum.VIDEO.getCode());
        GroupMsg groupMsg = new GroupMsg();
        groupMsg.setMsgContent("Content");
        ScrmPersonalWxMsgDTO dto1 = new ScrmPersonalWxMsgDTO(new ScrmPersonalWxMsg(), new GroupMsg());
        ScrmPersonalWxMsgDTO dto2 = new ScrmPersonalWxMsgDTO(wxMsg, groupMsg);
        msgEntityList.add(dto1);
        msgEntityList.add(dto2);
        // act
        scrmPersonalWxSendMessageService.processSendingVideSeo(msgEntityList);
        // assert
        assertEquals(2, msgEntityList.size());
        verify(horusVideoService, never()).process(anyString(), anyLong(), anyString());
    }

    /**
     * 测试场景：消息实体列表为空
     */
    @Test
    public void testSendMsgImmediately_MsgEntityListIsEmpty() throws Throwable {
        // arrange
        DelayMsgTask delayMsgTask = new DelayMsgTask();
        delayMsgTask.setMsgIdList(Collections.singletonList(1L));
        when(groupManageDomainService.queryWxMsgByIds(delayMsgTask.getMsgIdList())).thenReturn(Collections.emptyList());
        // act
        scrmPersonalWxSendMessageService.sendMsgImmediately(delayMsgTask);
        // assert
        verify(groupManageDomainService, times(1)).queryWxMsgByIds(delayMsgTask.getMsgIdList());
        verifyNoMoreInteractions(groupManageDomainService);
    }

    /**
     * 测试场景：群信息实体为空
     */
    @Test
    public void testSendMsgImmediately_GroupInfoEntityIsNull() throws Throwable {
        // arrange
        DelayMsgTask delayMsgTask = new DelayMsgTask();
        delayMsgTask.setMsgIdList(Collections.singletonList(1L));
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setAppId("appId");
        msg.setChatRoomWxSerialNo("chatRoomSerialNo");
        when(groupManageDomainService.queryWxMsgByIds(delayMsgTask.getMsgIdList())).thenReturn(Collections.singletonList(msg));
        when(groupManageDomainService.queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo())).thenReturn(null);
        // act
        scrmPersonalWxSendMessageService.sendMsgImmediately(delayMsgTask);
        // assert
        verify(groupManageDomainService, times(1)).queryWxMsgByIds(delayMsgTask.getMsgIdList());
        verify(groupManageDomainService, times(1)).queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo());
        verifyNoMoreInteractions(groupManageDomainService);
    }

    /**
     * 测试场景：过滤后的消息实体列表为空
     */
    @Test
    public void testSendMsgImmediately_FilteredMsgEntityListIsEmpty() throws Throwable {
        // arrange
        DelayMsgTask delayMsgTask = new DelayMsgTask();
        delayMsgTask.setMsgIdList(Collections.singletonList(1L));
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setAppId("appId");
        msg.setChatRoomWxSerialNo("chatRoomSerialNo");
        msg.setTaskId(1L);
        when(groupManageDomainService.queryWxMsgByIds(delayMsgTask.getMsgIdList())).thenReturn(Collections.singletonList(msg));
        when(groupManageDomainService.queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo())).thenReturn(new ScrmPersonalWxGroupInfoEntity());
        when(groupManageDomainService.queryMsgTaskByIds(anyList())).thenReturn(Collections.singletonList(new ScrmPersonalWxMsgTask()));
        // act
        scrmPersonalWxSendMessageService.sendMsgImmediately(delayMsgTask);
        // assert
        verify(groupManageDomainService, times(1)).queryWxMsgByIds(delayMsgTask.getMsgIdList());
        verify(groupManageDomainService, times(1)).queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo());
        verify(groupManageDomainService, times(1)).queryMsgTaskByIds(anyList());
        verifyNoMoreInteractions(groupManageDomainService);
    }

    /**
     * 测试场景：灰度开关开启
     */
    @Test
    public void testSendMsgImmediately_GraySwitchIsOn() throws Throwable {
        // arrange
        DelayMsgTask delayMsgTask = new DelayMsgTask();
        delayMsgTask.setMsgIdList(Collections.singletonList(1L));
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setAppId("appId");
        msg.setChatRoomWxSerialNo("chatRoomSerialNo");
        msg.setTaskId(1L);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setProjectId("projectId");
        when(groupManageDomainService.queryWxMsgByIds(delayMsgTask.getMsgIdList())).thenReturn(Collections.singletonList(msg));
        when(groupManageDomainService.queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo())).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryMsgTaskByIds(anyList())).thenReturn(Collections.singletonList(new ScrmPersonalWxMsgTask()));
        when(adapterRouter.getProcessor(MsgProcessor.class, groupInfoEntity.getAppId())).thenReturn(msgProcessor);
        try (MockedStatic<SwitchUtil> switchUtilMock = Mockito.mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(Lists.newArrayList(groupInfoEntity.getProjectId()))).thenReturn(true);
            // act
            scrmPersonalWxSendMessageService.sendMsgImmediately(delayMsgTask);
            // assert
            verify(groupManageDomainService, times(1)).queryWxMsgByIds(delayMsgTask.getMsgIdList());
            verify(groupManageDomainService, times(1)).queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo());
            verify(groupManageDomainService, times(1)).queryMsgTaskByIds(anyList());
            verify(adapterRouter, times(1)).getProcessor(MsgProcessor.class, groupInfoEntity.getAppId());
            verify(msgProcessor, times(1)).sendGroupMsgNow(eq(groupInfoEntity.getAppId()), eq(groupInfoEntity), anyList());
        }
    }

    /**
     * 测试场景：灰度开关关闭
     */
    @Test
    public void testSendMsgImmediately_GraySwitchIsOff() throws Throwable {
        // arrange
        DelayMsgTask delayMsgTask = new DelayMsgTask();
        delayMsgTask.setMsgIdList(Collections.singletonList(1L));
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setAppId("appId");
        msg.setChatRoomWxSerialNo("chatRoomSerialNo");
        msg.setTaskId(1L);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setProjectId("projectId");
        when(groupManageDomainService.queryWxMsgByIds(delayMsgTask.getMsgIdList())).thenReturn(Collections.singletonList(msg));
        when(groupManageDomainService.queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo())).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryMsgTaskByIds(anyList())).thenReturn(Collections.singletonList(new ScrmPersonalWxMsgTask()));
        try (MockedStatic<SwitchUtil> switchUtilMock = Mockito.mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(Lists.newArrayList(groupInfoEntity.getProjectId()))).thenReturn(false);
            // act
            scrmPersonalWxSendMessageService.sendMsgImmediately(delayMsgTask);
            // assert
            verify(groupManageDomainService, times(1)).queryWxMsgByIds(delayMsgTask.getMsgIdList());
            verify(groupManageDomainService, times(1)).queryGroupByChatroomSerialNo(msg.getAppId(), msg.getChatRoomWxSerialNo());
            verify(groupManageDomainService, times(1)).queryMsgTaskByIds(anyList());
            verifyNoMoreInteractions(groupManageDomainService);
        }
    }

    /**
     * 测试消息列表为空的情况
     */
    @Test
    public void testSendPrivateMsg_EmptyMsgList() throws Throwable {
        // arrange
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.emptyList();
        // act
        scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
        // assert
        verify(adapterRouter, never()).getProcessor(any(), anyString());
        verify(msgProcessor, never()).sendPrivateMsg(anyString(), anyString(), anyString(), anyList());
        verify(robotFunctionService, never()).sendPrivateChatMessages(any(SendPrivateChatMessagesRequest.class));
    }

    /**
     * 测试处理视频SEO后消息列表为空的情况
     */
    @Test
    public void testSendPrivateMsg_EmptyMsgListAfterVideoSEO() throws Throwable {
        // arrange
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setId(1L);
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.singletonList(new ScrmPersonalWxMsgDTO(msg, new GroupMsg()));
        try (MockedStatic<SwitchUtil> switchUtilMock = mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(false);
            AsyncInvokeResultDTO asyncInvokeResultDTO = new AsyncInvokeResultDTO();
            asyncInvokeResultDTO.setVcSerialNo("testSerialNo");
            when(robotFunctionService.sendPrivateChatMessages(any(SendPrivateChatMessagesRequest.class))).thenReturn(asyncInvokeResultDTO);
            // act
            scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
            // assert
            verify(personalWxCommonService).createSerialNoLog(anyString(), any(PersonalWxLogBusinessTypeEnum.class), any(PersonalWxLogBusinessRelateTableEnum.class), anyLong());
        }
    }

    /**
     * 测试灰度开关开启的情况
     */
    @Test
    public void testSendPrivateMsg_GraySwitchEnabled() throws Throwable {
        // arrange
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.singletonList(new ScrmPersonalWxMsgDTO(new ScrmPersonalWxMsg(), new GroupMsg()));
        try (MockedStatic<SwitchUtil> switchUtilMock = mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
            when(adapterRouter.getProcessor(MsgProcessor.class, "appId")).thenReturn(msgProcessor);
            // act
            scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
            // assert
            verify(msgProcessor).sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
        }
    }

    /**
     * 测试灰度开关未开启且发送消息成功的情况
     */
    @Test
    public void testSendPrivateMsg_GraySwitchDisabled_SendSuccess() throws Throwable {
        // arrange
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setId(1L);
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.singletonList(new ScrmPersonalWxMsgDTO(msg, new GroupMsg()));
        try (MockedStatic<SwitchUtil> switchUtilMock = mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(false);
            AsyncInvokeResultDTO asyncInvokeResultDTO = new AsyncInvokeResultDTO();
            asyncInvokeResultDTO.setNResult(1);
            asyncInvokeResultDTO.setVcSerialNo("testSerialNo");
            when(robotFunctionService.sendPrivateChatMessages(any(SendPrivateChatMessagesRequest.class))).thenReturn(asyncInvokeResultDTO);
            // act
            scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
            // assert
            verify(groupManageDomainService, times(2)).updateWxMsgList(anyList());
        }
    }

    /**
     * 测试灰度开关未开启且发送消息失败的情况
     */
    @Test
    public void testSendPrivateMsg_GraySwitchDisabled_SendFailure() throws Throwable {
        // arrange
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setId(1L);
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.singletonList(new ScrmPersonalWxMsgDTO(msg, new GroupMsg()));
        try (MockedStatic<SwitchUtil> switchUtilMock = mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(false);
            AsyncInvokeResultDTO asyncInvokeResultDTO = new AsyncInvokeResultDTO();
            asyncInvokeResultDTO.setNResult(0);
            asyncInvokeResultDTO.setVcSerialNo("testSerialNo");
            when(robotFunctionService.sendPrivateChatMessages(any(SendPrivateChatMessagesRequest.class))).thenReturn(asyncInvokeResultDTO);
            // act
            scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
            // assert
            verify(groupManageDomainService, times(2)).updateWxMsgList(anyList());
            verify(applicationEventPublisher).publishEvent(any());
        }
    }

    /**
     * 测试灰度开关未开启且发送消息抛出异常的情况
     */
    @Test
    public void testSendPrivateMsg_GraySwitchDisabled_SendThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxMsg msg = new ScrmPersonalWxMsg();
        msg.setId(1L);
        List<ScrmPersonalWxMsgDTO> wxMsgDTOS = Collections.singletonList(new ScrmPersonalWxMsgDTO(msg, new GroupMsg()));
        try (MockedStatic<SwitchUtil> switchUtilMock = mockStatic(SwitchUtil.class)) {
            switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(false);
            when(robotFunctionService.sendPrivateChatMessages(any(SendPrivateChatMessagesRequest.class))).thenThrow(new RuntimeException("Send msg failed"));
            // act & assert
            try {
                scrmPersonalWxSendMessageService.sendPrivateMsg("appId", "sendSerialNo", "receiveSerialNo", wxMsgDTOS);
                fail("Expected RuntimeException");
            } catch (RuntimeException e) {
                verify(groupManageDomainService).updateWxMsgList(anyList());
            }
        }
    }
}
