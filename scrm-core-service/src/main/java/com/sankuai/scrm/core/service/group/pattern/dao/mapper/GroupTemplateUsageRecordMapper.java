package com.sankuai.scrm.core.service.group.pattern.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.group.pattern.dao.entity.GroupTemplateUsageRecord;
import com.sankuai.scrm.core.service.group.pattern.dao.example.GroupTemplateUsageRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GroupTemplateUsageRecordMapper extends MybatisBLOBsMapper<GroupTemplateUsageRecord, GroupTemplateUsageRecordExample, Long> {
    int batchInsert(@Param("list") List<GroupTemplateUsageRecord> list);
}