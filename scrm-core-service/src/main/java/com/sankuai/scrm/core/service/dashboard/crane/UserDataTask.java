package com.sankuai.scrm.core.service.dashboard.crane;

import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionInvitationRecord;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionActivityExample;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionInvitationRecordExample;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionInvitationRecordMapper;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsUserDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESReadDomainService;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.group.dal.babymapper.MemberInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant.USER_DATA_DASHBOARD_STATISTICS_SNAPSHOT_INDEX;
import static com.sankuai.scrm.core.service.dashboard.domain.Utils.*;

/**
 * 用户数据定时任务
 */
@Component
@Slf4j
public class UserDataTask {

    @Autowired
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;
    @Autowired
    private DashBoardESReadDomainService dashBoardESReadDomainService;
    @Resource
    private ContactUserMapper contactUserMapper;
    @Resource
    private GroupFissionInvitationRecordMapper groupFissionInvitationRecordMapper;
    @Resource
    private MemberInfoEntityMapper memberInfoEntityMapper;
    @Resource
    private GroupFissionActivityMapper groupFissionActivityMapper;
    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;
    @Resource
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;



    public void getDataAndPushToEs() {
        try {
            log.info("com.sankuai.medicalcosmetology.scrm.core.dashboard:开始处理用户数据并载入ES");
            Date startOfYesterday = getStartOfDay(1);
            Date endOfYesterday = getEndOfDay(1);
            List<CorpAppConfig> allConfigs = corpAppConfigRepository.getAllConfigs();
            Map<String, Set<String>> activityUserDecrement = getActivityUserDecrement(startOfYesterday, endOfYesterday);
            Map<String, Set<String>> countActivityUserByCorpId = getActivityUserIncrement(startOfYesterday, endOfYesterday);
            allConfigs.forEach(corpAppConfig -> processCorpAppConfig(corpAppConfig, startOfYesterday, endOfYesterday, countActivityUserByCorpId, activityUserDecrement));
            // 由于留存率固定计算7天或30天前的留存客户/新增客户，因此这里可以计算实时数据中的留存率快照
            calRetentionRatesToday();
        } catch (Exception e) {
            log.error("com.sankuai.medicalcosmetology.scrm.core.dashboard:处理用户数据并载入ES失败", e);
        }
    }

    private void calRetentionRatesToday() {
        try {
            log.info("com.sankuai.medicalcosmetology.scrm.core.dashboard:开始计算留存率并写入快照");
            List<CorpAppConfig> allConfigs = corpAppConfigRepository.getAllConfigs();
            for (CorpAppConfig config : allConfigs) {
                EsUserDataSnapshot esUserDataSnapshot= EsUserDataSnapshot
                        .builder()
                        .corpId(config.getCorpId())
                        .versionTime(String.valueOf(LocalDate.now()))
                        .id(concatId(LocalDate.now(), config.getCorpId()))
                        .build();
                setRetentionRates(esUserDataSnapshot, config.getCorpId());
                dashBoardESWriteDomainService.updateESDocByIdSelective(USER_DATA_DASHBOARD_STATISTICS_SNAPSHOT_INDEX, esUserDataSnapshot, esUserDataSnapshot.getId());
            }
        } catch (Exception e) {
            log.error("com.sankuai.medicalcosmetology.scrm.core.dashboard:开始计算留存率并写入快照失败", e);
        }
    }


    private void processCorpAppConfig(CorpAppConfig corpAppConfig, Date startOfYesterday, Date endOfYesterday, Map<String, Set<String>> countActivityUserByCorpId, Map<String, Set<String>> activityUserDecrement) {
        String corpId = corpAppConfig.getCorpId();
        LocalDate yesterday = LocalDate.now().minusDays(1);
        EsUserDataSnapshot esUserDataSnapshot= EsUserDataSnapshot.builder().corpId(corpId).versionTime(String.valueOf(yesterday))
                .id(concatId(yesterday, corpId)).build();
        setTotalCustomer(esUserDataSnapshot, corpId);
        setIncrement(esUserDataSnapshot, corpId, startOfYesterday, endOfYesterday, countActivityUserByCorpId, activityUserDecrement);
        setDecrement(esUserDataSnapshot, corpId, startOfYesterday, endOfYesterday, countActivityUserByCorpId, activityUserDecrement);
        setNetAndRetentionRates(esUserDataSnapshot, corpId);
        dashBoardESWriteDomainService.updateESDocByIdSelective(USER_DATA_DASHBOARD_STATISTICS_SNAPSHOT_INDEX,esUserDataSnapshot,esUserDataSnapshot.getId());
    }

    /**
     * 设置总客户数
     * @param esUserDataSnapshot
     * @param corpId
     */
    private void setTotalCustomer(EsUserDataSnapshot esUserDataSnapshot, String corpId) {
        ContactUserExample contactUserExample=new ContactUserExample();
        contactUserExample.createCriteria().andCorpIdEqualTo(corpId).andUnionIdIsNotNull().andStatusEqualTo(1);
        List<String> contractUserExternalIdList = contactUserMapper.selectByExample(contactUserExample).stream().map(ContactUser::getExternalUserId).distinct().collect(Collectors.toList());
        esUserDataSnapshot.setCorpWxFriendsCount(contractUserExternalIdList.size());
        MemberInfoEntityExample memberInfoEntityExample=new MemberInfoEntityExample();
        memberInfoEntityExample.createCriteria().andCorpIdEqualTo(corpId).andUnionIdIsNotNull().andDeletedEqualTo(false).andStatusEqualTo((byte) 0).andMemberTypeEqualTo((byte) 2);
        List<String> memberInfoGroupMemberIdList = memberInfoEntityMapper.selectByExample(memberInfoEntityExample).stream().map(MemberInfoEntity::getGroupMemberId).distinct().collect(Collectors.toList());
        esUserDataSnapshot.setInGroupUsersCount(memberInfoGroupMemberIdList.size());
        esUserDataSnapshot.setCorpWxCustomersCount(getDistinctCount(contractUserExternalIdList, memberInfoGroupMemberIdList));
    }

    /**
     * 设置流失客户数
     */
    private void setDecrement(EsUserDataSnapshot esUserDataSnapshot, String corpId, Date startOfYesterday, Date endOfYesterday, Map<String, Set<String>> countActivityUserByCorpId, Map<String, Set<String>> activityUserDecrement) {
        ContactUserExample contactUserExample = new ContactUserExample();
        contactUserExample.createCriteria()
                .andStatusEqualTo(2)
                .andCorpIdEqualTo(corpId)
                .andUpdateTimeBetween(startOfYesterday, endOfYesterday);
        List<String> contactUserMapperUserIdList = contactUserMapper.selectByExample(contactUserExample)
                .stream()
                .map(ContactUser::getExternalUserId)
                .collect(Collectors.toList());

        MemberInfoEntityExample memberInfoEntityExample = new MemberInfoEntityExample();
        memberInfoEntityExample.createCriteria()
                .andDeletedEqualTo(false)
                .andStatusEqualTo((byte) 1)
                .andCorpIdEqualTo(corpId)
                .andUpdateTimeBetween(startOfYesterday, endOfYesterday)
                .andMemberTypeEqualTo((byte) 2);
        List<String> memberInfoEntityUserIdList = memberInfoEntityMapper.selectByExample(memberInfoEntityExample)
                .stream()
                .map(MemberInfoEntity::getGroupMemberId)
                .collect(Collectors.toList());

        esUserDataSnapshot.setCustomerDecrement(getDistinctCount(contactUserMapperUserIdList, memberInfoEntityUserIdList ));
        esUserDataSnapshot.setFriendDecrement(contactUserMapperUserIdList.size());
        esUserDataSnapshot.setInGroupUserDecrement(memberInfoEntityUserIdList.size());
        List<String> innerStationUnionIdList = dashBoardESReadDomainService.selectInnerStationUserDecrement(corpId);
        esUserDataSnapshot.setInnerStationUserDecrement(innerStationUnionIdList.size());
        Set<String> activityUnionIdSet = activityUserDecrement.getOrDefault(corpId, new HashSet<>());
        esUserDataSnapshot.setActivityUserDecrement(activityUnionIdSet.size());

        // 企微活码
        contactUserExample.clear();
        contactUserExample.createCriteria()
                .andStateIsNotNull()
                .andCorpIdEqualTo(corpId)
                .andStatusEqualTo(2)
                .andUpdateTimeBetween(startOfYesterday, endOfYesterday);
        List<String> corpWxDynamicCodeUserIdList = contactUserMapper.selectByExample(contactUserExample)
                .stream()
                .map(ContactUser::getExternalUserId)
                .distinct()
                .collect(Collectors.toList());
        esUserDataSnapshot.setCorpWxDynamicCodeUserDecrement(corpWxDynamicCodeUserIdList.size());

        // 群活码
        memberInfoEntityExample.clear();
        memberInfoEntityExample.createCriteria()
                .andStateIsNotNull()
                .andMemberTypeEqualTo((byte) 2)
                .andCorpIdEqualTo(corpId)
                .andStatusEqualTo((byte) 1)
                .andUpdateTimeBetween(startOfYesterday, endOfYesterday);
        List<String> groupDynamicCodeUserIdList = memberInfoEntityMapper.selectByExample(memberInfoEntityExample)
                .stream()
                .map(MemberInfoEntity::getGroupMemberId)
                .distinct()
                .collect(Collectors.toList());
        esUserDataSnapshot.setGroupDynamicCodeUserDecrement(groupDynamicCodeUserIdList.size());

        String appId = corpAppConfigRepository.getAppIdByCorpId(corpId);
        Set<String> innerStationUserIdSet = externalContactBaseInfoDomainService.queryBaseInfoByUnionIdList(appId, innerStationUnionIdList)
                .stream()
                .map(ExternalContactBaseInfo::getExternalUserId)
                .collect(Collectors.toSet());
        Set<String> activityUserIdSet = externalContactBaseInfoDomainService.queryBaseInfoByUnionIdList(appId, new ArrayList<>(activityUnionIdSet))
                .stream()
                .map(ExternalContactBaseInfo::getExternalUserId)
                .collect(Collectors.toSet());
        // 站外&置换：企微活码来源用户数+群活码来源用户数-站内来源用户数-活动来源用户数
        Collection<String> OutStationOrReplacementUserDecrement = CollectionUtils.subtract(
                CollectionUtils.subtract(
                        CollectionUtils.union(corpWxDynamicCodeUserIdList, groupDynamicCodeUserIdList),
                        activityUserIdSet
                ), innerStationUserIdSet
        );
        esUserDataSnapshot.setOutStationOrReplacementUserDecrement(OutStationOrReplacementUserDecrement.size());

        // 自增：流失客户数-企微活码来源用户数-群活码来源用户数
        Set<String> decrementUserIdSet = new HashSet<>();
        decrementUserIdSet.addAll(contactUserMapperUserIdList);
        decrementUserIdSet.addAll(memberInfoEntityUserIdList);
        Collection<String> selfUserDecrement = CollectionUtils.subtract(
                CollectionUtils.subtract(
                        decrementUserIdSet,
                        corpWxDynamicCodeUserIdList
                ), groupDynamicCodeUserIdList
        );
        esUserDataSnapshot.setSelfUserDecrement(selfUserDecrement.size());
    }

    /**
     * 设置增量客户数
     * @param esUserDataSnapshot
     * @param corpId
     * @param startOfYesterday
     * @param endOfYesterday
     * @param countActivityUserByCorpId
     * @param activityUserDecrement
     */
    public void setIncrement(EsUserDataSnapshot esUserDataSnapshot, String corpId, Date startOfYesterday, Date endOfYesterday, Map<String, Set<String>> countActivityUserByCorpId, Map<String, Set<String>> activityUserDecrement) {
        ContactUserExample contactUserExample = new ContactUserExample();
        contactUserExample.createCriteria()
                .andCorpIdEqualTo(corpId)
                // 统计增量时不去除当天流失的客户
//                .andStatusEqualTo(1)
                .andAddTimeBetween(startOfYesterday, endOfYesterday);
        List<String> contractUserIdList = contactUserMapper.selectByExample(contactUserExample)
                .stream()
                .map(ContactUser::getExternalUserId)
                .distinct()
                .collect(Collectors.toList());

        MemberInfoEntityExample memberInfoEntityExample = new MemberInfoEntityExample();
        memberInfoEntityExample.createCriteria()
                .andDeletedEqualTo(false)
                .andCorpIdEqualTo(corpId)
//                .andStatusEqualTo((byte) 0)
                .andMemberTypeEqualTo((byte) 2)
                .andAddTimeBetween(startOfYesterday, endOfYesterday);
        List<String> memberInfoUserIdList = memberInfoEntityMapper.selectByExample(memberInfoEntityExample)
                .stream()
                .map(MemberInfoEntity::getGroupMemberId)
                .distinct()
                .collect(Collectors.toList());

        esUserDataSnapshot.setCustomerIncrement(getDistinctCount(contractUserIdList, memberInfoUserIdList));
        esUserDataSnapshot.setFriendIncrement(contractUserIdList.size());
        esUserDataSnapshot.setInGroupUserIncrement(memberInfoUserIdList.size());
        List<String> innerStationUnionIdList = dashBoardESReadDomainService.selectInnerStationUserIncrement(corpId, startOfYesterday, endOfYesterday);
        esUserDataSnapshot.setInnerStationUserIncrement(innerStationUnionIdList.size());
        Set<String> activityUnionIdSet = countActivityUserByCorpId.getOrDefault(corpId, new HashSet<>());
        esUserDataSnapshot.setActivityUserIncrement(activityUnionIdSet.size());

        // 企微活码
        contactUserExample.clear();
        contactUserExample.createCriteria()
                .andStateIsNotNull()
                .andCorpIdEqualTo(corpId)
//                .andStatusEqualTo(1)
                .andAddTimeBetween(startOfYesterday, endOfYesterday);
        List<String> corpWxDynamicCodeUserIdList = contactUserMapper.selectByExample(contactUserExample)
                .stream()
                .map(ContactUser::getExternalUserId)
                .distinct()
                .collect(Collectors.toList());
        esUserDataSnapshot.setCorpWxDynamicCodeUserIncrement(corpWxDynamicCodeUserIdList.size());

        // 群活码
        memberInfoEntityExample.clear();
        memberInfoEntityExample.createCriteria()
                .andStateIsNotNull()
                .andCorpIdEqualTo(corpId)
                .andMemberTypeEqualTo((byte) 2)
//                .andStatusEqualTo((byte) 0)
                .andAddTimeBetween(startOfYesterday, endOfYesterday)
                .andDeletedEqualTo(false);
        List<String> groupDynamicLiveCodeUserIdList = memberInfoEntityMapper.selectByExample(memberInfoEntityExample)
                .stream()
                .map(MemberInfoEntity::getGroupMemberId)
                .distinct()
                .collect(Collectors.toList());
        esUserDataSnapshot.setGroupDynamicLiveCodeUserIncrement(groupDynamicLiveCodeUserIdList.size());

        String appId = corpAppConfigRepository.getAppIdByCorpId(corpId);
        Set<String> innerStationUserIncrementUserIdSet = externalContactBaseInfoDomainService.queryBaseInfoByUnionIdList(appId, innerStationUnionIdList)
                .stream()
                .map(ExternalContactBaseInfo::getExternalUserId)
                .collect(Collectors.toSet());
        Set<String> activityUserIncrementUserIdSet = externalContactBaseInfoDomainService.queryBaseInfoByUnionIdList(appId, new ArrayList<>(activityUnionIdSet))
                .stream()
                .map(ExternalContactBaseInfo::getExternalUserId)
                .collect(Collectors.toSet());
        // 站外&置换：企微活码来源用户数+群活码来源用户数-站内来源用户数-活动来源用户数
        Collection<String> OutStationOrReplacementUserIncrement = CollectionUtils.subtract(
                CollectionUtils.subtract(
                        CollectionUtils.union(corpWxDynamicCodeUserIdList, groupDynamicLiveCodeUserIdList),
                        innerStationUserIncrementUserIdSet
                ),
                activityUserIncrementUserIdSet
        );
        esUserDataSnapshot.setOutStationOrReplacementUserIncrement(OutStationOrReplacementUserIncrement.size());

        // 自增：新增客户数-企微活码来源用户数-群活码来源用户数
        Set<String> incrementUserIdSet = new HashSet<>();
        incrementUserIdSet.addAll(contractUserIdList);
        incrementUserIdSet.addAll(memberInfoUserIdList);
        Collection<String> selfUserIncrement = CollectionUtils.subtract(
                CollectionUtils.subtract(
                        incrementUserIdSet,
                        corpWxDynamicCodeUserIdList
                ), groupDynamicLiveCodeUserIdList
        );
        esUserDataSnapshot.setSelfUserIncrement(selfUserIncrement.size());
    }

    /**
     * 设置净增客户数&用户留存率
     * @param esUserDataSnapshot
     * @param corpId
     */
    private void setNetAndRetentionRates(EsUserDataSnapshot esUserDataSnapshot, String corpId) {
        esUserDataSnapshot.setNetCustomerIncrement(esUserDataSnapshot.getCustomerIncrement()- esUserDataSnapshot.getCustomerDecrement());
        // 获取 7 天前的日期时间，由于计算快照是计算昨天的快照，还需要加1天
        Date startOfSevenDay=getStartOfDay(8);
        Date endOfSevenDay=getEndOfDay(8);
        ContactUserExample contactUserExampleOne=getRetentionContactUserExample(startOfSevenDay,endOfSevenDay,corpId);
        ContactUserExample contactUserExampleTwo=getTotalContactUserExample(startOfSevenDay,endOfSevenDay,corpId);

        MemberInfoEntityExample memberInfoEntityExampleOne=getMemberInfoEntityRetentionExample(startOfSevenDay,endOfSevenDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleTwo=getMemberInfoEntityTotalExample(startOfSevenDay,endOfSevenDay,corpId);

        esUserDataSnapshot.setWeeklyUserRetentionRate(getRetentionRate(contactUserMapper.selectByExample(contactUserExampleOne).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                contactUserMapper.selectByExample(contactUserExampleTwo).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleOne).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleTwo).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList())));
        // 获取 30 天前的日期时间，由于计算快照是计算昨天的快照，还需要加1天
        Date startOfThirtyDay=getStartOfDay(31);
        Date endOfThirtyDay=getEndOfDay(31);
        ContactUserExample contactUserExampleThree=getRetentionContactUserExample(startOfThirtyDay,endOfThirtyDay,corpId);
        ContactUserExample contactUserExampleFour=getTotalContactUserExample(startOfThirtyDay,endOfThirtyDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleThree=getMemberInfoEntityRetentionExample(startOfThirtyDay,endOfThirtyDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleFour=getMemberInfoEntityTotalExample(startOfThirtyDay,endOfThirtyDay,corpId);
        esUserDataSnapshot.setMonthlyUserRetentionRate(getRetentionRate(contactUserMapper.selectByExample(contactUserExampleThree).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                contactUserMapper.selectByExample(contactUserExampleFour).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleThree).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleFour).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList())));
    }

    private void setRetentionRates(EsUserDataSnapshot esUserDataSnapshot, String corpId) {
        // 获取 7 天前的日期时间
        Date startOfSevenDay=getStartOfDay(7);
        Date endOfSevenDay=getEndOfDay(7);
        ContactUserExample contactUserExampleOne=getRetentionContactUserExample(startOfSevenDay,endOfSevenDay,corpId);
        ContactUserExample contactUserExampleTwo=getTotalContactUserExample(startOfSevenDay,endOfSevenDay,corpId);

        MemberInfoEntityExample memberInfoEntityExampleOne=getMemberInfoEntityRetentionExample(startOfSevenDay,endOfSevenDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleTwo=getMemberInfoEntityTotalExample(startOfSevenDay,endOfSevenDay,corpId);

        esUserDataSnapshot.setWeeklyUserRetentionRate(getRetentionRate(contactUserMapper.selectByExample(contactUserExampleOne).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                contactUserMapper.selectByExample(contactUserExampleTwo).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleOne).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleTwo).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList())));
        // 获取 30 天前的日期时间
        Date startOfThirtyDay=getStartOfDay(30);
        Date endOfThirtyDay=getEndOfDay(30);
        ContactUserExample contactUserExampleThree=getRetentionContactUserExample(startOfThirtyDay,endOfThirtyDay,corpId);
        ContactUserExample contactUserExampleFour=getTotalContactUserExample(startOfThirtyDay,endOfThirtyDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleThree=getMemberInfoEntityRetentionExample(startOfThirtyDay,endOfThirtyDay,corpId);
        MemberInfoEntityExample memberInfoEntityExampleFour=getMemberInfoEntityTotalExample(startOfThirtyDay,endOfThirtyDay,corpId);
        esUserDataSnapshot.setMonthlyUserRetentionRate(getRetentionRate(contactUserMapper.selectByExample(contactUserExampleThree).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                contactUserMapper.selectByExample(contactUserExampleFour).stream().map(ContactUser::getExternalUserId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleThree).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList()),
                memberInfoEntityMapper.selectByExample(memberInfoEntityExampleFour).stream().map(MemberInfoEntity::getGroupMemberId).collect(Collectors.toList())));
    }

    private MemberInfoEntityExample getMemberInfoEntityTotalExample(Date start,Date end, String corpId) {
        MemberInfoEntityExample memberInfoEntityExample = new MemberInfoEntityExample();
        memberInfoEntityExample.createCriteria().andDeletedEqualTo(false).andEnterTimeBetween(start,end).andCorpIdEqualTo(corpId).andMemberTypeEqualTo((byte) 2);
        return memberInfoEntityExample;
    }

    private MemberInfoEntityExample getMemberInfoEntityRetentionExample(Date start,Date end, String corpId) {
        MemberInfoEntityExample memberInfoEntityExample=new MemberInfoEntityExample();
        memberInfoEntityExample.createCriteria().andDeletedEqualTo(false).andEnterTimeBetween(start,end).andStatusEqualTo((byte) 0).andMemberTypeEqualTo((byte) 2).andCorpIdEqualTo(corpId);
        return memberInfoEntityExample;
    }

    private ContactUserExample getTotalContactUserExample(Date start,Date end, String corpId) {

        ContactUserExample contactUserExample = new ContactUserExample();
        contactUserExample.createCriteria().andCorpIdEqualTo(corpId).andAddTimeBetween(start, end);
        return contactUserExample;
    }

    private ContactUserExample getRetentionContactUserExample(Date start,Date end,String corpId) {
        ContactUserExample contactUserExample=new ContactUserExample();
        contactUserExample.createCriteria().andStatusEqualTo(1).andCorpIdEqualTo(corpId).andAddTimeBetween(start,end);
        return contactUserExample;
    }

    private Map<String, Set<String>> getActivityUserIncrement(Date startOfYesterday, Date endOfYesterday) {
        Map<String,Set<String>> countActivityUserByCorpId=new HashMap<>();
        //所有主体的新增活动来源用户数
        GroupFissionInvitationRecordExample groupFissionInvitationRecordExample=new GroupFissionInvitationRecordExample();
        groupFissionInvitationRecordExample.createCriteria().andInviteeUnionIdIsNotNull().andNewUserEqualTo(true).andCreateTimeBetween(startOfYesterday, endOfYesterday);
        Map<Long, List<String>> activityIdToUnionIdsMap = groupFissionInvitationRecordMapper.selectByExample(groupFissionInvitationRecordExample).stream()
                .collect(Collectors.groupingBy(GroupFissionInvitationRecord::getActivityId, Collectors.mapping(GroupFissionInvitationRecord::getInviteeUnionId, Collectors.toList())));
        activityIdToUnionIdsMap.forEach((activityId, inviteeUnionIds) -> {
            GroupFissionActivityExample groupFissionActivityExample=new GroupFissionActivityExample();
            groupFissionActivityExample.createCriteria().andIdEqualTo(activityId);
            List<String> appIds = groupFissionActivityMapper.selectByExample(groupFissionActivityExample).stream().map(GroupFissionActivity::getAppId).collect(Collectors.toList());
            appIds.forEach(s -> {
                String corpId = corpAppConfigRepository.getCorpIdByAppId(s);
                Set<String> currentSet = countActivityUserByCorpId.getOrDefault(corpId, new HashSet<>());
                currentSet.addAll(inviteeUnionIds);
                countActivityUserByCorpId.put(corpId, currentSet);

            });
        });
            return countActivityUserByCorpId;

    }

    private Map<String, Set<String>> getActivityUserDecrement(Date startOfYesterday, Date endOfYesterday) {
        Map<String, Set<String>> activityUserDecrement = new HashMap<>();
        GroupFissionInvitationRecordExample groupFissionInvitationRecordExample = new GroupFissionInvitationRecordExample();
        groupFissionInvitationRecordExample.createCriteria().andInviteeUnionIdIsNotNull().andInGroupStatusEqualTo(false).andUpdateTimeBetween(startOfYesterday, endOfYesterday);
        Map<Long, List<String>> activityIdToUnionIdsMap = groupFissionInvitationRecordMapper.selectByExample(groupFissionInvitationRecordExample).stream()
                .collect(Collectors.groupingBy(GroupFissionInvitationRecord::getActivityId, Collectors.mapping(GroupFissionInvitationRecord::getInviteeUnionId, Collectors.toList())));
        activityIdToUnionIdsMap.forEach((activityId, inviteeUnionIds) -> {
            GroupFissionActivityExample groupFissionActivityExample = new GroupFissionActivityExample();
            groupFissionActivityExample.createCriteria().andIdEqualTo(activityId);
            List<String> appIds = groupFissionActivityMapper.selectByExample(groupFissionActivityExample).stream().map(GroupFissionActivity::getAppId).collect(Collectors.toList());
            appIds.forEach(s -> {
                String corpId = corpAppConfigRepository.getCorpIdByAppId(s);
                // 先获取当前corpId对应的Set，如果不存在则创建一个新的HashSet
                Set<String> currentSet = activityUserDecrement.getOrDefault(corpId, new HashSet<>());
                // 将inviteeUnionIds添加到Set中
                currentSet.addAll(inviteeUnionIds);
                // 将更新后的Set放回Map中
                activityUserDecrement.put(corpId, currentSet);
            });
        });
        return activityUserDecrement;
    }


    public static Double getRetentionRate(List<String> retainedFriends, List<String> totalFriends,
                                          List<String> retainedGroupMembers, List<String> totalGroupMembers) {
        Long retainedCount = getDistinctCount(retainedFriends, retainedGroupMembers);
        Long totalCount = getDistinctCount(totalGroupMembers, totalFriends);
        if (totalCount == 0L) {
            return 0.0;
        }
        return ((double) retainedCount) / totalCount;
    }
}











