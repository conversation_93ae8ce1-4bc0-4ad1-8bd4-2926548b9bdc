package com.sankuai.scrm.core.service.activity.fission.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dz.srcm.activity.fission.enums.AwardTypeEnum;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import com.sankuai.dz.srcm.message.task.dto.MsgContentADTO;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionPrize;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionPrizeMapper;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroupFissionPrizeServiceTest {

    @Mock
    private GroupFissionPrizeMapper groupFissionPrizeMapper;

    @InjectMocks
    private GroupFissionPrizeService groupFissionPrizeService;

    /**
     * 测试输入参数为空时返回false
     */
    @Test
    public void testSaveGroupFissionPrizeWithEmptyInput() throws Throwable {
        // arrange
        List<RewardInfoRequest> emptyList = Collections.emptyList();
        Long activityId = 1L;
        // act
        boolean result1 = groupFissionPrizeService.saveGroupFissionPrize(emptyList, activityId);
        boolean result2 = groupFissionPrizeService.saveGroupFissionPrize(null, activityId);
        boolean result3 = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(createBasicRewardInfoRequest(1), createBasicRewardInfoRequest(2), createBasicRewardInfoRequest(3), createBasicRewardInfoRequest(4)), activityId);
        boolean result4 = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(createBasicRewardInfoRequest(1)), null);
        // assert
        assertFalse(result1);
        assertFalse(result2);
        assertFalse(result3);
        assertFalse(result4);
    }

    /**
     * 测试奖品数量为null时设置默认值-1
     */
    @Test
    public void testSaveGroupFissionPrizeWithNullRewardSize() throws Throwable {
        // arrange
        RewardInfoRequest request = createBasicRewardInfoRequest(1);
        request.setRewardSize(null);
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(1);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request), 1L);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).batchInsert(argThat(list -> list.get(0).getPrizeNum() == -1));
    }

    /**
     * 测试发放类型为小助手时补充额外信息
     */
    @Test
    public void testSaveGroupFissionPrizeWithContactType() throws Throwable {
        // arrange
        RewardInfoRequest request = createBasicRewardInfoRequest(1);
        request.setReceiveType(AwardTypeEnum.CONTACT.getCode());
        request.setAssistantAccount("testAccount");
        request.setAssistantQrCode("testQrCode");
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(1);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request), 1L);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).batchInsert(argThat(list -> "testAccount".equals(list.get(0).getAssistantAccount()) && "testQrCode".equals(list.get(0).getAssistantQrCode())));
    }

    /**
     * 测试批量插入成功
     */
    @Test
    public void testSaveGroupFissionPrizeSuccess() throws Throwable {
        // arrange
        RewardInfoRequest request1 = createBasicRewardInfoRequest(1);
        RewardInfoRequest request2 = createBasicRewardInfoRequest(2);
        request2.setReceiveType(AwardTypeEnum.GOODS.getCode());
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(2);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request1, request2), 1L);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).batchInsert(argThat(list -> list.size() == 2 && list.get(0).getStage() == 1 && list.get(1).getStage() == 2));
    }

    /**
     * 测试批量插入失败
     */
    @Test
    public void testSaveGroupFissionPrizeFailure() throws Throwable {
        // arrange
        RewardInfoRequest request = createBasicRewardInfoRequest(1);
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(0);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request), 1L);
        // assert
        assertFalse(result);
    }

    /**
     * 测试按stage排序逻辑
     */
    @Test
    public void testSaveGroupFissionPrizeWithSorting() throws Throwable {
        // arrange
        RewardInfoRequest request1 = createBasicRewardInfoRequest(2);
        RewardInfoRequest request2 = createBasicRewardInfoRequest(1);
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(2);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request1, request2), 1L);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).batchInsert(argThat(list -> list.get(0).getStage() == 1 && list.get(1).getStage() == 2));
    }

    /**
     * 测试msgContent序列化
     */
    @Test
    public void testSaveGroupFissionPrizeWithMsgContent() throws Throwable {
        // arrange
        RewardInfoRequest request = createBasicRewardInfoRequest(1);
        request.setMsgContentADTOList(Arrays.asList(new MsgContentADTO()));
        when(groupFissionPrizeMapper.batchInsert(anyList())).thenReturn(1);
        // act
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(Arrays.asList(request), 1L);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).batchInsert(argThat(list -> list.get(0).getMsgContent() != null));
    }

    private RewardInfoRequest createBasicRewardInfoRequest(int stage) {
        RewardInfoRequest request = new RewardInfoRequest();
        request.setStage(stage);
        request.setInvitationNum(10);
        request.setPriceName("test" + stage);
        request.setPrice(100);
        request.setPriceDecimal(BigDecimal.valueOf(100.0));
        request.setReceiveType(AwardTypeEnum.COUPON.getCode());
        request.setRewardType(1);
        request.setRewardSize(100);
        request.setPriceImage("image" + stage);
        return request;
    }
}
