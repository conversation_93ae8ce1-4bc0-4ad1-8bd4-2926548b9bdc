package com.sankuai.scrm.core.service.message.push.enums;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Set;

@Getter
public enum MsgPushDetailStatus {
    PREPARING(1, "准备中"),
    PENDING(2, "待发送"),
    SENDING(3, "发送中"),
    SUCCESS(4, "全部成功"),
    PARTIAL_SUCCESS(5, "部分成功"),
    FAILED(6, "失败"),
    UNKNOWN(7, "未知");

    public static final Set<Integer> NOT_SEND_STATUS = Sets.newHashSet(PREPARING.getCode(), PENDING.getCode(), SENDING.getCode());

    private final int code;

    private final String desc;

    MsgPushDetailStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
