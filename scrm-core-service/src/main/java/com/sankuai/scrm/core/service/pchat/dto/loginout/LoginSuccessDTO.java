package com.sankuai.scrm.core.service.pchat.dto.loginout;

import com.sankuai.scrm.core.service.pchat.dto.WxUserInfoDTO;
import lombok.Data;

/**
 * 1003
 *
 * @Description 登录成功回调
 * <AUTHOR>
 * @Create On 2023/11/7 18:51
 * @Version v1.0.0
 */

@Data
public class LoginSuccessDTO extends WxUserInfoDTO {

    /**
     * 商家业务关联流水
     */
    private String vcRelationSerialNo;

    /**
     * 用户二维码
     */
    private String vcPersonQRCode;

    /**
     * x
     */
    private Integer nTrueName;

    /**
     * 登录设备编号
     */
    private String vcDevice;

    /**
     * 是否可入大群（true：可入大群 false：不可入
     */
    private Boolean IsAllowJoinBigChatRoom;
}
