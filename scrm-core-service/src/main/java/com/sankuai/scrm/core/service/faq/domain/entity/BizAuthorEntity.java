package com.sankuai.scrm.core.service.faq.domain.entity;

import com.sankuai.scrm.core.service.faq.domain.vo.AuthorBizType;
import com.sankuai.scrm.core.service.faq.domain.vo.AuthorUserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> on 2022/12/13 4:45 PM
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BizAuthorEntity implements Serializable {
    /**
     * 业务ID
     */
    private String bizId;

    /**
     * {@link AuthorBizType}
     */
    private Integer bizType;

    /**
     * 用户ID
     */
    private String userId;
    /**
     * {@link AuthorUserType}
     */
    private Integer userType;
}
