package com.sankuai.scrm.core.service.dashboard.dal.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EsTransactionDataSnapshot {
    private String id;
    private String corpId;
    private String versionTime;
    private long transactionOrderCount;
    private String gtv;
    private String actualGtv;
    private String writeOffGtv;
    private String actualCheckGtv;
}
