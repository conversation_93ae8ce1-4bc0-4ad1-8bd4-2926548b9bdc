package com.sankuai.scrm.core.service.pchat.service.superadmin;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.request.scrm.superadmin.GroupListNoWebcastRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.superadmin.GroupListResponse;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.service.ScrmLoginService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/25 15:25
 * @Version v1.0.0
 */
public class ScrmGroupSuperManageServiceImplTest extends BaseMockTest {

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;
    @Mock
    private ScrmPersonalWxGroupInfoEntityMapper personalWxGroupInfoEntityMapper;
    @Mock
    private ScrmLoginService scrmLoginService;

    @InjectMocks
    private ScrmGroupSuperManageServiceImpl scrmGroupSuperManageService;
    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Before
    public void setUp() {
        Mockito.when(scrmLoginService.getCreator()).thenReturn("testUser");
    }

    /**
     * 测试groupList方法，当查询结果为空时
     */
    @Test
    public void testGroupListWhenResultIsEmpty() {
        GroupListNoWebcastRequest request = new GroupListNoWebcastRequest();
        request.setGroupName("testGroup");

        when(personalWxGroupInfoEntityMapper.countByExample(any())).thenReturn(0L);

        PageRemoteResponse<GroupListResponse> response = scrmGroupSuperManageService.groupList(request);

        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        assertEquals(0, response.getTotalHit());
        assertTrue(response.isEnd());
    }

    /**
     * 测试groupList方法，当查询结果非空但数据列表为空时
     */
    @Test
    public void testGroupListWhenDataListIsEmpty() {
        GroupListNoWebcastRequest request = new GroupListNoWebcastRequest();
        request.setGroupName("testGroup");

        when(personalWxGroupInfoEntityMapper.countByExample(any())).thenReturn(1L);
        when(personalWxGroupInfoEntityMapper.selectByExampleWithBLOBs(any())).thenReturn(new ArrayList<>());

        PageRemoteResponse<GroupListResponse> response = scrmGroupSuperManageService.groupList(request);

        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        assertEquals(1, response.getTotalHit());
        assertTrue(response.isEnd());
    }

    /**
     * 测试groupList方法，当查询结果非空且数据列表非空时
     */
    @Test
    public void testGroupListWhenDataListIsNotEmpty() {


        Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.pchat.derived.group.switch", false);
        GroupListNoWebcastRequest request = new GroupListNoWebcastRequest();
        request.setGroupName("testGroup");

        List<ScrmPersonalWxGroupInfoEntity> entities = Lists.newArrayList(new ScrmPersonalWxGroupInfoEntity());
        when(personalWxGroupInfoEntityMapper.countByExample(any())).thenReturn(1L);
        when(personalWxGroupInfoEntityMapper.selectByExampleWithBLOBs(any())).thenReturn(entities);

        PageRemoteResponse<GroupListResponse> response = scrmGroupSuperManageService.groupList(request);

        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
        assertEquals(1, response.getTotalHit());
    }
}
