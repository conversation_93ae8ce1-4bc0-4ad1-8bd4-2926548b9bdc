package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_Wx_SensitiveContent
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmWxSensitiveContent {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: rule_name
     *   说明: 规则名
     */
    private String ruleName;

    /**
     *   字段: user_type
     *   说明: 机器人类型 0.全部机器人，1.平台号，2.扫码号，3.托管号
     */
    private Integer userType;

    /**
     *   字段: all_robot
     *   说明: 是否影响全部机器人 1-全部 0-不为全部（注：当allrobot不为全部时，才返回userType对应的枚举值）
     */
    private Integer allRobot;

    /**
     *   字段: robot_serial_nos
     *   说明: 影响机器人的序列号集合
     */
    private String robotSerialNos;

    /**
     *   字段: keyword_type
     *   说明: 1-私聊/群聊/群公告敏感词 2-设置群内昵称敏感词 3-修改群名称敏感词 4-修改个性签名敏感词 5-修改机器人昵称敏感词 6-机器人主动添加好友 7-强加人接口
     */
    private Integer keywordType;

    /**
     *   字段: msg_type
     *   说明: 消息类型 2001: 文本  2002 ：图片2013: 小程序 2005：链接(keywordType为1时可用)
     */
    private Integer msgType;

    /**
     *   字段: word
     *   说明: 敏感词
     */
    private String word;

    /**
     *   字段: expire_time
     *   说明: 过期时间
     */
    private Date expireTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: app_id
     *   说明: app_id，用于区分业务
     */
    private String appId;
}