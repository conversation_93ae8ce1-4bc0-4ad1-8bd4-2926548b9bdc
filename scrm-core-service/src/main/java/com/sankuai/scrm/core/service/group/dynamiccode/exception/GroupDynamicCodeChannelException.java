package com.sankuai.scrm.core.service.group.dynamiccode.exception;

/**
 * 企微群活码的渠道来源相关异常
 */
public class GroupDynamicCodeChannelException extends RuntimeException {
    public GroupDynamicCodeChannelException() {
        super();
    }

    public GroupDynamicCodeChannelException(String message) {
        super(message);
    }

    public GroupDynamicCodeChannelException(String message, Throwable cause) {
        super(message, cause);
    }

    public GroupDynamicCodeChannelException(Throwable cause) {
        super(cause);
    }

    protected GroupDynamicCodeChannelException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
