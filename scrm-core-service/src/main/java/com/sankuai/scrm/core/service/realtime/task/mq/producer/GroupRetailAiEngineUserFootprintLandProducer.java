package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/5/7
 */
@Slf4j
@Component
public class GroupRetailAiEngineUserFootprintLandProducer implements InitializingBean {

    /**
     * producer实例请在业务初始化的时候创建好.
     * producer资源创建好后，再开放业务流量.
     * 请不要频繁创建producer实例：即不要发送一条消息都创建一次producer。一是性能不好，二是服务端会限制连接次数影响消息发送。发送完毕后，请进行close。
     * 注意：服务端对单ip创建相同主题的生产者实例数有限制，超过100个拒绝创建.
     * */
    private static IProducerProcessor producer;

    public void sendMessage(GroupRetailAiEngineMqMessageDTO executeMessage){
        if(executeMessage==null){
            return;
        }

        int retryTimes = 3;
        for (int i = 0; i < retryTimes; i++) {
            try {
                ProducerResult result = producer.sendMessage(JsonUtils.toStr(executeMessage));;
                if (result != null && ProducerStatus.SEND_OK == result.getProducerStatus()) {
                    return;
                }
            } catch (Exception e) {
                log.error("GroupRetailAiEngineUserFootprintLandProducer fail, Object:{}", executeMessage, e);
            }
        }
        log.error("GroupRetailAiEngineUserFootprintLandProducer fail after retry for {} times, Object:{}", retryTimes, executeMessage);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildProduceFactory(properties, "scrm.group.retail.ai.engine.user.footprint.land");
    }
}
