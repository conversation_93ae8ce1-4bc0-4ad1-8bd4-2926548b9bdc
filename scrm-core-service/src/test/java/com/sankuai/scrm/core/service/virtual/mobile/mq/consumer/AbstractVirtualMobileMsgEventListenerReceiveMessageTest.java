package com.sankuai.scrm.core.service.virtual.mobile.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileMaskReqTo;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileMaskResTo;
import com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService;
import com.sankuai.scrm.core.service.virtual.mobile.mq.event.MobileToken;
import com.sankuai.scrm.core.service.virtual.mobile.mq.event.VirtualMobileMsgByTokenEvent;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class AbstractVirtualMobileMsgEventListenerReceiveMessageTest {

    @Mock
    private EduDaxiangSendService eduDaxiangSendService;

    @Mock
    private TokenAccessThriftService.Iface tokenAccessThriftService;

    private TestableAbstractVirtualMobileMsgEventListener listener;

    @BeforeEach
    public void setUp() {
        listener = new TestableAbstractVirtualMobileMsgEventListener();
        ReflectionTestUtils.setField(listener, "eduDaxiangSendService", eduDaxiangSendService);
        ReflectionTestUtils.setField(listener, "tokenAccessThriftService", tokenAccessThriftService);
    }

    private VirtualMobileMsgByTokenEvent createValidEvent() {
        VirtualMobileMsgByTokenEvent event = new VirtualMobileMsgByTokenEvent();
        event.setRequestId("req123");
        event.setOrderId("order123");
        event.setAreaCode("86");
        MobileToken receiver = new MobileToken();
        receiver.setToken("receiverToken");
        receiver.setEncrypt("receiverEncrypt");
        event.setReceiver(receiver);
        MobileToken sender = new MobileToken();
        sender.setToken("senderToken");
        sender.setEncrypt("senderEncrypt");
        event.setSender(sender);
        event.setSenderShow("138****1234");
        event.setReceiverShow("139****5678");
        event.setSmsContent("test message");
        event.setTransferTime(System.currentTimeMillis());
        return event;
    }

    /**
     * 测试消息体为空的情况
     */
    @Test
    public void testReceiveMessageWithNullMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = listener.receiveMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(eduDaxiangSendService);
    }

    /**
     * 测试消息体为空字符串的情况
     */
    @Test
    public void testReceiveMessageWithBlankMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = listener.receiveMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(eduDaxiangSendService);
    }

    /**
     * 测试JSON解析失败返回null的情况
     */
    @Test
    public void testReceiveMessageWithInvalidJsonContent() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "invalid json");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = listener.receiveMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(eduDaxiangSendService);
    }

    /**
     * 测试短信内容为空的情况
     */
    @Test
    public void testReceiveMessageWithBlankSmsContent() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = listener.receiveMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(eduDaxiangSendService);
    }

    /**
     * 测试关键词不匹配的情况
     */
    @Test
    public void testReceiveMessageWithKeywordNotMatched() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("normal message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(eduDaxiangSendService);
        }
    }

    /**
     * 测试关键词匹配但没有通知配置的情况
     */
    @Test
    public void testReceiveMessageWithKeywordMatchedButNoNotifyConfig() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("urgent message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            lionMock.when(() -> Lion.get("baby-customer-service.operatorhelper.virtual.sms.daxiang.notify.config")).thenReturn("");
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(eduDaxiangSendService);
        }
    }

    /**
     * 测试成功发送通知的情况
     */
    @Test
    public void testReceiveMessageWithSuccessfulNotification() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("urgent message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            lionMock.when(() -> Lion.get("baby-customer-service.operatorhelper.virtual.sms.daxiang.notify.config")).thenReturn("{\"testApp\":[\"user1\",\"user2\"]}");
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(eduDaxiangSendService).sendTextMsg(anyString(), eq(Arrays.asList("user1", "user2")));
        }
    }

    /**
     * 测试处理过程中发生异常的情况 - 模拟eduDaxiangSendService抛出异常
     */
    @Test
    public void testReceiveMessageWithException() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("urgent message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            lionMock.when(() -> Lion.get("baby-customer-service.operatorhelper.virtual.sms.daxiang.notify.config")).thenReturn("{\"testApp\":[\"user1\",\"user2\"]}");
            // 模拟发送消息时抛出异常
            doThrow(new RuntimeException("Send message failed")).when(eduDaxiangSendService).sendTextMsg(anyString(), anyList());
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(eduDaxiangSendService).sendTextMsg(anyString(), eq(Arrays.asList("user1", "user2")));
        }
    }

    /**
     * 测试没有关键词配置的情况
     */
    @Test
    public void testReceiveMessageWithNoKeywordConfig() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("any message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Collections.emptyList());
            lionMock.when(() -> Lion.get("baby-customer-service.operatorhelper.virtual.sms.daxiang.notify.config")).thenReturn("{\"testApp\":[\"user1\",\"user2\"]}");
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(eduDaxiangSendService).sendTextMsg(anyString(), eq(Arrays.asList("user1", "user2")));
        }
    }

    /**
     * 测试通知配置为无效JSON的情况
     */
    @Test
    public void testReceiveMessageWithInvalidNotifyConfig() throws Throwable {
        // arrange
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("urgent message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            lionMock.when(() -> Lion.get("baby-customer-service.operatorhelper.virtual.sms.daxiang.notify.config")).thenReturn("invalid json");
            // act
            ConsumeStatus result = listener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(eduDaxiangSendService);
        }
    }

    /**
     * 测试AppId为空的情况
     */
    @Test
    public void testReceiveMessageWithEmptyAppId() throws Throwable {
        // arrange
        TestableAbstractVirtualMobileMsgEventListener emptyAppIdListener = new TestableAbstractVirtualMobileMsgEventListener() {

            @Override
            protected String getAppId() {
                return "";
            }
        };
        ReflectionTestUtils.setField(emptyAppIdListener, "eduDaxiangSendService", eduDaxiangSendService);
        ReflectionTestUtils.setField(emptyAppIdListener, "tokenAccessThriftService", tokenAccessThriftService);
        VirtualMobileMsgByTokenEvent event = createValidEvent();
        event.setSmsContent("urgent message");
        String messageContent = JSONObject.toJSONString(event);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageContent);
        MessagetContext context = new MessagetContext();
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList("baby-customer-service.operatorhelper.virtual.sms.keyword", String.class)).thenReturn(Arrays.asList("urgent", "important"));
            // act
            ConsumeStatus result = emptyAppIdListener.receiveMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(eduDaxiangSendService);
        }
    }

    // 测试用的具体实现类
    private static class TestableAbstractVirtualMobileMsgEventListener extends AbstractVirtualMobileMsgEventListener {

        @Override
        protected void init() throws Exception {
            // 测试实现
        }

        @Override
        protected String getAppId() {
            return "testApp";
        }

        @Override
        protected String getBizName() {
            return "测试业务";
        }
    }
}
