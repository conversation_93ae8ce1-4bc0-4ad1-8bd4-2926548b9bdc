package com.sankuai.scrm.core.service.pchat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import com.sankuai.dz.srcm.pchat.request.restriction.RestrictionAddUserRequest;
import com.sankuai.dz.srcm.pchat.request.restriction.RestrictionDeleteUserRequest;
import com.sankuai.dz.srcm.pchat.request.restriction.RestrictionUserDetailRequest;
import com.sankuai.dz.srcm.pchat.request.restriction.RestrictionUserSearchRequest;
import com.sankuai.dz.srcm.pchat.response.restriction.*;
import com.sankuai.dz.srcm.pchat.service.ScrmRestrictionIdentificationService;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmRestrictionUser;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionIdentificationDomainService;
import com.sankuai.scrm.core.service.pchat.enums.RestrictionIdentityTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.RestrictionReasonTypeEnum;
import com.sankuai.scrm.core.service.pchat.service.superadmin.SuperAdminSsoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2024/07/18 17:01
 */
@Slf4j
@MdpPigeonServer
public class ScrmRestrictionIdentificationServiceImpl implements ScrmRestrictionIdentificationService {

    @Resource
    private ScrmRestrictionIdentificationDomainService scrmRestrictionIdentificationDomainService;
    @Resource
    private ScrmPersonalWxGroupManageDomainService scrmPersonalWxGroupManageDomainService;

    @Override
    public PageRemoteResponse<RestrictionBlacklistResponse> getRestrictionBlacklist(PageRequest pageRequest) {
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("getRestrictionBlacklist param={},loginUser={}", pageRequest, loginUser);
        PagerList<ScrmRestrictionUser> restrictedUserPagerList = scrmRestrictionIdentificationDomainService.getRestrictionBlacklistUsers(pageRequest.getPageNo(), pageRequest.getPageSize());

        return PageRemoteResponse.success(
                restrictedUserPagerList.getData().stream().map(
                        e -> RestrictionBlacklistResponse.builder()
                                .wxId(e.getWxId())
                                .restrictedReason(RestrictionReasonTypeEnum.fromCode(e.getRestrictedReasonType()).getDesc())
                                .latestJoinGroupTime(
                                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(e.getLatestJoinGroupTime()))
                                .comment(e.getCommentInfo())
                                .build()).collect(Collectors.toList()),
                restrictedUserPagerList.getTotal(),
                restrictedUserPagerList.isEnd());
    }

    @Override
    public PageRemoteResponse<RestrictionWhitelistResponse> getRestrictionWhitelist(PageRequest pageRequest) {
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("getRestrictionWhitelist param={},loginUser={}", pageRequest, loginUser);
        PagerList<ScrmRestrictionUser> privilegedUserPagerList = scrmRestrictionIdentificationDomainService.getRestrictionWhitelistUsers(pageRequest.getPageNo(), pageRequest.getPageSize());

        return PageRemoteResponse.success(
                privilegedUserPagerList.getData().stream().map(
                        e -> RestrictionWhitelistResponse.builder()
                                .wxId(e.getWxId())
                                .nickname(e.getNickname())
                                .avatar(e.getAvatar())
                                .comment(e.getCommentInfo())
                                .build()).collect(Collectors.toList()),
                privilegedUserPagerList.getTotal(),
                privilegedUserPagerList.isEnd());
    }

    @Override
    public RemoteResponse<RestrictedUserDetailResponse> getRestrictedUserDetail(RestrictionUserDetailRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return RemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("getRestrictedUserDetail param={},loginUser={}", request, loginUser);

        PagerList<ScrmPersonalWxGroupMemberInfoEntity> joinGroupPagerList = scrmRestrictionIdentificationDomainService.getJoinGroupTime(request.getWxId(), request.getPageNo(), request.getPageSize());

        // 获取chatroomWxSerialNo列表
        List<String> groupIdList = joinGroupPagerList.getData().stream()
                .map(ScrmPersonalWxGroupMemberInfoEntity::getGroupId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> groupMap = scrmPersonalWxGroupManageDomainService.queryGroupByChatroomSerialNos(groupIdList).stream().collect(
                Collectors.toMap(ScrmPersonalWxGroupInfoEntity::getChatRoomWxSerialNo, ScrmPersonalWxGroupInfoEntity::getGroupName,(u1,u2)->u1));

        return RemoteResponse.success(
                RestrictedUserDetailResponse.builder()
                        .wxId(request.getWxId())
                        .joinGroupList(
                                PagerList.of(
                                        joinGroupPagerList.getTotal(),
                                        joinGroupPagerList.isEnd(),
                                        joinGroupPagerList.getData().stream().map(e -> JoinGroupRecord.builder()
                                                .nickname(Optional.ofNullable(e.getWxNickname()).orElse(""))
                                                .avatar(Optional.ofNullable(e.getAvatar()).orElse(""))
                                                .groupName(Optional.ofNullable(groupMap.get(e.getGroupId())).orElse(""))
                                                .joinGroupTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(
                                                        Optional.ofNullable(e.getEnterTime()).orElse(new Date(0))))
                                                .build()).collect(Collectors.toList())
                                )
                        ).build());
    }

    @Override
    public PageRemoteResponse<RestrictionBlacklistRelationInfoResponse> getRestrictedUserRelationInfo(RestrictionUserDetailRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return PageRemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("getRestrictedUserRelationInfo param={},loginUser={}", request, loginUser);

        if (!scrmRestrictionIdentificationDomainService.isAlreadyAddUser(request.getWxId(), RestrictionIdentityTypeEnum.RESTRICTED_USER)) {
            return PageRemoteResponse.fail("该用户不是风险用户");
        }

        PagerList<RestrictionBlacklistRelationInfoResponse> pagerList = scrmRestrictionIdentificationDomainService.getRestrictedUserRelation(request.getWxId(), request.getPageNo(), request.getPageSize());

        return PageRemoteResponse.success(pagerList.getData(), pagerList.getTotal(), pagerList.isEnd());
    }

    @Override
    public PageRemoteResponse<RestrictionUserSearchResponse> searchRestrictionUserByNickname(RestrictionUserSearchRequest request) {
        if (StringUtils.isEmpty(request.getKeyword())) {
            return PageRemoteResponse.success(new ArrayList<>(), 0, true);
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("searchRestrictionUserByNickname param={},loginUser={}", request, loginUser);
        PagerList<ScrmPersonalWxGroupMemberInfoEntity> memberInfoPagerList = scrmRestrictionIdentificationDomainService.searchUserByNickname(request.getKeyword(), request.getPageNo(), request.getPageSize());
        return PageRemoteResponse.success(memberInfoPagerList.getData().stream()
                        .map(e -> RestrictionUserSearchResponse.builder()
                                .wxId(Optional.ofNullable(e.getWxId()).orElse(""))
                                .nickname(Optional.ofNullable(e.getWxNickname()).orElse(""))
                                .avatar(Optional.ofNullable(e.getAvatar()).orElse(""))
                                .build())
                        .collect(Collectors.toList()),
                memberInfoPagerList.getTotal(),
                memberInfoPagerList.isEnd());
    }

    @Override
    public RemoteResponse<Boolean> addRestrictedUser(RestrictionAddUserRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return RemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("addRestrictedUser param={},loginUser={}", request, loginUser);
        if (scrmRestrictionIdentificationDomainService.isAlreadyAddUser(request.getWxId(), RestrictionIdentityTypeEnum.RESTRICTED_USER)) {
            return RemoteResponse.fail("请勿重复添加");
        }

        scrmRestrictionIdentificationDomainService.addUser(
                request.getWxId(),
                Optional.ofNullable(request.getComment()).orElse(""),
                RestrictionIdentityTypeEnum.RESTRICTED_USER,
                RestrictionReasonTypeEnum.ADDED_BLACKLIST_MANUALLY);

        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<Boolean> deleteRestrictedUser(RestrictionDeleteUserRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return RemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("deleteRestrictedUser param={},loginUser={}", request, loginUser);
        scrmRestrictionIdentificationDomainService.deleteUser(request.getWxId(), RestrictionIdentityTypeEnum.RESTRICTED_USER);

        List<String> restrictionWxIdList = scrmRestrictionIdentificationDomainService.getRestrictionWxIdList(request.getWxId());
        if (CollectionUtils.isNotEmpty(restrictionWxIdList)) {
            scrmRestrictionIdentificationDomainService.deleteUsers(restrictionWxIdList, RestrictionIdentityTypeEnum.RESTRICTED_USER);
        }
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<Boolean> addPrivilegedUser(RestrictionAddUserRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return RemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("addPrivilegedUser param={},loginUser={}", request, loginUser);
        if (scrmRestrictionIdentificationDomainService.isAlreadyAddUser(request.getWxId(), RestrictionIdentityTypeEnum.PRIVILEGED_USER)) {
            return RemoteResponse.fail("请勿重复添加");
        }

        scrmRestrictionIdentificationDomainService.addUser(
                request.getWxId(),
                Optional.ofNullable(request.getComment()).orElse(""),
                RestrictionIdentityTypeEnum.PRIVILEGED_USER,
                RestrictionReasonTypeEnum.ADDED_WHITELIST_MANUALLY);

        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<Boolean> deletePrivilegedUser(RestrictionDeleteUserRequest request) {
        if (StringUtils.isEmpty(request.getWxId())) {
            return RemoteResponse.fail("wxId不可为空");
        }
        String loginUser = SuperAdminSsoService.checkLoginUser();
        log.info("deletePrivilegedUser param={},loginUser={}", request, loginUser);
        scrmRestrictionIdentificationDomainService.deleteUser(request.getWxId(), RestrictionIdentityTypeEnum.PRIVILEGED_USER);
        return RemoteResponse.success(true);
    }
}
