<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.flowV2.dal.mapper.FlowEntryEventLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="entry_type" jdbcType="TINYINT" property="entryType" />
    <result column="event_type" jdbcType="TINYINT" property="eventType" />
    <result column="mt_user_id" jdbcType="BIGINT" property="mtUserId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="code_id" jdbcType="BIGINT" property="codeId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, entry_type, event_type, mt_user_id, union_id, code_id, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.flowV2.dal.example.FlowEntryEventLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from flow_entry_event_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from flow_entry_event_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from flow_entry_event_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.flowV2.dal.example.FlowEntryEventLogExample">
    delete from flow_entry_event_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into flow_entry_event_log (app_id, entry_type, event_type, 
      mt_user_id, union_id, code_id, 
      add_time, update_time)
    values (#{appId,jdbcType=VARCHAR}, #{entryType,jdbcType=TINYINT}, #{eventType,jdbcType=TINYINT}, 
      #{mtUserId,jdbcType=BIGINT}, #{unionId,jdbcType=VARCHAR}, #{codeId,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into flow_entry_event_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="entryType != null">
        entry_type,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="mtUserId != null">
        mt_user_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="codeId != null">
        code_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="entryType != null">
        #{entryType,jdbcType=TINYINT},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=TINYINT},
      </if>
      <if test="mtUserId != null">
        #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="codeId != null">
        #{codeId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.flowV2.dal.example.FlowEntryEventLogExample" resultType="java.lang.Long">
    select count(*) from flow_entry_event_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update flow_entry_event_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.entryType != null">
        entry_type = #{record.entryType,jdbcType=TINYINT},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=TINYINT},
      </if>
      <if test="record.mtUserId != null">
        mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.codeId != null">
        code_id = #{record.codeId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update flow_entry_event_log
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      entry_type = #{record.entryType,jdbcType=TINYINT},
      event_type = #{record.eventType,jdbcType=TINYINT},
      mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      code_id = #{record.codeId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog">
    update flow_entry_event_log
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="entryType != null">
        entry_type = #{entryType,jdbcType=TINYINT},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=TINYINT},
      </if>
      <if test="mtUserId != null">
        mt_user_id = #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="codeId != null">
        code_id = #{codeId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog">
    update flow_entry_event_log
    set app_id = #{appId,jdbcType=VARCHAR},
      entry_type = #{entryType,jdbcType=TINYINT},
      event_type = #{eventType,jdbcType=TINYINT},
      mt_user_id = #{mtUserId,jdbcType=BIGINT},
      union_id = #{unionId,jdbcType=VARCHAR},
      code_id = #{codeId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into flow_entry_event_log
    (app_id, entry_type, event_type, mt_user_id, union_id, code_id, add_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.entryType,jdbcType=TINYINT}, #{item.eventType,jdbcType=TINYINT}, 
        #{item.mtUserId,jdbcType=BIGINT}, #{item.unionId,jdbcType=VARCHAR}, #{item.codeId,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>