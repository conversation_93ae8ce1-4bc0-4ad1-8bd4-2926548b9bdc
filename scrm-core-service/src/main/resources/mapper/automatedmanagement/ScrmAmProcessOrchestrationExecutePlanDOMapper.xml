<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecutePlanDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_orchestration_id" jdbcType="BIGINT" property="processOrchestrationId" />
    <result column="process_orchestration_version" jdbcType="VARCHAR" property="processOrchestrationVersion" />
    <result column="process_orchestration_type" jdbcType="TINYINT" property="processOrchestrationType" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pack_status" jdbcType="TINYINT" property="packStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, process_orchestration_id, process_orchestration_version, process_orchestration_type, 
    task_start_time, status, update_time, pack_status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_execute_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_execute_plan
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_process_orchestration_execute_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample">
    delete from scrm_a_m_process_orchestration_execute_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_execute_plan (process_orchestration_id, process_orchestration_version, 
      process_orchestration_type, task_start_time, 
      status, update_time, pack_status
      )
    values (#{processOrchestrationId,jdbcType=BIGINT}, #{processOrchestrationVersion,jdbcType=VARCHAR}, 
      #{processOrchestrationType,jdbcType=TINYINT}, #{taskStartTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{updateTime,jdbcType=TIMESTAMP}, #{packStatus,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_execute_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="processOrchestrationId != null">
        process_orchestration_id,
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version,
      </if>
      <if test="processOrchestrationType != null">
        process_orchestration_type,
      </if>
      <if test="taskStartTime != null">
        task_start_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="packStatus != null">
        pack_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="processOrchestrationId != null">
        #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationType != null">
        #{processOrchestrationType,jdbcType=TINYINT},
      </if>
      <if test="taskStartTime != null">
        #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packStatus != null">
        #{packStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_process_orchestration_execute_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_process_orchestration_execute_plan
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationId != null">
        process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationVersion != null">
        process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationType != null">
        process_orchestration_type = #{record.processOrchestrationType,jdbcType=TINYINT},
      </if>
      <if test="record.taskStartTime != null">
        task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.packStatus != null">
        pack_status = #{record.packStatus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_process_orchestration_execute_plan
    set id = #{record.id,jdbcType=BIGINT},
      process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_type = #{record.processOrchestrationType,jdbcType=TINYINT},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      pack_status = #{record.packStatus,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO">
    update scrm_a_m_process_orchestration_execute_plan
    <set>
      <if test="processOrchestrationId != null">
        process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationType != null">
        process_orchestration_type = #{processOrchestrationType,jdbcType=TINYINT},
      </if>
      <if test="taskStartTime != null">
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="packStatus != null">
        pack_status = #{packStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO">
    update scrm_a_m_process_orchestration_execute_plan
    set process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_type = #{processOrchestrationType,jdbcType=TINYINT},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pack_status = #{packStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_process_orchestration_execute_plan
    (process_orchestration_id, process_orchestration_version, process_orchestration_type, 
      task_start_time, status, update_time, pack_status)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.processOrchestrationId,jdbcType=BIGINT}, #{item.processOrchestrationVersion,jdbcType=VARCHAR}, 
        #{item.processOrchestrationType,jdbcType=TINYINT}, #{item.taskStartTime,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=TINYINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.packStatus,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
</mapper>