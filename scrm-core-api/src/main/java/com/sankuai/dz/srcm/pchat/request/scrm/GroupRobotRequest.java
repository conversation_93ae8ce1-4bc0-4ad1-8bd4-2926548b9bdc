package com.sankuai.dz.srcm.pchat.request.scrm;

import com.sankuai.dz.srcm.pchat.request.Request;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/5/6 15:02
 * @Version v1.0.0
 */
@Data
public class GroupRobotRequest extends Request {
    /**
     * 群聊房间号
     */
    private String chatRoomWxSerialNo;
    /**
     * 直播id
     */
    private String webcastId;

    @Override
    public boolean validParam() {
        assertNotBlank(webcastId, "webcastId不能为空");
        assertNotBlank(chatRoomWxSerialNo, "chatRoomWxSerialNo不能为空");
        return super.validParam();
    }
}
