package com.sankuai.scrm.core.service.friend.dynamiccode.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCode;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.FriendChannelDynamicCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FriendChannelDynamicCodeMapper extends MybatisBaseMapper<FriendChannelDynamicCode, FriendChannelDynamicCodeExample, Long> {
    int batchInsert(@Param("list") List<FriendChannelDynamicCode> list);
}