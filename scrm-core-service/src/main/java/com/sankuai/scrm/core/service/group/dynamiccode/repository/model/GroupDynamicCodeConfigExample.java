package com.sankuai.scrm.core.service.group.dynamiccode.repository.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GroupDynamicCodeConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public GroupDynamicCodeConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public GroupDynamicCodeConfigExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public GroupDynamicCodeConfigExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public GroupDynamicCodeConfigExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNull() {
            addCriterion("code_id is null");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNotNull() {
            addCriterion("code_id is not null");
            return (Criteria) this;
        }

        public Criteria andCodeIdEqualTo(Long value) {
            addCriterion("code_id =", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotEqualTo(Long value) {
            addCriterion("code_id <>", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThan(Long value) {
            addCriterion("code_id >", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("code_id >=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThan(Long value) {
            addCriterion("code_id <", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThanOrEqualTo(Long value) {
            addCriterion("code_id <=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdIn(List<Long> values) {
            addCriterion("code_id in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotIn(List<Long> values) {
            addCriterion("code_id not in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdBetween(Long value1, Long value2) {
            addCriterion("code_id between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotBetween(Long value1, Long value2) {
            addCriterion("code_id not between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andTagIdListIsNull() {
            addCriterion("tag_id_list is null");
            return (Criteria) this;
        }

        public Criteria andTagIdListIsNotNull() {
            addCriterion("tag_id_list is not null");
            return (Criteria) this;
        }

        public Criteria andTagIdListEqualTo(String value) {
            addCriterion("tag_id_list =", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListNotEqualTo(String value) {
            addCriterion("tag_id_list <>", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListGreaterThan(String value) {
            addCriterion("tag_id_list >", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListGreaterThanOrEqualTo(String value) {
            addCriterion("tag_id_list >=", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListLessThan(String value) {
            addCriterion("tag_id_list <", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListLessThanOrEqualTo(String value) {
            addCriterion("tag_id_list <=", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListLike(String value) {
            addCriterion("tag_id_list like", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListNotLike(String value) {
            addCriterion("tag_id_list not like", value, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListIn(List<String> values) {
            addCriterion("tag_id_list in", values, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListNotIn(List<String> values) {
            addCriterion("tag_id_list not in", values, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListBetween(String value1, String value2) {
            addCriterion("tag_id_list between", value1, value2, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andTagIdListNotBetween(String value1, String value2) {
            addCriterion("tag_id_list not between", value1, value2, "tagIdList");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotIsNull() {
            addCriterion("owner_is_robot is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotIsNotNull() {
            addCriterion("owner_is_robot is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotEqualTo(Boolean value) {
            addCriterion("owner_is_robot =", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotNotEqualTo(Boolean value) {
            addCriterion("owner_is_robot <>", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotGreaterThan(Boolean value) {
            addCriterion("owner_is_robot >", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotGreaterThanOrEqualTo(Boolean value) {
            addCriterion("owner_is_robot >=", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotLessThan(Boolean value) {
            addCriterion("owner_is_robot <", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotLessThanOrEqualTo(Boolean value) {
            addCriterion("owner_is_robot <=", value, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotIn(List<Boolean> values) {
            addCriterion("owner_is_robot in", values, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotNotIn(List<Boolean> values) {
            addCriterion("owner_is_robot not in", values, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotBetween(Boolean value1, Boolean value2) {
            addCriterion("owner_is_robot between", value1, value2, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andOwnerIsRobotNotBetween(Boolean value1, Boolean value2) {
            addCriterion("owner_is_robot not between", value1, value2, "ownerIsRobot");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdIsNull() {
            addCriterion("group_template_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdIsNotNull() {
            addCriterion("group_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdEqualTo(Long value) {
            addCriterion("group_template_id =", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdNotEqualTo(Long value) {
            addCriterion("group_template_id <>", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdGreaterThan(Long value) {
            addCriterion("group_template_id >", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_template_id >=", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdLessThan(Long value) {
            addCriterion("group_template_id <", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("group_template_id <=", value, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdIn(List<Long> values) {
            addCriterion("group_template_id in", values, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdNotIn(List<Long> values) {
            addCriterion("group_template_id not in", values, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdBetween(Long value1, Long value2) {
            addCriterion("group_template_id between", value1, value2, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andGroupTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("group_template_id not between", value1, value2, "groupTemplateId");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerIsNull() {
            addCriterion("auto_change_group_owner is null");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerIsNotNull() {
            addCriterion("auto_change_group_owner is not null");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerEqualTo(Boolean value) {
            addCriterion("auto_change_group_owner =", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerNotEqualTo(Boolean value) {
            addCriterion("auto_change_group_owner <>", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerGreaterThan(Boolean value) {
            addCriterion("auto_change_group_owner >", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("auto_change_group_owner >=", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerLessThan(Boolean value) {
            addCriterion("auto_change_group_owner <", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerLessThanOrEqualTo(Boolean value) {
            addCriterion("auto_change_group_owner <=", value, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerIn(List<Boolean> values) {
            addCriterion("auto_change_group_owner in", values, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerNotIn(List<Boolean> values) {
            addCriterion("auto_change_group_owner not in", values, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_change_group_owner between", value1, value2, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andAutoChangeGroupOwnerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_change_group_owner not between", value1, value2, "autoChangeGroupOwner");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdIsNull() {
            addCriterion("new_owner_user_id is null");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdIsNotNull() {
            addCriterion("new_owner_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdEqualTo(String value) {
            addCriterion("new_owner_user_id =", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdNotEqualTo(String value) {
            addCriterion("new_owner_user_id <>", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdGreaterThan(String value) {
            addCriterion("new_owner_user_id >", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("new_owner_user_id >=", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdLessThan(String value) {
            addCriterion("new_owner_user_id <", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdLessThanOrEqualTo(String value) {
            addCriterion("new_owner_user_id <=", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdLike(String value) {
            addCriterion("new_owner_user_id like", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdNotLike(String value) {
            addCriterion("new_owner_user_id not like", value, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdIn(List<String> values) {
            addCriterion("new_owner_user_id in", values, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdNotIn(List<String> values) {
            addCriterion("new_owner_user_id not in", values, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdBetween(String value1, String value2) {
            addCriterion("new_owner_user_id between", value1, value2, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerUserIdNotBetween(String value1, String value2) {
            addCriterion("new_owner_user_id not between", value1, value2, "newOwnerUserId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdIsNull() {
            addCriterion("new_owner_mis_id is null");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdIsNotNull() {
            addCriterion("new_owner_mis_id is not null");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdEqualTo(String value) {
            addCriterion("new_owner_mis_id =", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdNotEqualTo(String value) {
            addCriterion("new_owner_mis_id <>", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdGreaterThan(String value) {
            addCriterion("new_owner_mis_id >", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdGreaterThanOrEqualTo(String value) {
            addCriterion("new_owner_mis_id >=", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdLessThan(String value) {
            addCriterion("new_owner_mis_id <", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdLessThanOrEqualTo(String value) {
            addCriterion("new_owner_mis_id <=", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdLike(String value) {
            addCriterion("new_owner_mis_id like", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdNotLike(String value) {
            addCriterion("new_owner_mis_id not like", value, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdIn(List<String> values) {
            addCriterion("new_owner_mis_id in", values, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdNotIn(List<String> values) {
            addCriterion("new_owner_mis_id not in", values, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdBetween(String value1, String value2) {
            addCriterion("new_owner_mis_id between", value1, value2, "newOwnerMisId");
            return (Criteria) this;
        }

        public Criteria andNewOwnerMisIdNotBetween(String value1, String value2) {
            addCriterion("new_owner_mis_id not between", value1, value2, "newOwnerMisId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}