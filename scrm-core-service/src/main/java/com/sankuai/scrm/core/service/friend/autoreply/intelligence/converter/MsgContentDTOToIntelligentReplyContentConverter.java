package com.sankuai.scrm.core.service.friend.autoreply.intelligence.converter;

import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.entity.IntelligentReplyContent;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.MsgContentDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/2/11 10:51
 * @Version v1.0.0
 */
public class MsgContentDTOToIntelligentReplyContentConverter {

    public static List<IntelligentReplyContent> convert(List<MsgContentDTO> msgContentDTOs) {
        List<IntelligentReplyContent> intelligentReplyContents = new ArrayList<>();
        msgContentDTOs.forEach(msgContentDTO -> intelligentReplyContents.add(convert(msgContentDTO)));
        return intelligentReplyContents;
    }

    public static IntelligentReplyContent convert(MsgContentDTO msgContentDTO) {
        if (msgContentDTO == null) {
            return null;
        }
        IntelligentReplyContent intelligentReplyContent = new IntelligentReplyContent();
        intelligentReplyContent.setContentType(msgContentDTO.getContentTypeTEnum());
        switch (msgContentDTO.getContentTypeTEnum()) {
            case TEXT:
                intelligentReplyContent.setContent(msgContentDTO.getTextDTO().getContent());
                break;
                case IMAGE:
                intelligentReplyContent.setContent(msgContentDTO.getImageDTO().getUrl());
                break;
                case VIDEO:
                intelligentReplyContent.setContent(msgContentDTO.getVideoDTO().getUrl());
                break;
                case MINI_PROGRAM:
                intelligentReplyContent.setContent(JsonUtils.toStr(msgContentDTO.getMiniProgramDTO()));
                break;
                case LINK:
                intelligentReplyContent.setContent(JsonUtils.toStr(msgContentDTO.getLinkDTO()));
                break;
            default:
                return null;
        }
        intelligentReplyContent.setSent(true);
        return intelligentReplyContent;
    }
}
