package com.sankuai.scrm.core.service.pchat.domain;

import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.sankuai.scrm.core.service.pchat.config.RobotLogoutNotifyConfig;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotLoginLog;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotLoginLogMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserInfoMapper;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.scrm.core.service.pchat.dto.CallbackDTO;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotClusterMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotInfoMapper;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxRobotLoginDomainServiceNotifyUserOnRobotLogoutTest {

    @InjectMocks
    private ScrmPersonalWxRobotLoginDomainService scrmPersonalWxRobotLoginDomainService;

    @Mock
    private ScrmPersonalWxRobotLoginLogMapper wxRobotLoginLogMapper;

    @Mock
    private ScrmPersonalWxUserInfoMapper personalWxUserInfoMapper;

    @Mock
    private EduDaxiangSendService eduDaxiangSendService;

    @Mock
    private ScrmPersonalWxUserDomainService wxUserDomainService;

    private String robotSerialNo;

    private List<ScrmPersonalWxRobotLoginLog> robotLoginLogList;

    private ScrmPersonalWxUserInfo scrmPersonalWxUserInfo;

    @Before
    public void setUp() {
        robotSerialNo = "testRobotSerialNo";
        robotLoginLogList = new ArrayList<>();
        scrmPersonalWxUserInfo = new ScrmPersonalWxUserInfo();
        scrmPersonalWxUserInfo.setNickname("testNickname");
        when(wxUserDomainService.queryUserByWxSerialNo(anyString())).thenReturn(scrmPersonalWxUserInfo);
        // Note: The incorrect mocking statement for Lion.getBean has been removed.
    }

    @Test
    public void testNotifyUserOnRobotLogoutWhenHandlerIsNull() throws Throwable {
        robotLoginLogList.add(new ScrmPersonalWxRobotLoginLog());
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(robotLoginLogList);
        scrmPersonalWxRobotLoginDomainService.notifyUserOnRobotLogout(robotSerialNo);
        verify(eduDaxiangSendService).sendTextMsg(anyString(), anyList());
    }

    @Test
    public void testNotifyUserOnRobotLogoutWhenHandlerIsNotNull() throws Throwable {
        ScrmPersonalWxRobotLoginLog log = new ScrmPersonalWxRobotLoginLog();
        log.setHandler("testHandler");
        robotLoginLogList.add(log);
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(robotLoginLogList);
        scrmPersonalWxRobotLoginDomainService.notifyUserOnRobotLogout(robotSerialNo);
        verify(eduDaxiangSendService).sendTextMsg(anyString(), anyList());
    }

    @Test
    public void testNotifyUserOnRobotLogoutWhenRobotSerialNoIsNull() throws Throwable {
        robotSerialNo = null;
        scrmPersonalWxRobotLoginDomainService.notifyUserOnRobotLogout(robotSerialNo);
        verify(wxRobotLoginLogMapper, never()).selectByExample(any());
    }

    @Test
    public void testNotifyUserOnRobotLogoutWhenRobotLoginLogListIsEmpty() throws Throwable {
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        scrmPersonalWxRobotLoginDomainService.notifyUserOnRobotLogout(robotSerialNo);
        verify(personalWxUserInfoMapper, never()).selectByExample(any());
    }

    @Test
    public void testNotifyUserOnRobotLogoutWhenScrmPersonalWxUserInfoIsNull() throws Throwable {
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(robotLoginLogList);
        scrmPersonalWxRobotLoginDomainService.notifyUserOnRobotLogout(robotSerialNo);
        verify(eduDaxiangSendService, never()).sendTextMsg(anyString(), anyList());
    }
}
