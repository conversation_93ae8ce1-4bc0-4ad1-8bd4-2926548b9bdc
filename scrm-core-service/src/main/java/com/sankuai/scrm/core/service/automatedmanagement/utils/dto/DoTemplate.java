package com.sankuai.scrm.core.service.automatedmanagement.utils.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/30
 */
@Data
public class DoTemplate {
    /*STRING("string", 1, "字符串"),
    INTEGER("int", 2, "整型"),
    BOOLEAN("boolean", 3, "布尔"),
    LONG("long", 4, "长整型"),
    DATETIME("datetime", 5, "日期"),
    BYTE("byte", 6, "字节"),

    STAFF("staff", 127, "员工"),
    UNKNOWN("unknown", 0, "未知"),*/

    private String name;
    private String type;
    private String desc;
    private Integer intValue;
    private Byte byteValue;
    private Long longValue;
    private Date dateValue;
    private String staff;
}
