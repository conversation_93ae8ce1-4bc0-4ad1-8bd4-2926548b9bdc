package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.pchat.anno.NTypeCallbackMapping;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 17:19
 * @Version v1.0.0
 */
public interface GroupMemberCbService extends PlatformCallbackService {
    /**
     * 新成员入群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4502, desc = "新成员入群回调接口（兼容PC）")
    RemoteResponse do4502(String strContext);

    /**
     * 群成员退群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4503, desc = "群成员退群回调接口（兼容PC）")
    RemoteResponse do4503(String strContext);

    /**
     * 群成员信息列表回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4501, desc = "群成员信息列表回调接口（兼容PC）")
    RemoteResponse do4501(String strContext);

    /**
     * 新成员入群扫码地址回调接口
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4519, desc = "新成员入群扫码地址回调接口")
    RemoteResponse do4519(String strContext);

    /**
     * 获取群成员信息回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4514, desc = "获取群成员信息回调")
    RemoteResponse do4514(String strContext);

}

