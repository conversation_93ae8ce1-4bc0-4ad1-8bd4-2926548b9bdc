package com.sankuai.scrm.core.service.pchat.service.tanjing;

import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.tanjing.GroupMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/6 13:51
 * @Version v1.0.0
 */

@Slf4j
@Service
public class GroupMemberServiceImpl implements GroupMemberService {

    @Autowired
    private TanjingRemoteInvokeService remoteInvokeService;

    @Autowired
    private ApiMappingService apiMappingService;

    @Override
    public AsyncInvokeResultDTO getChatRoomUserInfo(String vcMerchantNo, String vcRobotSerialNo, String vcChatRoomSerialNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vcMerchantNo", vcMerchantNo);
        paramMap.put("vcRobotSerialNo", vcRobotSerialNo);
        paramMap.put("vcChatRoomSerialNo", vcChatRoomSerialNo);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(GroupMemberService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO groupMembersProfile(String vcMerchantNo, String vcRobotSerialNo, String vcChatRoomSerialNo, String vcMemberSerialNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vcMerchantNo", vcMerchantNo);
        paramMap.put("vcRobotSerialNo", vcRobotSerialNo);
        paramMap.put("vcChatRoomSerialNo", vcChatRoomSerialNo);
        paramMap.put("vcMemberSerialNo", vcMemberSerialNo);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(GroupMemberService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }
}
