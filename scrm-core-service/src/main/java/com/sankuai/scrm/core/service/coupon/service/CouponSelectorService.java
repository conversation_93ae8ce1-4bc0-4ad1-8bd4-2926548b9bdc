package com.sankuai.scrm.core.service.coupon.service;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowCouponConfig;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowCouponInfoDTO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.sankuai.dz.srcm.coupon.request.IssueNextCouponRequest;
import com.sankuai.dz.srcm.coupon.dto.ValidNextCouponResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.CouponModuleInfoDTO;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 券选择服务
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
@Slf4j
@Service
public class CouponSelectorService {
    
    private static final String COUPON_CONFIG_KEY = "friday.intelligent.follow.coupon.info";
    
    @Resource
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;
    
    @Resource
    private NextCouponServiceImpl nextCouponService;
    
    /**
     * 选择最优券
     */
    public FridayIntelligentFollowCouponInfoDTO selectBestCoupon(CouponRequestContext context) {
        try {
            log.info("CouponSelectorService selectBestCoupon context: {}", context);
            
            if (context.getScene() == CouponSceneEnum.AI_INTELLIGENT_FOLLOW) {
                return selectCouponForAiScene(context);
            }
            
            // 其他场景的券选择逻辑可以在这里扩展
            log.warn("Unsupported coupon selection scene: {}", context.getScene());
            return null;
            
        } catch (Exception e) {
            log.error("CouponSelectorService selectBestCoupon error", e);
            return null;
        }
    }
    
    /**
     * AI场景券选择
     */
    private FridayIntelligentFollowCouponInfoDTO selectCouponForAiScene(CouponRequestContext context) {
        // 1. 获取券配置
        FridayIntelligentFollowCouponConfig couponConfig = getCouponConfig(context.getAppId());
        if (couponConfig == null) {
            log.warn("No coupon config found for appId: {}", context.getAppId());
            return null;
        }
        
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList = couponConfig.getCouponInfoList();
        if (CollectionUtils.isEmpty(couponInfoList)) {
            log.warn("Coupon info list is empty for appId: {}", context.getAppId());
            return null;
        }
        
        // 2. 检查用户是否已拥有候选券且该券未使用未过期
        FridayIntelligentFollowCouponInfoDTO existingValidCoupon = checkExistingValidCoupon(couponInfoList, context.getUserId());
        if (existingValidCoupon != null) {
            log.info("User already has valid coupon: couponId={}, userId={}", existingValidCoupon.getCouponId(), context.getUserId());
            // 设置为不需要新发券（用户已有有效券）
            existingValidCoupon.setNeedNewIssue(false);
            return existingValidCoupon;
        }
        
        // 3. 过滤用户已领取的券（旧逻辑）
        couponInfoList = filterUserReceivedCoupons(couponInfoList, context.getUserId());
        if (CollectionUtils.isEmpty(couponInfoList)) {
            log.info("All coupons already received by user: {}", context.getUserId());
            return null;
        }
        
        // 4. 根据业务规则选择最优券
        FridayIntelligentFollowCouponInfoDTO selectedCoupon = selectOptimalCoupon(couponInfoList, context);
        if (selectedCoupon != null) {
            // 设置为需要新发券
            selectedCoupon.setNeedNewIssue(true);
        }
        return selectedCoupon;
    }
    
    /**
     * 检查用户是否已拥有候选券且该券未使用未过期
     */
    private FridayIntelligentFollowCouponInfoDTO checkExistingValidCoupon(
            List<FridayIntelligentFollowCouponInfoDTO> couponInfoList, Long userId) {
        
        try {
            // 1. 批量查询用户已领取的券记录
            List<String> couponIds = couponInfoList.stream()
                .map(FridayIntelligentFollowCouponInfoDTO::getCouponId)
                .collect(Collectors.toList());
            
            Map<String, ScrmSceneCouponRecords> userReceivedCoupons = getUserReceivedCoupons(couponIds, userId);
            if (userReceivedCoupons.isEmpty()) {
                return null;
            }
            
            // 2. 检查已领取的券中是否有未使用且未过期的
            Date currentTime = new Date();
            for (FridayIntelligentFollowCouponInfoDTO couponInfo : couponInfoList) {
                String couponId = couponInfo.getCouponId();
                ScrmSceneCouponRecords record = userReceivedCoupons.get(couponId);
                
                // 如果用户已领取过这张券
                if (record != null) {
                    // 检查券是否已使用（usecoupontime不为null表示已使用）
                    if (record.getUsecoupontime() != null) {
                        log.debug("Coupon already used: couponId={}, userId={}, usedTime={}", 
                            couponId, userId, record.getUsecoupontime());
                        continue;
                    }
                    
                    // 检查券是否过期（endtime小于当前时间表示过期）
                    // 注意：根据字段注释，美团红包的结束时间可能不准，但仍需检查
                    if (record.getEndtime() != null && record.getEndtime().before(currentTime)) {
                        log.debug("Coupon expired: couponId={}, userId={}, endTime={}", 
                            couponId, userId, record.getEndtime());
                        continue;
                    }
                    
                    // 检查券是否还未生效（begintime大于当前时间表示未生效）
                    if (record.getBegintime() != null && record.getBegintime().after(currentTime)) {
                        log.debug("Coupon not yet effective: couponId={}, userId={}, beginTime={}", 
                            couponId, userId, record.getBegintime());
                        continue;
                    }
                    
                    // 如果券未使用且未过期且已生效，说明是有效券
                    log.info("Found existing valid coupon for user: couponId={}, userId={}", couponId, userId);
                    couponInfo.setStartTime(record.getBegintime());
                    couponInfo.setExpireTime(record.getEndtime());
                    return couponInfo;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("Check existing valid coupon failed", e);
            return null;
        }
    }
    
    /**
     * 获取券配置（带缓存）
     */
    private FridayIntelligentFollowCouponConfig getCouponConfig(String appId) {
        try {
            List<FridayIntelligentFollowCouponConfig> configList = Lion.getList(
                Environment.getAppName(), COUPON_CONFIG_KEY, 
                FridayIntelligentFollowCouponConfig.class);
            
            return configList.stream()
                .filter(config -> config.getAppId().equals(appId))
                .findFirst()
                .orElse(null);
                
        } catch (Exception e) {
            log.error("Get coupon config failed for appId: {}", appId, e);
            return null;
        }
    }
    
    /**
     * 选择最优券
     */
    private FridayIntelligentFollowCouponInfoDTO selectOptimalCoupon(
            List<FridayIntelligentFollowCouponInfoDTO> couponInfoList, 
            CouponRequestContext context) {
        
        // 按券金额倒序排列
        couponInfoList.sort(Comparator.comparing(FridayIntelligentFollowCouponInfoDTO::getCouponAmount).reversed());
        
        // 如果有门店信息，直接返回金额最大的券
        if (context.getShopInfo() != null) {
            log.debug("Select max amount coupon for shop scenario");
            return couponInfoList.get(0);
        }
        
        // 如果没有商品信息，返回null
        if (context.getProductInfo() == null) {
            log.debug("No product info provided, cannot select coupon");
            return null;
        }
        
        // 根据商品价格选择合适的券
        return selectCouponByProductPrice(couponInfoList, context);
    }
    
    /**
     * 根据商品价格选择券
     */
    private FridayIntelligentFollowCouponInfoDTO selectCouponByProductPrice(
            List<FridayIntelligentFollowCouponInfoDTO> couponInfoList,
            CouponRequestContext context) {
        
        BigDecimal productBasePrice = context.getProductInfo().getProductBasePrice();
        if (productBasePrice == null) {
            log.debug("Product base price is null, cannot select coupon");
            return null;
        }
        
        return couponInfoList.stream()
            .filter(coupon -> BigDecimal.valueOf(coupon.getCouponFullPrice()).compareTo(productBasePrice) <= 0)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 过滤用户已领取的券（优化批量查询）
     */
    private List<FridayIntelligentFollowCouponInfoDTO> filterUserReceivedCoupons(
        List<FridayIntelligentFollowCouponInfoDTO> couponInfoList, Long userId) {
        
        try {
            // 1. 批量查询用户已领取的券记录
            List<String> couponIds = couponInfoList.stream()
                .map(FridayIntelligentFollowCouponInfoDTO::getCouponId)
                .collect(Collectors.toList());
            
            Map<String, ScrmSceneCouponRecords> userReceivedCoupons = getUserReceivedCoupons(couponIds, userId);
            if (userReceivedCoupons.isEmpty()) {
                return couponInfoList;
            }
            
            // 2. 批量查询券的可领取状态
            Map<String, ValidNextCouponResultDTO> couponValidityMap = batchCheckCouponValidity(couponIds, userId);
            
            // 3. 过滤已领取且不可再领的券
            return couponInfoList.stream()
                .filter(coupon -> !isCouponAlreadyReceived(coupon.getCouponId(), userReceivedCoupons, couponValidityMap))
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("Filter user received coupons failed", e);
            // 异常情况下返回原列表，避免影响主流程
            return couponInfoList;
        }
    }
    
    /**
     * 批量获取用户已领取的券记录
     */
    private Map<String, ScrmSceneCouponRecords> getUserReceivedCoupons(List<String> couponIds, Long userId) {
        ScrmSceneCouponRecordsExample example = new ScrmSceneCouponRecordsExample();
        example.createCriteria()
            .andCoupongroupidIn(couponIds)
            .andUseridEqualTo(userId);
            
        List<ScrmSceneCouponRecords> records = scrmSceneCouponRecordDOMapper.selectByExample(example);
        
        return records.stream()
            .collect(Collectors.toMap(
                ScrmSceneCouponRecords::getCoupongroupid,
                record -> record,
                (existing, replacement) -> existing));
    }
    
    /**
     * 批量检查券的有效性（优化性能）
     */
    private Map<String, ValidNextCouponResultDTO> batchCheckCouponValidity(List<String> couponIds, Long userId) {
        Map<String, ValidNextCouponResultDTO> resultMap = new HashMap<>();
        
        // 并行处理以提升性能
        couponIds.parallelStream().forEach(couponId -> {
            try {
                ValidNextCouponResultDTO dto = checkSingleCouponValidity(couponId, userId);
                if (dto != null) {
                    resultMap.put(couponId, dto);
                }
            } catch (Exception e) {
                log.error("Check coupon validity failed: couponId={}, userId={}", couponId, userId, e);
            }
        });
        
        return resultMap;
    }
    
    /**
     * 检查单个券的有效性
     */
    private ValidNextCouponResultDTO checkSingleCouponValidity(String couponId, Long userId) {
        try {
            IssueNextCouponRequest request = new IssueNextCouponRequest();
            request.setMtUserId(userId);
            request.setCouponGroupId(couponId);
            request.setSceneCode(null);
            
            ValidNextCouponResultDTO resultDTO = new ValidNextCouponResultDTO();
            CouponModuleInfoDTO couponModuleInfoDTO = new CouponModuleInfoDTO();
            MktCouponInfoDTO mktCouponInfoDTO = new MktCouponInfoDTO();

            RemoteResponse<ValidNextCouponResultDTO> response = nextCouponService
                .getValidNextCouponResultDTORemoteResponse(request, resultDTO, couponModuleInfoDTO, mktCouponInfoDTO, "RetailAiScene"+"-"+couponId+"-"+userId);
            
            return (response != null && response.isSuccess()) ? response.getData() : null;
            
        } catch (Exception e) {
            log.error("Check single coupon validity failed: couponId={}, userId={}", couponId, userId, e);
            return null;
        }
    }
    
    /**
     * 判断券是否已被用户领取且不可再领
     */
    private boolean isCouponAlreadyReceived(String couponId, 
                                          Map<String, ScrmSceneCouponRecords> userReceivedCoupons,
                                          Map<String, ValidNextCouponResultDTO> couponValidityMap) {
        
        // 如果没有领取记录，说明未领取
        ScrmSceneCouponRecords record = userReceivedCoupons.get(couponId);
        if (record == null) {
            return false;
        }
        
        // 如果已领取，检查是否可再领
        ValidNextCouponResultDTO validity = couponValidityMap.get(couponId);
        return validity == null || !validity.isAcquirable();
    }
    
    /**
     * 判断指定券对用户是否可再领取（兼容老用法）
     */
    public boolean isCouponAcquirable(String couponGroupId, Long userId) {
        ValidNextCouponResultDTO dto = checkSingleCouponValidity(couponGroupId, userId);
        return dto != null && dto.isAcquirable();
    }
} 