package com.sankuai.scrm.core.service.pchat.thirdparty.swarm;

import lombok.Data;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/2/20 10:52
 * @Version v1.0.0
 */
@Data
public class TranscodeResponse {
    private String JobId;
    private Integer Code;
    private String Status;
    private Output Output;

    @Data
    public class Output {
        private Integer code;
        private String message;
        private String store_uri;
        private String vid;
        private Map<String, Object> encoded_video_info;
        private Map<String, Object> task_info;
        private Integer need_store_db;
        private Map<String, Object> extra_info;
    }
}
