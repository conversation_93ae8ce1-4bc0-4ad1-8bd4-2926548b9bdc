package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxIMOnlineConn;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxIMOnlineConnExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxIMOnlineConnMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxImDomainServiceQueryOnlineConnByLiveIdTest {

    @InjectMocks
    private ScrmPersonalWxImDomainService scrmPersonalWxImDomainService;

    @Mock
    private ScrmPersonalWxIMOnlineConnMapper personalWxIMOnlineConnMapper;

    @Before
    public void setUp() {
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        when(personalWxIMOnlineConnMapper.selectByExample(any(ScrmPersonalWxIMOnlineConnExample.class))).thenReturn(Collections.singletonList(onlineConn));
    }

    /**
     * 测试 appId 或 liveId 为空的情况
     */
    @Test
    public void testQueryOnlineConnByLiveId_EmptyAppIdOrLiveId() {
        List<ScrmPersonalWxIMOnlineConn> result = scrmPersonalWxImDomainService.queryOnlineConnByLiveId(null, "liveId");
        assertEquals(0, result.size());
        result = scrmPersonalWxImDomainService.queryOnlineConnByLiveId("appId", null);
        assertEquals(0, result.size());
    }

    /**
     * 测试 appId 和 liveId 都不为空，且数据库中存在对应的记录的情况
     */
    @Test
    public void testQueryOnlineConnByLiveId_ExistRecord() {
        List<ScrmPersonalWxIMOnlineConn> result = scrmPersonalWxImDomainService.queryOnlineConnByLiveId("appId", "liveId");
        assertEquals(1, result.size());
    }

    /**
     * 测试 appId 和 liveId 都不为空，但数据库中不存在对应的记录的情况
     */
    @Test
    public void testQueryOnlineConnByLiveId_NoRecord() {
        when(personalWxIMOnlineConnMapper.selectByExample(any(ScrmPersonalWxIMOnlineConnExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxIMOnlineConn> result = scrmPersonalWxImDomainService.queryOnlineConnByLiveId("appId", "liveId");
        assertEquals(0, result.size());
    }
}
