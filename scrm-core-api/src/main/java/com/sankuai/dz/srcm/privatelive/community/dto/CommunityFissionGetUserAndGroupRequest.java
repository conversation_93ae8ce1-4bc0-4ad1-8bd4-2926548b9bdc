package com.sankuai.dz.srcm.privatelive.community.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@TypeDoc(description = "裂变拉取用户和群聊请求")
public class CommunityFissionGetUserAndGroupRequest implements Serializable {
    @FieldDoc(description = "昵称")
    private String nickName;
    @FieldDoc(description = "直播id")
    private String liveId;
}
