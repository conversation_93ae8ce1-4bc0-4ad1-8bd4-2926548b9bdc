package com.sankuai.dz.srcm.activity.miniprogram.enums;

public enum LotteryDisplayStatusEnum {
    NOT_BUY(1, "未购买"),
    BUY_WITHOUT_WINING_LIST(2, "已购买无中奖名单"),
    BUY_WITH_WINING_LIST(3, "已购买有中奖名单"),
    FINISHED(4, "活动已结束");

    private int code;

    private String desc;

    LotteryDisplayStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
