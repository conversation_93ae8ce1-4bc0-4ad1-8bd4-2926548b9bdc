package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.platform.anno.NTypePlatformCallbackMappingAnno;


/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 19:06
 * @Version v1.0.0
 */
public interface LoginoutPlatformCallbackService extends PlatformCallbackService {

    /**
     * 机器人封号回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1010, desc = "机器人封号回调接口")
    RemoteResponse do1010(String strContext);

    /**
     * 商家新增平台机器人回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1048, desc = "商家新增平台机器人回调接口")
    RemoteResponse do1048(String strContext);

    /**
     * 登录失败回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1004, desc = "登录失败回调")
    RemoteResponse do1004(String strContext);

    /**
     * 机器人疑似封号回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1007, desc = "机器人疑似封号回调接口")
    RemoteResponse do1007(String strContext);

    /**
     * 机器人个人信息补充回调接口
     *
     * 机器人登录成功，每次推送完登录成功回调后，会将个人信息补充回调推送给商家；正常情况下此回调的个人信息是最新的机器人个人信息，也有可能出现机器人登录成功但是获取个人信息失败，这种情况下，此回调的个人信息数据取的是缓存数据，但是机器人可以正常的进行操作；
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1049, desc = "机器人个人信息补充回调接口")
    RemoteResponse do1049(String strContext);

    /**
     * 平台机器人回收回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1047, desc = "平台机器人回收回调")
    RemoteResponse do1047(String strContext);

    /**
     * 登录成功回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1003, desc = "登录成功回调")
    RemoteResponse do1003(String strContext);

    /**
     * 平台授权码回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1025, desc = "平台授权码回调接口")
    RemoteResponse do1025(String strContext);

    /**
     * 授权码扫码回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1026, desc = "授权码扫码回调接口")
    RemoteResponse do1026(String strContext);

    /**
     * 登录二维码回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1001, desc = "登录二维码回调接口")
    RemoteResponse do1001(String strContext);

    /**
     * 二维码扫码状态回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1002, desc = "二维码扫码状态回调接口")
    RemoteResponse do1002(String strContext);

    /**
     * 登出接口3.0（兼容PC）回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1005, desc = "登出接口3.0（兼容PC）回调")
    RemoteResponse do1005(String strContext);

    /**
     * 扫码号登录需安全验证回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1081, desc = "扫码号登录需安全验证回调")
    RemoteResponse do1081(String strContext);

    /**
     * 扫码号走人脸验证回调
     * 接收此回调即需要走人脸验证流程，且回调里的passcode（口令码）具有时效性，收到请尽快完成流程
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 1008, desc = "扫码号走人脸验证回调")
    RemoteResponse do1008(String strContext);
}

