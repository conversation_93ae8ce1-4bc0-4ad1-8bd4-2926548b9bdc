package com.sankuai.scrm.core.service.user.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_TagInfo
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmTagInfo {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: tag_id
     *   说明: 标签的id
     */
    private Integer tagId;

    /**
     *   字段: tag_type
     *   说明: 标签类型id
     */
    private Long tagType;

    /**
     *   字段: tag_name
     *   说明: 标签名称
     */
    private String tagName;

    /**
     *   字段: tag_value_type
     *   说明: 标识，该标签的值的value的类型。1为数字类型、2为字符串类型、3为日期类型、4为数字段（如：10,20）、5为geohash类型、6为整数类型
     */
    private Integer tagValueType;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: tag_desc
     *   说明: 标签描述
     */
    private String tagDesc;
}