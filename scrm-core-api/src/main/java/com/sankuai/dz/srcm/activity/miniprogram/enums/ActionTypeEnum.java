package com.sankuai.dz.srcm.activity.miniprogram.enums;

public enum ActionTypeEnum {

    UNKNOWN(0, "未知"),
    CREATE(1, "创建"),
    UPDATE(2, "更新"),
    DELETE(3, "删除");

    private int code;

    private String desc;

    ActionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ActionTypeEnum getActionTypeByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        ActionTypeEnum[] values = ActionTypeEnum.values();
        for (ActionTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
