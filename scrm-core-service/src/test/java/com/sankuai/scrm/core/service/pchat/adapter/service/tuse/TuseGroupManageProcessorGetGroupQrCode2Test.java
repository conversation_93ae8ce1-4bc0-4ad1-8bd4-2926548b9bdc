package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import com.sankuai.scrm.core.service.pchat.adapter.service.GroupManageProcessor;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.mq.dto.DelayRefreshGroupQRTask;
import com.sankuai.scrm.core.service.pchat.schedule.PersonalWxGroupQRRefreshService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TuseGroupManageProcessorGetGroupQrCode2Test {

    @InjectMocks
    private TuseGroupManageProcessor tuseGroupManageProcessor;

    @Mock
    private PersonalWxGroupQRRefreshService personalWxGroupQRRefreshService;

    /**
     * 测试正常情况：所有参数都有效，且 refreshQR 返回非空字符串
     */
    @Test
    public void testGetGroupQrCode_NormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setOwner("owner123");
        groupInfoEntity.setChatRoomWxSerialNo("chatroom123");
        when(personalWxGroupQRRefreshService.refreshQR(any(DelayRefreshGroupQRTask.class))).thenReturn("qrCode123");
        // act
        String result = tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        assertEquals("qrCode123", result);
    }

    /**
     * 测试异常情况：groupInfoEntity 为空
     */
    @Test(expected = NullPointerException.class)
    public void testGetGroupQrCode_NullGroupInfoEntity() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = null;
        // act
        tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        // Expecting NullPointerException
    }

    /**
     * 测试异常情况：groupInfoEntity.getOwner() 为空
     */
    @Test
    public void testGetGroupQrCode_NullOwner() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setOwner(null);
        groupInfoEntity.setChatRoomWxSerialNo("chatroom123");
        when(personalWxGroupQRRefreshService.refreshQR(argThat(task -> task.getRobotSerialNo() == null))).thenReturn(null);
        // act
        String result = tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况：groupInfoEntity.getChatRoomWxSerialNo() 为空
     */
    @Test
    public void testGetGroupQrCode_NullChatRoomWxSerialNo() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setOwner("owner123");
        groupInfoEntity.setChatRoomWxSerialNo(null);
        when(personalWxGroupQRRefreshService.refreshQR(argThat(task -> task.getChatroomSerialNo() == null))).thenReturn(null);
        // act
        String result = tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况：refreshQR 返回 null
     */
    @Test
    public void testGetGroupQrCode_RefreshQRReturnsNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setOwner("owner123");
        groupInfoEntity.setChatRoomWxSerialNo("chatroom123");
        when(personalWxGroupQRRefreshService.refreshQR(any(DelayRefreshGroupQRTask.class))).thenReturn(null);
        // act
        String result = tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况：refreshQR 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetGroupQrCode_RefreshQRThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setOwner("owner123");
        groupInfoEntity.setChatRoomWxSerialNo("chatroom123");
        when(personalWxGroupQRRefreshService.refreshQR(any(DelayRefreshGroupQRTask.class))).thenThrow(new RuntimeException("Error"));
        // act
        tuseGroupManageProcessor.getGroupQrCode(groupInfoEntity);
        // assert
        // Expecting RuntimeException
    }
}
