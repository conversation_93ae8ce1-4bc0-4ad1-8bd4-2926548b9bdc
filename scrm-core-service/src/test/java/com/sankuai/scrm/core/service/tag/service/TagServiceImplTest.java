package com.sankuai.scrm.core.service.tag.service;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.tag.request.AddCorpTagRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.dto.SsoUserInfoDTO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.dal.entity.Tag;
import com.sankuai.scrm.core.service.tag.dal.example.TagExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.TagMapper;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TagServiceImplTest {

    @InjectMocks
    private TagServiceImpl tagService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private TagDomainService tagDomainService;

    @Mock(lenient = true)
    private TagMapper tagMapper;

    @Test
    public void testAddCorpTagRequestIsNull() throws Throwable {
        RemoteResponse<Boolean> response = tagService.addCorpTag(null);
        assertEquals("request can't be empty", response.getMsg());
    }

    @Test
    public void testAddCorpTagTokenIsEmpty() throws Throwable {
        AddCorpTagRequest request = new AddCorpTagRequest();
        request.setToken("");
        RemoteResponse<Boolean> response = tagService.addCorpTag(request);
        assertEquals("appId can't be empty", response.getMsg());
    }

    @Test
    public void testAddCorpTagAppIdIsEmpty() throws Throwable {
        AddCorpTagRequest request = new AddCorpTagRequest();
        request.setToken("token");
        request.setAppId("");
        when(ssosvOpenApi.getUserInfoByAccessToken(request.getToken())).thenReturn(new SsoUserInfoDTO());
        RemoteResponse<Boolean> response = tagService.addCorpTag(request);
        assertEquals("appId can't be empty", response.getMsg());
    }

    @Test
    public void testAddCorpTagCorpIdIsEmpty() throws Throwable {
        AddCorpTagRequest request = new AddCorpTagRequest();
        request.setToken("token");
        request.setAppId("appId");
        when(ssosvOpenApi.getUserInfoByAccessToken(request.getToken())).thenReturn(new SsoUserInfoDTO());
        when(appConfigRepository.getCorpIdByAppId(request.getAppId())).thenReturn("");
        RemoteResponse<Boolean> response = tagService.addCorpTag(request);
        assertEquals("appId is wrong", response.getMsg());
    }

    @Test
    public void testAddCorpTagAddCorpTagFailed() throws Throwable {
        AddCorpTagRequest request = new AddCorpTagRequest();
        request.setToken("token");
        request.setAppId("appId");
        when(ssosvOpenApi.getUserInfoByAccessToken(request.getToken())).thenReturn(new SsoUserInfoDTO());
        when(appConfigRepository.getCorpIdByAppId(request.getAppId())).thenReturn("corpId");
        when(tagDomainService.addCorpTag(any(), any(), any(), any(), any())).thenReturn(false);
        RemoteResponse<Boolean> response = tagService.addCorpTag(request);
        assertEquals("success", response.getMsg());
        assertEquals(false, response.getData());
    }

    @Test
    public void testAddCorpTagSuccess() throws Throwable {
        AddCorpTagRequest request = new AddCorpTagRequest();
        request.setToken("token");
        request.setAppId("appId");
        when(ssosvOpenApi.getUserInfoByAccessToken(request.getToken())).thenReturn(new SsoUserInfoDTO());
        when(appConfigRepository.getCorpIdByAppId(request.getAppId())).thenReturn("corpId");
        when(tagDomainService.addCorpTag(any(), any(), any(), any(), any())).thenReturn(true);
        RemoteResponse<Boolean> response = tagService.addCorpTag(request);
        assertEquals("success", response.getMsg());
        assertEquals(true, response.getData());
    }

    /**
     * 测试queryTagByGroupId方法，当appId和tagGroupId都不为空时，应返回非空列表
     */
    @Test
    public void testQueryTagByGroupIdWithValidAppIdAndTagGroupId() {
        // arrange
        String appId = "testAppId";
        String tagGroupId = "testTagGroupId";
        List<Tag> expectedTags = Lists.newArrayList(new Tag());
        when(tagMapper.selectByExample(any(TagExample.class))).thenReturn(expectedTags);

        // act
        List<Tag> result = tagService.queryTagByGroupId(appId, tagGroupId, false);

        // assert
        assertFalse("结果列表应该非空", result.isEmpty());
        verify(tagMapper, times(1)).selectByExample(any(TagExample.class));
    }

    /**
     * 测试queryTagByGroupId方法，当appId为空时，应返回空列表
     */
    @Test
    public void testQueryTagByGroupIdWithEmptyAppId() {
        // arrange
        String appId = "";
        String tagGroupId = "testTagGroupId";

        // act
        List<Tag> result = tagService.queryTagByGroupId(appId, tagGroupId, false);

        // assert
        assertTrue("当appId为空时，应返回空列表", result.isEmpty());
        verify(tagMapper, never()).selectByExample(any(TagExample.class));
    }

    /**
     * 测试queryTagByGroupId方法，当tagGroupId为空时，应返回空列表
     */
    @Test
    public void testQueryTagByGroupIdWithEmptyTagGroupId() {
        // arrange
        String appId = "testAppId";
        String tagGroupId = "";

        // act
        List<Tag> result = tagService.queryTagByGroupId(appId, tagGroupId, false);

        // assert
        assertTrue("当tagGroupId为空时，应返回空列表", result.isEmpty());
        verify(tagMapper, never()).selectByExample(any(TagExample.class));
    }

    /**
     * 测试queryTagByGroupId方法，当fromMaster为true时，应调用forceMasterInLocalContext和clearLocalContext
     */
    @Test
    public void testQueryTagByGroupIdWithFromMasterTrue() {
        // arrange
        String appId = "testAppId";
        String tagGroupId = "testTagGroupId";
        List<Tag> expectedTags = Lists.newArrayList(new Tag());
        when(tagMapper.selectByExample(any(TagExample.class))).thenReturn(expectedTags);

        // act
        List<Tag> result = tagService.queryTagByGroupId(appId, tagGroupId, true);

        // assert
        assertFalse("结果列表应该非空", result.isEmpty());
        verify(tagMapper, times(1)).selectByExample(any(TagExample.class));
        // 注意：这里无法直接验证ZebraForceMasterHelper的调用，因为它是静态方法。在实际测试中，可以使用PowerMock等工具进行验证。
    }
}
