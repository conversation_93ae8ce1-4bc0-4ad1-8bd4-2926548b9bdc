package com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/10/24
 */
@Data
public class ScrmAmProcessOrchestrationBranchExeLogDTO implements Serializable {

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: node_id
     *   说明: 本节点id
     */
    private Long nodeId;

    /**
     *   字段: pre_node_id
     *   说明: 上级节点id
     */
    private Long preNodeId;

    /**
     *   字段: orderCount
     *   说明: 下单数量
     */
    private long orderCount;

    /**
     *   字段: clickCount
     *   说明: 浏览数量
     */
    private long clickCount;

    /**
     *   字段: quitGroupCount
     *   说明: 退群数量 0否 1是
     */
    private long quitGroupCount;

    /**
     *   字段: deleteFriendCount
     *   说明: 删好友数量 0否 1是
     */
    private long deleteFriendCount;

    /**
     * 综合流失率
     */
    private long lossCount;
}
