package com.sankuai.scrm.core.service.envrequestforwarding.fortest;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetTokenByCorpIdRequest;
import com.sankuai.service.fe.corp.wx.thrift.GetTokenByCorpIdResponse;
import com.sankuai.service.fe.corp.wx.thrift.open.OpenGroupService;
import com.sankuai.service.fe.corp.wx.thrift.open.QueryGroupInfoRequest;
import com.sankuai.service.fe.corp.wx.thrift.open.QueryGroupInfoResponse;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdInfoService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
@Slf4j
@MdpPigeonServer
public class TotestInterfaceImpl implements ToTestInterface {
    @Resource
    private OpenGroupService.Iface openGroupService;

    @Resource
    private ThirdInfoService.Iface rpcUserThirdInfoService;

    @Resource
    private CorpWxService.Iface corpWxService;

    @Override
    public String testCorpWxService(GetTokenByCorpIdRequest request) {
        try {
            GetTokenByCorpIdResponse result = corpWxService.getTokenByCorpId(request);
            if(result==null){return "null";}
            return JacksonUtils.simpleSerialize(result);
        }
        catch (Exception e){
            log.error("test error", e);
            return e.getMessage();
        }
    }
    @Override
    public String testOpenGroupService(QueryGroupInfoRequest request) {
        try {
            QueryGroupInfoResponse result = openGroupService.queryGroupInfo(request);
            if(result==null){return "null";}
            return JacksonUtils.simpleSerialize(result);
        }
        catch (Exception e){
            log.error("test error", e);
            return e.getMessage();
        }

    }
    @Override
    public String testThirdInfoService() {
        try {
            String result = rpcUserThirdInfoService.getMobikeAccessToken();
            if(result==null){return "null";}
            return JacksonUtils.simpleSerialize(result);
        }
        catch (Exception e){
            log.error("test error", e);
            return e.getMessage();
        }

    }
}
