package com.sankuai.scrm.core.service.user.domain.score.data;

import com.sankuai.dz.srcm.user.score.BehaviorDTO;
import com.sankuai.dz.srcm.user.score.BehaviorQueryContext;
import com.sankuai.dz.srcm.user.score.UserTagBehaviorEnum;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description 直播有营销互动
 * <AUTHOR>
 * @Create On 2025/4/3 17:35
 * @Version v1.0.0
 */
@Slf4j
@Component
public class BehaviorQueryLiveMarketingService implements BehaviorQueryService {
    @Override
    public List<BehaviorDTO> doQuery(BehaviorQueryContext<ScrmUserGrowthYimeiLiveUserTag> context) {
        return new ArrayList<>();//todo
    }

    @Override
    public List<UserTagBehaviorEnum> supportBehavior() {
        return Arrays.asList(UserTagBehaviorEnum.LIVE_MARKETING);
    }
}
