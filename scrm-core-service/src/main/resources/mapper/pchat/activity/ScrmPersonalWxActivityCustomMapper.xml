<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.activity.mapper.ScrmPersonalWxActivityCustomMapper">

  <resultMap id="groupMemberMap" type="com.sankuai.dz.srcm.pchat.response.activity.ActivityFissionMemberResponse">
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
  </resultMap>

  <!-- 重复进群开关开启 查询被邀请的群成员，永远只有第一次参与活动时被邀请入群的人，才是真的邀请者 -->
  <sql id="queryCheckReJoinGroupSwitchOnMember">
    select min(m.id) as minid, m.wx_id from Scrm_PersonalWx_Activity a
    INNER JOIN Scrm_PersonalWx_ActivityGroupMapping ag on ag.activity_id=a.id
    INNER JOIN Scrm_PersonalWx_GroupInfo g on (g.family_code=ag.group_family_code and g.project_id=a.project_id)
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo m on m.group_id=g.chat_room_wx_serial_no
    <where>
      a.id=#{activityId}
      and g.deleted=false
      and a.state in ('2','3')
      and m.invite_wx_id is not null
      and m.member_type='2'
      and m.add_time &lt; a.end_time
      <if test="checkLeaveGroupSwitch!=null and checkLeaveGroupSwitch=='1'.toString()">
        and m.status='0'
      </if>
    </where>
    group by m.wx_id
  </sql>

  <!-- 查询活动参与成员 -->
  <select id="queryActivityParticipationGroupMember" resultMap="groupMemberMap">
    select wx_id from (
    SELECT m.wx_id as wx_id,max(add_time) add_time from
    <include refid="queryActivityMemberSegment" />
    group by m.wx_id
    order by m.add_time desc
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
    ) t order by t.add_time desc
  </select>
  <sql id="queryActivityMemberSegment">
    Scrm_PersonalWx_GroupMemberInfo m
    <where>
      m.member_type='2'
      and group_id in
      <foreach collection="chatroomSerialNos" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
      <if test="keyword!=null and keyword!=''">
        and m.wx_nickname like #{keyword}
      </if>
    </where>
  </sql>
  <!-- 查询活动参与成员数量 -->
  <select id="queryActivityParticipationGroupMemberCount" resultType="java.lang.Integer">
    SELECT count(DISTINCT m.wx_id) as wx_id from
    <include refid="queryActivityMemberSegment" />
  </select>

  <resultMap id="validGroupMemberMap" type="com.sankuai.dz.srcm.pchat.dto.WxGroupMemberDTO">
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="invite_wx_id" jdbcType="VARCHAR" property="inviteWxId" />
  </resultMap>
  <!-- 查询活动中属于某人有效邀请的名单  跨群统计 -->
  <select id="queryValidInvitedList" resultMap="validGroupMemberMap">
    SELECT m.wx_id, m.invite_wx_id as inviteWxId from
    (
    <include refid="queryCheckReJoinGroupSwitchOnMember" />
    ) member
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo m on m.id=member.minid
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo inviter on (inviter.group_id=m.group_id and inviter.wx_id=m.invite_wx_id and inviter.member_type='2')
    where
    m.invite_wx_id =#{wxId}
  </select>
  <!-- 查询活动中属于某人有效邀请的名单  跨群统计 -->
  <select id="queryBatchValidInvitedList" resultMap="validGroupMemberMap">
    SELECT m.wx_id, m.invite_wx_id as invite_wx_id from
    (
    <include refid="queryCheckReJoinGroupSwitchOnMember" />
    ) member
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo m on m.id=member.minid
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo inviter on (inviter.group_id=m.group_id and inviter.wx_id=m.invite_wx_id and inviter.member_type='2')
    <where>
    <if test="wxInviteIds!=null and wxInviteIds.size > 0">
      and m.invite_wx_id in
      <foreach collection="wxInviteIds" item="listItem" open="(" close=")" separator=",">
        #{listItem}
      </foreach>
    </if>
      <if test="wxIds!=null and wxIds.size > 0">
        and m.wx_id in
        <foreach collection="wxIds" item="listItem" open="(" close=")" separator=",">
          #{listItem}
        </foreach>
      </if>
    </where>
  </select>

  <resultMap id="QueryToBeDistributeRewardRecordMap" type="com.sankuai.scrm.core.service.pchat.dto.activity.ActivityRewardRecordToBeDistributeDTO">
    <result column="wx_nickname" jdbcType="VARCHAR" property="wxNickname" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="invite_count" jdbcType="INTEGER" property="inviteCount" />
    <result column="reward_product_id" jdbcType="BIGINT" property="rewardProductId" />
    <result column="reward_record_id" jdbcType="BIGINT" property="rewardRecordId" />
    <result column="group_member_id" jdbcType="BIGINT" property="groupMemberId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
  </resultMap>
  <select id="queryToBeDistributeRewardRecord" resultMap="QueryToBeDistributeRewardRecordMap">
    SELECT r.id as reward_record_id,r.reward_product_id,r.invite_count,r.group_member_id,m.wx_nickname,m.avatar,m.user_id from
    <include refid="queryToBeDistributeRewardRecordSegment" />
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <sql id="queryToBeDistributeRewardRecordSegment">
    Scrm_PersonalWx_ActivityRewardRecord r
    INNER JOIN Scrm_PersonalWx_GroupMemberInfo m on m.id=r.group_member_id
    <where>
      r.activity_id=#{activityId}
      <if test="pushState!=null and pushState.size > 0">
        and r.push_state in
        <foreach collection="pushState" item="listItem" open="(" close=")" separator=",">
          #{listItem}
        </foreach>
      </if>
      and m.union_id is not null
    </where>
  </sql>
  <select id="queryToBeDistributeRewardRecordTotal" resultType="java.lang.Integer">
    SELECT count(1) from
    <include refid="queryToBeDistributeRewardRecordSegment" />
  </select>
  <resultMap id="StatRewardCountGroupByProductIdMap" type="com.sankuai.scrm.core.service.pchat.dto.activity.ActivityRewardProductStatDTO">
    <result column="reward_count" jdbcType="INTEGER" property="rewardCount" />
    <result column="reward_product_id" jdbcType="VARCHAR" property="rewardProductId" />
  </resultMap>
  <select id="statRewardCountGroupByProductId" resultMap="StatRewardCountGroupByProductIdMap">
    SELECT count(1) as reward_count ,reward_product_id from Scrm_PersonalWx_ActivityRewardRecord
    where activity_id=#{activityId}
    group by reward_product_id
  </select>

</mapper>