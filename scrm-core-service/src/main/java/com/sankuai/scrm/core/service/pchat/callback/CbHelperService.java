package com.sankuai.scrm.core.service.pchat.callback;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxAsyncSerialNoLog;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.CallbackDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxAsyncLogEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/21 11:08
 * @Version v1.0.0
 */
@Slf4j
@Service
public class CbHelperService {
    @Resource
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    public ScrmPersonalWxAsyncSerialNoLog getAndUpdateSerialLog(String strContext, CallbackDTO dto) {
        ScrmPersonalWxAsyncSerialNoLog serialNoLog = getScrmPersonalWxAsyncSerialNoLog(dto.getVcSerialNo());
        if (serialNoLog == null) {
            return null;
        }
        serialNoLog.setExecuteResult(dto.isSuccess() ? PersonalWxAsyncLogEnum.SUCCESS.getCode() : PersonalWxAsyncLogEnum.FAILURE.getCode());//todo
        serialNoLog.setExecuteResultExtra(strContext);
        groupManageDomainService.updateSerialLogById(serialNoLog);
        return serialNoLog;
    }

    public ScrmPersonalWxAsyncSerialNoLog getAndUpdateSerialLog(String strContext, String relaSerialNo, boolean isSuccess) {
        ScrmPersonalWxAsyncSerialNoLog serialNoLog = getScrmPersonalWxAsyncSerialNoLog(relaSerialNo);
        if (serialNoLog == null) {
            return null;
        }
        serialNoLog.setExecuteResult(isSuccess ? PersonalWxAsyncLogEnum.SUCCESS.getCode() : PersonalWxAsyncLogEnum.FAILURE.getCode());//todo
        serialNoLog.setExecuteResultExtra(strContext);
        groupManageDomainService.updateSerialLogById(serialNoLog);
        return serialNoLog;
    }

    public ScrmPersonalWxAsyncSerialNoLog getScrmPersonalWxAsyncSerialNoLog(String serialNo) {
        ScrmPersonalWxAsyncSerialNoLog serialNoLog = groupManageDomainService.querySerialLogBySerialNo(serialNo);
        return serialNoLog;
    }

    public ScrmPersonalWxAsyncSerialNoLog getScrmPersonalWxAsyncSerialNoLog(String serialNo, String businessType) {
        ScrmPersonalWxAsyncSerialNoLog serialNoLog = groupManageDomainService.querySerialLogBySerialNo(serialNo);
        return serialNoLog;
    }
}
