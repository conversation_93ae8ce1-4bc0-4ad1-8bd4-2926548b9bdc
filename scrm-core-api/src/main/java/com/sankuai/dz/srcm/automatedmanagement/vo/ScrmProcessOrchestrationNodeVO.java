package com.sankuai.dz.srcm.automatedmanagement.vo;

import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationConditionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  流程编排节点信息
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/17
 */
@Data
public class ScrmProcessOrchestrationNodeVO implements Serializable {

    /**
     *   字段: node_id
     *   说明: 本节点id
     */
    private Long nodeId;

    /**
     *   字段: pre_node_id
     *   说明: 上级节点id
     */
    private Long preNodeId;

    /**
     *   字段: node_type
     *   说明: 节点类型 (1:条件/2:行为)
     *   @see ScrmProcessOrchestrationNodeTypeEnum
     */
    private Integer nodeType;

    /**
     * 当nodeType=2时生效
     * @see ScrmProcessOrchestrationActionTypeEnum
     */
    private Integer actionNodeType = 0;

    /**
     * 当nodeType=1时生效
     * @see ScrmProcessOrchestrationConditionTypeEnum
     */
    private Integer conditionNodeType = 0;

    /**
     * 当nodeType=3时生效
     * @see ScrmProcessOrchestrationActionTypeEnum
     */
    private Integer autoActionNodeType = 0;

    /**
     * 子节点
     */
    private List<Long> childNodeIdList;

    /**************************条件节点字段*****************************/
    /**
     * 条件分支
     */
    private ScrmProcessOrchestrationConditionVO conditionBranch;

    /**
     * 延迟
     */
    private ScrmProcessOrchestrationDelayVO delayBranch;

    /**
     * 分流
     */
    private ScrmProcessOrchestrationSplitVO splitBranch;


    /**************************行为节点字段*****************************/
    /**
     * 行为
     */
    private ScrmProcessOrchestrationActionVO actionVO;

    /**
     * 自动化行为
     */
    private ScrmProcessOrchestrationActionVO automatedActionVO;

    private ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO;
}
