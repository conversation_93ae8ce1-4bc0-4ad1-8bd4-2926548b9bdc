package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.pchat.anno.NTypeCallbackMapping;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 15:34
 * @Version v1.0.0
 */
public interface RobotInfoCbService extends PlatformCallbackService {

    /**
     * 群信息回调（兼容PC）
     * 群名称、群主转移（机器人被转移为群主）、群头像变化，当多个机器人在群内时，所有机器人群信息变动回调参照以下规则：
     * 1、非机器人群主把群主转移给为管理员的机器人时，每个机器人不论是否开群都会收到群信息变动回调（4001），每个机器人收到群管理员变动回调（4510，被设置为群主的机器人管理员取消）
     * 2、非机器人群主把群主转移给非管理员机器人时，每个机器人收到4001回调
     * 3、非机器人群主把群主转移给普通成员的管理员时，每个机器人都会收到群信息变动回调（4001） ，同时每个机器人会收到群管理员变动回调（4510，普通群成员的管理员被取消变成了群主）
     * 4、非机器人群主把群主转移给普通成员时，每个机器人会收到4001回调
     * 机器人群主转移群主时，无群信息回调（4001）
     * 5、群成员进退群时，群人数变化也会有4001回调通知商家；
     * PC协议无企业微信外部群相关字段，仅支持接收全量群数据，群变动无感知；
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4001, desc = "群信息回调（兼容PC）")
    RemoteResponse do4001(String strContext);

    /**
     * 好友信息列表回调（兼容PC）
     * 1、机器人登录后，平台分批回调好友列表（3022），每批1000个好友，直至全部回调完成；
     * 2、机器人在不掉线的情况下每24小时，平台主动分批推送好友列表（3022）用于商家校验；
     *
     * 【主动推送好友列表】
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3022, desc = "好友信息列表回调（兼容PC）")
    RemoteResponse do3022(String strContext);

    /**
     * 修改机器人信息回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 1006, desc = "修改机器人信息回调")
    RemoteResponse do1006(String strContext);

    /**
     * 分页获取商家机器人详情列表回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 1090, desc = "分页获取商家机器人详情列表回调")
    RemoteResponse do1090(String strContext);

    /**
     * 获取机器人个人二维码3.0回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4517, desc = "获取机器人个人二维码3.0回调")
    RemoteResponse do4517(String strContext);

    /**
     * 【异步调用】异步分页获取机器人好友列表接口（仅PC可用）回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3034, desc = "【异步调用】异步分页获取机器人好友列表接口（仅PC可用）回调")
    RemoteResponse do3034(String strContext);
}

