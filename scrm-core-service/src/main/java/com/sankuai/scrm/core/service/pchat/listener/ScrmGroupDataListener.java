package com.sankuai.scrm.core.service.pchat.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteWxUserInfo;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupConsultantMapping;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxSendMQLogDomainService;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupConsultantBindDataDTO;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberPushDataDTO;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberStateChangeDataDTO;
import com.sankuai.scrm.core.service.pchat.dto.group.UserDataDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberJoinTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberStatusEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.TuseMsgNTypeEnum;
import com.sankuai.scrm.core.service.pchat.event.GroupConsultantBindEvent;
import com.sankuai.scrm.core.service.pchat.event.GroupMemberEvent;
import com.sankuai.scrm.core.service.pchat.event.GroupMemberRefreshEvent;
import com.sankuai.scrm.core.service.pchat.event.GroupMemberStateEvent;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmGroupActionRefreshProducer;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmGroupConsultantBindPushProducer;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmGroupMemberDataPushProducer;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmGroupMemberStatePushProducer;
import com.sankuai.scrm.core.service.pchat.service.WebcastService;
import com.sankuai.scrm.core.service.pchat.utils.PaginationProcessor;
import com.sankuai.scrm.core.service.user.enums.PersonalWxGroupActionType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 群数据监听服务
 *
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/3 19:40
 * @Version v1.0.0
 */
@Slf4j
@Service
public class ScrmGroupDataListener {
    @Resource
    private ScrmGroupMemberStatePushProducer groupMemberStatePushProducer;
    @Resource
    private ScrmGroupMemberDataPushProducer groupMemberDataPushProducer;
    @Resource
    private ScrmGroupConsultantBindPushProducer groupConsultantBindPushProducer;
    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private GroupActionListenerService groupActionListenerService;
    @Resource
    private ScrmGroupActionRefreshProducer groupActionRefreshProducer;
    @Resource
    private WebcastService webcastService;
    @Resource
    private ScrmPersonalWxSendMQLogDomainService personalWxSendMQLogDomainService;
    @Resource
    private RedisStoreClient redisClient;


    @EventListener
    @Async
    public void handleGroupMemberEvent(GroupMemberEvent event) {
        log.info("{},处理群成员进退群事件,{}", "ScrmGroupDataListener.handleGroupMemberEvent", JsonUtils.toStr(event.getGroupMemberInfoEntities()));

        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = event.getGroupMemberInfoEntities();
        if (CollectionUtil.isEmpty(groupMemberInfoEntities)) {
            return;
        }
        Map<String, String> chatroomSerialNoWithProjectIdMap = obtainProjectInfo(groupMemberInfoEntities);
        String liveId = chatroomSerialNoWithProjectIdMap.get(groupMemberInfoEntities.get(0).getGroupId());
        if (StringUtils.isBlank(liveId)) {
            log.info("liveId is null,groupId:{}", groupMemberInfoEntities.get(0).getGroupId());
            Cat.logEvent("LiveIdIsNull-handleGroupMemberEvent", groupMemberInfoEntities.get(0).getGroupId());
            return;
        }
        // 个人溯源
        if (webcastService.isPersonalTrace(liveId)) {
            doPersonalTraceMemberEvent(event, liveId, groupMemberInfoEntities);
            return;
        }
        doGroupTraceMemberEvent(liveId, groupMemberInfoEntities);
    }


    private void doPersonalTraceMemberEvent(GroupMemberEvent event, String liveId, List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities) {
        log.info("个人溯源有普通用户推数据");
        groupMemberInfoEntities.forEach(memberInfo -> {
            PersonalWxGroupMemberTypeEnum personalWxGroupMemberTypeEnum = PersonalWxGroupMemberTypeEnum.fromCode(memberInfo.getMemberType());
            if (personalWxGroupMemberTypeEnum != PersonalWxGroupMemberTypeEnum.NORMAL) {//机器人 不推
                return;
            }
            PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(memberInfo.getJoinType());
            GroupMemberPushDataDTO dto = buildGroupMemberPushDataDTO(liveId, memberInfo, obtainMemberConsultantTaskId(liveId, memberInfo), personalWxGroupMemberJoinTypeEnum);
            dto.setInviterUserInfo(findInviteWxUser(memberInfo));
            boolean isPushProducer = !TuseMsgNTypeEnum.MT_4502.getCode().equals(event.getEventSource()) || StringUtils.isNotBlank(memberInfo.getInviteWxId());
            pushMemberData(isPushProducer, memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
        });
    }

    /**
     * 获取成员对应的咨询师任务id
     *
     * @param liveId
     * @param m
     * @return
     */
    private Long obtainMemberConsultantTaskId(String liveId, ScrmPersonalWxGroupMemberInfoEntity m) {
        Long consultantTaskId = 1L;//个人溯源不必要推咨询师id，但标签逻辑需要咨询师id（内部逻辑只是用来判断，获取真实咨询师时是通过调用咨询师接口，此处返回1即可【占位】）
//        List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByProjectId(liveId, null);
//        if (CollectionUtil.isEmpty(consultantMappings)) {// 绑定咨询师
//            return consultantTaskId;
//        }
//        ScrmPersonalWxFirstInviteRecord firstInviteRecord = personalWxFirstInviteRecordDomainService.queryTop1FirstInvitedRecord(liveId, m.getWxId());
//        if (firstInviteRecord != null) {
//            ScrmPersonalWxGroupConsultantMapping mapping = consultantMappings.stream().filter(c -> c.getWxId().equals(firstInviteRecord.getRobotInviteWxId())).findFirst().orElse(null);
//            consultantTaskId = mapping != null ? mapping.getConsultantTaskId() : null;
//        }
        return consultantTaskId;
    }

    private void pushMemberData(boolean isPushProducer, ScrmPersonalWxGroupMemberInfoEntity memberInfo, GroupMemberPushDataDTO dto, PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum) {
        log.info("推送到咨询师:isPushProducer:{},member:{}", isPushProducer, memberInfo);
        if (isPushProducer) {
            sendGroupMemberMQ(dto);
        }
        log.info("推送入群离群事件");
        groupActionListenerService.pushGroupAction(dto.getWxId(), Objects.equals(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode(), memberInfo.getStatus()) ? PersonalWxGroupActionType.ENTER_GROUP : PersonalWxGroupActionType.EXIT_GROUP, dto.getMtUserId(), dto.getUnionId(), dto.getConsultantTaskId(), dto.getLiveId(), personalWxGroupMemberJoinTypeEnum);
    }

    private void sendGroupMemberMQ(GroupMemberPushDataDTO dto) {
        sendMQ("gmmq", dto.getChatroomSerialNo() + "_" + dto.getWxId(), JsonUtils.toStr(dto), o -> groupMemberDataPushProducer.sendAsyncMessage(o), dto.getLiveId(), dto.getConsultantTaskId(), dto.getChatroomSerialNo(),dto.getWxId(),dto.getUnionId(), dto.getActionType(),dto.getUserType());
    }

    private void doGroupTraceMemberEvent(String liveId, List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities) {
        log.info("群溯源推数据");
        Optional<ScrmPersonalWxGroupMemberInfoEntity> consultantMember = groupMemberInfoEntities.stream().filter(m -> Objects.equals(PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode(), m.getMemberType())).collect(Collectors.toList()).stream().findFirst();
        if (consultantMember.isPresent() && Objects.equals(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode(), consultantMember.get().getStatus())) {
            applicationEventPublisher.publishEvent(new GroupConsultantBindEvent(consultantMember.get()));
            applicationEventPublisher.publishEvent(new GroupMemberRefreshEvent(consultantMember.get()));
            return;
        }

        groupMemberInfoEntities.forEach(memberInfo -> {
            PersonalWxGroupMemberTypeEnum personalWxGroupMemberTypeEnum = PersonalWxGroupMemberTypeEnum.fromCode(memberInfo.getMemberType());
            if (personalWxGroupMemberTypeEnum != PersonalWxGroupMemberTypeEnum.NORMAL) {//机器人 不推
                return;
            }
            List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByChatroomSerialNo(memberInfo.getGroupId());
            if (CollectionUtil.isEmpty(consultantMappings)) {// 未绑定咨询师，不推送
                log.info("{},未绑定咨询师，chatroomSerialNo:{}", "GroupActionListenerService.handleGroupAction", memberInfo.getGroupId());
                return;
            }

            PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(memberInfo.getJoinType());
            GroupMemberPushDataDTO dto = buildGroupMemberPushDataDTO(liveId, memberInfo, consultantMappings.get(0).getConsultantTaskId(), personalWxGroupMemberJoinTypeEnum);

            pushMemberData(true, memberInfo, dto, personalWxGroupMemberJoinTypeEnum);
        });
    }

    private GroupMemberPushDataDTO buildGroupMemberPushDataDTO(String liveId, ScrmPersonalWxGroupMemberInfoEntity memberInfo, Long consultantTaskId, PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum) {
        GroupMemberPushDataDTO dto = new GroupMemberPushDataDTO();
        dto.setActionTime(new Date().getTime());
        dto.setActionType(Objects.equals(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode(), memberInfo.getStatus()) ? 1 : 2);
        dto.setChatroomSerialNo(memberInfo.getGroupId());
        dto.setConsultantTaskId(consultantTaskId);
        dto.setUnionId(memberInfo.getUnionId());
        dto.setWxId(memberInfo.getWxId());
        dto.setMtUserId(memberInfo.getUserId());
        dto.setWxNickname(memberInfo.getWxNickname());
        dto.setUserType(personalWxGroupMemberJoinTypeEnum != null ? personalWxGroupMemberJoinTypeEnum.getUserType() : null);
        dto.setLiveId(liveId);
        return dto;
    }

    /**
     * 查找要邀请人用户
     *
     * @param currentMember
     * @return
     */

    private MemberInviteWxUserInfo findInviteWxUser(ScrmPersonalWxGroupMemberInfoEntity currentMember) {
        MemberInviteWxUserInfo userInfo = new MemberInviteWxUserInfo();
        String groupId = currentMember.getGroupId();
        String inviteWxId = currentMember.getInviteWxId();
        if (StrUtil.isBlank(groupId) || StrUtil.isBlank(inviteWxId)) {
            return userInfo;
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupManageDomainService.queryGroupMemberListByWxIds(Collections.singletonList(groupId), Collections.singletonList(inviteWxId));
        if (CollectionUtil.isEmpty(groupMemberInfoEntities)) {
            return userInfo;
        }
        ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity = groupMemberInfoEntities.get(0);
        userInfo.setInviteMtUserId(groupMemberInfoEntity.getUserId() == null ? 0L : groupMemberInfoEntity.getUserId());
        userInfo.setInviteWxId(inviteWxId);
        userInfo.setInviteUnionId(groupMemberInfoEntity.getUnionId());
        userInfo.setMtUserId(currentMember.getUserId() == null ? 0L : currentMember.getUserId());
        userInfo.setWxId(currentMember.getWxId());
        userInfo.setUnionId(currentMember.getUnionId());
        userInfo.setEnterTime(currentMember.getEnterTime());
        return userInfo;
    }

    private Map<String, String> obtainProjectInfo(List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities) {
        List<String> chatroomSerialNos = groupMemberInfoEntities.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getGroupId).distinct().collect(Collectors.toList());
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntities = personalWxGroupManageDomainService.queryGroupByChatroomSerialNos(chatroomSerialNos);
        return groupInfoEntities.stream().filter(g -> StringUtils.isNotBlank(g.getProjectId())).collect(Collectors.toMap(ScrmPersonalWxGroupInfoEntity::getChatRoomWxSerialNo, ScrmPersonalWxGroupInfoEntity::getProjectId, (u1, u2) -> u1));
    }


    @EventListener
    @Async
    public void handleGroupMemberStateChangeEvent(GroupMemberStateEvent event) {
        log.info("{},处理成员状态绑定事件,{}", "ScrmGroupDataListener.handleGroupMemberStateChangeEvent", JsonUtils.toStr(event));
        ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity = (ScrmPersonalWxGroupMemberInfoEntity) event.getSource();
        PersonalWxGroupMemberTypeEnum personalWxGroupMemberTypeEnum = PersonalWxGroupMemberTypeEnum.fromCode(memberInfoEntity.getMemberType());
        if (personalWxGroupMemberTypeEnum != PersonalWxGroupMemberTypeEnum.NORMAL) {//机器人 不推
            return;
        }
        String chatroomSerialNo = memberInfoEntity.getGroupId();
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(chatroomSerialNo);
        if (groupInfoEntity == null || StringUtils.isBlank(groupInfoEntity.getProjectId())) {
            log.info("liveId is null,groupId={}", groupInfoEntity == null ? "" : groupInfoEntity.getChatRoomWxSerialNo());
            Cat.logEvent("LiveIdIsNull-handleGroupMemberStateChangeEvent", groupInfoEntity == null ? "" : groupInfoEntity.getChatRoomWxSerialNo());
            return;
        }
        String liveId = groupInfoEntity.getProjectId();
        if (webcastService.isPersonalTrace(liveId)) {
            doPersonalTraceMemberStateChangeEvent(memberInfoEntity);
            return;
        }
        doGroupMemberStateChangeEvent(memberInfoEntity);
    }

    private void doPersonalTraceMemberStateChangeEvent(ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity) {
        log.info("个人溯源有普通用户变更推数据");
        doPushGroupMemberStateChange(memberInfoEntity);
    }

    private void doGroupMemberStateChangeEvent(ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity) {
        List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByChatroomSerialNo(memberInfoEntity.getGroupId());
        if (CollectionUtil.isEmpty(consultantMappings)) {// 未绑定咨询师，不推送
            log.info("{},未绑定咨询师，chatroomSerialNo:{}", "GroupActionListenerService.handleGroupAction", memberInfoEntity.getGroupId());
            return;
        }
        doPushGroupMemberStateChange(memberInfoEntity);
    }

    private void doPushGroupMemberStateChange(ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity) {
        PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(memberInfoEntity.getJoinType());
        GroupMemberStateChangeDataDTO dto = buildGroupMemberStateChangeDataDTO(memberInfoEntity, personalWxGroupMemberJoinTypeEnum);
        sendGroupMemberStateChangeMQ(dto);
        // 推送入群离群事件
        pushGroupMemberStateChangeActionEvent(memberInfoEntity, dto);
    }

    private void sendGroupMemberStateChangeMQ(GroupMemberStateChangeDataDTO dto) {
        sendMQ("gmscmq", dto.getChatroomSerialNo() + "_" + dto.getWxId(), JsonUtils.toStr(dto), o -> groupMemberStatePushProducer.sendAsyncMessage(o), dto.getChatroomSerialNo(), dto.getWxId(), dto.getUnionId(), dto.getUserType());
    }

    private GroupMemberStateChangeDataDTO buildGroupMemberStateChangeDataDTO(ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity, PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum) {
        GroupMemberStateChangeDataDTO dto = new GroupMemberStateChangeDataDTO();
        dto.setWxId(memberInfoEntity.getWxId());
        dto.setWxNickname(memberInfoEntity.getWxNickname());
        dto.setChatroomSerialNo(memberInfoEntity.getGroupId());
        dto.setUserType(personalWxGroupMemberJoinTypeEnum != null ? personalWxGroupMemberJoinTypeEnum.getUserType() : null);
        dto.setUnionId(memberInfoEntity.getUnionId());
        dto.setMtUserId(memberInfoEntity.getUserId());
        return dto;
    }

    private void pushGroupMemberStateChangeActionEvent(ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity, GroupMemberStateChangeDataDTO dto) {
        List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByChatroomSerialNo(memberInfoEntity.getGroupId());
        if (CollectionUtil.isEmpty(consultantMappings)) {// 未绑定咨询师，不推送
            log.info("{},未绑定咨询师，chatroomSerialNo:{}", "GroupActionListenerService.handleGroupAction", memberInfoEntity.getGroupId());
            return;
        }
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(memberInfoEntity.getGroupId());
        if (groupInfoEntity == null) {
            log.info("{},群信息不存在，chatroomSerialNo:{}", "GroupActionListenerService.handleGroupAction", memberInfoEntity.getGroupId());
            return;
        }
        PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(memberInfoEntity.getJoinType());

        groupActionListenerService.pushGroupAction(memberInfoEntity.getWxId(), PersonalWxGroupActionType.ENTER_GROUP, dto.getMtUserId(), dto.getUnionId(), consultantMappings.get(0).getConsultantTaskId(), groupInfoEntity.getProjectId(), personalWxGroupMemberJoinTypeEnum);
    }

    @EventListener
    @Async
    public void handleGroupConsultantBindEvent(GroupConsultantBindEvent event) {
        String eventStr = JsonUtils.toStr(event);
        log.info("{},处理咨询师绑定事件，{}", "ScrmGroupDataListener.handleGroupConsultantBindEvent", eventStr);
        ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity = (ScrmPersonalWxGroupMemberInfoEntity) event.getSource();
        if (!PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode().equals(memberInfoEntity.getMemberType())) {
            log.info("不是咨询师，不推送:{}", eventStr);
            return;
        }
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(memberInfoEntity.getGroupId());
        if (groupInfoEntity == null || StringUtils.isBlank(groupInfoEntity.getProjectId())) {
            log.info("liveId is null,groupId={}", groupInfoEntity == null ? "" : groupInfoEntity.getChatRoomWxSerialNo());
            Cat.logEvent("LiveIdIsNull-handleGroupConsultantBindEvent", groupInfoEntity == null ? "" : groupInfoEntity.getChatRoomWxSerialNo());
            return;
        }
        if (webcastService.isPersonalTrace(groupInfoEntity.getProjectId())) {
            log.info("个人溯源，此处不推数据");
            return;
        }
        List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByChatroomSerialNo(memberInfoEntity.getGroupId());
        if (CollectionUtil.isEmpty(consultantMappings)) {// 未绑定咨询师，不推送
            log.info("未绑定咨询师，不推送:{}", eventStr);
            return;
        }
        GroupConsultantBindDataDTO dto = new GroupConsultantBindDataDTO();
        dto.setChatroomSerialNo(memberInfoEntity.getGroupId());
        dto.setConsultantTaskId(consultantMappings.get(0).getConsultantTaskId());
        List<UserDataDTO> dataDTOList = new ArrayList<>();
        PaginationProcessor processor = new PaginationProcessor(100, 1);
        processor.processAll(() -> personalWxGroupManageDomainService.queryGroupMemberByGroupSerialNo(Collections.singletonList(memberInfoEntity.getGroupId()), PersonalWxGroupMemberTypeEnum.NORMAL, PageRequest.of(processor.getCurrentPage(), processor.getPageSize())), wxGroupMemberInfoEntities -> {
            Map<String, String> chatroomSerialNoWithProjectIdMap = obtainProjectInfo(wxGroupMemberInfoEntities);

            dataDTOList.addAll(wxGroupMemberInfoEntities.stream().map(m -> {
                UserDataDTO userDataDTO = new UserDataDTO();
                userDataDTO.setUnionId(m.getUnionId());
                userDataDTO.setWxId(m.getWxId());
                userDataDTO.setMtUserId(m.getUserId());
                userDataDTO.setWxNickname(m.getWxNickname());
                PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(m.getJoinType());
                userDataDTO.setUserType(personalWxGroupMemberJoinTypeEnum != null ? personalWxGroupMemberJoinTypeEnum.getUserType() : null);
                // 推送入群行为事件
                groupActionListenerService.pushGroupAction(m.getWxId(), PersonalWxGroupActionType.ENTER_GROUP, userDataDTO.getMtUserId(), userDataDTO.getUnionId(), dto.getConsultantTaskId(), chatroomSerialNoWithProjectIdMap.get(m.getGroupId()), personalWxGroupMemberJoinTypeEnum);
                return userDataDTO;
            }).collect(Collectors.toList()));

        });

        dto.setUserList(dataDTOList);
        sendGroupConsultantBindMQ(dto);
    }

    private void sendGroupConsultantBindMQ(GroupConsultantBindDataDTO dto) {
//        String key = StrUtil.format("{}_{}", dto.getChatroomSerialNo(), dto.getConsultantTaskId());
//        sendMQ("gcbmq", key, o -> {
        groupConsultantBindPushProducer.sendAsyncMessage(JsonUtils.toStr(dto));

//        });
    }

    @EventListener
    @Async
    public void handleGroupMemberRefreshEvent(GroupMemberRefreshEvent event) {
        String eventStr = JsonUtils.toStr(event);
        log.info("{},处理咨询师绑定刷新数据事件，{}", "ScrmGroupDataListener.handleGroupMemberRefreshEvent", eventStr);
        ScrmPersonalWxGroupMemberInfoEntity consultantMemberInfoEntity = (ScrmPersonalWxGroupMemberInfoEntity) event.getSource();
        if (!PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode().equals(consultantMemberInfoEntity.getMemberType())) {
            return;
        }
        List<ScrmPersonalWxGroupConsultantMapping> consultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByChatroomSerialNo(consultantMemberInfoEntity.getGroupId());
        if (CollectionUtil.isEmpty(consultantMappings)) {// 未绑定咨询师，不推送
            log.info("未绑定咨询师，不推送:{}", eventStr);
            return;
        }
        GroupConsultantBindDataDTO dto = new GroupConsultantBindDataDTO();
        dto.setChatroomSerialNo(consultantMemberInfoEntity.getGroupId());
        dto.setConsultantTaskId(consultantMappings.get(0).getConsultantTaskId());
        List<UserDataDTO> dataDTOList = new ArrayList<>();
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupManageDomainService.queryGroupMemberByInviteWxId(consultantMemberInfoEntity.getGroupId(), consultantMemberInfoEntity.getWxId());

        groupMemberInfoEntities.forEach(m -> {
            UserDataDTO userDataDTO = new UserDataDTO();
            userDataDTO.setUnionId(m.getUnionId());
            userDataDTO.setWxId(m.getWxId());
            userDataDTO.setMtUserId(m.getUserId());
            userDataDTO.setWxNickname(m.getWxNickname());
            PersonalWxGroupMemberJoinTypeEnum personalWxGroupMemberJoinTypeEnum = PersonalWxGroupMemberJoinTypeEnum.fromCode(m.getJoinType());
            userDataDTO.setUserType(personalWxGroupMemberJoinTypeEnum != null ? personalWxGroupMemberJoinTypeEnum.getUserType() : null);
            dataDTOList.add(userDataDTO);
        });

        dto.setUserList(dataDTOList);
        sendGroupMemberRefreshEventMQ(dto);
    }

    private void sendGroupMemberRefreshEventMQ(GroupConsultantBindDataDTO dto) {
        groupActionRefreshProducer.sendAsyncMessage(JsonUtils.toStr(dto));
    }

    private void sendMQ(String category, String queryKey, String json, Consumer<String> consumer, Object... params) {
        Boolean uniquePushSwitch = Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.group.push.mq.unique.switch", true);
        if (!uniquePushSwitch) {
            consumer.accept(json);
            return;
        }
        if (params == null || params.length == 0) {
            consumer.accept(json);
            return;
        }
        StringBuilder format = new StringBuilder();
        for (int i = 0; i < params.length; i++) {
            format.append("{}_");
        }
        idempotentSendMQ(category, StrUtil.format(format.toString(), params), queryKey, json, consumer);
    }

    private void idempotentSendMQ(String category, String key, String queryKey, String json, Consumer<String> consumer) {
        boolean success = false;
        try {
            int hashCode = key.hashCode();
            String memoryCategory = "scrm_mq_idempotent";
            log.info("推送mq：category={},key={},hashCode={}", category, key, hashCode);
            String uniqueKey = category + "_" + hashCode;
            // 优先从redis中查询是否重复
            Object memoryCache = redisClient.get(new StoreKey(memoryCategory, uniqueKey));
            if (memoryCache != null) {
                log.info("redis命中，消息已推送过，不再推送");
                success = true;
                return;
            }
            if (personalWxSendMQLogDomainService.exists(uniqueKey)) {
                log.info("db命中，消息已推送过，不再推送");
                success = true;
                return;
            }
            consumer.accept(json);
            success = true;
            redisClient.set(new StoreKey(memoryCategory, uniqueKey), System.currentTimeMillis());
            personalWxSendMQLogDomainService.save(category, uniqueKey, queryKey, json);
        } catch (Exception e) {
            log.error("idempotentSendMQ error,category={},key={},json={}", category, key, json, e);
        } finally {
            if (!success) {// 防止因为其他原因发送mq不成功，只要走到mq发送，或者缓存命中就人为发送过
                log.info("mq发送失败，切换为发送");
                consumer.accept(json);
            }
        }
    }

}
