package com.sankuai.scrm.core.service.activity.fission.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: group_fission_invitation_record
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GroupFissionInvitationRecord {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: inviter_union_id
     *   说明: 邀请人的unionId
     */
    private String inviterUnionId;

    /**
     *   字段: invitee_union_id
     *   说明: 被邀请人的unionId
     */
    private String inviteeUnionId;

    /**
     *   字段: activity_id
     *   说明: 活动id
     */
    private Long activityId;

    /**
     *   字段: new_user
     *   说明: 是否为新客
     */
    private Boolean newUser;

    /**
     *   字段: enter_time
     *   说明: 入群时间
     */
    private Date enterTime;

    /**
     *   字段: in_group_status
     *   说明: 在群状态，true-在群，false-退群
     */
    private Boolean inGroupStatus;

    /**
     *   字段: risk_user
     *   说明: 被风控用户
     */
    private Boolean riskUser;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: invitee_mt_city_id
     *   说明: 被邀请人对应的美团城市id
     */
    private Long inviteeMtCityId;
}