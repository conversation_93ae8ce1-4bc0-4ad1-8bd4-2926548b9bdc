package com.sankuai.scrm.core.service.envrequestforwarding.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfflineDataSyncConfig implements Serializable {
    private List<String> corpIds;
    private List<String> swimLanes;
    private List<String> interfaceNames;
    private String grayKey;
    private String grayValue;
    //处理线下请求的线上接口url
    private String offlineRequestHandleUrl;
    //将线上数据同步到线下的接口url
    private String offlineDataSyncUrl;
}
