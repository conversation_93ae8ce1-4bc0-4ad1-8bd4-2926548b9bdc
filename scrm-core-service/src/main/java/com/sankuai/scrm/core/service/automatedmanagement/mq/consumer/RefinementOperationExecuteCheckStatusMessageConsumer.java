package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

@Component
@Slf4j
public class RefinementOperationExecuteCheckStatusMessageConsumer implements InitializingBean, DisposableBean {
    @Autowired
    private ExecuteWriteDomainService xecuteDomainService;

    public ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        log.info("RefinementOperationExecuteCheckStatusMessageConsumer recvMessage: {}", message.getMessageID());
        String msgBody = message.getBody();
        if (StringUtils.isBlank(msgBody)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        RefinementOperationExecuteMessage msg = JsonUtils.toObject(msgBody, RefinementOperationExecuteMessage.class);

        if (msg == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            xecuteDomainService.executeWxOfficialService(msg.getProcessOrchestrationId(), msg.getProcessOrchestrationVersion(), msg.getAppId());
            log.info("RefinementOperationExecuteMessageConsumer EXECUTION_TASK success:"+message.getMessageID());
        } catch (ExecutionException | InterruptedException | TimeoutException e) {
            log.error("RefinementOperationExecuteMessageConsumer TIMED_PROCESS_ORCHESTRATION failed:"+message.getMessageID(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建.
     * */
    private static IConsumerProcessor consumer;
    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.refinement.operation.task.execute.check.status.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildCommonConsumerFactory(properties, "scrm.refinement.operation.task.execute.check.status");

        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }
}
