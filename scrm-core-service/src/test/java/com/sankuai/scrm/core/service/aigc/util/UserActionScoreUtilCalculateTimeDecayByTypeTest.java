package com.sankuai.scrm.core.service.aigc.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.aigc.service.dto.UserActionScoreConfigDTO;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UserActionScoreUtilCalculateTimeDecayByTypeTest {

    private UserActionScoreUtil userActionScoreUtil = new UserActionScoreUtil();

    /**
     * 使用反射创建UserAction实例
     */
    private Object createUserAction(String actionType, String contentType, Long timestamp, Long itemId, Long shopId, Long cat0Id, Long cat1Id, Long cat2Id) throws Exception {
        Class<?> userActionClass = Class.forName("com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil$UserAction");
        Constructor<?> constructor = userActionClass.getDeclaredConstructor(UserActionScoreUtil.class, String.class, String.class, Long.class, Long.class, Long.class, Long.class, Long.class, Long.class);
        constructor.setAccessible(true);
        return constructor.newInstance(userActionScoreUtil, actionType, contentType, timestamp, itemId, shopId, cat0Id, cat1Id, cat2Id);
    }

    /**
     * 使用反射调用calculateTimeDecayByType方法
     */
    private double invokeCalculateTimeDecayByType(long daysDiff, double decayFactor, Object action, UserActionScoreConfigDTO scoreRuleDTO, String calculationType) throws Exception {
        Method method = UserActionScoreUtil.class.getDeclaredMethod("calculateTimeDecayByType", long.class, double.class, Class.forName("com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil$UserAction"), UserActionScoreConfigDTO.class, String.class);
        method.setAccessible(true);
        return (Double) method.invoke(userActionScoreUtil, daysDiff, decayFactor, action, scoreRuleDTO, calculationType);
    }

    /**
     * 测试余弦衰减计算场景
     */
    @Test
    public void testCalculateTimeDecayByTypeCosine() throws Throwable {
        // arrange
        long daysDiff = 7;
        double decayFactor = 0.9;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setCat0CalculationTypes(new HashMap<Long, String>() {

            {
                put(1L, "cosine");
            }
        });
        scoreRuleDTO.setCat0CalculationParams(new HashMap<Long, Map<String, Double>>() {

            {
                put(1L, new HashMap<String, Double>() {

                    {
                        put("lambda", 0.5);
                    }
                });
            }
        });
        scoreRuleDTO.setCat0CycleDays(new HashMap<Long, Integer>() {

            {
                put(1L, 14);
            }
        });
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "cosine");
        // assert
        double expected = Math.pow(decayFactor, daysDiff) * Math.abs(0.5 * Math.cos(Math.PI * daysDiff / 14));
        assertEquals(expected, result, 0.0001);
    }

    /**
     * 测试线性衰减计算场景
     */
    @Test
    public void testCalculateTimeDecayByTypeLinear() throws Throwable {
        // arrange
        long daysDiff = 5;
        double decayFactor = 0.95;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setCat1CalculationTypes(new HashMap<Long, String>() {

            {
                put(1L, "linear");
            }
        });
        scoreRuleDTO.setCat1CalculationParams(new HashMap<Long, Map<String, Double>>() {

            {
                put(1L, new HashMap<String, Double>() {

                    {
                        put("slope", 1.2);
                    }
                });
            }
        });
        scoreRuleDTO.setCat1CycleDays(new HashMap<Long, Integer>() {

            {
                put(1L, 10);
            }
        });
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "linear");
        // assert
        double linearComponent = Math.max(0, 1 - 1.2 * daysDiff / 10);
        double expected = Math.pow(decayFactor, daysDiff) * linearComponent;
        assertEquals(expected, result, 0.0001);
    }

    /**
     * 测试指数衰减计算场景
     */
    @Test
    public void testCalculateTimeDecayByTypeExponential() throws Throwable {
        // arrange
        long daysDiff = 3;
        double decayFactor = 0.98;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setCat2CalculationTypes(new HashMap<Long, String>() {

            {
                put(1L, "exponential");
            }
        });
        scoreRuleDTO.setCat2CalculationParams(new HashMap<Long, Map<String, Double>>() {

            {
                put(1L, new HashMap<String, Double>() {

                    {
                        put("alpha", 0.8);
                    }
                });
            }
        });
        scoreRuleDTO.setCat2CycleDays(new HashMap<Long, Integer>() {

            {
                put(1L, 7);
            }
        });
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "exponential");
        // assert
        double expComponent = Math.exp(-0.8 * daysDiff / 7);
        double expected = Math.pow(decayFactor, daysDiff) * expComponent;
        assertEquals(expected, result, 0.0001);
    }

    /**
     * 测试默认衰减计算场景
     */
    @Test
    public void testCalculateTimeDecayByTypeDefault() throws Throwable {
        // arrange
        long daysDiff = 10;
        double decayFactor = 0.85;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setDefaultCycleDays(14);
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "default");
        // assert
        double expected = Math.pow(decayFactor, daysDiff);
        assertEquals(expected, result, 0.0001);
    }

    /**
     * 测试边界场景(daysDiff=0)
     */
    @Test
    public void testCalculateTimeDecayByTypeBoundary() throws Throwable {
        // arrange
        long daysDiff = 0;
        double decayFactor = 0.9;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setCat0CalculationTypes(new HashMap<Long, String>() {

            {
                put(1L, "cosine");
            }
        });
        scoreRuleDTO.setCat0CycleDays(new HashMap<Long, Integer>() {

            {
                put(1L, 14);
            }
        });
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "cosine");
        // assert
        // 任何数的0次方都是1
        assertEquals(1.0, result, 0.0001);
    }

    /**
     * 测试参数缺失场景(使用默认值)
     */
    @Test
    public void testCalculateTimeDecayByTypeMissingParams() throws Throwable {
        // arrange
        long daysDiff = 2;
        double decayFactor = 0.95;
        Object action = createUserAction("click", "product", System.currentTimeMillis(), 1L, 1L, 1L, 1L, 1L);
        UserActionScoreConfigDTO scoreRuleDTO = new UserActionScoreConfigDTO();
        scoreRuleDTO.setCat1CalculationTypes(new HashMap<Long, String>() {

            {
                put(1L, "linear");
            }
        });
        scoreRuleDTO.setCat1CycleDays(new HashMap<Long, Integer>() {

            {
                put(1L, 10);
            }
        });
        // act
        double result = invokeCalculateTimeDecayByType(daysDiff, decayFactor, action, scoreRuleDTO, "linear");
        // assert
        // 使用默认slope=1.0
        double linearComponent = Math.max(0, 1 - 1.0 * daysDiff / 10);
        double expected = Math.pow(decayFactor, daysDiff) * linearComponent;
        assertEquals(expected, result, 0.0001);
    }
}
