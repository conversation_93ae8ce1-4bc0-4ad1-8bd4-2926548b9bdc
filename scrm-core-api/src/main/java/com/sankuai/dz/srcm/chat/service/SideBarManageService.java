package com.sankuai.dz.srcm.chat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.chat.dto.SideBarInfoDTO;

public interface SideBarManageService {

    PageRemoteResponse<SideBarInfoDTO> queryAllSideBarList(String appId);

    RemoteResponse<Boolean> updateSideBarStatus(String appId, String name, Integer status);

}
