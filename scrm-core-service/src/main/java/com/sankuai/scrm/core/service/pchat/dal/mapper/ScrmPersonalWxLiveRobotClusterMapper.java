package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveRobotCluster;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxLiveRobotClusterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxLiveRobotClusterMapper extends MybatisBaseMapper<ScrmPersonalWxLiveRobotCluster, ScrmPersonalWxLiveRobotClusterExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxLiveRobotCluster> list);
}