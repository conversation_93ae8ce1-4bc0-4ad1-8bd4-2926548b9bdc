package com.sankuai.scrm.core.service.activity.miniprogram.domain;


import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.UserParticipateActivityLog;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.example.UserParticipateActivityLogExample;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper.UserParticipateActivityLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ActivityParticipateEventDomainService {

    @Resource
    private UserParticipateActivityLogMapper participateActivityLogMapper;

    public boolean saveActivityParticipateEvent(UserParticipateActivityLog activityLog) {
        if (ObjectUtils.isEmpty(activityLog) || activityLog.getActivityId() == null ||
                activityLog.getMtUserId() == null || activityLog.getMtCityId() == null || activityLog.getEventType() == null) {
            return false;
        }
        return participateActivityLogMapper.insertSelective(activityLog) > 0;
    }

    public List<UserParticipateActivityLog> queryParticipateActivityLog(Long mtUserId, Long activityId) {
        if (mtUserId == null || activityId == null) {
            return Lists.newArrayList();
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andMtUserIdEqualTo(mtUserId)
                .andActivityIdEqualTo(activityId);
        example.setOrderByClause("add_time DESC");
        return participateActivityLogMapper.selectByExample(example);
    }

    public UserParticipateActivityLog queryParticipateActivityLogByUserId(Long mtUserId) {
        if (mtUserId == null) {
            return null;
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andMtUserIdEqualTo(mtUserId);
        example.setOrderByClause("add_time DESC");
        List<UserParticipateActivityLog> participateActivityLogs = participateActivityLogMapper.selectByExample(example);
        return CollectionUtils.isEmpty(participateActivityLogs) ? null : participateActivityLogs.get(0);
    }

    public List<UserParticipateActivityLog> batchQueryParticipateActivityLog(List<Long> activityIds) {
        if(CollectionUtils.isEmpty(activityIds)){
            return Lists.newArrayList();
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andActivityIdIn(activityIds);
        return participateActivityLogMapper.selectByExample(example);
    }

    public List<UserParticipateActivityLog> batchQueryLogByPathParam(List<String> pathParams) {
        if(CollectionUtils.isEmpty(pathParams)){
            return Lists.newArrayList();
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andPathParamIn(pathParams);
        return participateActivityLogMapper.selectByExample(example);
    }

    public List<UserParticipateActivityLog>  queryParticipateActivityLogByActivityId(Long activityId){
        if(activityId == null){
            return Lists.newArrayList();
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andActivityIdEqualTo(activityId);
        return participateActivityLogMapper.selectByExample(example);
    }

    public List<UserParticipateActivityLog>  queryParticipateLogByPAthParam(String pathParam){
        if(StringUtils.isBlank(pathParam)){
            return Lists.newArrayList();
        }
        UserParticipateActivityLogExample example = new UserParticipateActivityLogExample();
        example.createCriteria()
                .andPathParamEqualTo(pathParam);
        return participateActivityLogMapper.selectByExample(example);
    }
}
