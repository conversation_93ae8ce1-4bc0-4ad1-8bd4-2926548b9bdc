package com.sankuai.scrm.core.service.pchat.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmPersonalWxCallbackMsgExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmPersonalWxCallbackMsgExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmPersonalWxCallbackMsgExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxCallbackMsgExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxCallbackMsgExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNTypeIsNull() {
            addCriterion("n_type is null");
            return (Criteria) this;
        }

        public Criteria andNTypeIsNotNull() {
            addCriterion("n_type is not null");
            return (Criteria) this;
        }

        public Criteria andNTypeEqualTo(String value) {
            addCriterion("n_type =", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeNotEqualTo(String value) {
            addCriterion("n_type <>", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeGreaterThan(String value) {
            addCriterion("n_type >", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeGreaterThanOrEqualTo(String value) {
            addCriterion("n_type >=", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeLessThan(String value) {
            addCriterion("n_type <", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeLessThanOrEqualTo(String value) {
            addCriterion("n_type <=", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeLike(String value) {
            addCriterion("n_type like", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeNotLike(String value) {
            addCriterion("n_type not like", value, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeIn(List<String> values) {
            addCriterion("n_type in", values, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeNotIn(List<String> values) {
            addCriterion("n_type not in", values, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeBetween(String value1, String value2) {
            addCriterion("n_type between", value1, value2, "nType");
            return (Criteria) this;
        }

        public Criteria andNTypeNotBetween(String value1, String value2) {
            addCriterion("n_type not between", value1, value2, "nType");
            return (Criteria) this;
        }

        public Criteria andStrSignIsNull() {
            addCriterion("str_sign is null");
            return (Criteria) this;
        }

        public Criteria andStrSignIsNotNull() {
            addCriterion("str_sign is not null");
            return (Criteria) this;
        }

        public Criteria andStrSignEqualTo(String value) {
            addCriterion("str_sign =", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignNotEqualTo(String value) {
            addCriterion("str_sign <>", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignGreaterThan(String value) {
            addCriterion("str_sign >", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignGreaterThanOrEqualTo(String value) {
            addCriterion("str_sign >=", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignLessThan(String value) {
            addCriterion("str_sign <", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignLessThanOrEqualTo(String value) {
            addCriterion("str_sign <=", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignLike(String value) {
            addCriterion("str_sign like", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignNotLike(String value) {
            addCriterion("str_sign not like", value, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignIn(List<String> values) {
            addCriterion("str_sign in", values, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignNotIn(List<String> values) {
            addCriterion("str_sign not in", values, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignBetween(String value1, String value2) {
            addCriterion("str_sign between", value1, value2, "strSign");
            return (Criteria) this;
        }

        public Criteria andStrSignNotBetween(String value1, String value2) {
            addCriterion("str_sign not between", value1, value2, "strSign");
            return (Criteria) this;
        }

        public Criteria andProcessorIsNull() {
            addCriterion("processor is null");
            return (Criteria) this;
        }

        public Criteria andProcessorIsNotNull() {
            addCriterion("processor is not null");
            return (Criteria) this;
        }

        public Criteria andProcessorEqualTo(String value) {
            addCriterion("processor =", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorNotEqualTo(String value) {
            addCriterion("processor <>", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorGreaterThan(String value) {
            addCriterion("processor >", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorGreaterThanOrEqualTo(String value) {
            addCriterion("processor >=", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorLessThan(String value) {
            addCriterion("processor <", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorLessThanOrEqualTo(String value) {
            addCriterion("processor <=", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorLike(String value) {
            addCriterion("processor like", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorNotLike(String value) {
            addCriterion("processor not like", value, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorIn(List<String> values) {
            addCriterion("processor in", values, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorNotIn(List<String> values) {
            addCriterion("processor not in", values, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorBetween(String value1, String value2) {
            addCriterion("processor between", value1, value2, "processor");
            return (Criteria) this;
        }

        public Criteria andProcessorNotBetween(String value1, String value2) {
            addCriterion("processor not between", value1, value2, "processor");
            return (Criteria) this;
        }

        public Criteria andMerchantNoIsNull() {
            addCriterion("merchant_no is null");
            return (Criteria) this;
        }

        public Criteria andMerchantNoIsNotNull() {
            addCriterion("merchant_no is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantNoEqualTo(String value) {
            addCriterion("merchant_no =", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoNotEqualTo(String value) {
            addCriterion("merchant_no <>", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoGreaterThan(String value) {
            addCriterion("merchant_no >", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_no >=", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoLessThan(String value) {
            addCriterion("merchant_no <", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoLessThanOrEqualTo(String value) {
            addCriterion("merchant_no <=", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoLike(String value) {
            addCriterion("merchant_no like", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoNotLike(String value) {
            addCriterion("merchant_no not like", value, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoIn(List<String> values) {
            addCriterion("merchant_no in", values, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoNotIn(List<String> values) {
            addCriterion("merchant_no not in", values, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoBetween(String value1, String value2) {
            addCriterion("merchant_no between", value1, value2, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andMerchantNoNotBetween(String value1, String value2) {
            addCriterion("merchant_no not between", value1, value2, "merchantNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoIsNull() {
            addCriterion("serial_no is null");
            return (Criteria) this;
        }

        public Criteria andSerialNoIsNotNull() {
            addCriterion("serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNoEqualTo(String value) {
            addCriterion("serial_no =", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotEqualTo(String value) {
            addCriterion("serial_no <>", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThan(String value) {
            addCriterion("serial_no >", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("serial_no >=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThan(String value) {
            addCriterion("serial_no <", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThanOrEqualTo(String value) {
            addCriterion("serial_no <=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLike(String value) {
            addCriterion("serial_no like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotLike(String value) {
            addCriterion("serial_no not like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoIn(List<String> values) {
            addCriterion("serial_no in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotIn(List<String> values) {
            addCriterion("serial_no not in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoBetween(String value1, String value2) {
            addCriterion("serial_no between", value1, value2, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotBetween(String value1, String value2) {
            addCriterion("serial_no not between", value1, value2, "serialNo");
            return (Criteria) this;
        }

        public Criteria andNResultIsNull() {
            addCriterion("n_result is null");
            return (Criteria) this;
        }

        public Criteria andNResultIsNotNull() {
            addCriterion("n_result is not null");
            return (Criteria) this;
        }

        public Criteria andNResultEqualTo(Integer value) {
            addCriterion("n_result =", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultNotEqualTo(Integer value) {
            addCriterion("n_result <>", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultGreaterThan(Integer value) {
            addCriterion("n_result >", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("n_result >=", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultLessThan(Integer value) {
            addCriterion("n_result <", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultLessThanOrEqualTo(Integer value) {
            addCriterion("n_result <=", value, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultIn(List<Integer> values) {
            addCriterion("n_result in", values, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultNotIn(List<Integer> values) {
            addCriterion("n_result not in", values, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultBetween(Integer value1, Integer value2) {
            addCriterion("n_result between", value1, value2, "nResult");
            return (Criteria) this;
        }

        public Criteria andNResultNotBetween(Integer value1, Integer value2) {
            addCriterion("n_result not between", value1, value2, "nResult");
            return (Criteria) this;
        }

        public Criteria andVcResultIsNull() {
            addCriterion("vc_result is null");
            return (Criteria) this;
        }

        public Criteria andVcResultIsNotNull() {
            addCriterion("vc_result is not null");
            return (Criteria) this;
        }

        public Criteria andVcResultEqualTo(String value) {
            addCriterion("vc_result =", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultNotEqualTo(String value) {
            addCriterion("vc_result <>", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultGreaterThan(String value) {
            addCriterion("vc_result >", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultGreaterThanOrEqualTo(String value) {
            addCriterion("vc_result >=", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultLessThan(String value) {
            addCriterion("vc_result <", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultLessThanOrEqualTo(String value) {
            addCriterion("vc_result <=", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultLike(String value) {
            addCriterion("vc_result like", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultNotLike(String value) {
            addCriterion("vc_result not like", value, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultIn(List<String> values) {
            addCriterion("vc_result in", values, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultNotIn(List<String> values) {
            addCriterion("vc_result not in", values, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultBetween(String value1, String value2) {
            addCriterion("vc_result between", value1, value2, "vcResult");
            return (Criteria) this;
        }

        public Criteria andVcResultNotBetween(String value1, String value2) {
            addCriterion("vc_result not between", value1, value2, "vcResult");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andProcessorResultIsNull() {
            addCriterion("processor_result is null");
            return (Criteria) this;
        }

        public Criteria andProcessorResultIsNotNull() {
            addCriterion("processor_result is not null");
            return (Criteria) this;
        }

        public Criteria andProcessorResultEqualTo(String value) {
            addCriterion("processor_result =", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultNotEqualTo(String value) {
            addCriterion("processor_result <>", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultGreaterThan(String value) {
            addCriterion("processor_result >", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultGreaterThanOrEqualTo(String value) {
            addCriterion("processor_result >=", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultLessThan(String value) {
            addCriterion("processor_result <", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultLessThanOrEqualTo(String value) {
            addCriterion("processor_result <=", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultLike(String value) {
            addCriterion("processor_result like", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultNotLike(String value) {
            addCriterion("processor_result not like", value, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultIn(List<String> values) {
            addCriterion("processor_result in", values, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultNotIn(List<String> values) {
            addCriterion("processor_result not in", values, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultBetween(String value1, String value2) {
            addCriterion("processor_result between", value1, value2, "processorResult");
            return (Criteria) this;
        }

        public Criteria andProcessorResultNotBetween(String value1, String value2) {
            addCriterion("processor_result not between", value1, value2, "processorResult");
            return (Criteria) this;
        }

        public Criteria andNTypeDescIsNull() {
            addCriterion("n_type_desc is null");
            return (Criteria) this;
        }

        public Criteria andNTypeDescIsNotNull() {
            addCriterion("n_type_desc is not null");
            return (Criteria) this;
        }

        public Criteria andNTypeDescEqualTo(String value) {
            addCriterion("n_type_desc =", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescNotEqualTo(String value) {
            addCriterion("n_type_desc <>", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescGreaterThan(String value) {
            addCriterion("n_type_desc >", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescGreaterThanOrEqualTo(String value) {
            addCriterion("n_type_desc >=", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescLessThan(String value) {
            addCriterion("n_type_desc <", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescLessThanOrEqualTo(String value) {
            addCriterion("n_type_desc <=", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescLike(String value) {
            addCriterion("n_type_desc like", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescNotLike(String value) {
            addCriterion("n_type_desc not like", value, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescIn(List<String> values) {
            addCriterion("n_type_desc in", values, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescNotIn(List<String> values) {
            addCriterion("n_type_desc not in", values, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescBetween(String value1, String value2) {
            addCriterion("n_type_desc between", value1, value2, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andNTypeDescNotBetween(String value1, String value2) {
            addCriterion("n_type_desc not between", value1, value2, "nTypeDesc");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoIsNull() {
            addCriterion("chat_room_wx_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoIsNotNull() {
            addCriterion("chat_room_wx_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoEqualTo(String value) {
            addCriterion("chat_room_wx_serial_no =", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoNotEqualTo(String value) {
            addCriterion("chat_room_wx_serial_no <>", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoGreaterThan(String value) {
            addCriterion("chat_room_wx_serial_no >", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("chat_room_wx_serial_no >=", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoLessThan(String value) {
            addCriterion("chat_room_wx_serial_no <", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoLessThanOrEqualTo(String value) {
            addCriterion("chat_room_wx_serial_no <=", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoLike(String value) {
            addCriterion("chat_room_wx_serial_no like", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoNotLike(String value) {
            addCriterion("chat_room_wx_serial_no not like", value, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoIn(List<String> values) {
            addCriterion("chat_room_wx_serial_no in", values, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoNotIn(List<String> values) {
            addCriterion("chat_room_wx_serial_no not in", values, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoBetween(String value1, String value2) {
            addCriterion("chat_room_wx_serial_no between", value1, value2, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andChatRoomWxSerialNoNotBetween(String value1, String value2) {
            addCriterion("chat_room_wx_serial_no not between", value1, value2, "chatRoomWxSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoIsNull() {
            addCriterion("robot_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoIsNotNull() {
            addCriterion("robot_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoEqualTo(String value) {
            addCriterion("robot_serial_no =", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoNotEqualTo(String value) {
            addCriterion("robot_serial_no <>", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoGreaterThan(String value) {
            addCriterion("robot_serial_no >", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("robot_serial_no >=", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoLessThan(String value) {
            addCriterion("robot_serial_no <", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoLessThanOrEqualTo(String value) {
            addCriterion("robot_serial_no <=", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoLike(String value) {
            addCriterion("robot_serial_no like", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoNotLike(String value) {
            addCriterion("robot_serial_no not like", value, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoIn(List<String> values) {
            addCriterion("robot_serial_no in", values, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoNotIn(List<String> values) {
            addCriterion("robot_serial_no not in", values, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoBetween(String value1, String value2) {
            addCriterion("robot_serial_no between", value1, value2, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andRobotSerialNoNotBetween(String value1, String value2) {
            addCriterion("robot_serial_no not between", value1, value2, "robotSerialNo");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIsNull() {
            addCriterion("message_type is null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIsNotNull() {
            addCriterion("message_type is not null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeEqualTo(Integer value) {
            addCriterion("message_type =", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotEqualTo(Integer value) {
            addCriterion("message_type <>", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThan(Integer value) {
            addCriterion("message_type >", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("message_type >=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThan(Integer value) {
            addCriterion("message_type <", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("message_type <=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIn(List<Integer> values) {
            addCriterion("message_type in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotIn(List<Integer> values) {
            addCriterion("message_type not in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeBetween(Integer value1, Integer value2) {
            addCriterion("message_type between", value1, value2, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("message_type not between", value1, value2, "messageType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}