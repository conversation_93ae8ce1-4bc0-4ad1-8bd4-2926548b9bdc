package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.tgc.process.enums.PlatformEnum;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.enums.SplitType;
import com.sankuai.scrm.core.service.abtest.service.ABTestService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintDiversionProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Properties;
import com.sankuai.scrm.core.service.abtest.service.ABTestHandleRequest;

@Slf4j
@Component
public class DzBizDataUserTrackViewConsumer implements InitializingBean, DisposableBean {

    @Autowired
    private RealTimeTaskConsumerConfig consumerConfig;

    @Autowired
    private GroupRetailAiEngineUserFootprintDiversionProducer userFootprintDiversionProducer;

    @Autowired
    private ABTestService<Object> abTestService;

    @Autowired
    private MtUserCenterAclService mtUserCenterAclService;

    private IConsumerProcessor consumer;

    private ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        try {
            ScrmDzBizDataUserTrackDTO scrmDzBizDataUserTrackDTO = JsonUtils.toObjectSafe(message.getBody(), ScrmDzBizDataUserTrackDTO.class);
            if(null == scrmDzBizDataUserTrackDTO || CollectionUtils.isEmpty(scrmDzBizDataUserTrackDTO.getUserid())){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            PlatformEnum platForm = PlatformEnum.MT;
            if("dp".equalsIgnoreCase(scrmDzBizDataUserTrackDTO.getPlatformlist().get(0))){
                platForm = PlatformEnum.DP;
            }
            if (consumerConfig.isInWhitelistAppIdUnknown(scrmDzBizDataUserTrackDTO.getUserid(),platForm)) {
                // log.info("DzBizDataUserTrackViewConsumer recvMessage, message is {}", JsonUtils.toStr(message));
                GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
                mqMessageDTO.setUserTrackDTO(scrmDzBizDataUserTrackDTO);
                userFootprintDiversionProducer.sendMessage(mqMessageDTO);
            }else {
                Long userId = scrmDzBizDataUserTrackDTO.getUserid().get(0);
                ABTestContext abTestContext = new ABTestContext();
                abTestContext.setUserId(userId);
                abTestContext.setPlatForm(platForm);
                abTestContext.setAppId("default");
                ABTestHandleRequest<Object> request = ABTestHandleRequest.builder()
                    .userId(userId)
                    .splitType(SplitType.DOT_CARE_USER_ID)
                    .appId("default")
                    .param(null)
                    .context(abTestContext)
                    .strategyName("")
                    .build();
                abTestService.handle(request);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("DzBizDataUserTrackViewConsumer error, message is {}", JsonUtils.toStr(message), e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

    }

    @Override
    public void destroy() throws Exception {
        if (this.consumer != null) {
            this.consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm_dz_biz_data_user_track_view");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "dz_biz_data_user_track_view");
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }

}
