package com.sankuai.dz.srcm.chat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.chat.dto.ChatAggregationAccountRelationDTO;

import java.util.List;

public interface ImAccountManageService {

    PageRemoteResponse<ChatAggregationAccountRelationDTO> queryChatAggregationAccountRelationList(String appId);

    RemoteResponse<Boolean> updateChatAggregationAccountRelation(String appId, String misId, List<String> assistantAccountList, Integer actionType);

    void syncChatAggregationAccountRelationFromLionConfig();

}
