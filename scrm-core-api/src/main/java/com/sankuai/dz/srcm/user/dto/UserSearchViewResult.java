package com.sankuai.dz.srcm.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户搜索视图数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserSearchViewResult implements Serializable {
    
    // ==================== 核心标识字段 ====================
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务链
     */
    private Integer cChain;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 分区日期(yyyy-mm-dd)
     */
    private String partitionDate;
    
    /**
     * 统计口径维度
     */
    private String calDim;
    
    /**
     * 到综BUID（枚举值：1=LifeEvent，2=教育及母婴，3=休娱和丽人，4=医药健康）,-10000代表全部
     */
    private Integer bizlnBuCode;
    
    /**
     * 来源信息
     */
    private String sourceInfo;
    
    /**
     * 是否直接访问
     */
    private Integer isDirect;
    
    /**
     * 商品类型
     */
    private String itemType;

    // ==================== 周期性统计数据 ====================
    
    /**
     * 周期性搜索统计数据
     */
    private List<PeriodSearchStat> periodStats;

    // ==================== 最后搜索相关信息 ====================
    
    /**
     * 最后搜索日期
     */
    private String lastSearchDate;
    
    /**
     * 最后搜索距离现在的天数
     */
    private Integer lastSearchToNowDays;
    
    /**
     * 最后一天搜索关键词次数
     */
    private Long lastdaySearchKeywordCnt;
    
    /**
     * 最后一天点击查询量
     */
    private Long lastdayClickQv;
    
    /**
     * 最后一天查询量
     */
    private Long lastdayQv;
    
    /**
     * 最后一天点击商品次数
     */
    private Long lastdayClickItemCnt;
    
    /**
     * 最后一天浏览商品次数
     */
    private Long lastdayViewItemCnt;
} 