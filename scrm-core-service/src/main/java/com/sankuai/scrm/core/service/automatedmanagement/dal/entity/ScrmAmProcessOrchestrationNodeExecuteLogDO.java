package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: scrm_a_m_process_orchestration_node_execute_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmProcessOrchestrationNodeExecuteLogDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: process_orchestration_node_id
     *   说明: 流程编排分支节点id
     */
    private Long processOrchestrationNodeId;

    /**
     *   字段: execute_count
     *   说明: 进入此分支节点对象次数
     */
    private Long executeCount;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}