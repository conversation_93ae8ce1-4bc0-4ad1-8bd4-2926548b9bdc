package com.sankuai.dz.srcm.group.enums;

public enum GroupListOrderTypeEnum {

    CREATE_TIME_RECENTLY("按建群时间最近",0),
    CREATE_TIME_FURTHEST("按建群时间最远",1),
    MEMBER_NUMS_DESC("按群人数降序",2),
    MEMBER_NUMS_ASC("按群人数升序",3);

    String desc;
    int code;

    GroupListOrderTypeEnum(String desc, int code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
