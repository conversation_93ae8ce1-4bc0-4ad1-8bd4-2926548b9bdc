package com.sankuai.dz.srcm.group.enums;

import lombok.Getter;

@Getter
public enum CityLevelType {
    MUNICIPALITY("直辖市",2),
    COUNTY_CITY("区/县级市",3),
    TOWNSHIP("乡镇",4);

    String desc;
    int code;

    CityLevelType(String desc, int code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
