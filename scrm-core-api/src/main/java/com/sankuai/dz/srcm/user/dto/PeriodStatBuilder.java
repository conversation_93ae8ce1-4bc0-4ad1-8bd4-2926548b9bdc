package com.sankuai.dz.srcm.user.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 周期统计构建器工具类
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class PeriodStatBuilder {
    
    /**
     * 构建交易周期统计数据
     */
    public static List<PeriodTradeStat> buildTradeStats() {
        List<PeriodTradeStat> stats = new ArrayList<>();
        String[] periods = {"1d", "3d", "5d", "7d", "15d", "30d", "60d", "90d", "180d", "360d", "365d", "his"};
        
        for (String period : periods) {
            PeriodTradeStat stat = new PeriodTradeStat();
            stat.setPeriod(period);
            stats.add(stat);
        }
        
        return stats;
    }
    
    /**
     * 构建搜索周期统计数据
     */
    public static List<PeriodSearchStat> buildSearchStats() {
        List<PeriodSearchStat> stats = new ArrayList<>();
        String[] periods = {"1d", "3d", "5d", "7d", "15d", "30d", "60d", "90d", "180d", "360d"};
        
        for (String period : periods) {
            PeriodSearchStat stat = new PeriodSearchStat();
            stat.setPeriod(period);
            stats.add(stat);
        }
        
        return stats;
    }
    
    /**
     * 构建访问周期统计数据
     */
    public static List<PeriodVisitStat> buildVisitStats() {
        List<PeriodVisitStat> stats = new ArrayList<>();
        String[] periods = {"1d", "3d", "5d", "7d", "15d", "30d", "60d", "90d", "180d", "360d", "365d"};
        
        for (String period : periods) {
            PeriodVisitStat stat = new PeriodVisitStat();
            stat.setPeriod(period);
            stats.add(stat);
        }
        
        return stats;
    }
    
    /**
     * 根据周期标识获取对应的统计对象
     */
    public static PeriodTradeStat getTradeStatByPeriod(List<PeriodTradeStat> stats, String period) {
        return stats.stream()
                .filter(stat -> period.equals(stat.getPeriod()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据周期标识获取对应的搜索统计对象
     */
    public static PeriodSearchStat getSearchStatByPeriod(List<PeriodSearchStat> stats, String period) {
        return stats.stream()
                .filter(stat -> period.equals(stat.getPeriod()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据周期标识获取对应的访问统计对象
     */
    public static PeriodVisitStat getVisitStatByPeriod(List<PeriodVisitStat> stats, String period) {
        return stats.stream()
                .filter(stat -> period.equals(stat.getPeriod()))
                .findFirst()
                .orElse(null);
    }
} 