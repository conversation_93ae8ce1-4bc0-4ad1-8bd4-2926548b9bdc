package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowCouponInfoDTO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponResult;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import org.mockito.junit.*;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
public class CouponFacadeServiceTest {

    @Mock
    private CouponSelectorService couponSelectorService;

    @Mock
    private CouponIssueService couponIssueService;

    @Mock
    private CouponRecordService couponRecordService;

    @InjectMocks
    private CouponFacadeService couponFacadeService;

    private CouponRequestContext context;

    @BeforeEach
    public void setUp() {
        context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("coupon123");
    }

    /**
     * 测试参数校验失败的场景
     */
    @Test
    public void testIssueCoupon_ValidationFails() {
        // arrange
        // 设置无效的用户ID
        context.setUserId(null);
        // act
        CouponResult result;
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            result = couponFacadeService.issueCoupon(context);
        }
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("用户ID不能为空", result.getErrorMessage());
        verifyNoInteractions(couponIssueService);
        verifyNoInteractions(couponRecordService);
    }

    /**
     * 测试非AI场景直接发券的情况
     */
    @Test
    public void testIssueCoupon_NonAiSceneDirectIssue() {
        // arrange
        CouponResult successResult = CouponResult.success(Collections.emptyList());
        when(couponIssueService.issueCoupon(any())).thenReturn(successResult);
        doNothing().when(couponRecordService).record(any(), any());
        // act
        CouponResult result;
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            result = couponFacadeService.issueCoupon(context);
        }
        // assert
        assertTrue(result.isSuccess());
        verify(couponIssueService).issueCoupon(context);
        verify(couponRecordService).record(eq(context), eq(successResult));
        // 非AI场景不需要选券
        verifyNoInteractions(couponSelectorService);
    }

    /**
     * 测试发券失败的情况
     */
    @Test
    public void testIssueCoupon_IssueFails() {
        // arrange
        CouponResult failResult = CouponResult.fail("ISSUE_FAILED", "发券失败");
        when(couponIssueService.issueCoupon(any())).thenReturn(failResult);
        doNothing().when(couponRecordService).record(any(), any());
        // act
        CouponResult result;
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            result = couponFacadeService.issueCoupon(context);
        }
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ISSUE_FAILED", result.getErrorCode());
        assertEquals("发券失败", result.getErrorMessage());
        verify(couponIssueService).issueCoupon(context);
        verify(couponRecordService).record(eq(context), eq(failResult));
    }

    /**
     * 测试发券过程中发生异常的情况
     */
    @Test
    public void testIssueCoupon_ExceptionOccurs() {
        // arrange
        when(couponIssueService.issueCoupon(any())).thenThrow(new RuntimeException("测试异常"));
        // act
        CouponResult result;
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            result = couponFacadeService.issueCoupon(context);
        }
        // assert
        assertFalse(result.isSuccess());
        assertEquals("SYSTEM_ERROR", result.getErrorCode());
        assertEquals("系统异常", result.getErrorMessage());
        verify(couponIssueService).issueCoupon(context);
        // 异常情况下不会记录
        verifyNoInteractions(couponRecordService);
    }

    /**
     * 测试AI场景下缺少AppId的情况
     */
    @Test
    public void testIssueCoupon_AiSceneMissingAppId() {
        // arrange
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        // 缺少AppId
        context.setAppId(null);
        // act
        CouponResult result;
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            result = couponFacadeService.issueCoupon(context);
        }
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("AI场景下AppId不能为空", result.getErrorMessage());
        verifyNoInteractions(couponSelectorService);
        verifyNoInteractions(couponIssueService);
        verifyNoInteractions(couponRecordService);
    }

    private void invokeRecordCatMetric(CouponFacadeService service, String method, String result, long startTime) throws Exception {
        Method recordCatMetricMethod = CouponFacadeService.class.getDeclaredMethod("recordCatMetric", String.class, String.class, long.class);
        recordCatMetricMethod.setAccessible(true);
        recordCatMetricMethod.invoke(service, method, result, startTime);
    }

    @Test
    public void testRecordCatMetricNormalExecution() throws Throwable {
        // arrange
        // 1 second ago
        long startTime = System.currentTimeMillis() - 1000;
        String method = "testMethod";
        String result = "SUCCESS";
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("testMethod_SUCCESS")));
            catMock.verify(() -> Cat.logMetricForDuration(eq("CouponFacade.testMethod.cost"), anyLong()));
            catMock.verify(() -> Cat.logMetricForCount(eq("CouponFacade.testMethod.SUCCESS")));
        }
    }

    @Test
    public void testRecordCatMetricEmptyMethod() throws Throwable {
        // arrange
        long startTime = System.currentTimeMillis();
        String method = "";
        String result = "SUCCESS";
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("_SUCCESS")));
            catMock.verify(() -> Cat.logMetricForDuration(eq("CouponFacade..cost"), anyLong()));
            catMock.verify(() -> Cat.logMetricForCount(eq("CouponFacade..SUCCESS")));
        }
    }

    @Test
    public void testRecordCatMetricNullResult() throws Throwable {
        // arrange
        long startTime = System.currentTimeMillis();
        String method = "testMethod";
        String result = null;
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("testMethod_null")));
            catMock.verify(() -> Cat.logMetricForDuration(eq("CouponFacade.testMethod.cost"), anyLong()));
            catMock.verify(() -> Cat.logMetricForCount(eq("CouponFacade.testMethod.null")));
        }
    }

    @Test
    public void testRecordCatMetricVeryOldStartTime() throws Throwable {
        // arrange
        // Very old time
        long startTime = 1L;
        String method = "testMethod";
        String result = "SUCCESS";
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("testMethod_SUCCESS")));
            catMock.verify(() -> Cat.logMetricForDuration(eq("CouponFacade.testMethod.cost"), longThat(value -> value > 0)));
            catMock.verify(() -> Cat.logMetricForCount(eq("CouponFacade.testMethod.SUCCESS")));
        }
    }

    @Test
    public void testRecordCatMetricCatException() throws Throwable {
        // arrange
        long startTime = System.currentTimeMillis();
        String method = "testMethod";
        String result = "SUCCESS";
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // Make Cat throw exception on first call
            catMock.when(() -> Cat.logEvent(anyString(), anyString())).thenThrow(new RuntimeException("Cat error"));
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            // Verify that the method was called but we can't verify the log.error since it's a static final field
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("testMethod_SUCCESS")));
            // We can't verify the other Cat calls since the exception would have prevented them from being called
            // The main verification here is that the exception was caught and didn't propagate
        }
    }

    @Test
    public void testRecordCatMetricAllNullParameters() throws Throwable {
        // arrange
        long startTime = System.currentTimeMillis();
        String method = null;
        String result = null;
        CouponFacadeService service = new CouponFacadeService();
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            invokeRecordCatMetric(service, method, result, startTime);
            // assert
            catMock.verify(() -> Cat.logEvent(eq("CouponFacade"), eq("null_null")));
            catMock.verify(() -> Cat.logMetricForDuration(eq("CouponFacade.null.cost"), anyLong()));
            catMock.verify(() -> Cat.logMetricForCount(eq("CouponFacade.null.null")));
        }
    }

    private CouponResult invokeValidateIssueCouponRequest(CouponRequestContext context) throws Exception {
        Method method = CouponFacadeService.class.getDeclaredMethod("validateIssueCouponRequest", CouponRequestContext.class);
        method.setAccessible(true);
        return (CouponResult) method.invoke(couponFacadeService, context);
    }

    @Test
    public void testValidateIssueCouponRequest_NullContext() throws Throwable {
        // arrange
        CouponRequestContext context = null;
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("请求上下文不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_NullUserId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(null);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("用户ID不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_ZeroUserId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(0L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("用户ID不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_NullScene() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(null);
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("发券场景不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_NonAISceneBlankCouponGroupId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("券ID不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_AISceneBlankAppId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        context.setAppId("");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("AI场景下AppId不能为空", result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_ValidNonAIScene() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertTrue(result.isSuccess());
        assertNull(result.getErrorCode());
        assertNull(result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_ValidAIScene() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        context.setAppId("test-app");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertTrue(result.isSuccess());
        assertNull(result.getErrorCode());
        assertNull(result.getErrorMessage());
    }

    @Test
    public void testValidateIssueCouponRequest_MinValidUserId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(1L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = invokeValidateIssueCouponRequest(context);
        // assert
        assertTrue(result.isSuccess());
        assertNull(result.getErrorCode());
        assertNull(result.getErrorMessage());
    }


}
