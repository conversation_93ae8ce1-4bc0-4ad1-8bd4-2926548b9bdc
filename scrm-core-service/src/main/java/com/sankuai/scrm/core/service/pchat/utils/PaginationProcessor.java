package com.sankuai.scrm.core.service.pchat.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/29 17:56
 * @Version v1.0.0
 */
@Slf4j
public class PaginationProcessor {
    private final int pageSize;
    /**
     * 数据库分页，从0开始
     */
    private int currentPage;
    private int initCurrentPage;

    public PaginationProcessor(final int pageSize, int currentPage) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.initCurrentPage = this.currentPage = Math.max(currentPage, 0);
    }

    public int getPageSize() {
        return pageSize;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void reset() {
        this.currentPage = this.initCurrentPage;
    }

    /**
     * 分页单条数据处理器
     *
     * @param loadDataCallback    加载数据回调
     * @param processDataCallback 处理数据回调
     * @param <T>
     */
    public <T> void process(Supplier<PagerList<T>> loadDataCallback, Consumer<T> processDataCallback) {
        if (loadDataCallback == null || processDataCallback == null) {
            return;
        }
        boolean hasNext = true;
        log.info("分页处理数据开始");
        do {
            PagerList<T> pagerList = loadDataCallback.get();
            if (pagerList == null) {
                log.info("未查询到分页数据，不处理");
                break;
            }
            log.info("处理分页数据：当前页:{},页大小:{},本次获取数据量:{}", currentPage, pageSize, pagerList.getTotal());
            if (pagerList.getTotal() == 0 || CollectionUtil.isEmpty(pagerList.getData())) {
                log.info("分页数据查询结果为空，不处理");
                break;
            }
            pagerList.getData().forEach(m -> {
                processDataCallback.accept(m);
            });
            if (pagerList.getData().size() < pageSize) {
                hasNext = false;
            }
            this.currentPage++;
        } while (hasNext);
        log.info("分页处理数据完毕");
    }

    public <T> void processAll(Supplier<PagerList<T>> loadDataCallback, Consumer<List<T>> processDataCallback) {
        if (loadDataCallback == null || processDataCallback == null) {
            return;
        }
        boolean hasNext = true;
        log.info("分页处理数据开始");
        do {
            PagerList<T> pagerList = loadDataCallback.get();
            if (pagerList == null) {
                log.info("未查询到分页数据，不处理");
                break;
            }
            log.info("处理分页数据：当前页:{},页大小:{},本次获取数据量:{}", currentPage, pageSize, pagerList.getTotal());
            if (pagerList.getTotal() == 0 || CollectionUtil.isEmpty(pagerList.getData())) {
                log.info("分页数据查询结果为空，不处理");
                break;
            }
            processDataCallback.accept(pagerList.getData());
            if (pagerList.getData().size() < pageSize) {
                hasNext = false;
            }
            this.currentPage++;
        } while (hasNext);
        log.info("分页处理数据完毕");
    }

    /**
     * 分页处理，并自带终止
     *
     * @param loadDataCallback
     * @param processDataCallback
     * @param <T>
     */
    public <T> void processAllWithTerminal(Supplier<PagerList<T>> loadDataCallback, Function<List<T>, Boolean> processDataCallback) {
        if (loadDataCallback == null || processDataCallback == null) {
            return;
        }
        boolean hasNext = true;
        log.info("分页处理数据开始");
        do {
            PagerList<T> pagerList = loadDataCallback.get();
            if (pagerList == null) {
                log.info("未查询到分页数据，不处理");
                break;
            }
            log.info("处理分页数据：当前页:{},页大小:{},本次获取数据量:{}", currentPage, pageSize, pagerList.getTotal());
            if (pagerList.getTotal() == 0 || CollectionUtil.isEmpty(pagerList.getData())) {
                log.info("分页数据查询结果为空，不处理");
                break;
            }
            Boolean terminal = processDataCallback.apply(pagerList.getData());
            if (Boolean.TRUE == terminal) {
                log.info("业务处理逻辑自动终止：当前页:{},页大小:{},本次获取数据量:{}", currentPage, pageSize, pagerList.getTotal());
                hasNext = false;
            }
            if (pagerList.getData().size() < pageSize) {
                hasNext = false;
            }
            this.currentPage++;
        } while (hasNext);
        log.info("分页处理数据完毕");
    }


}
