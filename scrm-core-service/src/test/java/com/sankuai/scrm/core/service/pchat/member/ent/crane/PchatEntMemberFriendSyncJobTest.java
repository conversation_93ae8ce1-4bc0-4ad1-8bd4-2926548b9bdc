package com.sankuai.scrm.core.service.pchat.member.ent.crane;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxStaffAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxBatchContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetailListResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.adapter.utils.WechatTypeUtils;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.member.ent.service.PchatEntMemberFriendService;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PchatEntMemberFriendSyncJobTest {

    @InjectMocks
    private PchatEntMemberFriendSyncJob pchatEntMemberFriendSyncJob;

    @Mock
    private WechatTypeUtils wechatTypeUtils;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @Mock
    private ScrmPersonalWxUserDomainService wxUserDomainService;

    @Mock
    private PchatEntMemberFriendService pchatEntMemberFriendService;

    private String appId = "appId";

    private String corpId = "corpId";

    private String userId = "userId";

    @Mock
    private CorpWxStaffAcl corpWxStaffAcl;

    private Method syncMethod;

    @Before
    public void setUp() throws Exception {
        syncMethod = PchatEntMemberFriendSyncJob.class.getDeclaredMethod("sync", String.class);
        syncMethod.setAccessible(true);
    }

    private void invokeSyncMethod(String appId, String corpId, String userId) throws Exception {
        Method syncMethod = PchatEntMemberFriendSyncJob.class.getDeclaredMethod("sync", String.class, String.class, String.class);
        syncMethod.setAccessible(true);
        syncMethod.invoke(pchatEntMemberFriendSyncJob, appId, corpId, userId);
    }

    /**
     * Tests the sync method under normal conditions.
     */
    @Test
    public void testSyncNormal() throws Throwable {
        // Arrange
        when(wechatTypeUtils.getEnterpriseAppIdList()).thenReturn(Arrays.asList("appId1", "appId2"));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        // Act
        pchatEntMemberFriendSyncJob.sync();
        // Assert
        verify(wechatTypeUtils, times(1)).getEnterpriseAppIdList();
        verify(appConfigRepository, atLeastOnce()).getCorpIdByAppId(anyString());
    }

    /**
     * Tests the sync method when wechatTypeUtils.getEnterpriseAppIdList() returns an empty list.
     */
    @Test
    public void testSyncEmptyList() throws Throwable {
        // Arrange
        when(wechatTypeUtils.getEnterpriseAppIdList()).thenReturn(Collections.emptyList());
        // Act
        pchatEntMemberFriendSyncJob.sync();
        // Assert
        verify(wechatTypeUtils, times(1)).getEnterpriseAppIdList();
    }

    /**
     * Tests the sync method when wechatTypeUtils.getEnterpriseAppIdList() throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testSyncException() throws Throwable {
        // Arrange
        when(wechatTypeUtils.getEnterpriseAppIdList()).thenThrow(new RuntimeException());
        // Act
        pchatEntMemberFriendSyncJob.sync();
    }

    @Test
    public void testSyncResponseUserDetailListIsNotEmpty() throws Throwable {
        // Create WxContactUserDetail
        WxContactUserDetail contactUserDetail = new WxContactUserDetail();
        contactUserDetail.setExternalUserId("externalUserId");
        contactUserDetail.setName("Test User");
        contactUserDetail.setAvatar("avatar_url");
        contactUserDetail.setType(1);
        contactUserDetail.setGender(1);
        contactUserDetail.setUnionId("unionId");
        // Create WxBatchContactUserDetail
        WxBatchContactUserDetail batchUserDetail = new WxBatchContactUserDetail();
        batchUserDetail.setContactUserDetail(contactUserDetail);
        // Create response with success status and non-empty user list
        WxContactUserDetailListResponse response = new WxContactUserDetailListResponse();
        response.setErrCode(0);
        response.setNextCursor("cursor");
        response.setUserDetailList(Collections.singletonList(batchUserDetail));
        // Mock batchGetUserDetail to return our response
        when(corpWxContactAcl.batchGetUserDetail(eq(corpId), eq(Collections.singletonList(userId)), isNull())).thenReturn(response);
        // Mock buildWxUserInfo
        ScrmPersonalWxUserInfo userInfo = new ScrmPersonalWxUserInfo();
        userInfo.setSerialNo("externalUserId");
        userInfo.setWxId("externalUserId");
        when(pchatEntMemberFriendService.buildWxUserInfo(eq(appId), eq(contactUserDetail))).thenReturn(userInfo);
        // Mock saveOrUpdateRobotFriendInfo to return success
        List<ScrmPersonalWxUserInfo> returnList = Collections.singletonList(userInfo);
        when(wxUserDomainService.saveOrUpdateRobotFriendInfo(eq(appId), eq(userId), argThat(list -> list != null && !list.isEmpty()), argThat(map -> map != null))).thenReturn(returnList);
        // Execute the method
        invokeSyncMethod(appId, corpId, userId);
        // Verify interactions
        verify(corpWxContactAcl).batchGetUserDetail(eq(corpId), eq(Collections.singletonList(userId)), isNull());
        verify(pchatEntMemberFriendService).buildWxUserInfo(eq(appId), eq(contactUserDetail));
        verify(wxUserDomainService).saveOrUpdateRobotFriendInfo(eq(appId), eq(userId), argThat(list -> list != null && !list.isEmpty()), argThat(map -> map != null));
    }

    @Test
    public void testSyncUserIdIsNull() throws Throwable {
        invokeSyncMethod(appId, corpId, null);
        verify(corpWxContactAcl, never()).batchGetUserDetail(anyString(), anyList(), anyString());
    }

    @Test
    public void testSyncResponseIsNull() throws Throwable {
        invokeSyncMethod(appId, corpId, userId);
        verify(wxUserDomainService, never()).saveOrUpdateRobotFriendInfo(anyString(), anyString(), anyList(), anyMap());
    }

    @Test
    public void testSyncResponseIsFailure() throws Throwable {
        WxContactUserDetailListResponse response = new WxContactUserDetailListResponse();
        response.setErrCode(1);
        invokeSyncMethod(appId, corpId, userId);
        verify(wxUserDomainService, never()).saveOrUpdateRobotFriendInfo(anyString(), anyString(), anyList(), anyMap());
    }

    @Test
    public void testSyncResponseHasNoNextPage() throws Throwable {
        WxContactUserDetailListResponse response = new WxContactUserDetailListResponse();
        response.setErrCode(0);
        response.setNextCursor(null);
        invokeSyncMethod(appId, corpId, userId);
        verify(wxUserDomainService, never()).saveOrUpdateRobotFriendInfo(anyString(), anyString(), anyList(), anyMap());
    }

    @Test
    public void testSyncResponseUserDetailListIsEmpty() throws Throwable {
        WxContactUserDetailListResponse response = new WxContactUserDetailListResponse();
        response.setErrCode(0);
        response.setNextCursor("cursor");
        response.setUserDetailList(Collections.emptyList());
        invokeSyncMethod(appId, corpId, userId);
        verify(wxUserDomainService, never()).saveOrUpdateRobotFriendInfo(anyString(), anyString(), anyList(), anyMap());
    }

    @Test
    public void testSyncAppIdIsNull() throws Throwable {
        // When
        syncMethod.invoke(pchatEntMemberFriendSyncJob, (String) null);
        // Then
        verify(appConfigRepository, never()).getCorpIdByAppId(anyString());
    }

    @Test
    public void testSyncCorpIdIsNull() throws Throwable {
        // Given
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        // When
        syncMethod.invoke(pchatEntMemberFriendSyncJob, appId);
        // Then
        verify(corpWxStaffAcl, never()).getFollowUserList(anyString());
    }

    @Test
    public void testSyncFollowUserListIsNull() throws Throwable {
        // Given
        String appId = "appId";
        String corpId = "corpId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(corpWxStaffAcl.getFollowUserList(corpId)).thenReturn(null);
        // When
        syncMethod.invoke(pchatEntMemberFriendSyncJob, appId);
        // Then
        verify(corpWxContactAcl, never()).batchGetUserDetail(anyString(), anyList(), anyString());
    }
}
