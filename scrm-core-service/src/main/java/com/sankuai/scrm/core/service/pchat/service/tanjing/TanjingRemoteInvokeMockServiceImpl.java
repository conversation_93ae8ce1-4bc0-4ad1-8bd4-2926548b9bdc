package com.sankuai.scrm.core.service.pchat.service.tanjing;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.dz.srcm.pchat.dto.InvokeResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/16 14:13
 * @Version v1.0.0
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "tanjing.mock", havingValue = "true")
public class TanjingRemoteInvokeMockServiceImpl implements TanjingRemoteInvokeService {
    @Override
    public <T extends InvokeResultDTO> T request(String url, Map<String, Object> paramMap, Class<T> cls) {
        log.info("模拟发送远程请求：url={},param:{}", url, paramMap);
        String result = MockThreadLocal.getMockResult();
        if (result == null) {
            result = "{\"nResult\":1,\"vcResult\":\"SUCCESS\",\"vcSerialNo\":\""+System.currentTimeMillis()+"\"}";
        }
        return JSONObject.parseObject(result, cls);

    }

    @Override
    public <T extends InvokeResultDTO> T request(String url, Map<String, Object> paramMap, TypeReference<T> typeReference) {
        log.info("模拟发送远程请求：url={},param:{}", url, paramMap);
        String result = MockThreadLocal.getMockResult();
        if (result == null) {
            result = "{\"nResult\":1,\"vcResult\":\"SUCCESS\",\"vcSerialNo\":\""+System.currentTimeMillis()+"\"}";
        }
        return JSONObject.parseObject(result, typeReference);
    }
}
