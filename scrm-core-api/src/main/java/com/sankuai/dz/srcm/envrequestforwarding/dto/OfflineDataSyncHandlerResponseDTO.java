package com.sankuai.dz.srcm.envrequestforwarding.dto;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfflineDataSyncHandlerResponseDTO implements Serializable {
    @Builder.Default
    private ResultTypeEnum resultTypeEnum=ResultTypeEnum.UNKNOWN;
}
