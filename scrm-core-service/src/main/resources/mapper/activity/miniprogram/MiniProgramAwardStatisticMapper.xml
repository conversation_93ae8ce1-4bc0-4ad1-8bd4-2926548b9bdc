<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper.MiniProgramAwardStatisticMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="award_no" jdbcType="VARCHAR" property="awardNo" />
    <result column="award_time" jdbcType="TIMESTAMP" property="awardTime" />
    <result column="award_count" jdbcType="INTEGER" property="awardCount" />
    <result column="participants_count" jdbcType="INTEGER" property="participantsCount" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, activity_id, award_no, award_time, award_count, participants_count, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.example.MiniProgramAwardStatisticExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_program_award_statistic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_program_award_statistic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_program_award_statistic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.example.MiniProgramAwardStatisticExample">
    delete from mini_program_award_statistic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_program_award_statistic (activity_id, award_no, award_time, 
      award_count, participants_count, add_time, 
      update_time)
    values (#{activityId,jdbcType=INTEGER}, #{awardNo,jdbcType=VARCHAR}, #{awardTime,jdbcType=TIMESTAMP}, 
      #{awardCount,jdbcType=INTEGER}, #{participantsCount,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_program_award_statistic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="awardNo != null">
        award_no,
      </if>
      <if test="awardTime != null">
        award_time,
      </if>
      <if test="awardCount != null">
        award_count,
      </if>
      <if test="participantsCount != null">
        participants_count,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        #{activityId,jdbcType=INTEGER},
      </if>
      <if test="awardNo != null">
        #{awardNo,jdbcType=VARCHAR},
      </if>
      <if test="awardTime != null">
        #{awardTime,jdbcType=TIMESTAMP},
      </if>
      <if test="awardCount != null">
        #{awardCount,jdbcType=INTEGER},
      </if>
      <if test="participantsCount != null">
        #{participantsCount,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.example.MiniProgramAwardStatisticExample" resultType="java.lang.Long">
    select count(*) from mini_program_award_statistic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_program_award_statistic
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=INTEGER},
      </if>
      <if test="record.awardNo != null">
        award_no = #{record.awardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.awardTime != null">
        award_time = #{record.awardTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.awardCount != null">
        award_count = #{record.awardCount,jdbcType=INTEGER},
      </if>
      <if test="record.participantsCount != null">
        participants_count = #{record.participantsCount,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_program_award_statistic
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=INTEGER},
      award_no = #{record.awardNo,jdbcType=VARCHAR},
      award_time = #{record.awardTime,jdbcType=TIMESTAMP},
      award_count = #{record.awardCount,jdbcType=INTEGER},
      participants_count = #{record.participantsCount,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic">
    update mini_program_award_statistic
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=INTEGER},
      </if>
      <if test="awardNo != null">
        award_no = #{awardNo,jdbcType=VARCHAR},
      </if>
      <if test="awardTime != null">
        award_time = #{awardTime,jdbcType=TIMESTAMP},
      </if>
      <if test="awardCount != null">
        award_count = #{awardCount,jdbcType=INTEGER},
      </if>
      <if test="participantsCount != null">
        participants_count = #{participantsCount,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic">
    update mini_program_award_statistic
    set activity_id = #{activityId,jdbcType=INTEGER},
      award_no = #{awardNo,jdbcType=VARCHAR},
      award_time = #{awardTime,jdbcType=TIMESTAMP},
      award_count = #{awardCount,jdbcType=INTEGER},
      participants_count = #{participantsCount,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into mini_program_award_statistic
    (activity_id, award_no, award_time, award_count, participants_count, add_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.activityId,jdbcType=INTEGER}, #{item.awardNo,jdbcType=VARCHAR}, #{item.awardTime,jdbcType=TIMESTAMP}, 
        #{item.awardCount,jdbcType=INTEGER}, #{item.participantsCount,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>