package com.sankuai.scrm.core.service.open.convert;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.open.dto.VoiceMessageDTO;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenChattingMessage;
import java.lang.reflect.Method;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.srcm.open.dto.ChatHistoryDataDTO;
import com.sankuai.dz.srcm.open.dto.ImageMessageDTO;
import com.sankuai.dz.srcm.open.dto.LinkMessageDTO;
import com.sankuai.dz.srcm.open.dto.MiniProgramMessageDTO;
import com.sankuai.dz.srcm.open.dto.TextMessageDTO;
import com.sankuai.dz.srcm.open.enums.MsgTypeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import com.sankuai.dz.srcm.open.dto.*;
import org.junit.jupiter.api.BeforeEach;

/**
 * Test class for ScrmOpenGroupMsgBodyConvert.createVoiceMessageDTO method
 */
@ExtendWith(MockitoExtension.class)
class ScrmOpenGroupMsgBodyConvertTest {

    @Mock
    private OpenChattingMessage mockedMessage;

    @InjectMocks
    private ScrmOpenGroupMsgBodyConvert scrmOpenGroupMsgBodyConvert;

    private Method createMessageDataDTOMethod;

    private Method getCreateVoiceMessageDTOMethod() throws NoSuchMethodException {
        Method method = ScrmOpenGroupMsgBodyConvert.class.getDeclaredMethod("createVoiceMessageDTO", OpenChattingMessage.class);
        method.setAccessible(true);
        return method;
    }

    /**
     * Test createVoiceMessageDTO with normal text content
     */
    @Test
    @DisplayName("Should create VoiceMessageDTO with normal content")
    public void testCreateVoiceMessageDTO_WithNormalContent() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getTextContent()).thenReturn("Test voice message");
        Method method = getCreateVoiceMessageDTOMethod();
        // act
        VoiceMessageDTO result = (VoiceMessageDTO) method.invoke(null, message);
        // assert
        assertNotNull(result, "VoiceMessageDTO should not be null");
        assertEquals("Test voice message", result.getContent(), "Content should match the input text content");
        verify(message, times(1)).getTextContent();
    }

    /**
     * Test createVoiceMessageDTO with null text content
     */
    @Test
    @DisplayName("Should create VoiceMessageDTO with null content")
    public void testCreateVoiceMessageDTO_WithNullContent() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getTextContent()).thenReturn(null);
        Method method = getCreateVoiceMessageDTOMethod();
        // act
        VoiceMessageDTO result = (VoiceMessageDTO) method.invoke(null, message);
        // assert
        assertNotNull(result, "VoiceMessageDTO should not be null");
        assertNull(result.getContent(), "Content should be null");
        verify(message, times(1)).getTextContent();
    }

    /**
     * Test createVoiceMessageDTO with empty text content
     */
    @Test
    @DisplayName("Should create VoiceMessageDTO with empty content")
    public void testCreateVoiceMessageDTO_WithEmptyContent() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getTextContent()).thenReturn("");
        Method method = getCreateVoiceMessageDTOMethod();
        // act
        VoiceMessageDTO result = (VoiceMessageDTO) method.invoke(null, message);
        // assert
        assertNotNull(result, "VoiceMessageDTO should not be null");
        assertEquals("", result.getContent(), "Content should be empty string");
        verify(message, times(1)).getTextContent();
    }

    /**
     * Test createVoiceMessageDTO with null message parameter
     */
    @Test
    @DisplayName("Should throw NullPointerException when message is null")
    public void testCreateVoiceMessageDTO_WithNullMessage() throws Throwable {
        // arrange
        Method method = getCreateVoiceMessageDTOMethod();
        // act & assert
        assertThrows(java.lang.reflect.InvocationTargetException.class, () -> {
            method.invoke(null, new Object[] { null });
        }, "Should throw InvocationTargetException wrapping NullPointerException when message is null");
    }

    @Test
    public void testCreateChatHistoryMessageDTO_EmptyList() throws Throwable {
        // arrange
        List<OpenChattingMessage> input = new ArrayList<>();
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCreateChatHistoryMessageDTO_NullInput() throws Throwable {
        // arrange
        List<OpenChattingMessage> input = null;
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleTextMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("TEXT");
        when(message.getTextContent()).thenReturn("Test message");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.get(0).getMsgType());
        assertNotNull(result.get(0).getText());
        assertEquals("Test message", result.get(0).getText().getContent());
        verify(message, times(1)).getMessageType();
        verify(message, times(1)).getTextContent();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleImageMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("IMAGE");
        when(message.getImageUrl()).thenReturn("http://test.com/image.jpg");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.IMAGE.getCode(), result.get(0).getMsgType());
        assertNotNull(result.get(0).getImage());
        assertEquals("http://test.com/image.jpg", result.get(0).getImage().getUrl());
        verify(message, times(1)).getMessageType();
        verify(message, times(1)).getImageUrl();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleMiniProgramMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("MINI_PROGRAM");
        when(message.getMiniAppId()).thenReturn("mini123");
        when(message.getMiniTitle()).thenReturn("Mini App");
        when(message.getMiniPagePath()).thenReturn("/home");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.MINI_PROGRAM.getCode(), result.get(0).getMsgType());
        assertNotNull(result.get(0).getMiniProgram());
        assertEquals("mini123", result.get(0).getMiniProgram().getAppId());
        assertEquals("Mini App", result.get(0).getMiniProgram().getTitle());
        assertEquals("/home", result.get(0).getMiniProgram().getPagePath());
        verify(message, times(1)).getMessageType();
        verify(message, times(1)).getMiniAppId();
        verify(message, times(1)).getMiniTitle();
        verify(message, times(1)).getMiniPagePath();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleH5CardMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("H5_CARD");
        when(message.getH5Title()).thenReturn("H5 Title");
        when(message.getH5Description()).thenReturn("H5 Description");
        when(message.getH5LinkUrl()).thenReturn("http://test.com/h5");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.NEWS.getCode(), result.get(0).getMsgType());
        assertNotNull(result.get(0).getLink());
        assertEquals("H5 Title", result.get(0).getLink().getTitle());
        assertEquals("H5 Description", result.get(0).getLink().getDescription());
        assertEquals("http://test.com/h5", result.get(0).getLink().getUrl());
        verify(message, times(1)).getMessageType();
        verify(message, times(1)).getH5Title();
        verify(message, times(1)).getH5Description();
        verify(message, times(1)).getH5LinkUrl();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleAudioMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("AUDIO");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.VOICE.getCode(), result.get(0).getMsgType());
        assertNotNull(result.get(0).getVoice());
        verify(message, times(1)).getMessageType();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_SingleChatHistoryMessage() throws Throwable {
        // arrange
        OpenChattingMessage innerMessage = mock(OpenChattingMessage.class);
        when(innerMessage.getMessageType()).thenReturn("TEXT");
        when(innerMessage.getTextContent()).thenReturn("Inner text");
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("CHAT_HISTORY");
        when(message.getHistoryTitle()).thenReturn("Chat History");
        when(message.getHistoryMessages()).thenReturn(Collections.singletonList(innerMessage));
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(MsgTypeEnum.CHAT_HISTORY.getCode(), result.get(0).getMsgType());
        assertEquals("Chat History", result.get(0).getHistoryTitle());
        assertNotNull(result.get(0).getChatHistoryList());
        assertEquals(1, result.get(0).getChatHistoryList().size());
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.get(0).getChatHistoryList().get(0).getMsgType());
        verify(message, times(1)).getMessageType();
        verify(message, times(1)).getHistoryTitle();
        verify(message, times(1)).getHistoryMessages();
        verify(innerMessage, times(1)).getMessageType();
        verify(innerMessage, times(1)).getTextContent();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_MixedMessageTypes() throws Throwable {
        // arrange
        OpenChattingMessage textMessage = mock(OpenChattingMessage.class);
        when(textMessage.getMessageType()).thenReturn("TEXT");
        when(textMessage.getTextContent()).thenReturn("Text message");
        OpenChattingMessage imageMessage = mock(OpenChattingMessage.class);
        when(imageMessage.getMessageType()).thenReturn("IMAGE");
        when(imageMessage.getImageUrl()).thenReturn("http://test.com/image.jpg");
        List<OpenChattingMessage> input = Arrays.asList(textMessage, imageMessage);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.get(0).getMsgType());
        assertEquals(MsgTypeEnum.IMAGE.getCode(), result.get(1).getMsgType());
        verify(textMessage, times(1)).getMessageType();
        verify(textMessage, times(1)).getTextContent();
        verify(imageMessage, times(1)).getMessageType();
        verify(imageMessage, times(1)).getImageUrl();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_UnsupportedMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = mock(OpenChattingMessage.class);
        when(message.getMessageType()).thenReturn("UNSUPPORTED_TYPE");
        List<OpenChattingMessage> input = Collections.singletonList(message);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(message, times(1)).getMessageType();
    }

    @Test
    public void testCreateChatHistoryMessageDTO_NestedChatHistory() throws Throwable {
        // arrange
        OpenChattingMessage innerTextMessage = mock(OpenChattingMessage.class);
        when(innerTextMessage.getMessageType()).thenReturn("TEXT");
        when(innerTextMessage.getTextContent()).thenReturn("Inner text");
        OpenChattingMessage innerImageMessage = mock(OpenChattingMessage.class);
        when(innerImageMessage.getMessageType()).thenReturn("IMAGE");
        when(innerImageMessage.getImageUrl()).thenReturn("http://test.com/inner.jpg");
        OpenChattingMessage chatHistoryMessage = mock(OpenChattingMessage.class);
        when(chatHistoryMessage.getMessageType()).thenReturn("CHAT_HISTORY");
        when(chatHistoryMessage.getHistoryTitle()).thenReturn("Nested History");
        when(chatHistoryMessage.getHistoryMessages()).thenReturn(Arrays.asList(innerTextMessage, innerImageMessage));
        OpenChattingMessage outerTextMessage = mock(OpenChattingMessage.class);
        when(outerTextMessage.getMessageType()).thenReturn("TEXT");
        when(outerTextMessage.getTextContent()).thenReturn("Outer text");
        List<OpenChattingMessage> input = Arrays.asList(outerTextMessage, chatHistoryMessage);
        // act
        List<ChatHistoryDataDTO> result = ScrmOpenGroupMsgBodyConvert.createChatHistoryMessageDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // 检查外层消息
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.get(0).getMsgType());
        assertEquals("Outer text", result.get(0).getText().getContent());
        // 检查嵌套聊天记录
        assertEquals(MsgTypeEnum.CHAT_HISTORY.getCode(), result.get(1).getMsgType());
        assertEquals("Nested History", result.get(1).getHistoryTitle());
        assertEquals(2, result.get(1).getChatHistoryList().size());
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.get(1).getChatHistoryList().get(0).getMsgType());
        assertEquals(MsgTypeEnum.IMAGE.getCode(), result.get(1).getChatHistoryList().get(1).getMsgType());
        // 验证mock调用
        verify(outerTextMessage, times(1)).getMessageType();
        verify(outerTextMessage, times(1)).getTextContent();
        verify(chatHistoryMessage, times(1)).getMessageType();
        verify(chatHistoryMessage, times(1)).getHistoryTitle();
        verify(chatHistoryMessage, times(1)).getHistoryMessages();
        verify(innerTextMessage, times(1)).getMessageType();
        verify(innerTextMessage, times(1)).getTextContent();
        verify(innerImageMessage, times(1)).getMessageType();
        verify(innerImageMessage, times(1)).getImageUrl();
    }

    private ChatHistoryDataDTO invokeCreateChatHistoryDataDTO(OpenChattingMessage message) throws Exception {
        Method method = ScrmOpenGroupMsgBodyConvert.class.getDeclaredMethod("createChatHistoryDataDTO", OpenChattingMessage.class);
        method.setAccessible(true);
        return (ChatHistoryDataDTO) method.invoke(null, message);
    }

    @Test
    public void testCreateChatHistoryDataDTO_TextMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("TEXT");
        message.setTextContent("Hello World");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.getMsgType());
        assertNotNull(result.getText());
        assertEquals("Hello World", result.getText().getContent());
        assertNull(result.getImage());
        assertNull(result.getMiniProgram());
    }

    @Test
    public void testCreateChatHistoryDataDTO_ImageMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("IMAGE");
        message.setImageUrl("http://example.com/image.jpg");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.IMAGE.getCode(), result.getMsgType());
        assertNotNull(result.getImage());
        assertEquals("http://example.com/image.jpg", result.getImage().getUrl());
        assertNull(result.getText());
        assertNull(result.getMiniProgram());
    }

    @Test
    public void testCreateChatHistoryDataDTO_MiniProgramMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("MINI_PROGRAM");
        message.setMiniAppId("app123");
        message.setMiniOriginAppId("origin123");
        message.setMiniTitle("Mini Title");
        message.setMiniPagePath("/pages/index");
        message.setMiniAppName("Mini App");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.MINI_PROGRAM.getCode(), result.getMsgType());
        assertNotNull(result.getMiniProgram());
        assertEquals("app123", result.getMiniProgram().getAppId());
        assertEquals("origin123", result.getMiniProgram().getOriginAppId());
        assertEquals("Mini Title", result.getMiniProgram().getTitle());
        assertEquals("/pages/index", result.getMiniProgram().getPagePath());
        assertEquals("Mini App", result.getMiniProgram().getAppName());
        assertNull(result.getText());
    }

    @Test
    public void testCreateChatHistoryDataDTO_LinkMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("H5_CARD");
        message.setH5Title("H5 Title");
        message.setH5Description("H5 Description");
        message.setH5LinkUrl("http://example.com");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.NEWS.getCode(), result.getMsgType());
        assertNotNull(result.getLink());
        assertEquals("H5 Title", result.getLink().getTitle());
        assertEquals("H5 Description", result.getLink().getDescription());
        assertEquals("http://example.com", result.getLink().getUrl());
        assertNull(result.getText());
    }

    @Test
    public void testCreateChatHistoryDataDTO_VoiceMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("AUDIO");
        message.setTextContent("Audio content");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.VOICE.getCode(), result.getMsgType());
        assertNotNull(result.getVoice());
        assertEquals("Audio content", result.getVoice().getContent());
        assertNull(result.getText());
    }

    @Test
    public void testCreateChatHistoryDataDTO_ChatHistoryMessage() throws Throwable {
        // arrange
        OpenChattingMessage innerMessage = new OpenChattingMessage();
        innerMessage.setMessageType("TEXT");
        innerMessage.setTextContent("Inner message");
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("CHAT_HISTORY");
        message.setHistoryTitle("Chat History");
        message.setHistoryMessages(Collections.singletonList(innerMessage));
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.CHAT_HISTORY.getCode(), result.getMsgType());
        assertEquals("Chat History", result.getHistoryTitle());
        assertNotNull(result.getChatHistoryList());
        assertEquals(1, result.getChatHistoryList().size());
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.getChatHistoryList().get(0).getMsgType());
        assertNotNull(result.getChatHistoryList().get(0).getText());
        assertEquals("Inner message", result.getChatHistoryList().get(0).getText().getContent());
    }

    @Test
    public void testCreateChatHistoryDataDTO_UnsupportedMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("UNSUPPORTED_TYPE");
        // act
        ChatHistoryDataDTO result = invokeCreateChatHistoryDataDTO(message);
        // assert
        assertNull(result);
    }

    @Test
    public void testCreateChatHistoryDataDTO_NullMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType(null);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokeCreateChatHistoryDataDTO(message));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @BeforeEach
    public void setup() throws NoSuchMethodException {
        createMessageDataDTOMethod = ScrmOpenGroupMsgBodyConvert.class.getDeclaredMethod("createMessageDataDTO", OpenChattingMessage.class);
        createMessageDataDTOMethod.setAccessible(true);
    }

    @Test
    public void testCreateMessageDataDTOTextMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("TEXT");
        message.setTextContent("Hello World");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.getMsgType());
        assertNotNull(result.getText());
        assertEquals("Hello World", result.getText().getContent());
        assertNull(result.getImage());
        assertNull(result.getMiniProgram());
    }

    @Test
    public void testCreateMessageDataDTOImageMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("IMAGE");
        message.setImageUrl("http://example.com/image.jpg");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.IMAGE.getCode(), result.getMsgType());
        assertNotNull(result.getImage());
        assertEquals("http://example.com/image.jpg", result.getImage().getUrl());
        assertNull(result.getText());
    }

    @Test
    public void testCreateMessageDataDTOMiniProgramMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("MINI_PROGRAM");
        message.setMiniAppId("app123");
        message.setMiniOriginAppId("origin123");
        message.setMiniTitle("Mini Title");
        message.setMiniPagePath("/index");
        message.setMiniAppName("Mini App");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.MINI_PROGRAM.getCode(), result.getMsgType());
        assertNotNull(result.getMiniProgram());
        assertEquals("app123", result.getMiniProgram().getAppId());
        assertEquals("origin123", result.getMiniProgram().getOriginAppId());
        assertEquals("Mini Title", result.getMiniProgram().getTitle());
        assertEquals("/index", result.getMiniProgram().getPagePath());
        assertEquals("Mini App", result.getMiniProgram().getAppName());
    }

    @Test
    public void testCreateMessageDataDTOH5CardMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("H5_CARD");
        message.setH5Title("H5 Title");
        message.setH5Description("H5 Description");
        message.setH5LinkUrl("http://example.com/h5");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.NEWS.getCode(), result.getMsgType());
        assertNotNull(result.getLink());
        assertEquals("H5 Title", result.getLink().getTitle());
        assertEquals("H5 Description", result.getLink().getDescription());
        assertEquals("http://example.com/h5", result.getLink().getUrl());
    }

    @Test
    public void testCreateMessageDataDTOAudioMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("AUDIO");
        message.setTextContent("Audio content");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.VOICE.getCode(), result.getMsgType());
        assertNotNull(result.getVoice());
        assertEquals("Audio content", result.getVoice().getContent());
    }

    @Test
    public void testCreateMessageDataDTOChatHistoryMessage() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("CHAT_HISTORY");
        OpenChattingMessage historyMessage = new OpenChattingMessage();
        historyMessage.setMessageType("TEXT");
        historyMessage.setTextContent("History text");
        message.setHistoryMessages(Collections.singletonList(historyMessage));
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.CHAT_HISTORY.getCode(), result.getMsgType());
        assertNotNull(result.getChatHistoryList());
        assertEquals(1, result.getChatHistoryList().size());
        assertEquals("History text", result.getChatHistoryList().get(0).getText().getContent());
    }

    @Test
    public void testCreateMessageDataDTOUnsupportedMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("UNSUPPORTED_TYPE");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNull(result);
    }

    @Test
    public void testCreateMessageDataDTOEmptyMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        // empty message type
        message.setMessageType("");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNull(result);
    }

    @Test
    public void testCreateMessageDataDTOEmptyContent() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("TEXT");
        // empty content
        message.setTextContent("");
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.getMsgType());
        assertNotNull(result.getText());
        assertEquals("", result.getText().getContent());
    }

    @Test
    public void testCreateMessageDataDTOEmptyHistoryMessages() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("CHAT_HISTORY");
        // empty history messages
        message.setHistoryMessages(Collections.emptyList());
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.CHAT_HISTORY.getCode(), result.getMsgType());
        assertNotNull(result.getChatHistoryList());
        assertTrue(result.getChatHistoryList().isEmpty());
    }

    @Test
    public void testCreateMessageDataDTONullContent() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("TEXT");
        // null content
        message.setTextContent(null);
        // act
        MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
        // assert
        assertNotNull(result);
        assertEquals(MsgTypeEnum.TEXT.getCode(), result.getMsgType());
        assertNotNull(result.getText());
        assertNull(result.getText().getContent());
    }

    @Test
    public void testCreateMessageDataDTONullMessageType() throws Throwable {
        // arrange
        OpenChattingMessage message = new OpenChattingMessage();
        // Don't set message type, it will be null by default
        // act & assert
        try {
            MessageDataDTO result = (MessageDataDTO) createMessageDataDTOMethod.invoke(null, message);
            assertNull(result, "Result should be null for null message type");
        } catch (Exception e) {
            // If NPE occurs, that's also acceptable as it indicates the method doesn't handle null types
            assertTrue(e.getCause() instanceof NullPointerException, "Expected NullPointerException for null message type");
        }
    }
}
