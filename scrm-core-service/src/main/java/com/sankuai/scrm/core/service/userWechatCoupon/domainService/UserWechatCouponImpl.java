package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.userWechatCoupon.UserWechatCoupon;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponRequest;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponResponse;
import com.sankuai.scrm.core.service.activity.fission.service.ActivityFissionServiceImpl;
import com.sankuai.scrm.core.service.external.contact.bo.ExternalContactAddBO;
import com.sankuai.scrm.core.service.external.contact.domain.FriendWelcomeMsgDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.entity.ScrmUserWechatCoupon;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.enums.UserWechatCouponUsed;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.example.ScrmUserWechatCouponExample;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.mapper.ScrmUserWechatCouponMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@MdpPigeonServer
@Slf4j
public class UserWechatCouponImpl implements UserWechatCoupon {

    @Resource
    private ScrmUserWechatCouponMapper scrmUserWechantCouponMapper;
    @Resource
    private ActivityFissionServiceImpl activityFissionService;
    @Resource
    private UnifiedCouponInfoService unifiedCouponInfoService;

    @Autowired
    private CorpWxContactAcl corpWxContactAcl;
    @Autowired
    private FriendWelcomeMsgDomainService friendWelcomeMsgDomainService;


    @Override
    public List<UserWechatCouponResponse> getUserWechatCoupon(UserWechatCouponRequest userWechatCouponRequest) {
        log.info("UserWechatCouponImpl.getUserWechatCoupon 查询优惠券,param:{}"
                , userWechatCouponRequest);
        List<UserWechatCouponResponse> userWechatCouponResponses = new ArrayList<>();
        if (checkParam(userWechatCouponRequest)) {
            log.info("UserWechatCouponImpl.getUserWechatCoupon 查询优惠券参数错误,param:{}"
                    , userWechatCouponRequest);
            return userWechatCouponResponses;
        }
        List<ScrmUserWechatCoupon> scrmUserWechantCoupons = getScrmUserWechantCoupons(userWechatCouponRequest);
        if (CollectionUtils.isEmpty(scrmUserWechantCoupons)){
            log.info("UserWechatCouponImpl.getUserWechatCoupon 未查询到相关优惠券,param:{}"
                    , userWechatCouponRequest);
            return userWechatCouponResponses;
        }
        List<String> unifiedCouponDTOSIds = getUserWechatCouponResponses(userWechatCouponRequest, scrmUserWechantCoupons);
        if (CollectionUtils.isEmpty(unifiedCouponDTOSIds)){
            log.info("UserWechatCouponImpl.getUserWechatCoupon 无可用优惠券,param:{}"
                    , userWechatCouponRequest);
            return userWechatCouponResponses;
        }
        for (ScrmUserWechatCoupon scrmUserWechantCoupon : scrmUserWechantCoupons){
            if (!unifiedCouponDTOSIds.contains(scrmUserWechantCoupon.getCouponid())){
                continue;
            }
            RemoteResponse<MktCouponInfoDTO> mktCouponInfoDTORemoteResponse = activityFissionService.queryMktCouponInfo(scrmUserWechantCoupon.getCouponcode());
            if (!mktCouponInfoDTORemoteResponse.isSuccess()||mktCouponInfoDTORemoteResponse.getData()==null){
                log.info("UserWechatCouponImpl.getUserWechatCoupon 获取优惠券相关啊信息失败,couponId:{}"
                        , scrmUserWechantCoupon.getCouponid());
                continue;
            }
            MktCouponInfoDTO mktCouponInfoDTO = mktCouponInfoDTORemoteResponse.getData();
            UserWechatCouponResponse userWechatCouponResponse = UserWechatCouponResponse.builder()
                    .discountAmount(mktCouponInfoDTO.getDiscountAmount())
                    .remainStock(mktCouponInfoDTO.getRemainStock())
                    .couponGroupName(mktCouponInfoDTO.getCouponGroupName())
                    .couponId(scrmUserWechantCoupon.getCouponid())
                    .priceLimit(mktCouponInfoDTO.getPriceLimit())
                    .validBeginTime(mktCouponInfoDTO.getValidBeginTime())
                    .validEndTime(mktCouponInfoDTO.getValidEndTime())
                    .used(scrmUserWechantCoupon.getUsed())
                    .build();
            userWechatCouponResponses.add(userWechatCouponResponse);
            if (scrmUserWechantCoupon.getUsed()==UserWechatCouponUsed.NEVER_USED.getCode()){
                scrmUserWechantCoupon.setUsed(UserWechatCouponUsed.ALREADY_USED.getCode());
                updateUsed(scrmUserWechantCoupon);
            }
        }
        return userWechatCouponResponses;
    }


    private void updateUsed(ScrmUserWechatCoupon scrmUserWechantCoupon) {
        ScrmUserWechatCouponExample scrmUserWechatCouponExample = new ScrmUserWechatCouponExample();
        ScrmUserWechatCouponExample.Criteria criteria = scrmUserWechatCouponExample.createCriteria()
                .andCouponidEqualTo(scrmUserWechantCoupon.getCouponid())
                .andCouponcodeEqualTo(scrmUserWechantCoupon.getCouponcode())
                .andCategoryidsEqualTo(scrmUserWechantCoupon.getCategoryids())
                .andPoiidEqualTo(scrmUserWechantCoupon.getPoiid())
                .andMtuseridEqualTo(scrmUserWechantCoupon.getMtuserid());
        if (scrmUserWechantCoupon.getMtcityid()!=null){
                        criteria.andMtcityidEqualTo(scrmUserWechantCoupon.getMtcityid());
        }
        if (scrmUserWechantCoupon.getDpcityid()!=null){
            criteria.andDpcityidEqualTo(scrmUserWechantCoupon.getDpcityid());
        }
        scrmUserWechantCouponMapper.updateByExample(scrmUserWechantCoupon, scrmUserWechatCouponExample);
    }

    private List<String> getUserWechatCouponResponses(UserWechatCouponRequest userWechatCouponRequest, List<ScrmUserWechatCoupon> scrmUserWechantCoupons) {
        BatchLoadCouponRequest batchLoadCouponRequest=new BatchLoadCouponRequest();
        batchLoadCouponRequest.setUnifiedCouponIdList(scrmUserWechantCoupons.stream()
                .map(scrmUserWechantCoupon -> String.valueOf(scrmUserWechantCoupon.getCouponid())).collect(Collectors.toList()));
        batchLoadCouponRequest.setUserType("MT");
        batchLoadCouponRequest.setUserId(userWechatCouponRequest.getMtUserId());
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> batchLoadCouponResponse = unifiedCouponInfoService.batchLoadCoupon(batchLoadCouponRequest);
        if (!batchLoadCouponResponse.isSuccess()){
            log.info("UserWechatCouponImpl.getUserWechatCoupon 查询优惠券使用情况错误,param:{},result:{}"
                    , userWechatCouponRequest, batchLoadCouponResponse);
            return null;
        }
        List<UnifiedCouponDTO> unifiedCouponDTOS = batchLoadCouponResponse.getResult();
        List<String> unifiedCouponDTOSIds = unifiedCouponDTOS.stream().filter(
                unifiedCouponDTO -> !unifiedCouponDTO.isUsed() && unifiedCouponDTO.isAvailable()).map(UnifiedCouponDTO::getUnifiedCouponId)
                .collect(Collectors.toList());
        return unifiedCouponDTOSIds;
    }

    private List<ScrmUserWechatCoupon> getScrmUserWechantCoupons(UserWechatCouponRequest userWechatCouponRequest) {
        ScrmUserWechatCouponExample scrmUserWechantCouponExample = new ScrmUserWechatCouponExample();
        ScrmUserWechatCouponExample.Criteria criteria = scrmUserWechantCouponExample.createCriteria();
                criteria.andCategoryidsEqualTo(userWechatCouponRequest.getCategoryIds())
                .andPoiidEqualTo(userWechatCouponRequest.getPoiId())
                .andMtuseridEqualTo(userWechatCouponRequest.getMtUserId());
        if (userWechatCouponRequest.getUnionId()!=null){
            criteria.andUnionidEqualTo(userWechatCouponRequest.getUnionId());
        }
        if (userWechatCouponRequest.getDpCityId()!=null){
            criteria.andDpcityidEqualTo(userWechatCouponRequest.getDpCityId());
        }
        if (userWechatCouponRequest.getMtCityId()!=null){
            criteria.andMtuseridEqualTo(userWechatCouponRequest.getMtUserId());
        }
        List<ScrmUserWechatCoupon> scrmUserWechantCoupons = scrmUserWechantCouponMapper.selectByExample(scrmUserWechantCouponExample);
        return scrmUserWechantCoupons;
    }

    private boolean checkParam(UserWechatCouponRequest userWechatCouponRequest) {
        if (userWechatCouponRequest == null) {
            return true;
        }
        if (userWechatCouponRequest.getCategoryIds()==null||userWechatCouponRequest.getCategoryIds().isEmpty()){
            return true;
        }
        if (userWechatCouponRequest.getPoiId()==null){
            return true;
        }
        if (userWechatCouponRequest.getMtCityId()==null&&userWechatCouponRequest.getDpCityId()==null){
            return true;
        }
        if (userWechatCouponRequest.getMtUserId()==null){
            return true;
        }
        return false;
    }
}
