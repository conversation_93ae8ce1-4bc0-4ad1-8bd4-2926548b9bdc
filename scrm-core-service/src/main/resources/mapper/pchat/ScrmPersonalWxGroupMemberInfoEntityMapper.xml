<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMemberInfoEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="user_serial_no" jdbcType="VARCHAR" property="userSerialNo" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="join_type" jdbcType="INTEGER" property="joinType" />
    <result column="invite_wx_id" jdbcType="VARCHAR" property="inviteWxId" />
    <result column="member_nick_name" jdbcType="VARCHAR" property="memberNickName" />
    <result column="wx_nickname" jdbcType="VARCHAR" property="wxNickname" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="member_type" jdbcType="TINYINT" property="memberType" />
    <result column="group_remark" jdbcType="VARCHAR" property="groupRemark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="enter_time" jdbcType="TIMESTAMP" property="enterTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="time_trace" jdbcType="BIGINT" property="timeTrace" />
    <result column="exit_time" jdbcType="TIMESTAMP" property="exitTime" />
    <result column="invite_user_serial_no" jdbcType="VARCHAR" property="inviteUserSerialNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, wx_id, user_serial_no, union_id, user_id, group_id, join_type, invite_wx_id, 
    member_nick_name, wx_nickname, avatar, member_type, group_remark, status, state, 
    enter_time, deleted, add_time, update_time, time_trace, exit_time, invite_user_serial_no
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_GroupMemberInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_GroupMemberInfo
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_GroupMemberInfo
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample">
    delete from Scrm_PersonalWx_GroupMemberInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_GroupMemberInfo (app_id, wx_id, user_serial_no, 
      union_id, user_id, group_id, 
      join_type, invite_wx_id, member_nick_name, 
      wx_nickname, avatar, member_type, 
      group_remark, status, state, 
      enter_time, deleted, add_time, 
      update_time, time_trace, exit_time, 
      invite_user_serial_no)
    values (#{appId,jdbcType=VARCHAR}, #{wxId,jdbcType=VARCHAR}, #{userSerialNo,jdbcType=VARCHAR}, 
      #{unionId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{groupId,jdbcType=VARCHAR}, 
      #{joinType,jdbcType=INTEGER}, #{inviteWxId,jdbcType=VARCHAR}, #{memberNickName,jdbcType=VARCHAR}, 
      #{wxNickname,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR}, #{memberType,jdbcType=TINYINT}, 
      #{groupRemark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{state,jdbcType=VARCHAR}, 
      #{enterTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{timeTrace,jdbcType=BIGINT}, #{exitTime,jdbcType=TIMESTAMP}, 
      #{inviteUserSerialNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_GroupMemberInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="userSerialNo != null">
        user_serial_no,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="joinType != null">
        join_type,
      </if>
      <if test="inviteWxId != null">
        invite_wx_id,
      </if>
      <if test="memberNickName != null">
        member_nick_name,
      </if>
      <if test="wxNickname != null">
        wx_nickname,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="memberType != null">
        member_type,
      </if>
      <if test="groupRemark != null">
        group_remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="enterTime != null">
        enter_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="timeTrace != null">
        time_trace,
      </if>
      <if test="exitTime != null">
        exit_time,
      </if>
      <if test="inviteUserSerialNo != null">
        invite_user_serial_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="userSerialNo != null">
        #{userSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="joinType != null">
        #{joinType,jdbcType=INTEGER},
      </if>
      <if test="inviteWxId != null">
        #{inviteWxId,jdbcType=VARCHAR},
      </if>
      <if test="memberNickName != null">
        #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="wxNickname != null">
        #{wxNickname,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        #{memberType,jdbcType=TINYINT},
      </if>
      <if test="groupRemark != null">
        #{groupRemark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="enterTime != null">
        #{enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="timeTrace != null">
        #{timeTrace,jdbcType=BIGINT},
      </if>
      <if test="exitTime != null">
        #{exitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inviteUserSerialNo != null">
        #{inviteUserSerialNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_GroupMemberInfo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_GroupMemberInfo
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.userSerialNo != null">
        user_serial_no = #{record.userSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.joinType != null">
        join_type = #{record.joinType,jdbcType=INTEGER},
      </if>
      <if test="record.inviteWxId != null">
        invite_wx_id = #{record.inviteWxId,jdbcType=VARCHAR},
      </if>
      <if test="record.memberNickName != null">
        member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.wxNickname != null">
        wx_nickname = #{record.wxNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.avatar != null">
        avatar = #{record.avatar,jdbcType=VARCHAR},
      </if>
      <if test="record.memberType != null">
        member_type = #{record.memberType,jdbcType=TINYINT},
      </if>
      <if test="record.groupRemark != null">
        group_remark = #{record.groupRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.enterTime != null">
        enter_time = #{record.enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.timeTrace != null">
        time_trace = #{record.timeTrace,jdbcType=BIGINT},
      </if>
      <if test="record.exitTime != null">
        exit_time = #{record.exitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inviteUserSerialNo != null">
        invite_user_serial_no = #{record.inviteUserSerialNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_GroupMemberInfo
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      user_serial_no = #{record.userSerialNo,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      join_type = #{record.joinType,jdbcType=INTEGER},
      invite_wx_id = #{record.inviteWxId,jdbcType=VARCHAR},
      member_nick_name = #{record.memberNickName,jdbcType=VARCHAR},
      wx_nickname = #{record.wxNickname,jdbcType=VARCHAR},
      avatar = #{record.avatar,jdbcType=VARCHAR},
      member_type = #{record.memberType,jdbcType=TINYINT},
      group_remark = #{record.groupRemark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      state = #{record.state,jdbcType=VARCHAR},
      enter_time = #{record.enterTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      time_trace = #{record.timeTrace,jdbcType=BIGINT},
      exit_time = #{record.exitTime,jdbcType=TIMESTAMP},
      invite_user_serial_no = #{record.inviteUserSerialNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity">
    update Scrm_PersonalWx_GroupMemberInfo
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="userSerialNo != null">
        user_serial_no = #{userSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=INTEGER},
      </if>
      <if test="inviteWxId != null">
        invite_wx_id = #{inviteWxId,jdbcType=VARCHAR},
      </if>
      <if test="memberNickName != null">
        member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      </if>
      <if test="wxNickname != null">
        wx_nickname = #{wxNickname,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        member_type = #{memberType,jdbcType=TINYINT},
      </if>
      <if test="groupRemark != null">
        group_remark = #{groupRemark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
      <if test="enterTime != null">
        enter_time = #{enterTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="timeTrace != null">
        time_trace = #{timeTrace,jdbcType=BIGINT},
      </if>
      <if test="exitTime != null">
        exit_time = #{exitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inviteUserSerialNo != null">
        invite_user_serial_no = #{inviteUserSerialNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity">
    update Scrm_PersonalWx_GroupMemberInfo
    set app_id = #{appId,jdbcType=VARCHAR},
      wx_id = #{wxId,jdbcType=VARCHAR},
      user_serial_no = #{userSerialNo,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=VARCHAR},
      join_type = #{joinType,jdbcType=INTEGER},
      invite_wx_id = #{inviteWxId,jdbcType=VARCHAR},
      member_nick_name = #{memberNickName,jdbcType=VARCHAR},
      wx_nickname = #{wxNickname,jdbcType=VARCHAR},
      avatar = #{avatar,jdbcType=VARCHAR},
      member_type = #{memberType,jdbcType=TINYINT},
      group_remark = #{groupRemark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      state = #{state,jdbcType=VARCHAR},
      enter_time = #{enterTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      time_trace = #{timeTrace,jdbcType=BIGINT},
      exit_time = #{exitTime,jdbcType=TIMESTAMP},
      invite_user_serial_no = #{inviteUserSerialNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_GroupMemberInfo
    (app_id, wx_id, user_serial_no, union_id, user_id, group_id, join_type, invite_wx_id, 
      member_nick_name, wx_nickname, avatar, member_type, group_remark, status, state, 
      enter_time, deleted, add_time, update_time, time_trace, exit_time, invite_user_serial_no
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.wxId,jdbcType=VARCHAR}, #{item.userSerialNo,jdbcType=VARCHAR}, 
        #{item.unionId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, #{item.groupId,jdbcType=VARCHAR}, 
        #{item.joinType,jdbcType=INTEGER}, #{item.inviteWxId,jdbcType=VARCHAR}, #{item.memberNickName,jdbcType=VARCHAR}, 
        #{item.wxNickname,jdbcType=VARCHAR}, #{item.avatar,jdbcType=VARCHAR}, #{item.memberType,jdbcType=TINYINT}, 
        #{item.groupRemark,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.state,jdbcType=VARCHAR}, 
        #{item.enterTime,jdbcType=TIMESTAMP}, #{item.deleted,jdbcType=BIT}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.timeTrace,jdbcType=BIGINT}, #{item.exitTime,jdbcType=TIMESTAMP}, 
        #{item.inviteUserSerialNo,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>