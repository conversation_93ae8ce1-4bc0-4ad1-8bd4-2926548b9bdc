package com.sankuai.scrm.core.service.abtest.strategy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.scrm.core.service.abtest.config.ABTestConfig;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;

import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LossGroupStrategyExecuteTest {

    @Mock
    private ABTestConfig abTestConfig;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Captor
    private ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> dtoCaptor;

    @InjectMocks
    private TestLossGroupStrategy lossGroupStrategy;

    private ABTestContext context;

    private IntelligentFollowActionTrackDTO trackDTO;

    // Create a testable subclass that overrides the abstract method
    static class TestLossGroupStrategy extends LossGroupStrategy {

        @Override
        public void fillABTestRecordMessageDTO(ABTestContext context, GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO) {
            // Do nothing in test implementation
        }

        @Override
        public String getName() {
            return "LossGroup";
        }
    }

    @BeforeEach
    public void setUp() {
        // Create a test context
        context = new ABTestContext();
        context.setAppId("testAppId");
        context.setUserId(123456L);
        context.setRandomValue(50);
        context.setStrategyName("testStrategy");
        context.setRandomValueStart(0);
        context.setRandomValueEnd(100);
        context.setUniqueName("testUniqueName");
        context.setAbTestStatus(1);
        // Create a test trackDTO
        trackDTO = new IntelligentFollowActionTrackDTO();
        trackDTO.setAppId("testAppId");
        trackDTO.setUserId(123456L);
    }

    /**
     * Test execute method when app ID is in the configured app IDs list
     */
    @Test
    public void testExecuteWhenAppIdIsInConfiguredList() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(Arrays.asList("testAppId", "otherAppId"));
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        ABTestContext result = lossGroupStrategy.execute(trackDTO, context);
        // assert
        verify(testRecordMessageProducer).sendMessage(dtoCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO capturedDto = dtoCaptor.getValue();
        assertEquals("testAppId", capturedDto.getAppId());
        assertEquals(123456L, capturedDto.getMtUserId());
        assertEquals("v1.0", capturedDto.getTestVersion());
        assertEquals("testStrategy", capturedDto.getStrategyName());
        assertEquals(50, capturedDto.getRandomValue());
        assertEquals(0, capturedDto.getRandomValueStart());
        assertEquals(100, capturedDto.getRandomValueEnd());
        assertEquals("testUniqueName", capturedDto.getUniqueName());
        assertEquals(1, capturedDto.getStatus());
        assertEquals(0, capturedDto.getActionType());
        assertSame(context, result);
    }

    /**
     * Test execute method when app ID is not in the configured app IDs list
     */
    @Test
    public void testExecuteWhenAppIdIsNotInConfiguredList() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(Arrays.asList("otherAppId1", "otherAppId2"));
        // act
        ABTestContext result = lossGroupStrategy.execute(trackDTO, context);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        assertSame(context, result);
    }

    /**
     * Test execute method when app IDs list is empty
     */
    @Test
    public void testExecuteWhenAppIdsListIsEmpty() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(Collections.emptyList());
        // act
        ABTestContext result = lossGroupStrategy.execute(trackDTO, context);
        // assert
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        assertSame(context, result);
    }

    /**
     * Test execute method when app IDs list is null
     */
    @Test
    public void testExecuteWhenAppIdsListIsNull() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            lossGroupStrategy.execute(trackDTO, context);
        });
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test execute method with null context
     */
    @Test
    public void testExecuteWithNullContext() throws Throwable {
        // arrange
        context = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            lossGroupStrategy.execute(trackDTO, context);
        });
        verify(testRecordMessageProducer, never()).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    /**
     * Test execute method with null trackDTO
     */
    @Test
    public void testExecuteWithNullTrackDTO() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(Arrays.asList("testAppId"));
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        // act
        ABTestContext result = lossGroupStrategy.execute(null, context);
        // assert
        verify(testRecordMessageProducer).sendMessage(dtoCaptor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO capturedDto = dtoCaptor.getValue();
        assertEquals("v1.0", capturedDto.getTestVersion());
        assertSame(context, result);
    }

    /**
     * Test execute method when testRecordMessageProducer throws an exception
     */
    @Test
    public void testExecuteWhenProducerThrowsException() throws Throwable {
        // arrange
        when(abTestConfig.getAppIds()).thenReturn(Arrays.asList("testAppId"));
        when(abTestConfig.getTestVersion("testAppId")).thenReturn("v1.0");
        doThrow(new RuntimeException("Test exception")).when(testRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            lossGroupStrategy.execute(trackDTO, context);
        });
        verify(testRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }
}
