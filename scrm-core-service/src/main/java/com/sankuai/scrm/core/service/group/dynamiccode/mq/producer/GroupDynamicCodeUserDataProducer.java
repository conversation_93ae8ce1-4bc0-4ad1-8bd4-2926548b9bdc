package com.sankuai.scrm.core.service.group.dynamiccode.mq.producer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.group.dynamiccode.mq.entity.GroupDynamicCodeUserEntity;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 企微社群群活码相关延迟任务的 producer
 */
@Slf4j
@Component
public class GroupDynamicCodeUserDataProducer implements InitializingBean {

    private IProducerProcessor<?, String> producer;

    public void sendMsg(GroupDynamicCodeUserEntity eventMsg) {
        if (eventMsg == null) {
            return;
        }
        try {
            ProducerResult result = producer.sendMessage(JsonUtils.toStr(eventMsg));
            if (result == null || !(ProducerStatus.SEND_OK == result.getProducerStatus())) {
                log.error("GroupDynamicCodeUserDataProducer group member change event msg send fail, eventMsg:{}", eventMsg);
            }
        } catch (Exception e) {
            log.error("GroupDynamicCodeUserDataProducer group member change event msg send fail, eventMsg:{}", eventMsg, e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildProduceFactory(properties, "scrm.group.dynamic.code.user.msg");
    }
}
