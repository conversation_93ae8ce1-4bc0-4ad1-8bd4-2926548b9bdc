package com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeAndGroupRelation;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeAndGroupRelationExample;

public interface GroupDynamicCodeAndGroupRelationMapper extends MybatisBaseMapper<GroupDynamicCodeAndGroupRelation, GroupDynamicCodeAndGroupRelationExample, Long> {
}