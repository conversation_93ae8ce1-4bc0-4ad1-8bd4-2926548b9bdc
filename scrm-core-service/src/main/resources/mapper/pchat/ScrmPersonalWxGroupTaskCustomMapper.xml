<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupTaskCustomMapper">
    <update id="incrSuccessCount">
        update Scrm_PersonalWx_GroupTask set current_success_count=current_success_count+1
        ,update_time=current_timestamp where id=#{id} and
        current_success_count&lt;group_count
    </update>
    <update id="incrCreatedGroupCount">
        update Scrm_PersonalWx_GroupTask set created_group_count=created_group_count+1
        ,update_time=current_timestamp where id=#{id}
    </update>
    <update id="execSuccess">
        update Scrm_PersonalWx_GroupTask set execute_result='1' ,update_time=current_timestamp where id=#{id} and
        current_success_count>=group_count
    </update>
    <update id="incrFailureCount">
        update Scrm_PersonalWx_GroupTask set current_failure_count=current_failure_count+1
        ,update_time=current_timestamp where id=#{id} and
        current_failure_count&lt;group_count
    </update>
    <update id="execFailure">
        update Scrm_PersonalWx_GroupTask set execute_result='4' ,update_time=current_timestamp where id=#{id} and
        current_failure_count=group_count
    </update>
    <update id="execPartFailure">
        update Scrm_PersonalWx_GroupTask set execute_result='3' ,update_time=current_timestamp where id=#{id} and
        and current_failure_count>0 and current_success_count>0
    </update>
</mapper>