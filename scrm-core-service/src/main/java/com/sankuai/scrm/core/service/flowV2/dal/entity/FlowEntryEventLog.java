package com.sankuai.scrm.core.service.flowV2.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: flow_entry_event_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FlowEntryEventLog {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: entry_type
     *   说明: 入口类型
     */
    private Integer entryType;

    /**
     *   字段: event_type
     *   说明: 事件类型
     */
    private Integer eventType;

    /**
     *   字段: mt_user_id
     *   说明: 美团userId
     */
    private Long mtUserId;

    /**
     *   字段: union_id
     *   说明: 微信unionId
     */
    private String unionId;

    /**
     *   字段: code_id
     *   说明: 码id
     */
    private Long codeId;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}