package com.sankuai.scrm.core.service.pchat.service.live;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmLiveInfoDomainService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmLiveManageServiceImplQueryLiveWxTypeTest {

    @InjectMocks
    private ScrmLiveManageServiceImpl scrmLiveManageService;

    @Mock
    private ScrmLiveInfoDomainService scrmLiveInfoDomainService;

    private MockedStatic<CheckPermissionUtil> checkPermissionUtilMockedStatic;

    @BeforeEach
    public void setUp() {
        checkPermissionUtilMockedStatic = Mockito.mockStatic(CheckPermissionUtil.class);
        // Default behavior - do nothing when permission check is called
        checkPermissionUtilMockedStatic.when(() -> CheckPermissionUtil.checkUserPermission(anyString(), any(AuthorityCodeEnum.class))).thenAnswer(invocation -> null);
    }

    @AfterEach
    public void tearDown() {
        checkPermissionUtilMockedStatic.close();
    }

    /**
     * 测试直播ID为空的情况
     */
    @Test
    public void testQueryLiveWxTypeLiveIdIsEmpty() throws Throwable {
        // arrange
        String liveId = "";
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("直播id为空", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试直播信息为null的情况
     */
    @Test
    public void testQueryLiveWxTypeLiveInfoIsNull() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        when(scrmLiveInfoDomainService.queryLiveInfo(liveId)).thenReturn(null);
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试直播信息中wxType为null的情况
     */
    @Test
    public void testQueryLiveWxTypeWxTypeIsNull() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        ScrmLiveInfo liveInfo = new ScrmLiveInfo();
        liveInfo.setWxType(null);
        when(scrmLiveInfoDomainService.queryLiveInfo(liveId)).thenReturn(liveInfo);
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试wxType无法转换为WeChatType枚举的情况
     */
    @Test
    public void testQueryLiveWxTypeInvalidWxTypeCode() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        ScrmLiveInfo liveInfo = new ScrmLiveInfo();
        // 不存在的类型代码
        liveInfo.setWxType(999);
        when(scrmLiveInfoDomainService.queryLiveInfo(liveId)).thenReturn(liveInfo);
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试正常获取个人微信类型的情况
     */
    @Test
    public void testQueryLiveWxTypePersonalWechat() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        ScrmLiveInfo liveInfo = new ScrmLiveInfo();
        // PERSONAL_WECHAT
        liveInfo.setWxType(1);
        when(scrmLiveInfoDomainService.queryLiveInfo(liveId)).thenReturn(liveInfo);
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMsg());
        assertEquals(Integer.valueOf(1), response.getData());
    }

    /**
     * 测试正常获取企业微信类型的情况
     */
    @Test
    public void testQueryLiveWxTypeEnterpriseWechat() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        ScrmLiveInfo liveInfo = new ScrmLiveInfo();
        // ENTERPRISE_WECHAT
        liveInfo.setWxType(2);
        when(scrmLiveInfoDomainService.queryLiveInfo(liveId)).thenReturn(liveInfo);
        // act
        RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMsg());
        assertEquals(Integer.valueOf(2), response.getData());
    }


}
