package com.sankuai.scrm.core.service.pchat.service.activity;

import com.sankuai.dz.srcm.pchat.request.activity.ActivityCreateRequest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivity;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityGroupMapping;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxInviteRelationDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.runner.RunWith;
import java.lang.reflect.Method;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PersonalActivityServiceImplSaveActivityGroupMappingTest {

    @InjectMocks
    private PersonalActivityServiceImpl personalActivityService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private PersonalActivityCommonService personalActivityCommonService;

    @Mock
    private ScrmPersonalWxInviteRelationDomainService scrmPersonalWxInviteRelationDomainService;

    private Method saveActivityGroupMappingMethod;

    private Method saveActivityGroupMappingWithListMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        saveActivityGroupMappingMethod = PersonalActivityServiceImpl.class.getDeclaredMethod("saveActivityGroupMapping", ActivityCreateRequest.class, String.class, ScrmPersonalWxActivity.class);
        saveActivityGroupMappingMethod.setAccessible(true);
        saveActivityGroupMappingWithListMethod = PersonalActivityServiceImpl.class.getDeclaredMethod("saveActivityGroupMapping", String.class, ScrmPersonalWxActivity.class, List.class);
        saveActivityGroupMappingWithListMethod.setAccessible(true);
    }

    /**
     * 测试场景：request.getMatchAllGroup() 为 true，且 queryGroupInfoByWebCastId 返回空列表
     */
    @Test
    public void testSaveActivityGroupMappingMatchAllGroupTrueWithEmptyGroupList() throws Throwable {
        // arrange
        ActivityCreateRequest request = new ActivityCreateRequest();
        request.setMatchAllGroup(true);
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setAppId("appId");
        activity.setProjectId("projectId");
        activity.setId(1L);
        String creator = "creator";
        when(groupManageDomainService.queryGroupInfoByWebCastId(activity.getAppId(), activity.getProjectId())).thenReturn(Collections.emptyList());
        // act
        saveActivityGroupMappingMethod.invoke(personalActivityService, request, creator, activity);
        // assert
        verify(groupManageDomainService).queryGroupInfoByWebCastId(activity.getAppId(), activity.getProjectId());
        verify(personalActivityCommonService, never()).buildActivityGroupMapping(any(), any(), any());
        verify(scrmPersonalWxInviteRelationDomainService, never()).bindActivityGroupsInviteRelationByMappingList(anyLong(), anyList());
    }

    /**
     * 测试场景：request.getMatchAllGroup() 为 true，且 queryGroupInfoByWebCastId 抛出异常
     */
    @Test(expected = Exception.class)
    public void testSaveActivityGroupMappingMatchAllGroupTrueWithException() throws Throwable {
        // arrange
        ActivityCreateRequest request = new ActivityCreateRequest();
        request.setMatchAllGroup(true);
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setAppId("appId");
        activity.setProjectId("projectId");
        activity.setId(1L);
        String creator = "creator";
        when(groupManageDomainService.queryGroupInfoByWebCastId(activity.getAppId(), activity.getProjectId())).thenThrow(new RuntimeException("Test Exception"));
        // act
        saveActivityGroupMappingMethod.invoke(personalActivityService, request, creator, activity);
    }

    /**
     * 测试场景：request.getMatchAllGroup() 为 false，且 request.getGroupIds() 为空
     */
    @Test
    public void testSaveActivityGroupMappingMatchAllGroupFalseWithEmptyGroupIds() throws Throwable {
        // arrange
        ActivityCreateRequest request = new ActivityCreateRequest();
        request.setMatchAllGroup(false);
        request.setGroupIds(Collections.emptyList());
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setId(1L);
        String creator = "creator";
        // act
        saveActivityGroupMappingMethod.invoke(personalActivityService, request, creator, activity);
        // assert
        verify(groupManageDomainService, never()).queryGroupByIds(any());
        verify(personalActivityCommonService, never()).buildActivityGroupMapping(any(), any(), any());
        verify(scrmPersonalWxInviteRelationDomainService, never()).bindActivityGroupsInviteRelationByMappingList(anyLong(), anyList());
    }

    /**
     * 测试场景：request.getMatchAllGroup() 为 false，且 request.getGroupIds() 非空，queryGroupByIds 返回空列表
     */
    @Test
    public void testSaveActivityGroupMappingMatchAllGroupFalseWithNonEmptyGroupIdsAndEmptyGroupList() throws Throwable {
        // arrange
        ActivityCreateRequest request = new ActivityCreateRequest();
        request.setMatchAllGroup(false);
        request.setGroupIds(Collections.singletonList(1L));
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setId(1L);
        String creator = "creator";
        when(groupManageDomainService.queryGroupByIds(request.getGroupIds())).thenReturn(Collections.emptyList());
        // act
        saveActivityGroupMappingMethod.invoke(personalActivityService, request, creator, activity);
        // assert
        verify(groupManageDomainService).queryGroupByIds(request.getGroupIds());
        verify(personalActivityCommonService, never()).buildActivityGroupMapping(any(), any(), any());
        verify(scrmPersonalWxInviteRelationDomainService, never()).bindActivityGroupsInviteRelationByMappingList(anyLong(), anyList());
    }

    /**
     * 测试场景：request.getMatchAllGroup() 为 false，且 request.getGroupIds() 非空，queryGroupByIds 抛出异常
     */
    @Test(expected = Exception.class)
    public void testSaveActivityGroupMappingMatchAllGroupFalseWithNonEmptyGroupIdsAndException() throws Throwable {
        // arrange
        ActivityCreateRequest request = new ActivityCreateRequest();
        request.setMatchAllGroup(false);
        request.setGroupIds(Collections.singletonList(1L));
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setId(1L);
        String creator = "creator";
        when(groupManageDomainService.queryGroupByIds(request.getGroupIds())).thenThrow(new RuntimeException("Test Exception"));
        // act
        saveActivityGroupMappingMethod.invoke(personalActivityService, request, creator, activity);
    }
}
