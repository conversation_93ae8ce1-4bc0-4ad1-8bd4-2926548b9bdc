package com.sankuai.dz.srcm.automatedmanagement.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/12/4
 */
@Data
public class CheckRealTimeTaskTimeWindowRequest implements Serializable {
    /**
     * 流程编排id 不填则判断为新建场景，有则判断为更新场景
     */
    private Long processOrchestrationId;

    /**
     * 实时场景id
     */
    private Long realTimeSceneId;

    /**
     *   字段: begin_time
     *   说明: 任务有效期开始时间 YYYY-MM-dd HH:mm:ss
     */
    private String beginTime;

    /**
     *   字段: end_time
     *   说明: 任务有效期结束时间 YYYY-MM-dd HH:mm:ss
     */
    private String endTime;
}
