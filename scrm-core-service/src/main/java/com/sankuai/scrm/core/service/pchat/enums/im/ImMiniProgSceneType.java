package com.sankuai.scrm.core.service.pchat.enums.im;

import lombok.Getter;

/**
 * @Description im 连接类型
 * <AUTHOR>
 * @Create On 2024/1/16 14:29
 * @Version v1.0.0
 */
@Getter
public enum ImMiniProgSceneType {

    ALL("0", "all"),
    IM("1", "im"),
    GROUP("2", "群发消息"),

    ;

    private final String code;

    private final String desc;

    ImMiniProgSceneType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImMiniProgSceneType fromCode(String code) {
        for (ImMiniProgSceneType enumValue : ImMiniProgSceneType.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }
}
