package com.sankuai.scrm.core.service.pchat.dal.example;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxRobotLoginLogExampleCreateCriteriaTest {

    private ScrmPersonalWxRobotLoginLogExample scrmPersonalWxRobotLoginLogExample;

    @Before
    public void setUp() {
        scrmPersonalWxRobotLoginLogExample = new ScrmPersonalWxRobotLoginLogExample();
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmPersonalWxRobotLoginLogExample.Criteria criteria = scrmPersonalWxRobotLoginLogExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmPersonalWxRobotLoginLogExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmPersonalWxRobotLoginLogExample.Criteria criteria1 = scrmPersonalWxRobotLoginLogExample.createCriteria();
        ScrmPersonalWxRobotLoginLogExample.Criteria criteria2 = scrmPersonalWxRobotLoginLogExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmPersonalWxRobotLoginLogExample.getOredCriteria().size());
    }
}
