package com.sankuai.scrm.core.service.user.predict.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.user.dto.UserHesitationResult;
import com.sankuai.scrm.core.service.user.dal.entity.userView.BusinessScoreConfig;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserIntendVisitView;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserTradeView;
import com.sankuai.scrm.core.service.user.util.BusinessScoreConfigUtil;
import com.sankuai.scrm.core.service.user.util.UserDataFetchUtil;
import com.sankuai.scrm.core.service.user.util.UserDataFetchUtil.UserDataResult;
import java.math.BigDecimal;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UserHesitationAnalysisServiceImplAnalyzeUserHesitationTest {

    @Mock
    private UserDataFetchUtil userDataFetchUtil;

    @Mock
    private BusinessScoreConfigUtil businessScoreConfigUtil;

    @InjectMocks
    private UserHesitationAnalysisServiceImpl service;

    private UserTradeView mockTradeView;

    private UserIntendVisitView mockVisitView;

    private UserSearchView mockSearchView;

    private BusinessScoreConfig mockConfig;

    private BusinessScoreConfig.HesitationConfig mockHesitationConfig;

    private UserDataResult mockUserDataResult;

    @BeforeEach
    public void setUp() {
        // Initialize common test objects
        mockTradeView = new UserTradeView();
        mockVisitView = new UserIntendVisitView();
        mockSearchView = new UserSearchView();
        mockConfig = new BusinessScoreConfig();
        mockHesitationConfig = new BusinessScoreConfig.HesitationConfig();
        mockConfig.setHesitationConfig(mockHesitationConfig);
        mockUserDataResult = new UserDataResult();
        mockUserDataResult.setSuccess(true);
        mockUserDataResult.setTradeData(mockTradeView);
        mockUserDataResult.setVisitData(mockVisitView);
        mockUserDataResult.setSearchData(mockSearchView);
    }

    /**
     * Test case for null userPk parameter
     */
    @Test
    public void testAnalyzeUserHesitation_NullUserPk() throws Throwable {
        // arrange
        String userPk = null;
        String appId = "app123";
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty userPk parameter
     */
    @Test
    public void testAnalyzeUserHesitation_EmptyUserPk() throws Throwable {
        // arrange
        String userPk = "";
        String appId = "app123";
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for blank userPk parameter
     */
    @Test
    public void testAnalyzeUserHesitation_BlankUserPk() throws Throwable {
        // arrange
        String userPk = "   ";
        String appId = "app123";
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for failed user data fetch
     */
    @Test
    public void testAnalyzeUserHesitation_FailedUserDataFetch() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        UserDataResult failedResult = new UserDataResult();
        failedResult.setSuccess(false);
        failedResult.setErrorMessage("Failed to fetch user data");
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(failedResult);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
        verify(userDataFetchUtil).fetchUserData(userPk, appId);
    }

    /**
     * Test case for null business config
     */
    @Test
    public void testAnalyzeUserHesitation_NullBusinessConfig() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(null);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
        verify(userDataFetchUtil).fetchUserData(userPk, appId);
        verify(businessScoreConfigUtil).getBusinessConfig(appId);
    }

    /**
     * Test case for null hesitation config
     */
    @Test
    public void testAnalyzeUserHesitation_NullHesitationConfig() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        BusinessScoreConfig configWithNullHesitation = new BusinessScoreConfig();
        configWithNullHesitation.setHesitationConfig(null);
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(configWithNullHesitation);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
        verify(userDataFetchUtil).fetchUserData(userPk, appId);
        verify(businessScoreConfigUtil).getBusinessConfig(appId);
    }

    /**
     * Test case for exception during processing
     */
    @Test
    public void testAnalyzeUserHesitation_ExceptionDuringProcessing() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenThrow(new RuntimeException("Test exception"));
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNull(result);
        verify(userDataFetchUtil).fetchUserData(userPk, appId);
    }

    /**
     * Test case for successful analysis with default weights
     */
    @Test
    public void testAnalyzeUserHesitation_SuccessWithDefaultWeights() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data
        mockTradeView.setTrade7dCnt(0L);
        mockTradeView.setLast2tdBuySucDays(10);
        // Set up visit data
        mockVisitView.setVisit7dPv(5L);
        mockVisitView.setLast2tdVisitDays(5);
        // Set up search data - assuming calculateRepeatSearch returns 1
        mockSearchView.setSearchKeywordCnt7d(10L);
        // Set up hesitation config with null weights (to use defaults)
        mockHesitationConfig.setNoRecentPurchaseWeight(null);
        mockHesitationConfig.setRepeatSearchWeight(null);
        mockHesitationConfig.setBrowseNoBuyWeight(null);
        mockHesitationConfig.setPriceHesitationWeight(null);
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // Since we can't mock private methods directly with Mockito, we'll test the actual implementation
        // This assumes calculateRepeatSearch and calculatePriceHesitation work as expected
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertEquals(1, result.getNoRecentPurchase());
        assertEquals(1, result.getBrowseNoBuy());
        // We can't assert on repeatSearch and priceHesitation as they depend on private methods
        // But we can verify the total score is calculated
        assertNotNull(result.getTotalHesitationScore());
    }

    /**
     * Test case for successful analysis with custom weights
     */
    @Test
    public void testAnalyzeUserHesitation_SuccessWithCustomWeights() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data
        mockTradeView.setTrade7dCnt(0L);
        mockTradeView.setLast2tdBuySucDays(10);
        // Set up visit data
        mockVisitView.setVisit7dPv(5L);
        mockVisitView.setLast2tdVisitDays(5);
        // Set up search data - assuming calculateRepeatSearch returns 1
        mockSearchView.setSearchKeywordCnt7d(10L);
        // Set up hesitation config with custom weights
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.4"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.3"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.2"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.1"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertEquals(1, result.getNoRecentPurchase());
        assertEquals(1, result.getBrowseNoBuy());
        // We can't assert on repeatSearch and priceHesitation as they depend on private methods
        // But we can verify the total score is calculated
        assertNotNull(result.getTotalHesitationScore());
    }

    /**
     * Test case for no recent purchase calculation - positive case
     */
    @Test
    public void testAnalyzeUserHesitation_NoRecentPurchasePositive() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data - no recent purchases
        mockTradeView.setTrade7dCnt(0L);
        // Set up visit data - has recent visits
        mockVisitView.setVisit7dPv(5L);
        mockVisitView.setLast2tdVisitDays(5);
        // Set up hesitation config
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("1.0"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getNoRecentPurchase());
        // Since all other weights are 0, the total score should equal noRecentPurchase * 1.0
        assertEquals(new BigDecimal("1.00"), result.getTotalHesitationScore());
    }

    /**
     * Test case for no recent purchase calculation - negative case
     */
    @Test
    public void testAnalyzeUserHesitation_NoRecentPurchaseNegative() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data - has recent purchases
        mockTradeView.setTrade7dCnt(3L);
        // Set up visit data - has recent visits
        mockVisitView.setVisit7dPv(5L);
        // Set up hesitation config
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("1.0"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getNoRecentPurchase());
        // Since all other weights are 0, the total score should be 0
        assertEquals(new BigDecimal("0.00"), result.getTotalHesitationScore());
    }

    /**
     * Test case for browse no buy calculation - positive case
     */
    @Test
    public void testAnalyzeUserHesitation_BrowseNoBuyPositive() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data - last purchase was a long time ago
        mockTradeView.setLast2tdBuySucDays(20);
        // Set up visit data - recent visit
        mockVisitView.setLast2tdVisitDays(5);
        // Set up hesitation config
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("1.0"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getBrowseNoBuy());
        // Since all other weights are 0, the total score should equal browseNoBuy * 1.0
        assertEquals(new BigDecimal("1.00"), result.getTotalHesitationScore());
    }

    /**
     * Test case for browse no buy calculation - negative case
     */
    @Test
    public void testAnalyzeUserHesitation_BrowseNoBuyNegative() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data - recent purchase
        mockTradeView.setLast2tdBuySucDays(3);
        // Set up visit data - last visit was before last purchase
        mockVisitView.setLast2tdVisitDays(5);
        // Set up hesitation config
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("1.0"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getBrowseNoBuy());
        // Since all other weights are 0, the total score should be 0
        assertEquals(new BigDecimal("0.00"), result.getTotalHesitationScore());
    }

    /**
     * Test case for null trade data and visit data
     */
    @Test
    public void testAnalyzeUserHesitation_NullTradeAndVisitData() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up null trade and visit data
        mockUserDataResult.setTradeData(null);
        mockUserDataResult.setVisitData(null);
        // Set up hesitation config
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.5"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.0"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.5"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getNoRecentPurchase());
        assertEquals(0, result.getBrowseNoBuy());
        // Since both features should return 0, the total score should be 0
        assertEquals(new BigDecimal("0.00"), result.getTotalHesitationScore());
    }

    /**
     * Test case for all features positive
     */
    @Test
    public void testAnalyzeUserHesitation_AllFeaturesPositive() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data
        mockTradeView.setTrade7dCnt(0L);
        mockTradeView.setLast2tdBuySucDays(20);
        // Set up visit data
        mockVisitView.setVisit7dPv(5L);
        mockVisitView.setLast2tdVisitDays(5);
        // Set up search data
        mockSearchView.setSearchKeywordCnt7d(10L);
        // Set up hesitation config with equal weights
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.25"));
        // Set up other config values that might be needed for private methods
        mockHesitationConfig.setRepeatSearchMultiplier(new BigDecimal("1.0"));
        mockHesitationConfig.setPriceHesitationTradePeriod(30);
        mockHesitationConfig.setPriceHesitationMultiplier(new BigDecimal("1.0"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getNoRecentPurchase());
        assertEquals(1, result.getBrowseNoBuy());
        // We can't assert on repeatSearch and priceHesitation as they depend on private methods
        // But we can verify the total score is calculated
        assertNotNull(result.getTotalHesitationScore());
    }

    /**
     * Test case for all features negative
     */
    @Test
    public void testAnalyzeUserHesitation_AllFeaturesNegative() throws Throwable {
        // arrange
        String userPk = "dp123";
        String appId = "app123";
        // Set up trade data
        mockTradeView.setTrade7dCnt(3L);
        mockTradeView.setLast2tdBuySucDays(3);
        // Set up visit data
        mockVisitView.setVisit7dPv(0L);
        mockVisitView.setLast2tdVisitDays(5);
        // Set up search data
        mockSearchView.setSearchKeywordCnt7d(0L);
        // Set up hesitation config with equal weights
        mockHesitationConfig.setNoRecentPurchaseWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setRepeatSearchWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setBrowseNoBuyWeight(new BigDecimal("0.25"));
        mockHesitationConfig.setPriceHesitationWeight(new BigDecimal("0.25"));
        when(userDataFetchUtil.fetchUserData(userPk, appId)).thenReturn(mockUserDataResult);
        when(businessScoreConfigUtil.getBusinessConfig(appId)).thenReturn(mockConfig);
        // act
        UserHesitationResult result = service.analyzeUserHesitation(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getNoRecentPurchase());
        assertEquals(0, result.getBrowseNoBuy());
        // We can't assert on repeatSearch and priceHesitation as they depend on private methods
        // But we can verify the total score is calculated
        assertNotNull(result.getTotalHesitationScore());
    }
}
