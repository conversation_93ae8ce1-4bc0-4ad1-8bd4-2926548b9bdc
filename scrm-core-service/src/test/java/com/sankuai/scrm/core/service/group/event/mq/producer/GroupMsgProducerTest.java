package com.sankuai.scrm.core.service.group.event.mq.producer;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupMsgProducerTest {

    private GroupMsgProducer groupMsgProducer;

    @Before
    public void setUp() {
        groupMsgProducer = new GroupMsgProducer();
    }

    /**
     * 测试 dataMap 为 null 或者为空的情况
     */
    @Test
    public void testSupportDataMapIsNull() throws Throwable {
        assertFalse(groupMsgProducer.support(null));
    }

    /**
     * 测试 dataMap 不为空，但是 Event 字段的值不等于 "change_external_chat" 的情况
     */
    @Test
    public void testSupportDataMapIsNotEmptyButEventIsNotChangeExternalChat() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "other_event");
        assertFalse(groupMsgProducer.support(dataMap));
    }

    /**
     * 测试 dataMap 不为空，且 Event 字段的值等于 "change_external_chat" 的情况
     */
    @Test
    public void testSupportDataMapIsNotEmptyAndEventIsChangeExternalChat() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "change_external_chat");
        assertTrue(groupMsgProducer.support(dataMap));
    }
}
