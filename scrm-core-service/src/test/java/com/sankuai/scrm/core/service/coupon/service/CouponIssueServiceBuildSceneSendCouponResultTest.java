package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.scrm.core.service.coupon.dto.CouponResult;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;

@ExtendWith(MockitoExtension.class)
class CouponIssueServiceBuildSceneSendCouponResultTest {

    private final CouponIssueService couponIssueService = new CouponIssueService();

    private CouponResult invokePrivateBuildSceneSendCouponResult(SceneSendCouponResponse sendResult) throws Exception {
        CouponIssueService service = new CouponIssueService();
        Method method = CouponIssueService.class.getDeclaredMethod("buildSceneSendCouponResult", SceneSendCouponResponse.class);
        method.setAccessible(true);
        return (CouponResult) method.invoke(service, sendResult);
    }

    /**
     * Test when sendResult is null
     * Should return fail result with error code ISSUE_FAILED
     */
    @Test
    public void testBuildSceneSendCouponResult_NullSendResult() throws Throwable {
        // arrange
        SceneSendCouponResponse sendResult = null;
        // act
        CouponResult result = invokePrivateBuildSceneSendCouponResult(sendResult);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ISSUE_FAILED", result.getErrorCode());
        assertEquals("发券失败", result.getErrorMessage());
    }

    /**
     * Test when sendResult is not successful
     * Should return fail result with error code ISSUE_FAILED
     */
    @Test
    public void testBuildSceneSendCouponResult_NotSuccessful() throws Throwable {
        // arrange
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(false);
        // act
        CouponResult result = invokePrivateBuildSceneSendCouponResult(sendResult);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ISSUE_FAILED", result.getErrorCode());
        assertEquals("发券失败", result.getErrorMessage());
    }

    /**
     * Test when sendResult is successful but successList is empty
     * Should return fail result with error code ISSUE_FAILED
     */
    @Test
    public void testBuildSceneSendCouponResult_SuccessfulButEmptySuccessList() throws Throwable {
        // arrange
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        sendResult.setSuccessList(Collections.emptyList());
        // act
        CouponResult result = invokePrivateBuildSceneSendCouponResult(sendResult);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ISSUE_FAILED", result.getErrorCode());
        assertEquals("发券失败", result.getErrorMessage());
    }

    /**
     * Test when sendResult is successful with non-empty successList
     * Should return success result with coupon info list
     */
    @Test
    public void testBuildSceneSendCouponResult_SuccessfulWithCoupons() throws Throwable {
        // arrange
        NextCouponInfoDTO couponInfo = new NextCouponInfoDTO();
        couponInfo.setUnifiedCouponId("coupon1");
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        sendResult.setSuccessList(Arrays.asList(couponInfo));
        // act
        CouponResult result = invokePrivateBuildSceneSendCouponResult(sendResult);
        // assert
        assertTrue(result.isSuccess());
        assertTrue(result.isNewCouponReceived());
        assertEquals(1, result.getCouponInfoList().size());
        assertEquals("coupon1", result.getCouponInfoList().get(0).getUnifiedCouponId());
    }

    /**
     * Test when sendResult is successful with both successList and failList
     * Should return success result with only successList coupons
     */
    @Test
    public void testBuildSceneSendCouponResult_SuccessfulWithBothLists() throws Throwable {
        // arrange
        NextCouponInfoDTO successCoupon = new NextCouponInfoDTO();
        successCoupon.setUnifiedCouponId("success1");
        NextCouponInfoDTO failCoupon = new NextCouponInfoDTO();
        failCoupon.setUnifiedCouponId("fail1");
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        sendResult.setSuccessList(Arrays.asList(successCoupon));
        sendResult.setFailList(Arrays.asList(failCoupon));
        // act
        CouponResult result = invokePrivateBuildSceneSendCouponResult(sendResult);
        // assert
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCouponInfoList().size());
        assertEquals("success1", result.getCouponInfoList().get(0).getUnifiedCouponId());
    }

    @Test
    public void testNeedsDistributorCodeWhenSceneIsProcessOrchestration() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        Method method = CouponIssueService.class.getDeclaredMethod("needsDistributorCode", CouponRequestContext.class);
        method.setAccessible(true);
        // act
        boolean result = (boolean) method.invoke(couponIssueService, context);
        // assert
        assertTrue(result);
    }

    @Test
    public void testNeedsDistributorCodeWhenSceneIsNotProcessOrchestration() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        // Any other scene
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        Method method = CouponIssueService.class.getDeclaredMethod("needsDistributorCode", CouponRequestContext.class);
        method.setAccessible(true);
        // act
        boolean result = (boolean) method.invoke(couponIssueService, context);
        // assert
        assertFalse(result);
    }

    @Test
    public void testNeedsDistributorCodeWhenContextIsNull() throws Throwable {
        // arrange
        CouponRequestContext context = null;
        Method method = CouponIssueService.class.getDeclaredMethod("needsDistributorCode", CouponRequestContext.class);
        method.setAccessible(true);
        // act & assert
        assertThrows(java.lang.reflect.InvocationTargetException.class, () -> {
            method.invoke(couponIssueService, context);
        });
    }

    @Test
    public void testNeedsDistributorCodeWhenSceneIsNull() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(null);
        Method method = CouponIssueService.class.getDeclaredMethod("needsDistributorCode", CouponRequestContext.class);
        method.setAccessible(true);
        // act
        boolean result = (boolean) method.invoke(couponIssueService, context);
        // assert
        assertFalse(result);
    }
}
