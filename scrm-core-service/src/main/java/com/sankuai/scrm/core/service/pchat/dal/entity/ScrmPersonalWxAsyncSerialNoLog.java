package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_AsyncSerialNoLog
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxAsyncSerialNoLog {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: business_type
     *   说明: 个微接口业务
     */
    private String businessType;

    /**
     *   字段: serial_no
     *   说明: 流水号
     */
    private String serialNo;

    /**
     *   字段: relate_table
     *   说明: 相关表信息
     */
    private String relateTable;

    /**
     *   字段: relate_table_id
     *   说明: 相关表id
     */
    private Long relateTableId;

    /**
     *   字段: creator
     *   说明: 创建者的mis号
     */
    private String creator;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 创建时间
     */
    private Date updateTime;

    /**
     *   字段: app_id
     *   说明: app_id，用于区分业务
     */
    private String appId;

    /**
     *   字段: extra_data
     *   说明: 用于存储其他格外数据
     */
    private String extraData;

    /**
     *   字段: extra_data_type
     *   说明: 用于存储其他格外数据的数据类型
     */
    private String extraDataType;

    /**
     *   字段: execute_result
     *   说明: 执行结果 2 success 3 faliure 1 running
     */
    private Integer executeResult;

    /**
     *   字段: execute_result_extra
     *   说明: 执行信息
     */
    private String executeResultExtra;
}