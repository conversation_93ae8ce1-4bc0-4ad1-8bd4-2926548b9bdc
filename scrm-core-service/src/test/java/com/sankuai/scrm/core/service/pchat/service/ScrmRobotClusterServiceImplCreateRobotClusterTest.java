package com.sankuai.scrm.core.service.pchat.service;

import com.sankuai.scrm.core.service.pchat.service.superadmin.SuperAdminSsoService;
import com.sankuai.scrm.core.service.pchat.service.superadmin.SSOUseInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotCluster;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxRobotChooseDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxAccountClusterHandleLogDomainService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import com.sankuai.dz.srcm.pchat.request.cluster.CreateRobotClusterRequest;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRobotClusterServiceImplCreateRobotClusterTest {

    @InjectMocks
    private ScrmRobotClusterServiceImpl scrmRobotClusterService;

    @Mock
    private ScrmPersonalWxRobotChooseDomainService wxRobotChooseDomainService;

    @Mock
    private ScrmPersonalWxAccountClusterHandleLogDomainService handleLogDomainService;

    private CreateRobotClusterRequest request;

    private SSOUseInfo loginUser;

    private ScrmPersonalWxRobotCluster cluster;

    @Before
    public void setUp() {
        request = new CreateRobotClusterRequest();
        request.setClusterName("testCluster");
        loginUser = new SSOUseInfo();
        loginUser.setLogin("testUser");
        cluster = new ScrmPersonalWxRobotCluster();
        cluster.setClusterNo("testClusterNo");
    }

    @Test
    public void testCreateRobotClusterRequestIsNull() throws Throwable {
        RemoteResponse<Boolean> response = scrmRobotClusterService.createRobotCluster(null);
        assertEquals("参数不能为空", response.getMsg());
    }

    @Test
    public void testCreateRobotClusterClusterNameIsEmpty() throws Throwable {
        request.setClusterName("");
        RemoteResponse<Boolean> response = scrmRobotClusterService.createRobotCluster(request);
        assertEquals("蔟名不能为空", response.getMsg());
    }

    @Test
    public void testCreateRobotClusterClusterAlreadyExists() throws Throwable {
        when(wxRobotChooseDomainService.queryRobotClusterByClusterName(anyString())).thenReturn(cluster);
        RemoteResponse<Boolean> response = scrmRobotClusterService.createRobotCluster(request);
        assertEquals("存在同名蔟，请更换名称", response.getMsg());
    }

    @Test
    public void testCreateRobotClusterSuccess() throws Throwable {
        try (MockedStatic<SuperAdminSsoService> mockedStatic = Mockito.mockStatic(SuperAdminSsoService.class)) {
            mockedStatic.when(SuperAdminSsoService::getLoginUser).thenReturn(loginUser);
            when(wxRobotChooseDomainService.queryRobotClusterByClusterName(anyString())).thenReturn(null);
            when(wxRobotChooseDomainService.saveRobotCluster(anyString(), anyInt())).thenReturn(cluster);
            RemoteResponse<Boolean> response = scrmRobotClusterService.createRobotCluster(request);
            assertEquals("success", response.getMsg());
            verify(handleLogDomainService, times(1)).insert(eq(loginUser.getLogin()), anyString(), anyInt(), anyInt(), anyString());
        }
    }
}
