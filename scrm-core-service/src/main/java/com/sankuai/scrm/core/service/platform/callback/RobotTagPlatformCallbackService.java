package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.platform.anno.NTypePlatformCallbackMappingAnno;


/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/7/21 11:18
 * @Version v1.0.0
 */
public interface RobotTagPlatformCallbackService extends PlatformCallbackService {
    /**
     * 获取机器人的标签列表
     * @param strContext
     * @return
     */
    @NTypePlatformCallbackMappingAnno(nType = 2001, desc = "获取机器人的标签列表回调")
    RemoteResponse do2001(String strContext);
}

