package com.sankuai.scrm.core.service.platform.domain;

import com.sankuai.scrm.core.service.platform.domain.dto.WxGroupInsertContext;
import com.sankuai.scrm.core.service.platform.domain.dto.WxGroupQueryContext;
import com.sankuai.scrm.core.service.platform.domain.dto.WxGroupUpdateContext;


public interface WxGroupDomain {

     /**
      * 插入
      */
     Long insertWxGroup(WxGroupInsertContext context);


     /**
      * 更新
      */
     Boolean updateSingleWxGroup(WxGroupUpdateContext context);



     WxGroupQueryContext query(WxGroupQueryContext context);
}
