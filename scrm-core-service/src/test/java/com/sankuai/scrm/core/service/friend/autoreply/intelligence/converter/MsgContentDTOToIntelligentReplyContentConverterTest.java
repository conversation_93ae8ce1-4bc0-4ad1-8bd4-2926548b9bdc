package com.sankuai.scrm.core.service.friend.autoreply.intelligence.converter;

import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.entity.IntelligentReplyContent;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.MsgContentDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.*;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class MsgContentDTOToIntelligentReplyContentConverterTest {

    @Test
    public void testConvertMsgContentDTONull() throws Throwable {
        MsgContentDTO msgContentDTO = null;
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNull(result);
    }

    @Test
    public void testConvertMsgContentDTOContentTypeText() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
        msgContentDTO.setTextDTO(new TextDTO());
        msgContentDTO.getTextDTO().setContent("test");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.TEXT, result.getContentType());
        assertEquals("test", result.getContent());
    }

    @Test
    public void testConvertMsgContentDTOContentTypeImage() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.IMAGE);
        msgContentDTO.setImageDTO(new com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.ImageDTO());
        msgContentDTO.getImageDTO().setUrl("test");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.IMAGE, result.getContentType());
        assertEquals("test", result.getContent());
    }

    @Test
    public void testConvertMsgContentDTOContentTypeVideo() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.VIDEO);
        msgContentDTO.setVideoDTO(new com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.VideoDTO());
        msgContentDTO.getVideoDTO().setUrl("test");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.VIDEO, result.getContentType());
        assertEquals("test", result.getContent());
    }

    @Test
    public void testConvertMsgContentDTOContentTypeMiniProgram() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
        msgContentDTO.setMiniProgramDTO(new com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.MiniProgramDTO());
        msgContentDTO.getMiniProgramDTO().setOriginAppId("test");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentType());
        assertEquals("{\"originAppId\":\"test\",\"appId\":null,\"icon\":null,\"title\":null,\"description\":null,\"thumbnail\":null,\"pagePath\":null,\"lch\":null}", result.getContent());
    }

    @Test
    public void testConvertMsgContentDTOContentTypeLink() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.LINK);
        msgContentDTO.setLinkDTO(new com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.LinkDTO());
        msgContentDTO.getLinkDTO().setUrl("test");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.LINK, result.getContentType());
        assertEquals("{\"url\":\"test\",\"title\":null,\"thumbUrl\":null,\"description\":null}", result.getContent());
    }

    @Test
    public void testConvertMsgContentDTOContentTypeOther() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
        // Fix: Initialize TextDTO and set content to avoid NullPointerException
        msgContentDTO.setTextDTO(new TextDTO());
        msgContentDTO.getTextDTO().setContent("other content");
        IntelligentReplyContent result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTO);
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.TEXT, result.getContentType());
        assertEquals("other content", result.getContent());
    }

    @Test
    public void testConvertEmptyList() throws Throwable {
        List<MsgContentDTO> msgContentDTOs = Arrays.asList();
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertTextList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
        TextDTO textDTO = new TextDTO();
        textDTO.setContent("test");
        msgContentDTO.setTextDTO(textDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("test", result.get(0).getContent());
    }

    @Test
    public void testConvertImageList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.IMAGE);
        ImageDTO imageDTO = new ImageDTO();
        imageDTO.setUrl("test");
        msgContentDTO.setImageDTO(imageDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("test", result.get(0).getContent());
    }

    @Test
    public void testConvertVideoList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.VIDEO);
        VideoDTO videoDTO = new VideoDTO();
        videoDTO.setUrl("test");
        msgContentDTO.setVideoDTO(videoDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("test", result.get(0).getContent());
    }

    @Test
    public void testConvertMiniProgramList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
        MiniProgramDTO miniProgramDTO = new MiniProgramDTO();
        miniProgramDTO.setOriginAppId("test");
        msgContentDTO.setMiniProgramDTO(miniProgramDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("{\"originAppId\":\"test\",\"appId\":null,\"icon\":null,\"title\":null,\"description\":null,\"thumbnail\":null,\"pagePath\":null,\"lch\":null}", result.get(0).getContent());
    }

    @Test
    public void testConvertLinkList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.LINK);
        LinkDTO linkDTO = new LinkDTO();
        linkDTO.setUrl("test");
        msgContentDTO.setLinkDTO(linkDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("{\"url\":\"test\",\"title\":null,\"thumbUrl\":null,\"description\":null}", result.get(0).getContent());
    }

    @Test
    public void testConvertOtherList() throws Throwable {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
        TextDTO textDTO = new TextDTO();
        textDTO.setContent("test");
        msgContentDTO.setTextDTO(textDTO);
        List<MsgContentDTO> msgContentDTOs = Arrays.asList(msgContentDTO);
        List<IntelligentReplyContent> result = MsgContentDTOToIntelligentReplyContentConverter.convert(msgContentDTOs);
        assertEquals(1, result.size());
        assertEquals("test", result.get(0).getContent());
    }
}