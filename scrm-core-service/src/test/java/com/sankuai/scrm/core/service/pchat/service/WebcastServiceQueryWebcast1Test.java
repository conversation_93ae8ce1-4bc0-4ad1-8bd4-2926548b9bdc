package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.WebcastListResponse;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.LivePageData;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomsQueryRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveStatusEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmLiveInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmLiveInfoDomainService;
import com.sankuai.scrm.core.service.pchat.dto.WebcastDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WebcastServiceQueryWebcast1Test {

    @InjectMocks
    private WebcastService webcastService;

    @Mock
    private LiveRoomRpcService liveRoomRpcService;

    @Mock
    private ScrmLiveInfoDomainService scrmLiveInfoDomainService;

    private WebcastDTO dto;

    private LiveRoomInfo liveRoomInfo;

    private LivePageData<LiveRoomInfo> liveRoomInfos;

    private List<ScrmLiveInfo> scrmLiveInfos;

    @Before
    public void setUp() {
        dto = new WebcastDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        liveRoomInfo = new LiveRoomInfo();
        liveRoomInfo.setLiveId("123");
        liveRoomInfo.setTitle("Live 1");
        liveRoomInfo.setStatus(LiveStatusEnum.IN_LIVE.getValue());
        liveRoomInfos = new LivePageData<>();
        liveRoomInfos.setData(Collections.singletonList(liveRoomInfo));
        liveRoomInfos.setTotalRecord(1);
        // Assuming ScrmLiveInfo is a mockable entity or a simple DTO, we mock it here.
        ScrmLiveInfo scrmLiveInfo = mock(ScrmLiveInfo.class);
        scrmLiveInfos = Collections.singletonList(scrmLiveInfo);
    }

    @Test
    public void testQueryWebcast_Normal() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            when(liveRoomRpcService.queryLiveRooms(any(LiveRoomsQueryRequest.class))).thenReturn(liveRoomInfos);
            when(scrmLiveInfoDomainService.batchQueryLiveInfoList(anyList())).thenReturn(scrmLiveInfos);
            PageRemoteResponse<WebcastListResponse> response = webcastService.queryWebcast(dto);
            assertNotNull(response);
            assertEquals(1, response.getData().size());
            assertEquals("123", response.getData().get(0).getWebcastId());
            assertEquals(1, response.getTotalHit());
        }
    }

    @Test(expected = NullPointerException.class)
    public void testQueryWebcast_NullDto() throws Throwable {
        webcastService.queryWebcast(null);
    }

    @Test
    public void testQueryWebcast_EmptyLiveRooms() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            liveRoomInfos.setData(Collections.emptyList());
            liveRoomInfos.setTotalRecord(0);
            when(liveRoomRpcService.queryLiveRooms(any(LiveRoomsQueryRequest.class))).thenReturn(liveRoomInfos);
            PageRemoteResponse<WebcastListResponse> response = webcastService.queryWebcast(dto);
            assertNotNull(response);
            assertEquals(0, response.getData().size());
            assertEquals(0, response.getTotalHit());
        }
    }

    @Test(expected = RuntimeException.class)
    public void testQueryWebcast_Exception() throws Throwable {
        try (MockedStatic<AnchorAuthUtils> mocked = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mocked.when(AnchorAuthUtils::getMainAnchorId).thenThrow(new RuntimeException("未登录"));
            webcastService.queryWebcast(dto);
        }
    }
}
