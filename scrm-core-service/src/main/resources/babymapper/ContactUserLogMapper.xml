<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.friend.dynamiccode.dal.babymapper.ContactUserLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="ExternalUserId" jdbcType="VARCHAR" property="externalUserId" />
    <result column="UnionId" jdbcType="VARCHAR" property="unionId" />
    <result column="CorpId" jdbcType="VARCHAR" property="corpId" />
    <result column="OrgId" jdbcType="BIGINT" property="orgId" />
    <result column="StaffId" jdbcType="VARCHAR" property="staffId" />
    <result column="Source" jdbcType="VARCHAR" property="source" />
    <result column="ActionType" jdbcType="INTEGER" property="actionType" />
    <result column="ActionTime" jdbcType="TIMESTAMP" property="actionTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    Id, ExternalUserId, UnionId, CorpId, OrgId, StaffId, Source, ActionType, ActionTime
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.ContactUserLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Baby_OperatorHelper_ContactUserLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Baby_OperatorHelper_ContactUserLog
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Baby_OperatorHelper_ContactUserLog
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.ContactUserLogExample">
    delete from Baby_OperatorHelper_ContactUserLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Baby_OperatorHelper_ContactUserLog (ExternalUserId, UnionId, CorpId, 
      OrgId, StaffId, Source, 
      ActionType, ActionTime)
    values (#{externalUserId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=BIGINT}, #{staffId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{actionType,jdbcType=INTEGER}, #{actionTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Baby_OperatorHelper_ContactUserLog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="externalUserId != null">
        ExternalUserId,
      </if>
      <if test="unionId != null">
        UnionId,
      </if>
      <if test="corpId != null">
        CorpId,
      </if>
      <if test="orgId != null">
        OrgId,
      </if>
      <if test="staffId != null">
        StaffId,
      </if>
      <if test="source != null">
        Source,
      </if>
      <if test="actionType != null">
        ActionType,
      </if>
      <if test="actionTime != null">
        ActionTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.example.ContactUserLogExample" resultType="java.lang.Long">
    select count(*) from Baby_OperatorHelper_ContactUserLog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Baby_OperatorHelper_ContactUserLog
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.externalUserId != null">
        ExternalUserId = #{record.externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        UnionId = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.corpId != null">
        CorpId = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        OrgId = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.staffId != null">
        StaffId = #{record.staffId,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        Source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.actionType != null">
        ActionType = #{record.actionType,jdbcType=INTEGER},
      </if>
      <if test="record.actionTime != null">
        ActionTime = #{record.actionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Baby_OperatorHelper_ContactUserLog
    set Id = #{record.id,jdbcType=BIGINT},
      ExternalUserId = #{record.externalUserId,jdbcType=VARCHAR},
      UnionId = #{record.unionId,jdbcType=VARCHAR},
      CorpId = #{record.corpId,jdbcType=VARCHAR},
      OrgId = #{record.orgId,jdbcType=BIGINT},
      StaffId = #{record.staffId,jdbcType=VARCHAR},
      Source = #{record.source,jdbcType=VARCHAR},
      ActionType = #{record.actionType,jdbcType=INTEGER},
      ActionTime = #{record.actionTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog">
    update Baby_OperatorHelper_ContactUserLog
    <set>
      <if test="externalUserId != null">
        ExternalUserId = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        UnionId = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        CorpId = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        OrgId = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        StaffId = #{staffId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        Source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null">
        ActionType = #{actionType,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        ActionTime = #{actionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog">
    update Baby_OperatorHelper_ContactUserLog
    set ExternalUserId = #{externalUserId,jdbcType=VARCHAR},
      UnionId = #{unionId,jdbcType=VARCHAR},
      CorpId = #{corpId,jdbcType=VARCHAR},
      OrgId = #{orgId,jdbcType=BIGINT},
      StaffId = #{staffId,jdbcType=VARCHAR},
      Source = #{source,jdbcType=VARCHAR},
      ActionType = #{actionType,jdbcType=INTEGER},
      ActionTime = #{actionTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="Id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Baby_OperatorHelper_ContactUserLog
    (ExternalUserId, UnionId, CorpId, OrgId, StaffId, Source, ActionType, ActionTime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.externalUserId,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, #{item.corpId,jdbcType=VARCHAR}, 
        #{item.orgId,jdbcType=BIGINT}, #{item.staffId,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR}, 
        #{item.actionType,jdbcType=INTEGER}, #{item.actionTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>