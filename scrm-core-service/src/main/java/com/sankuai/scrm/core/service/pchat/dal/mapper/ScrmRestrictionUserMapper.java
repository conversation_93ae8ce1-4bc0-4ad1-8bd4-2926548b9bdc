package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmRestrictionUser;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmRestrictionUserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmRestrictionUserMapper extends MybatisBaseMapper<ScrmRestrictionUser, ScrmRestrictionUserExample, Long> {
    int batchInsert(@Param("list") List<ScrmRestrictionUser> list);
}