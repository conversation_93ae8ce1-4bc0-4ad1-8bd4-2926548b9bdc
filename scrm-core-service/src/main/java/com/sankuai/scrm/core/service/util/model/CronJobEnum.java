package com.sankuai.scrm.core.service.util.model;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/26
 */
public enum CronJobEnum {
    EVERY("每天", 0),
    DAY("日", 1),
    <PERSON><PERSON><PERSON>("月", 2),
    <PERSON>EE<PERSON>("周", 3),
    YEAR("年", 4),
    ;

    private final String name;

    private final Integer value;

    CronJobEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
