package com.sankuai.scrm.core.service.friend.cluecode.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.scrm.core.service.friend.cluecode.dal.entity.ClueCode;
import com.sankuai.scrm.core.service.friend.cluecode.domain.ClueCodeDomainService;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.babymapper.ContactWayInfoMapper;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactWayInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ClueCodeClearJob {

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private ClueCodeDomainService clueCodeDomainService;

    @Autowired
    private ContactWayInfoMapper contactWayInfoMapper;

    @Autowired
    private CorpWxContactAcl corpWxContactAcl;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.clue.code.clear")
    public void execute() {
        List<CorpAppConfig> configs = appConfigRepository.getConfigs();
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        configs.stream().map(CorpAppConfig::getAppId).forEach(this::clearClueCodeForAppId);
    }

    private void clearClueCodeForAppId(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        Date now = new Date();
        Date date = DateUtils.addDays(now, -3);
        int offset = 0;
        int limit = 50;
        while (true) {
            List<ClueCode> clueCodeList = clueCodeDomainService.queryClueCodeList(appId, date, offset, limit);
            if (CollectionUtils.isEmpty(clueCodeList)) {
                break;
            }
            clueCodeList.forEach(this::clearClueCode);
            offset += limit;
        }
    }

    private void clearClueCode(ClueCode clueCode) {
        if (clueCode == null || clueCode.getContactWayId() == null) {
            return;
        }
        ContactWayInfo contactWayInfo = queryContactWayInfo(clueCode.getContactWayId());
        if (contactWayInfo == null || BooleanUtils.isTrue(contactWayInfo.getIsDelete())) {
            return;
        }
        try {
            boolean result = corpWxContactAcl.deleteContactWayInfo(contactWayInfo.getCorpId(), contactWayInfo.getConfigId());
            log.error("ClueCodeClearJob.clearClueCode result={}, clueCode={}, contactWayInfo={}", result, clueCode, contactWayInfo);
            if (result) {
                deleteContactWayInfo(contactWayInfo.getId());
                clueCode.setContactWayId(null);
                clueCodeDomainService.updateClueCode(clueCode);
            }
        } catch (Exception e) {
            log.error("ClueCodeClearJob.clearClueCode exception, clueCode={}", clueCode, e);
        }
    }

    private ContactWayInfo queryContactWayInfo(Long contactWayId) {
        if (contactWayId == null) {
            return null;
        }
        return contactWayInfoMapper.selectByPrimaryKey(contactWayId);
    }

    private void deleteContactWayInfo(Long contactWayId) {
        if (contactWayId == null) {
            return;
        }
        ContactWayInfo contactWayInfo = ContactWayInfo.builder()
                .id(contactWayId)
                .isDelete(true)
                .build();
        contactWayInfoMapper.updateByPrimaryKeySelective(contactWayInfo);
    }
}
