package com.sankuai.dz.srcm.user.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/10 11:21
 * @Version v1.0.0
 */
@Data
public class CustomerHighPotentialDTO implements Serializable {
    @FieldDoc(description = "咨询师有的客资数，key咨询师id，value有的条数")
    private Map<Long,Integer> highPotentialCountMap;
}
