package com.sankuai.dz.srcm.tag.enums;

public enum SyncTypeEnum {

    UNKNOWN(0, "未知"),
    SYNC_WECHAT(1, "同步到企业微信"),
    SYNC_PRIVATE_DOMAIN(2, "同步到整体私域"),
    SYNC_POTENTIAL_CUSTOMER(3, "同步到潜客池");

    private int code;

    private String desc;

    SyncTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static SyncTypeEnum getSyncTypeEnumByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        SyncTypeEnum[] values = SyncTypeEnum.values();
        for (SyncTypeEnum value : values) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
