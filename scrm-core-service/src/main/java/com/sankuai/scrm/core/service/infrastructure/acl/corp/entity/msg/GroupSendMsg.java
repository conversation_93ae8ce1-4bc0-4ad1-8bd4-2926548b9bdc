package com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.TextVO;
import com.sankuai.scrm.core.service.infrastructure.util.JacksonSecondTimeDeserializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GroupSendMsg implements Serializable {

    @JsonProperty("msgid")
    private String msgId;

    private String creator;

    @JsonProperty("create_time")
    @JsonDeserialize(using = JacksonSecondTimeDeserializer.class)
    private Date createTime;

    @JsonProperty("create_type")
    private Integer createType;

    private TextVO text;

    private List<AttachmentVO> attachments;
}
