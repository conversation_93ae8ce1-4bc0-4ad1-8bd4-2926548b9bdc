package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.enums.PersonalMsgSendTypeEnum;
import com.sankuai.dz.srcm.pchat.request.im.ImGroupSetupRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.response.im.ImGroupSetupResponse;
import com.sankuai.dz.srcm.pchat.tanjing.GroupManageService;
import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.adapter.service.ImManageProcessor;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMsgMapper;
import com.sankuai.scrm.core.service.pchat.domain.im.PChatWxImDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalMsgTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * 测试类：TuseImManageProcessorTest
 */
@RunWith(MockitoJUnitRunner.class)
public class TuseImManageProcessorTest {

    @InjectMocks
    private TuseImManageProcessor tuseImManageProcessor;

    @Mock
    private GroupManageService groupManageService;

    @Mock
    private PChatWxImDomainService pChatWxImDomainService;

    @Mock
    private ScrmPersonalWxGroupMsgMapper personalWxGroupMsgMapper;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Mock
    private ScrmPersonalWxGroupInfoEntityMapper wxGroupInfoEntityMapper;

    @Mock
    private PchatConfig pchatConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Helper method to invoke the private method using reflection.
     */
    private ScrmPersonalWxGroupMsg invokeBuildScrmPersonalWxGroupMsg(List<GroupMsg> msgList) throws Exception {
        Method method = TuseImManageProcessor.class.getDeclaredMethod("buildScrmPersonalWxGroupMsg", List.class);
        method.setAccessible(true);
        return (ScrmPersonalWxGroupMsg) method.invoke(tuseImManageProcessor, msgList);
    }

    /**
     * 测试用例：testSetGroupNameSuccess
     * 描述：测试 setGroupName 方法在正常情况下的行为
     */
    @Test
    public void testSetGroupNameSuccess() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        String robotSerialNo = "robotSerialNo";
        String groupName = "newGroupName";
        AsyncInvokeResultDTO asyncInvokeResultDTO = spy(new AsyncInvokeResultDTO());
        when(asyncInvokeResultDTO.isSuccess()).thenReturn(true);
        when(groupManageService.modifyGroupName(ApiConstants.merchantNo, robotSerialNo, groupInfoEntity.getChatRoomWxSerialNo(), groupName)).thenReturn(asyncInvokeResultDTO);
        // act
        tuseImManageProcessor.setGroupName(groupInfoEntity, robotSerialNo, groupName);
        // assert
        verify(groupManageService).modifyGroupName(ApiConstants.merchantNo, robotSerialNo, groupInfoEntity.getChatRoomWxSerialNo(), groupName);
    }

    /**
     * 测试用例：testSetGroupNameFailure
     * 描述：测试 setGroupName 方法在失败情况下的行为
     */
    @Test(expected = RuntimeException.class)
    public void testSetGroupNameFailure() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        String robotSerialNo = "robotSerialNo";
        String groupName = "newGroupName";
        AsyncInvokeResultDTO asyncInvokeResultDTO = spy(new AsyncInvokeResultDTO());
        when(asyncInvokeResultDTO.isSuccess()).thenReturn(false);
        when(asyncInvokeResultDTO.getVcResult()).thenReturn("Operation failed");
        when(groupManageService.modifyGroupName(ApiConstants.merchantNo, robotSerialNo, groupInfoEntity.getChatRoomWxSerialNo(), groupName)).thenReturn(asyncInvokeResultDTO);
        // act & assert
        tuseImManageProcessor.setGroupName(groupInfoEntity, robotSerialNo, groupName);
    }

    /**
     * 测试用例：testSetGroupNameException
     * 描述：测试 setGroupName 方法在 groupManageService.modifyGroupName 抛出异常时的行为
     */
    @Test(expected = RuntimeException.class)
    public void testSetGroupNameException() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        String robotSerialNo = "robotSerialNo";
        String groupName = "newGroupName";
        when(groupManageService.modifyGroupName(ApiConstants.merchantNo, robotSerialNo, groupInfoEntity.getChatRoomWxSerialNo(), groupName)).thenThrow(new RuntimeException("Service Exception"));
        // act & assert
        tuseImManageProcessor.setGroupName(groupInfoEntity, robotSerialNo, groupName);
    }

    /**
     * 测试正常情况：msgList 不为空，且包含至少一个 GroupMsg 对象
     */
    @Test
    public void testBuildScrmPersonalWxGroupMsgNormalCase() throws Throwable {
        // arrange
        List<GroupMsg> msgList = new ArrayList<>();
        GroupMsg msg1 = new GroupMsg();
        msg1.setMsgType("text");
        GroupMsg msg2 = new GroupMsg();
        msg2.setMsgType("image");
        msgList.add(msg1);
        msgList.add(msg2);
        when(personalWxCommonService.getCreator()).thenReturn("creator");
        // act
        ScrmPersonalWxGroupMsg result = invokeBuildScrmPersonalWxGroupMsg(msgList);
        // assert
        assertNotNull(result);
        assertEquals("creator", result.getCreator());
        // Convert expected and actual msgTypes to sets and compare
        Set<String> expectedMsgTypes = new HashSet<>(Arrays.asList("text", "image"));
        Set<String> actualMsgTypes = new HashSet<>(Arrays.asList(result.getMsgTypes().replaceAll("[\\[\\]\\s]", "").split(",")));
        assertEquals(expectedMsgTypes, actualMsgTypes);
        assertEquals(JSONObject.toJSONString(msgList), result.getContent());
        assertEquals(new Date().getTime(), result.getAddTime().getTime(), 1000);
        // Use the static field directly
        assertEquals(PchatConfig.AppID, result.getAppId());
        assertEquals(PersonalMsgSendTypeEnum.IMMEDIATELY.getCode(), result.getSendType());
    }
}
