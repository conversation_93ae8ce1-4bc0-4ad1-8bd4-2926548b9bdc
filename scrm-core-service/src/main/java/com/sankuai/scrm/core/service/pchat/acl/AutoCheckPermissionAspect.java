package com.sankuai.scrm.core.service.pchat.acl;

import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.exceptions.LiveBusinessException;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.LiveBusinessErrorEnum;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * the permission function copied from <com.sankuai.dzrtc.dzrtc.privatelive.biz> ,thanks the group of qianhao
 * <p>
 * AutoCheckPermissionAspect -> CheckPermissionDelegate -> AuthorizationCheckHandler  __|
 * AuthorizationCheckSpi-> implement class
 * @create 2024/8/21
 */
@Slf4j
@Aspect
@Order(1)
@Component
public class AutoCheckPermissionAspect {

    @Resource
    private CheckPermissionDelegate checkPermissionDelegate;

    @Before("@annotation(com.sankuai.scrm.core.service.pchat.acl.AutoCheckPermission)")
    public void checkPermission(JoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        AutoCheckPermission annotation = method.getAnnotation(AutoCheckPermission.class);
        if (annotation == null) {
            throw new LiveBusinessException(LiveBusinessErrorEnum.PARAM_ERROR);
        }
        AuthorityCodeEnum authorityCodeEnum = annotation.authorityCodeEnum();
        String liveId = null;
        if (annotation.needCheckLiveId()) {
            liveId = getLiveId(annotation.LiveIdParamName(), methodSignature.getParameterNames(), joinPoint.getArgs());
        }
        boolean hasPermission = checkPermissionDelegate.checkUserPermission(liveId, authorityCodeEnum);
        if (!hasPermission) {
            throw new LiveBusinessException("当前账号无权限执行该操作");
        }
    }

    private String getLiveId(String liveIdParamName, String[] parameterNames, Object[] parameterValues) {
        if (StringUtils.isBlank(liveIdParamName)) {
            return null;
        }
        Object liveIdValue = null;
        //若是参数方法中为liveId的，则直接取
        boolean hasLiveIdParam = false;
        for (int i = 0; i < parameterNames.length; i++) {
            if (liveIdParamName.equals(parameterNames[i])) {
                liveIdValue = parameterValues[i];
                hasLiveIdParam = true;
                break;
            }
        }
        if (liveIdValue != null) {
            return liveIdValue.toString();
        }
        if (hasLiveIdParam) {
            return null;
        }
        //否则请求只是一个类，需要从参数中取
        Object parameterValue = parameterValues[0];
        if (parameterValue != null && !parameterValue.getClass().isPrimitive()) {
            try {
                Class<?> clazz = parameterValue.getClass();
                Field[] declaredFields = clazz.getDeclaredFields();
                for (Field field : declaredFields) {
                    if (liveIdParamName.equals(field.getName())) {
                        field.setAccessible(true);
                        liveIdValue = field.get(parameterValue);
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("[AutoCheckPermissionAspect] Failed to reflectively access liveId", e);
            }
        }
        if (liveIdValue == null) {
//            throw new LiveBusinessException(LiveBusinessErrorEnum.PARAM_ERROR);
            return null;
        }
        return liveIdValue.toString();
    }


}
