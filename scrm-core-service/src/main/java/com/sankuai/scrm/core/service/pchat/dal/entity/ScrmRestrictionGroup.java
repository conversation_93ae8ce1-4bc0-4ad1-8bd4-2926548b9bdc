package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: scrm_restriction_group
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmRestrictionGroup {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: 业务id
     */
    private String appId;

    /**
     *   字段: chat_room_wx_serial_no
     *   说明: 聊天室序列号
     */
    private String chatRoomWxSerialNo;

    /**
     *   字段: deleted
     *   说明: 是否删除 0 未删除
     */
    private Boolean deleted;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: project_id
     *   说明: 直播项目id
     */
    private String projectId;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: updater
     *   说明: 更新者
     */
    private String updater;

    /**
     *   字段: status
     *   说明: 1 有效风险群 0 无效风险群
     */
    private String status;
}