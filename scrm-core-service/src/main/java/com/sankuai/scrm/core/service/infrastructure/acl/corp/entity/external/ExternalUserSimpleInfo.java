package com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.external;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.scrm.core.service.infrastructure.util.JacksonSecondTimeDeserializer;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExternalUserSimpleInfo {

    @JsonProperty("is_customer")
    private boolean isCustomer;

    @JsonProperty("tmp_openid")
    private String tmpOpenId;

    @JsonProperty("external_userid")
    private String externalUserId;

    private String name;

    @JsonProperty("follow_userid")
    private String followUserId;

    @JsonProperty("chat_id")
    private String groupId;

    @JsonProperty("chat_name")
    private String groupName;

    @JsonProperty("add_time")
    @JsonDeserialize(using = JacksonSecondTimeDeserializer.class)
    private Date addTime;
}
