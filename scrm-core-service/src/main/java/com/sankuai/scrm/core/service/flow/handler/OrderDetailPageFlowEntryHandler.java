package com.sankuai.scrm.core.service.flow.handler;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.flow.dto.CorpWxFlowMaterialDTO;
import com.sankuai.dz.srcm.flow.dto.CorpWxLandingPageMaterialDTO;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.enums.PageLocationType;
import com.sankuai.dz.srcm.flow.enums.PlatformType;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.dz.srcm.flowV2.dto.MaterialDTO;
import com.sankuai.dz.srcm.flowV2.enums.AreaType;
import com.sankuai.dz.srcm.flowV2.enums.FilterType;
import com.sankuai.scrm.core.service.flow.config.OrderDetailFlowEntryConfig;
import com.sankuai.scrm.core.service.flow.context.FlowEntryContext;
import com.sankuai.scrm.core.service.flow.domain.FlowMaterialDomainService;
import com.sankuai.scrm.core.service.flow.enums.OrderProductType;
import com.sankuai.scrm.core.service.flow.enums.ValidateType;
import com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.haima.HaimaAclService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class OrderDetailPageFlowEntryHandler extends AbstractFlowEntryHandler<OrderDetailFlowEntryConfig> {

    private static final String ENTRY_BANNER_KEY = "xydporderdetailcommunity";

    @Autowired
    private HaimaAclService haimaAclService;

    @Autowired
    private FlowMaterialDomainService flowMaterialDomainService;

    @Override
    public boolean canHandle(Integer pageTypeCode) {
        return pageTypeCode != null && PageLocationType.ORDER_DETAIL_PAGE.getCode() == pageTypeCode;
    }

    @Override
    protected String checkParam(FlowEntryWxMaterialRequest request) {
        Integer platform = request.getPlatform();
        if (platform == null) {
            return "platform不可为空";
        }
        Long userId = request.getUserId();
        if (userId == null || userId <= 0) {
            //点评预付场景，前端获取不到userId。只有不是预付且不是点评的，才抛
            if (request.getOrderProductType() != null
                    && request.getOrderProductType() != OrderProductType.yufu.getCode()
                    && request.getPlatform() != PlatformType.DP.getCode()) {
                return "userId不合法";
            }
        }
        Integer dpCityId = request.getDpCityId();
        Integer mtCityId = request.getMtCityId();
        if (dpCityId == null && mtCityId == null) {
            return "城市信息为空";
        }
        String orderId = request.getOrderId();
        if (StringUtils.isBlank(orderId)) {
            return "优惠码订详页位置必须传订单id";
        }
        return null;
    }

    @Override
    protected void preProcess(FlowEntryWxMaterialRequest request) {
        preProcessor.transformCity(request);
    }

    @Override
    protected List<ValidateType> getValidateTypeList(OrderDetailFlowEntryConfig config) {
        List<ValidateType> validateTypeList = Lists.newArrayList();
        validateTypeList.add(ValidateType.CATEGORY);
        validateTypeList.add(ValidateType.CITY);
        if (!config.isAllowDuplicated()) {
            validateTypeList.add(ValidateType.NO_ADD_WX);
        }
        validateTypeList.add(ValidateType.CHANNEL);
        Set<String> corpIdList = flowMaterialDomainService.getBizAreaCorpIdList();
        if (corpIdList.contains(config.getCorpId())) {
            validateTypeList.add(ValidateType.BIZ_AREA);
        }
        return validateTypeList;
    }

    @Override
    protected List<ValidateType> getValidateTypeList(EntryConfigDetailDTO config) {
        List<ValidateType> validateTypeList = Lists.newArrayList();
        validateTypeList.add(ValidateType.CATEGORY);
        AreaType areaType = AreaType.getAreaTypeByCode(config.getScope().getAreaType());
        if (areaType != null) {
            if (AreaType.MT_FRONT_CITY.equals(areaType)) {
                validateTypeList.add(ValidateType.CITY);
            } else if (AreaType.DISTRICT_CODE.equals(areaType)) {
                validateTypeList.add(ValidateType.COUNTY);
            }
        }
        FilterType filterType = FilterType.getFilterTypeByCode(config.getFilterType());
        if (filterType != null) {
            if (FilterType.ADD_FRIEND_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_FRIEND);
            } else if (FilterType.ADD_GROUP_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_GROUP);
            } else if (FilterType.ALL_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_WX);
            }
        }
        validateTypeList.add(ValidateType.CHANNEL);
        Set<String> corpIdList = flowMaterialDomainService.getBizAreaCorpIdList();
        if (corpIdList.contains(appConfigRepository.getCorpIdByAppId(config.getAppId()))) {
            validateTypeList.add(ValidateType.BIZ_AREA);
        }
        return validateTypeList;
    }

    @Override
    protected CorpWxFlowMaterialDTO generateMaterial(FlowEntryWxMaterialRequest request, OrderDetailFlowEntryConfig config) {
        CorpWxFlowMaterialDTO materialDTO = new CorpWxFlowMaterialDTO();
        materialDTO.setShowResource(true);
        materialDTO.setPicUrl(config.getPicUrl());
        materialDTO.setButtonText(config.getButtonText());
        String flowId = UUID.randomUUID().toString();
        materialDTO.setJumpUrl(flowMaterialDomainService.buildFlowJumpUrl(flowId, null, request.getCategoryId()));
        CorpWxLandingPageMaterialDTO landingPageMaterialDTO = new CorpWxLandingPageMaterialDTO();
        landingPageMaterialDTO.setQrCodeUrl(config.getQrCodeUrl());
        landingPageMaterialDTO.setQrCodeDesc(config.getQrCodeName());
        landingPageMaterialDTO.setBackgroundImageUrl(config.getBackGroundImageUrl());
        flowMaterialDomainService.generateFlowAndMaterialRelation(landingPageMaterialDTO, request,
                config.getCorpId(), flowId, config.getChannel());
        return materialDTO;
    }

    @Override
    protected CorpWxFlowMaterialDTO generateMaterial(FlowEntryContext<OrderDetailFlowEntryConfig> context) {
        FlowEntryWxMaterialRequest request = context.getRequest();
        if (context.isUseConfigV2()) {
            EntryConfigDetailDTO configV2 = context.getConfigV2();
            MaterialDTO material = configV2.getMaterial();
            CorpWxFlowMaterialDTO materialDTO = new CorpWxFlowMaterialDTO();
            materialDTO.setShowResource(true);
            materialDTO.setPicUrl(material.getBannerPic());
            materialDTO.setButtonText(material.getButtonText());
            String flowId = UUID.randomUUID().toString();
            materialDTO.setJumpUrl(flowMaterialDomainService.buildFlowJumpUrl(flowId, null, request.getCategoryId()));
            CorpWxLandingPageMaterialDTO landingPageMaterialDTO = getLandingPageMaterialDTO(context);
            flowMaterialDomainService.generateFlowAndMaterialRelation(landingPageMaterialDTO, request,
                    appConfigRepository.getCorpIdByAppId(configV2.getAppId()), flowId,
                    configV2.getExtraConfig().getSourceChannel());
            return materialDTO;
        } else {
            return generateMaterial(request, context.getConfig());
        }
    }

    @Override
    protected List<OrderDetailFlowEntryConfig> queryFlowEntryConfigs(FlowEntryWxMaterialRequest request) {
        List<OrderDetailFlowEntryConfig> configs = Lists.newArrayList();
        List<FlowEntryConfig> newConfigList = queryNewFlowEntryConfigs(request);
        if (CollectionUtils.isNotEmpty(newConfigList)) {
            newConfigList.forEach(config -> {
                OrderDetailFlowEntryConfig orderDetailFlowEntryConfig = new OrderDetailFlowEntryConfig();
                orderDetailFlowEntryConfig.setConfigV2(flowEntryConfigDomainService.buildEntryConfigDetailDTO(config));
                configs.add(orderDetailFlowEntryConfig);
            });
        }
        List<HaimaContent> haimaContents = queryHaimaConfig();
        if (CollectionUtils.isEmpty(haimaContents)) {
            return Lists.newArrayList();
        }
        haimaContents.forEach(content -> {
            try {
                OrderDetailFlowEntryConfig config = new OrderDetailFlowEntryConfig();
                String secondCategoryIdStr = content.getContentString("bizln_cat1_code");
                String mtCityListStr = content.getContentString("mtCityList");
                config.setCorpId(content.getContentString("organization"));
                config.setChannel(content.getContentString("channel"));
                config.setAllowDuplicated("0".equals(content.getContentString("deduplication")));
                config.setMtCityIdList(JsonUtils.toList(mtCityListStr, Integer.class));
                config.setCategoryIdList(JsonUtils.toList(secondCategoryIdStr, Long.class));
                config.setPicUrl(content.getContentString("p1"));
                config.setJumpUrl(content.getContentString("jumpUrl"));
                config.setQrCodeUrl(content.getContentString("weComAccountQRcode"));
                config.setQrCodeName(content.getContentString("qrCodename"));
                config.setBackGroundImageUrl(content.getContentString("p3"));
                config.setButtonText(content.getContentString("buttontext"));
                String spuIdListStr = content.getContentString("spuId");
                config.setSpuId(JsonUtils.toList(spuIdListStr, Long.class));
                configs.add(config);
            } catch (Exception e) {
                log.error("OrderDetailPageFlowEntryHandlerV2.queryFlowEntryConfigs has exception, content:{}", content, e);
            }
        });
        return configs;
    }

    private List<HaimaContent> queryHaimaConfig() {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(ENTRY_BANNER_KEY);
        return haimaAclService.getInnerContentList(request);
    }
}
