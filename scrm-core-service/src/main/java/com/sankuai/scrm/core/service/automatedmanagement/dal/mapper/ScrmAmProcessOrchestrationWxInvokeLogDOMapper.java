package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmAmProcessOrchestrationWxInvokeLogDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationWxInvokeLogDO, ScrmAmProcessOrchestrationWxInvokeLogDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationWxInvokeLogDO> list);
}