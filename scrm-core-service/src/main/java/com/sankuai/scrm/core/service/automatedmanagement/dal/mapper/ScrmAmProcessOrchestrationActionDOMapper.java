package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmAmProcessOrchestrationActionDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationActionDO, ScrmAmProcessOrchestrationActionDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationActionDO> list);
}