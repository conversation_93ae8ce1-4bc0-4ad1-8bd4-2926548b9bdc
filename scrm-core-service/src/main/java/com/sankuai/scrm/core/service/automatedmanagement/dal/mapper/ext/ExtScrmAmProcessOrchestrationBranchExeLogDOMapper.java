package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationBranchExeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationBranchExeLogDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExtScrmAmProcessOrchestrationBranchExeLogDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationBranchExeLogDO, ScrmAmProcessOrchestrationBranchExeLogDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationBranchExeLogDO> list);
}