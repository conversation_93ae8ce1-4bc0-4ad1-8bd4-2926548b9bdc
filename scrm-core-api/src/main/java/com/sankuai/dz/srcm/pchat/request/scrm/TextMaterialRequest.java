package com.sankuai.dz.srcm.pchat.request.scrm;

import com.sankuai.dz.srcm.pchat.request.PageRequest;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/11/15 14:17
 * @Version v1.0.0
 */
@Data
public class TextMaterialRequest extends PageRequest {
    private String webcastId;
    private String content;
    private Long id;

    @Override
    public boolean validParam() {
        assertNotBlank(webcastId, "webcastId不能为空");
        return super.validParam();
    }
}
