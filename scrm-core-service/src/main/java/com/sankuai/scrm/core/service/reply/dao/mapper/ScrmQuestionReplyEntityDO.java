package com.sankuai.scrm.core.service.reply.dao.mapper;

import com.sankuai.scrm.core.service.reply.dao.bo.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ScrmQuestionReplyEntityDO {

    int insert(ScrmQuestionReplyBo scrmQuestionReplyBo);

    int update(ScrmQuestionReplyBo scrmQuestionReplyBo);

    ScrmQuestionReplyBo queryById(@Param("id") long replyId);

    List<ScrmQuestionStatusStatisticsBo> statusStatisticsGroupByCandidateHandler(@Param("corpId") String corpId, @Param("candidateHandler") String candidateHandler);

    List<ScrmQuestionReplyBo> getQuestionReplyListByHandlerAndStatus(@Param("corpId") String corpId, @Param("candidateHandler") String candidateHandler,
                                                                     @Param("status") Integer status, @Param("startNum") int startNum,
                                                                     @Param("size") int size);

    int countQuestionReplyByHandlerAndStatus(@Param("corpId") String corpId, @Param("candidateHandler") String candidateHandler,
                                             @Param("status") Integer status);

    List<ScrmQuestionStatisticsBo> statisticsScrmQuestion(@Param("corpId") String corpId, @Param("handler") String handler,
                                                          @Param("status") Integer status, @Param("scene") Integer scene,
                                                          @Param("version") Integer version, @Param("minRound") Integer minRound,
                                                          @Param("maxRound") Integer maxRound,
                                                          @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<ScrmQuestionHangStatisticsBo> statisticsScrmQuestionHang(@Param("corpId") String corpId, @Param("handlerList") List<String> handlerList,
                                                                  @Param("status") Integer status, @Param("scene") Integer scene,
                                                                  @Param("version") Integer version,
                                                                  @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<ScrmQuestionAutoEndStatisticsBo> statisticsScrmQuestionAutoEnd(@Param("corpId") String corpId, @Param("handlerList") List<String> handlerList,
                                                                        @Param("status") Integer status, @Param("scene") Integer scene,
                                                                        @Param("version") Integer version,
                                                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    List<ScrmQuestionReplyBo> queryScrmQuestionListByHandler(@Param("corpId") String corpId, @Param("handler") String handler,
                                                             @Param("asker") String askerName, @Param("askerId") String askerId,
                                                             @Param("status") Integer status, @Param("scene") Integer scene,
                                                             @Param("version") Integer version,
                                                             @Param("isOvertime") Integer isOvertime, @Param("startNum") int startNum,
                                                             @Param("size") int size);

    long countScrmQuestionListByHandler(@Param("corpId") String corpId, @Param("handler") String handler,
                                        @Param("asker") String asker, @Param("status") Integer status,
                                        @Param("scene") Integer scene, @Param("isOvertime") Integer isOvertime,
                                        @Param("version") Integer version);


    List<ScrmQuestionReplyBo> queryAllScrmQuestionByHandlerAndAsker(@Param("corpId") String corpId, @Param("handler") String handler,
                                                                    @Param("asker") String asker, @Param("status") Integer status,
                                                                    @Param("scene") Integer scene);

    List<ScrmQuestionReplyBo> queryAllScrmQuestionByGroup(@Param("corpId") String corpId, @Param("groupId") String groupId,
                                                          @Param("asker") String asker, @Param("status") Integer status);

    ScrmQuestionReplyBo queryScrmQuestionNewByGroup(@Param("corpId") String corpId, @Param("groupId") String groupId,
                                                    @Param("asker") String asker);

    List<String> getQuestionReplyHandlerByCorpId(@Param("corpId") String corpId);

    int updateIsRobotByRealHandlerUserId(@Param("realHandlerUserId")String realHandlerUserId, @Param("isRobot")Integer isRobot);
}
