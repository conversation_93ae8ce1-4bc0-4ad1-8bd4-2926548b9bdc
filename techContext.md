# 技术背景 (Tech Context)

*   **主要技术栈:**
    *   语言: Java (版本需进一步确认，通常 MDP 会指定)
    *   框架: 基于美团 MDP (Meituan Develop Platform)，底层为 Spring Boot。
    *   数据库: MySQL (通过 Zebra 访问)，Mybatis 作为 ORM 框架。
    *   缓存: Redis (通过 Squirrel 访问)。
    *   消息队列: Kafka (通过 Mafka 访问)。
    *   RPC/服务调用: Pigeon。
    *   Web 框架: Spring Web MVC (通过 `mdp-boot-starter-web`)。
    *   常用库: Guava, Fastjson, Apache Commons CSV, EasyExcel, Apache HttpClient。
*   **开发环境设置:** (需要查看项目 README 或相关 wiki)
*   **技术约束:**
    *   强依赖企业微信的 API 和能力 (通过 `corp-wx-client` 或直接调用)。
    *   部分功能可能仍在建设中或规划中。
    *   深度集成美团内部生态，依赖众多内部平台和服务（如 Zebra, Squirrel, Maf<PERSON>, <PERSON><PERSON>, Persona, Next, 深海, 保时洁, 大象, 以及众多业务 API）。
    *   企微配置（如 API 回调、可信 IP）可能需要微信客服加白。
    *   机器人入驻依赖云手机和深海平台。
*   **关键依赖:**
    *   内部核心: `scrm-core-api`
    *   美团基础组件: MDP, Zebra, Squirrel, Mafka, Pigeon
    *   企业微信: `corp-wx-client` 或直接 API 调用
    *   内部业务/平台: Persona, Next, 深海, 保时洁, 大象, `general-unified-search-api`, `dztrade-common-light`, `product-selectify-api`, `sku-api`, `beautycontent.store.api`, `wedding-file-sdk`, `holiday-sdk`, `baby-common-util`, `baby-customer-api`, `corp-crm-client`, `kms-pangolin-sdk`, `task-api`, `beauty-beautibot-api` 等。
*   **代码仓库:**
    *   API: `scrm-core-api/`
    *   Service: `scrm-core-service/` 