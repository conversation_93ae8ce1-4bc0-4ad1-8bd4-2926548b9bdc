package com.sankuai.scrm.core.service.coupon.domain;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.coupon.dto.CouponInfoDTO;
import com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo;
import com.sankuai.scrm.core.service.coupon.dal.example.CouponInfoExample;
import com.sankuai.scrm.core.service.coupon.dal.mapper.CouponInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CommonCouponDomainService {

    @Autowired
    private CouponInfoMapper couponInfoMapper;

    public boolean createCoupon(CouponInfoDTO couponInfoDTO, String appId) {
        if (StringUtils.isEmpty(appId) || couponInfoDTO == null || StringUtils.isEmpty(couponInfoDTO.getCouponCode()) ||
                couponInfoDTO.getCouponTemplateId() == null || StringUtils.isEmpty(couponInfoDTO.getCreator())) {
            return false;
        }
        CouponInfo record = CouponInfo.builder()
                .couponCode(couponInfoDTO.getCouponCode())
                .couponAmount(couponInfoDTO.getAmount())
                .couponTemplateId(couponInfoDTO.getCouponTemplateId())
                .creator(couponInfoDTO.getCreator())
                .appId(appId)
                .build();
        return couponInfoMapper.insertSelective(record) > 0;
    }

    public boolean updateCoupon(CouponInfoDTO couponInfoDTO) {
        if (couponInfoDTO == null || couponInfoDTO.getId() == null || StringUtils.isEmpty(couponInfoDTO.getCouponCode()) ||
                couponInfoDTO.getCouponTemplateId() == null || StringUtils.isEmpty(couponInfoDTO.getCreator())) {
            return false;
        }
        CouponInfo record = CouponInfo.builder()
                .id(couponInfoDTO.getId())
                .couponCode(couponInfoDTO.getCouponCode())
                .couponAmount(couponInfoDTO.getAmount())
                .couponTemplateId(couponInfoDTO.getCouponTemplateId())
                .creator(couponInfoDTO.getCreator())
                .build();
        return couponInfoMapper.updateByPrimaryKeySelective(record) > 0;
    }

    public boolean deleteCoupon(Long id) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.coupon.domain.CommonCouponDomainService.deleteCoupon(java.lang.Long)");
        if (id == null) {
            return false;
        }
        CouponInfo record = CouponInfo.builder().id(id).deleted(true).build();
        return couponInfoMapper.updateByPrimaryKeySelective(record) > 0;
    }

    public CouponInfo queryCouponInfo(Long id) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.coupon.domain.CommonCouponDomainService.queryCouponInfo(java.lang.Long)");
        if (id == null) {
            return null;
        }
        return couponInfoMapper.selectByPrimaryKey(id);
    }

    public List<CouponInfo> queryCouponInfoList(String appId, Integer offset, Integer rows) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.coupon.domain.CommonCouponDomainService.queryCouponInfoList(java.lang.String,java.lang.Integer,java.lang.Integer)");
        if (StringUtils.isEmpty(appId)) {
            return Lists.newArrayList();
        }
        CouponInfoExample example = new CouponInfoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(false);
        if (offset != null && offset >= 0) {
            example.setOffset(offset);
        }
        if (rows != null && rows > 0) {
            example.setRows(rows);
        }
        return couponInfoMapper.selectByExample(example);
    }

    public long countCoupon(String appId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.coupon.domain.CommonCouponDomainService.countCoupon(java.lang.String)");
        if (StringUtils.isEmpty(appId)) {
            return 0;
        }
        CouponInfoExample example = new CouponInfoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(false);
        return couponInfoMapper.countByExample(example);
    }


}
