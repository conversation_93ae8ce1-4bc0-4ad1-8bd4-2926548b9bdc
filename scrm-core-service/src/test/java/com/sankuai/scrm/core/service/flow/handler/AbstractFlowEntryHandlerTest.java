package com.sankuai.scrm.core.service.flow.handler;

import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.scrm.core.service.flow.context.FlowEntryContext;
import com.sankuai.scrm.core.service.flow.enums.ValidateType;
import com.sankuai.scrm.core.service.flow.validator.FlowEntryValidator;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import java.util.List;
import com.sankuai.scrm.core.service.flow.config.BaseFlowEntryConfig;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFlowEntryHandlerTest {

    @Mock
    private FlowEntryContext<BaseFlowEntryConfig> context;

    @Mock
    private FlowEntryWxMaterialRequest request;

    @Mock
    private BaseFlowEntryConfig config;

    @Mock
    private EntryConfigDetailDTO configV2;

    @Mock
    private FlowEntryValidator validator1;

    @Mock
    private FlowEntryValidator validator2;

    private Map<ValidateType, FlowEntryValidator> flowEntryValidatorMap;

    private AbstractFlowEntryHandler<BaseFlowEntryConfig> handler;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        handler = mock(AbstractFlowEntryHandler.class);
        flowEntryValidatorMap = new HashMap<>();
        flowEntryValidatorMap.put(ValidateType.CITY, validator1);
        flowEntryValidatorMap.put(ValidateType.COUNTY, validator2);
        // Use reflection to set the private field
        Field field = AbstractFlowEntryHandler.class.getDeclaredField("flowEntryValidatorMap");
        field.setAccessible(true);
        field.set(handler, flowEntryValidatorMap);
    }

    /**
     * 测试异常路径：useConfigV2 为 true，版本不匹配
     */
    @Test
    public void testIsMatch_UseConfigV2True_VersionMismatch() throws Throwable {
        // arrange
        // act
        boolean result = handler.isMatch(context);
        // assert
        assertFalse(result);
        verifyNoInteractions(validator1, validator2);
    }

    /**
     * 测试异常路径：useConfigV2 为 false，版本不匹配
     */
    @Test
    public void testIsMatch_UseConfigV2False_VersionMismatch() throws Throwable {
        // arrange
        // act
        boolean result = handler.isMatch(context);
        // assert
        assertFalse(result);
        verifyNoInteractions(validator1, validator2);
    }
}
