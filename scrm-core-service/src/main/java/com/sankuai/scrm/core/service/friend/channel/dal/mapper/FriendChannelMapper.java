package com.sankuai.scrm.core.service.friend.channel.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.friend.channel.dal.entity.FriendChannel;
import com.sankuai.scrm.core.service.friend.channel.dal.example.FriendChannelExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FriendChannelMapper extends MybatisBaseMapper<FriendChannel, FriendChannelExample, Long> {
    int batchInsert(@Param("list") List<FriendChannel> list);
}