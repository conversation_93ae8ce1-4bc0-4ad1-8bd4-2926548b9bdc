package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.pchat.anno.NTypeCallbackMapping;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 17:15
 * @Version v1.0.0
 */
public interface RobotFunctionCbService extends PlatformCallbackService {
    /**
     * 通过好友请求回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3011, desc = "通过好友请求回调接口（兼容PC）")
    RemoteResponse do3011(String strContext);

    /**
     * 【直接回调】机器人被加/主动加好友（成为好友）回调接口
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3005, desc = "【直接回调】机器人被加/主动加好友（成为好友）回调接口")
    RemoteResponse do3005(String strContext);

    /**
     * 【直接回调】主动添加好友结果回调接口（还未成为好友）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3002, desc = "【直接回调】主动添加好友结果回调接口（还未成为好友）")
    RemoteResponse do3002(String strContext);

    /**
     * 设置好友黑名单接口回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3009, desc = "设置好友黑名单接口回调")
    RemoteResponse do3009(String strContext);

    /**
     * 撤销机器人邀请好友入群回调接口
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4511, desc = "撤销机器人邀请好友入群回调接口")
    RemoteResponse do4511(String strContext);

    /**
     * 删除联系人好友结果回调（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3004, desc = "删除联系人好友结果回调（兼容PC）")
    RemoteResponse do3004(String strContext);

    /**
     * 【直接回调】好友回复验证消息回调接口
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3023, desc = "【直接回调】好友回复验证消息回调接口")
    RemoteResponse do3023(String strContext);

    /**
     * 机器人回复验证消息回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3024, desc = "机器人回复验证消息回调")
    RemoteResponse do3024(String strContext);

    /**
     * 【直接回调】新好友请求回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3003, desc = "【直接回调】新好友请求回调接口（兼容PC）")
    RemoteResponse do3003(String strContext);

    /**
     * 私聊消息发送结果回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 5004, desc = "私聊消息发送结果回调接口（兼容PC）")
    RemoteResponse do5004(String strContext);

    /**
     * 机器人下载消息文件回调（兼容PC）
     * 商家调用下载消息（群聊私聊）文件接口后，商家收到此回调；
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 5005, desc = "机器人下载消息文件回调（兼容PC）")
    RemoteResponse do5005(String strContext);

    /**
     * 机器人私聊信息回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 5001, desc = "机器人私聊信息回调")
    RemoteResponse do5001(String strContext);

    /**
     * 机器邀请好友入群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3006, desc = "机器邀请好友入群回调接口（兼容PC）")
    RemoteResponse do3006(String strContext);

    /**
     * 【直接回调】机器人收到入群邀请回调（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4506, desc = "【直接回调】机器人收到入群邀请回调（兼容PC）")
    RemoteResponse do4506(String strContext);

    /**
     * 机器人入群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4505, desc = "机器人入群回调接口（兼容PC）")
    RemoteResponse do4505(String strContext);

    /**
     * 扫码入群回调接口
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4504, desc = "扫码入群回调接口")
    RemoteResponse do4504(String strContext);

    /**
     * 修改好友备注接口回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3008, desc = "修改好友备注接口回调")
    RemoteResponse do3008(String strContext);

    /**
     * 设置保存群至通讯录回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 4010, desc = "设置保存群至通讯录回调接口（兼容PC）")
    RemoteResponse do4010(String strContext);

    /**
     * 好友信息变动回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3015, desc = "好友信息变动回调")
    RemoteResponse do3015(String strContext);

    /**
     * 个微去企微通过好友请求回调
     *
     * @param strContext
     */
    @NTypeCallbackMapping(nType = 3033, desc = "个微去企微通过好友请求回调")
    RemoteResponse do3033(String strContext);
}

