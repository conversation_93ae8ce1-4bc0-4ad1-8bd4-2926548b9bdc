<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmSceneProcessPriorityDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="process_orchestration_id" jdbcType="BIGINT" property="processOrchestrationId" />
    <result column="process_orchestration_version" jdbcType="VARCHAR" property="processOrchestrationVersion" />
    <result column="process_orchestration_node_id" jdbcType="BIGINT" property="processOrchestrationNodeId" />
    <result column="couponGroupId" jdbcType="VARCHAR" property="coupongroupid" />
    <result column="couponAmount" jdbcType="DECIMAL" property="couponamount" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, scene_id, app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
    couponGroupId, couponAmount, record_time, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmSceneProcessPriorityDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_scene_process_priority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_scene_process_priority
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_scene_process_priority
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmSceneProcessPriorityDOExample">
    delete from scrm_a_m_scene_process_priority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_scene_process_priority (scene_id, app_id, process_orchestration_id, 
      process_orchestration_version, process_orchestration_node_id, 
      couponGroupId, couponAmount, record_time, 
      add_time, update_time)
    values (#{sceneId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{processOrchestrationId,jdbcType=BIGINT}, 
      #{processOrchestrationVersion,jdbcType=VARCHAR}, #{processOrchestrationNodeId,jdbcType=BIGINT}, 
      #{coupongroupid,jdbcType=VARCHAR}, #{couponamount,jdbcType=DECIMAL}, #{recordTime,jdbcType=TIMESTAMP}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_scene_process_priority
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id,
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version,
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id,
      </if>
      <if test="coupongroupid != null">
        couponGroupId,
      </if>
      <if test="couponamount != null">
        couponAmount,
      </if>
      <if test="recordTime != null">
        record_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="coupongroupid != null">
        #{coupongroupid,jdbcType=VARCHAR},
      </if>
      <if test="couponamount != null">
        #{couponamount,jdbcType=DECIMAL},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmSceneProcessPriorityDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_scene_process_priority
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_scene_process_priority
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationId != null">
        process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationVersion != null">
        process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationNodeId != null">
        process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="record.coupongroupid != null">
        couponGroupId = #{record.coupongroupid,jdbcType=VARCHAR},
      </if>
      <if test="record.couponamount != null">
        couponAmount = #{record.couponamount,jdbcType=DECIMAL},
      </if>
      <if test="record.recordTime != null">
        record_time = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_scene_process_priority
    set id = #{record.id,jdbcType=BIGINT},
      scene_id = #{record.sceneId,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      couponGroupId = #{record.coupongroupid,jdbcType=VARCHAR},
      couponAmount = #{record.couponamount,jdbcType=DECIMAL},
      record_time = #{record.recordTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO">
    update scrm_a_m_scene_process_priority
    <set>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="coupongroupid != null">
        couponGroupId = #{coupongroupid,jdbcType=VARCHAR},
      </if>
      <if test="couponamount != null">
        couponAmount = #{couponamount,jdbcType=DECIMAL},
      </if>
      <if test="recordTime != null">
        record_time = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO">
    update scrm_a_m_scene_process_priority
    set scene_id = #{sceneId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      couponGroupId = #{coupongroupid,jdbcType=VARCHAR},
      couponAmount = #{couponamount,jdbcType=DECIMAL},
      record_time = #{recordTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_scene_process_priority
    (scene_id, app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
      couponGroupId, couponAmount, record_time, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.sceneId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR}, #{item.processOrchestrationId,jdbcType=BIGINT}, 
        #{item.processOrchestrationVersion,jdbcType=VARCHAR}, #{item.processOrchestrationNodeId,jdbcType=BIGINT}, 
        #{item.coupongroupid,jdbcType=VARCHAR}, #{item.couponamount,jdbcType=DECIMAL}, 
        #{item.recordTime,jdbcType=TIMESTAMP}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>