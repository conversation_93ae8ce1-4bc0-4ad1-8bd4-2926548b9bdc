package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.MsgProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.*;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMsgMapper;
import com.sankuai.scrm.core.service.pchat.enums.PersonalIsBooleanStrEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalIsDeletedEnum;
import com.sankuai.scrm.core.service.pchat.service.LockService;
import com.sankuai.scrm.core.service.pchat.service.ScrmGroupWelcomeMsgServiceImpl;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxSendMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalGroupWelcomeMsgDomainServiceTest {

    @InjectMocks
    private ScrmPersonalGroupWelcomeMsgDomainService welcomeMsgDomainService;

    @Mock(lenient = true)
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @Mock(lenient = true)
    private ScrmPersonalWxMsgDomainService personalWxMsgDomainService;
    @Mock(lenient = true)
    private LockService lockService;
    @Mock(lenient = true)
    private ScrmPersonalWxGroupMsgMapper personalWxGroupMsgMapper;
    @Mock(lenient = true)
    private ScrmPersonalWxSendMessageService personalWxSendMessageService;
    @Mock(lenient = true)
    private PrivateLiveAdapterRouter adapterRouter;

    /**
     * Test case: Empty member list
     * Expected: Method should return early without processing
     */
    @Test
    public void testSendWelcomeMsg_EmptyMemberList() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> emptyList = Collections.emptyList();
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, emptyList);
        // assert
        verify(personalWxGroupManageDomainService, never()).queryGroupByChatroomSerialNo(anyString());
    }

    /**
     * Test case: Valid member list but group not found
     * Expected: Method should return early after group check
     */
    @Test
    public void testSendWelcomeMsg_GroupNotFound() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(chatroomSerialNo)).thenReturn(null);
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService).queryGroupByChatroomSerialNo(chatroomSerialNo);
        verify(personalWxGroupManageDomainService, never()).queryGroupMsgById(anyLong());
    }

    /**
     * Test case: Valid member list but welcome message ID is null
     * Expected: Method should return early after welcome message ID check
     */
    @Test
    public void testSendWelcomeMsg_WelcomeMsgIdNull() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setWelcomeMsgId(null);
        when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(chatroomSerialNo)).thenReturn(groupInfo);
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService).queryGroupByChatroomSerialNo(chatroomSerialNo);
        verify(personalWxGroupManageDomainService, never()).queryGroupMsgById(anyLong());
    }

    /**
     * Test case: Welcome message is deleted
     * Expected: Method should return early after checking message deleted status
     */
    @Test
    public void testSendWelcomeMsg_WelcomeMsgDeleted() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setWelcomeMsgId(1L);
        ScrmPersonalWxGroupMsg groupMsg = new ScrmPersonalWxGroupMsg();
        groupMsg.setDeleted(PersonalIsDeletedEnum.DELETED.getCode());
        when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(chatroomSerialNo)).thenReturn(groupInfo);
        when(personalWxGroupManageDomainService.queryGroupMsgById(1L)).thenReturn(groupMsg);
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService).queryGroupByChatroomSerialNo(chatroomSerialNo);
        verify(personalWxGroupManageDomainService).queryGroupMsgById(1L);
    }

    /**
     * Test case: Exception occurs during processing
     * Expected: Exception should be caught and logged
     */
    @Test
    public void testSendWelcomeMsg_ExceptionHandling() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(chatroomSerialNo)).thenThrow(new RuntimeException("Test exception"));
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService).queryGroupByChatroomSerialNo(chatroomSerialNo);
    }

    /**
     * Test case: Null chatroom serial number
     * Expected: Method should return early after checking chatroom serial number
     */
    @Test
    public void testSendWelcomeMsg_NullChatroomSerialNo() {
        // arrange
        String chatroomSerialNo = null;
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService, never()).queryGroupByChatroomSerialNo(anyString());
    }

    @Test
    public void testSendWelcomeMsgSuccess() {
        // arrange
        String chatroomSerialNo = "testRoom";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberList = new ArrayList<>();
        ScrmPersonalWxGroupMemberInfoEntity member = new ScrmPersonalWxGroupMemberInfoEntity();
        member.setTimeTrace(123L);
        memberList.add(member);
        ScrmPersonalWxGroupInfoEntityWithBLOBs wxGroupInfoEntityWithBLOBs = new ScrmPersonalWxGroupInfoEntityWithBLOBs();
        wxGroupInfoEntityWithBLOBs.setWelcomeMsgId(1L);
        wxGroupInfoEntityWithBLOBs.setDeleted(false);
        ScrmPersonalWxGroupMsg mockMsg = new ScrmPersonalWxGroupMsg();
        mockMsg.setId(1L);
        mockMsg.setDeleted(PersonalIsBooleanStrEnum.FALSE.getDesc());
        mockMsg.setSendTimeFrequency(1);
        when(personalWxGroupMsgMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(mockMsg));

        when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(any())).thenReturn(wxGroupInfoEntityWithBLOBs);
        when((personalWxMsgDomainService.queryWelcomeMsgLog(any()))).thenReturn(null);
        // 使用Answer接口模拟Lambda表达式
        doAnswer(new Answer<Void>() {
            @Override
            public Void answer(InvocationOnMock invocation) throws Throwable {
                String input = invocation.getArgument(0);
                // 模拟Lambda表达式的行为
                System.out.println("Lambda executed with input: " + input);
                return null;
            }
        }).when(lockService).usingLockUniqueCategory(anyString(), any(), any(), any());

        // act
        welcomeMsgDomainService.sendWelcomeMsg(chatroomSerialNo, memberList);
        // assert
        verify(personalWxGroupManageDomainService, times(1)).queryGroupByChatroomSerialNo(anyString());
    }

    @Test
    public void testSend() {
        boolean result = true;
        try {
            ScrmPersonalWxGroupInfoEntityWithBLOBs wxGroupInfoEntityWithBLOBs = new ScrmPersonalWxGroupInfoEntityWithBLOBs();
            wxGroupInfoEntityWithBLOBs.setWelcomeMsgId(1L);
            wxGroupInfoEntityWithBLOBs.setDeleted(false);
            wxGroupInfoEntityWithBLOBs.setId(1L);
            when(personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(any())).thenReturn(wxGroupInfoEntityWithBLOBs);

            ScrmPersonalWxGroupMsg mockMsg = new ScrmPersonalWxGroupMsg();
            mockMsg.setId(1L);
            mockMsg.setDeleted(PersonalIsBooleanStrEnum.FALSE.getDesc());
            mockMsg.setSendTimeFrequency(1);
            when(personalWxGroupMsgMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(mockMsg));

            when(personalWxSendMessageService.saveMsg(any(), any(), any())).thenReturn(new ArrayList<>());

            when(adapterRouter.getProcessor(any(), any())).thenReturn(mock(MsgProcessor.class));
//            when(personalWxMsgDomainService.saveWelcomeMsgLog(any(), any(), any())).thenReturn(new ScrmPersonalWxWelcomeMsgLog());
            invokePrivateMethod("send", ScrmPersonalGroupWelcomeMsgDomainService.class, welcomeMsgDomainService, wxGroupInfoEntityWithBLOBs, mockMsg, 1L);

        } catch (Exception e) {
            result= false;
        }
        assertEquals(true, result);
    }

    private Object invokePrivateMethod(String methodName, Class<?> clazz, Object instance, Object... args) throws Exception {
        // Specify the parameter type
        Method method = clazz.getDeclaredMethod(methodName, ScrmPersonalWxGroupInfoEntity.class, ScrmPersonalWxGroupMsg.class, Long.class);
        method.setAccessible(true);
        return method.invoke(instance, args);
    }
}
