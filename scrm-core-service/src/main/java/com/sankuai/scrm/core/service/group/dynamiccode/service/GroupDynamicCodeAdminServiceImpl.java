package com.sankuai.scrm.core.service.group.dynamiccode.service;

import com.dianping.cat.Cat;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.group.dynamiccode.service.GroupDynamicCodeAdminService;
import lombok.extern.slf4j.Slf4j;

/**
 * {@inheritDoc}
 */
@Slf4j
@MdpPigeonServer
public class GroupDynamicCodeAdminServiceImpl implements GroupDynamicCodeAdminService {


    /**
     * {@inheritDoc}
     */
    @Deprecated
    @Override
    public RemoteResponse<Object> queryJoinWayConfig(long dynamicCodeId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.group.dynamiccode.service.GroupDynamicCodeAdminServiceImpl.queryJoinWayConfig(long)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    /**
     * {@inheritDoc}
     */
    @Deprecated
    @Override
    public RemoteResponse<Boolean> updateJoinWayConfig(long dynamicCodeId, String requestBody) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.group.dynamiccode.service.GroupDynamicCodeAdminServiceImpl.updateJoinWayConfig(long,java.lang.String)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    /**
     * {@inheritDoc}
     */
    @Deprecated
    @Override
    public RemoteResponse<Boolean> scheduleDynamicCodeDelayTask(String taskStr) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.scrm.core.service.group.dynamiccode.service.GroupDynamicCodeAdminServiceImpl.scheduleDynamicCodeDelayTask(java.lang.String)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

}
