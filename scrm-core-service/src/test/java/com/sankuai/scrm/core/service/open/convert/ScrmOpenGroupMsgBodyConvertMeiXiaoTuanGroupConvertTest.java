package com.sankuai.scrm.core.service.open.convert;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.srcm.open.msgbody.ScrmOpenGroupMsgBody;
import com.sankuai.scrm.core.service.chat.mq.msg.GroupChatMsg;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenChattingMessage;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenGroupBriefed;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenGroupMemberBriefed;
import java.time.Instant;
import java.util.ArrayList;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmOpenGroupMsgBodyConvertMeiXiaoTuanGroupConvertTest {

    @Mock
    private GroupChatMsg groupChatMsg;

    @Mock
    private OpenGroupBriefed openGroupBriefed;

    @Mock
    private OpenGroupMemberBriefed openGroupMemberBriefed;

    @Mock
    private OpenChattingMessage openChattingMessage;

    /**
     * Test case for null input message
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_NullMessage() throws Throwable {
        // arrange
        GroupChatMsg msg = null;
        long receiveCount = 1L;
        String appId = "testAppId";
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(msg, new ArrayList<>(), appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for text message with positive receive count
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_TextMessage_PositiveReceiveCount() throws Throwable {
        // arrange
        long receiveCount = 1L;
        String appId = "testAppId";
        long messageTime = Instant.now().getEpochSecond();
        setupBasicMocks("testGroupId", "testUserId", "TEXT", messageTime);
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(groupChatMsg, new ArrayList<>(), appId);
        // assert
        assertNotNull(result);
        assertEquals(appId, result.getAppId());
        assertEquals("testGroupId", result.getGroupId());
        assertEquals("testUserId", result.getSendWxUserId());
        assertNotNull(result.getMsgData());
        assertEquals(1, result.getMsgData().size());
        assertEquals("1", result.getMsgData().get(0).getMsgType());
    }

    /**
     * Test case for image message with zero receive count
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_ImageMessage_ZeroReceiveCount() throws Throwable {
        // arrange
        long receiveCount = 0L;
        String appId = "testAppId";
        long messageTime = Instant.now().getEpochSecond();
        setupBasicMocks("testGroupId", "testUserId", "IMAGE", messageTime);
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(groupChatMsg, new ArrayList<>(), appId);
        // assert
        assertNotNull(result);
        assertEquals("2", result.getMsgData().get(0).getMsgType());
    }

    /**
     * Test case for mini program message
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_MiniProgramMessage() throws Throwable {
        // arrange
        long receiveCount = 1L;
        String appId = "testAppId";
        long messageTime = Instant.now().getEpochSecond();
        setupBasicMocks("testGroupId", "testUserId", "MINI_PROGRAM", messageTime);
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(groupChatMsg, new ArrayList<>(), appId);
        // assert
        assertNotNull(result);
        assertEquals("3", result.getMsgData().get(0).getMsgType());
    }

    /**
     * Test case for H5 card message
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_H5CardMessage() throws Throwable {
        // arrange
        long receiveCount = 1L;
        String appId = "testAppId";
        long messageTime = Instant.now().getEpochSecond();
        setupBasicMocks("testGroupId", "testUserId", "H5_CARD", messageTime);
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(groupChatMsg, new ArrayList<>(), appId);
        // assert
        assertNotNull(result);
        assertEquals("4", result.getMsgData().get(0).getMsgType());
    }

    /**
     * Test case for unsupported message type
     */
    @Test
    public void testMeiXiaoTuanGroupConvert_UnsupportedMessageType() throws Throwable {
        // arrange
        long receiveCount = 1L;
        String appId = "testAppId";
        long messageTime = Instant.now().getEpochSecond();
        setupBasicMocks("testGroupId", "testUserId", "UNSUPPORTED", messageTime);
        // act
        ScrmOpenGroupMsgBody result = ScrmOpenGroupMsgBodyConvert.meiXiaoTuanGroupConvert(groupChatMsg, new ArrayList<>(), appId);
        // assert
        assertNotNull(result);
        assertTrue(result.getMsgData().isEmpty());
    }

    private void setupBasicMocks(String groupId, String userId, String messageType, long messageTime) {
        when(groupChatMsg.getGroup()).thenReturn(openGroupBriefed);
        when(openGroupBriefed.getGroupId()).thenReturn(groupId);
        when(groupChatMsg.getMember()).thenReturn(openGroupMemberBriefed);
        when(openGroupMemberBriefed.getUserId()).thenReturn(userId);
        when(groupChatMsg.getMessage()).thenReturn(openChattingMessage);
        when(openChattingMessage.getMessageType()).thenReturn(messageType);
        when(groupChatMsg.getMessageTime()).thenReturn(messageTime);
    }
}
