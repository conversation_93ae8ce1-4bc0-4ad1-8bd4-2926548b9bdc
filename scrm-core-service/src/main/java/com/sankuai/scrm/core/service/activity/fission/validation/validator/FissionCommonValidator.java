package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import com.sankuai.dz.srcm.activity.fission.request.*;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.enums.ActivityTypeEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.AbstractFissionChainValidator;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainMarkEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannel;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class FissionCommonValidator implements AbstractFissionChainValidator<GroupFissionActivityRequest> {

    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;
    @Resource
    private GroupFissionActivityMapper groupFissionActivityMapper;
    @Resource
    private GroupDynamicCodeChannelConfigMapper codeChannelConfigMapper;
    @Resource
    private GroupDynamicCodeChannelMapper groupDynamicCodeChannelMapper;

    @Override
    public void validate(GroupFissionActivityRequest request, String operation) {
        // 公共校验
        commonCheck(request);

        Long startTime = request.getActivityBaseInfo().getStartTime();
        Long endTime = request.getActivityBaseInfo().getEndTime();

        // 新增操作校验
        if (StringUtils.equals(operation, FissionChainOperationEnum.INSERT.getOperation())) {
            if (startTime >= endTime || startTime < System.currentTimeMillis()) {
                throw new FissionValidatorException("开始时间应小于结束时间或开始时间不能小于当前时间");
            }
        }

        // 更新操作校验
        if (StringUtils.equals(operation, FissionChainOperationEnum.UPDATE.getOperation())) {
            GroupFissionActivity groupActivityResult = groupFissionActivityMapper.selectByPrimaryKey(request.getActivityId());
            if (ObjectUtils.isEmpty(groupActivityResult)) {
                throw new FissionValidatorException("裂变活动不存在");
            }

            PosterInfoRequest posterInfoRe = request.getPosterInfo();
            GroupDynamicCodeChannelConfig posterResult = codeChannelConfigMapper.selectByPrimaryKey(posterInfoRe.getId());
            if (ObjectUtils.isEmpty(posterResult)) {
                throw new FissionValidatorException("裂变活动对应的海报不存在");
            }

            GroupDynamicCodeChannel channelResult = groupDynamicCodeChannelMapper.selectByPrimaryKey(groupActivityResult.getChannelId());
            if (ObjectUtils.isEmpty(channelResult)) {
                throw new FissionValidatorException("裂变活动对应的渠道不存在");
            }

            if (!Objects.equals(request.getAppId(), groupActivityResult.getAppId())) {
                throw new FissionValidatorException("不支持修改appId");
            }

            // 活动开始前可以修改活动开始时间、结束时间；活动开始后不允许修改活动开始时间
            if (System.currentTimeMillis() < groupActivityResult.getStartTime().getTime()) {
                if (startTime >= endTime || startTime < System.currentTimeMillis()) {
                    throw new FissionValidatorException("开始时间应小于结束时间或开始时间不能小于当前时间");
                }
            }
        }
    }

    private void commonCheck(GroupFissionActivityRequest request) {
        if (request == null || request.getActivityBaseInfo() == null) {
            throw new FissionValidatorException("创建活动出现异常");
        }

        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();

        Byte activityType = activityBaseInfo.getActivityType();
        Long startTime = activityBaseInfo.getStartTime();
        Long endTime = activityBaseInfo.getEndTime();

        // 默认设置为群裂变活动
        activityBaseInfo.setActivityType(activityType == null ? ActivityTypeEnum.GROUP.getCode() : activityType);

        // 请求参数校验
        if (ObjectUtils.isEmpty(corpAppConfigRepository.getConfigByAppId(request.getAppId())) ||
                StringUtils.isBlank(request.getAppId())) {
            throw new FissionValidatorException("appId不能为空");
        }

        if (ObjectUtils.isEmpty(activityBaseInfo)) {
            throw new FissionValidatorException("创建活动基本信息配置错误");
        }

        String activityName = activityBaseInfo.getActivityName();
        if (StringUtils.isEmpty(activityName)) {
            throw new FissionValidatorException("对外活动名称不能为空");
        }

        String activityInnerName = activityBaseInfo.getActivityInnerName();
        if (StringUtils.isEmpty(activityInnerName)) {
            throw new FissionValidatorException("对内活动名称不能为空");
        }

        if (startTime == null || endTime == null) {
            throw new FissionValidatorException("开始和结束时间不能为空");
        }

        String rule = activityBaseInfo.getRule();
        if (StringUtils.isBlank(rule)) {
            throw new FissionValidatorException("活动规则不能为空");
        }

        if (activityBaseInfo.getActivityHeadImg() == null) {
            throw new FissionValidatorException("活动头图不能为空");
        }

        ShareCardInfo shareCardInfo = request.getShareCardInfo();
        if (ObjectUtils.isEmpty(shareCardInfo) || StringUtils.isBlank(shareCardInfo.getCardImg()) ||
                StringUtils.isBlank(shareCardInfo.getCardTitle())) {
            throw new FissionValidatorException("分享卡片信息不能为空");
        }

        if (StringUtils.isBlank(activityBaseInfo.getBackgroundImg())) {
            throw new FissionValidatorException("朋友圈分享底图不能为空");
        }

        if (CollectionUtils.isEmpty(rewardInfoList)) {
            throw new FissionValidatorException("裂变活动奖品列表不能为空");
        }

        for (RewardInfoRequest rewardRequest : rewardInfoList) {
            if (rewardRequest.getReceiveType() == null) {
                throw new FissionValidatorException("奖品发放类型不能为空");
            }
            if (rewardRequest.getRewardType() == null) {
                throw new FissionValidatorException("奖品类型不能为空");
            }
            if (rewardRequest.getPriceName() == null) {
                throw new FissionValidatorException("奖品名称不能为空");
            }
        }
    }

    @Override
    public String getMark() {
        return FissionChainMarkEnum.COMMON.getMark();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
