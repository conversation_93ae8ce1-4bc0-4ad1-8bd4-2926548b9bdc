package com.sankuai.scrm.core.service.friend.channel.domain;

import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.friend.channel.dto.FriendChannelInfoDTO;
import com.sankuai.dz.srcm.friend.channel.dto.FriendChannelListDTO;
import com.sankuai.scrm.core.service.friend.channel.dal.entity.FriendChannel;
import com.sankuai.scrm.core.service.friend.channel.dal.example.FriendChannelExample;
import com.sankuai.scrm.core.service.friend.channel.dal.mapper.FriendChannelMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FriendChannelDomainService {

    @Resource
    private FriendChannelMapper friendChannelMapper;

    public boolean saveFriendChannel(String appId, String name) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.friend.channel.domain.FriendChannelDomainService.saveFriendChannel(java.lang.String,java.lang.String)");
        try {
            log.info("FriendChannelDomainService.saveFriendChannel: 创建好友渠道, appId={}, name={}", appId, name);
            FriendChannel friendChannel = FriendChannel.builder()
                    .appId(appId)
                    .name(name)
                    .isDelete(false)
                    .build();
            int saveResult = friendChannelMapper.insertSelective(friendChannel);
            if (saveResult < 0) {
                log.error("FriendChannelDomainService.saveFriendChannel: 保存好友渠道失败, friendChannel={}", friendChannel);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("FriendChannelDomainService.saveFriendChannel: 创建好友渠道出现异常", e);
            return false;
        }
    }

    public Long createFriendChannel(String appId, String name) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(name)) {
            return null;
        }
        FriendChannel record = FriendChannel.builder()
                .appId(appId)
                .name(name)
                .isDelete(false)
                .build();
        int row = friendChannelMapper.insertSelective(record);
        return row > 0 ? record.getId() : null;
    }

    public List<FriendChannel> queryFriendChannelByName(String appId, String name) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(name)) {
            return Lists.newArrayList();
        }
        FriendChannelExample channelExample = new FriendChannelExample();
        channelExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andNameEqualTo(name)
                .andIsDeleteEqualTo(false);
        return friendChannelMapper.selectByExample(channelExample);
    }

    public List<FriendChannelListDTO> queryFriendChannelList(String appId, Integer pageSize, Integer pageNum) {
        try {
            log.info("FriendChannelDomainService.queryFriendChannelList: 分页查询好友渠道列表, appId={}, pageSize={}, pageNum={}", appId, pageSize, pageNum);

            FriendChannelExample example = buildFriendChannelExample(appId);
            example.page(pageNum - 1, pageSize);

            List<FriendChannel> friendChannels = friendChannelMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(friendChannels)) {
                log.info("FriendChannelDomainService.queryFriendChannelList: 查询结果为空, appId={}, pageSize={}, pageNum={}", appId, pageSize, pageNum);
                return Collections.emptyList();
            }
            return buildFriendChannelDTOList(friendChannels);
        } catch (Exception e) {
            log.error("FriendChannelDomainService.queryFriendChannelList: 查询好友渠道列表出现异常", e);
            return Collections.emptyList();
        }
    }

    public FriendChannelInfoDTO queryFriendChannelInfo(Long channelId) {
        if (channelId == null) {
            return null;
        }
        FriendChannel friendChannel = friendChannelMapper.selectByPrimaryKey(channelId);
        if (friendChannel == null) {
            return null;
        }
        return FriendChannelInfoDTO.builder()
                .id(friendChannel.getId())
                .channelName(friendChannel.getName())
                .build();
    }

    public long queryFriendChannelListCount(String appId) {
        FriendChannelExample example = buildFriendChannelExample(appId);
        return friendChannelMapper.countByExample(example);
    }

    public boolean deleteFriendChannel(Long channelId) {
        if (channelId == null) {
            return false;
        }
        FriendChannel record = FriendChannel.builder()
                .id(channelId)
                .isDelete(true)
                .build();
        return friendChannelMapper.updateByPrimaryKeySelective(record) > 0;
    }

    //--------private method----
    private List<FriendChannelListDTO> buildFriendChannelDTOList(List<FriendChannel> friendChannels) {
        return friendChannels.stream()
                .map(friendChannel -> FriendChannelListDTO.builder()
                        .id(friendChannel.getId())
                        .name(friendChannel.getName())
                        .build())
                .collect(Collectors.toList());
    }


    private FriendChannelExample buildFriendChannelExample(String appId) {
        FriendChannelExample example = new FriendChannelExample();
        example.createCriteria().andAppIdEqualTo(appId).andIsDeleteEqualTo(false);
        example.setOrderByClause("id desc");
        return example;
    }


}
