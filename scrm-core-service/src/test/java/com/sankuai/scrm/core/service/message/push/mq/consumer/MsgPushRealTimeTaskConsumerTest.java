package com.sankuai.scrm.core.service.message.push.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MsgPushRealTimeTaskConsumerTest {

    @Mock
    private IConsumerProcessor consumer;

    @InjectMocks
    private MsgPushRealTimeTaskConsumer msgPushRealTimeTaskConsumer;

    private void setPrivateField(Object target, String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * Test when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        setPrivateField(msgPushRealTimeTaskConsumer, "consumer", null);
        // act & assert
        assertDoesNotThrow(() -> msgPushRealTimeTaskConsumer.destroy());
    }

    /**
     * Test when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange
        setPrivateField(msgPushRealTimeTaskConsumer, "consumer", consumer);
        // act
        msgPushRealTimeTaskConsumer.destroy();
        // assert
        verify(consumer).close();
        verifyNoMoreInteractions(consumer);
    }

    /**
     * Test when consumer.close() throws exception - should propagate exception
     */
    @Test
    public void testDestroyWhenCloseThrowsException() throws Throwable {
        // arrange
        setPrivateField(msgPushRealTimeTaskConsumer, "consumer", consumer);
        Exception expectedException = new Exception("Close failed");
        doThrow(expectedException).when(consumer).close();
        // act & assert
        Exception actualException = assertThrows(Exception.class, () -> msgPushRealTimeTaskConsumer.destroy());
        assertSame(expectedException, actualException);
        verify(consumer).close();
    }
}
