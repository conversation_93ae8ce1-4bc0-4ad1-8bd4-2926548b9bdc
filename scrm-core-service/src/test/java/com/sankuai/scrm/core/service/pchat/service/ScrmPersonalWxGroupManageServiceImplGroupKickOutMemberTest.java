package com.sankuai.scrm.core.service.pchat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupKickOutMemberRequest;
import com.sankuai.dz.srcm.pchat.service.ScrmGroupManageService;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import com.sankuai.scrm.core.service.pchat.adapter.bo.GroupMemberRemoveBO;
import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.MemberProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntityWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupInfoEntityExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.domain.group.PrivateLiveGroupMemberDomainService;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.util.SwitchUtil;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxGroupManageServiceImplGroupKickOutMemberTest {

    @Mock
    private ScrmPersonalWxGroupInfoEntityMapper personalWxGroupInfoEntityMapper;

    @Mock
    private PrivateLiveGroupMemberDomainService groupManageDomainService;

    @Mock
    private PrivateLiveAdapterRouter adapterRouter;

    @Mock
    private ScrmGroupManageService groupManageService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    private GroupKickOutMemberRequest request;

    private ScrmPersonalWxGroupInfoEntityWithBLOBs groupInfoEntity;

    private ScrmPersonalWxGroupMemberInfoEntity memberInfoEntity;

    @Before
    public void setUp() {
        request = new GroupKickOutMemberRequest();
        request.setGroupId(1L);
        request.setGroupWxSerialNo("groupWxSerialNo");
        groupInfoEntity = new ScrmPersonalWxGroupInfoEntityWithBLOBs();
        groupInfoEntity.setId(1L);
        groupInfoEntity.setOwner("owner");
        groupInfoEntity.setAdminList("[\"admin1\", \"admin2\"]");
        groupInfoEntity.setProjectId("projectId");
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomWxSerialNo");
        groupInfoEntity.setAppId("appId");
        memberInfoEntity = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfoEntity.setId(1L);
        memberInfoEntity.setUserSerialNo("userSerialNo");
    }

    /**
     * 测试异常情况，groupId 为空
     */
    @Test
    public void testGroupKickOutMember_GroupIdIsNull() throws Throwable {
        // arrange
        request.setGroupId(null);
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> scrmPersonalWxGroupManageService.groupKickOutMember(request));
    }

    /**
     * 测试异常情况，groupWxSerialNo 为空
     */
    @Test
    public void testGroupKickOutMember_GroupWxSerialNoIsNull() throws Throwable {
        // arrange
        request.setGroupWxSerialNo(null);
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> scrmPersonalWxGroupManageService.groupKickOutMember(request));
    }

    /**
     * 测试异常情况，群信息不存在
     */
    @Test
    public void testGroupKickOutMember_GroupInfoNotFound() throws Throwable {
        // arrange
        when(personalWxGroupInfoEntityMapper.selectByExampleWithBLOBs(any(ScrmPersonalWxGroupInfoEntityExample.class))).thenReturn(Collections.emptyList());
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> scrmPersonalWxGroupManageService.groupKickOutMember(request));
    }

    @InjectMocks
    private ScrmPersonalWxGroupManageServiceImpl scrmPersonalWxGroupManageService;
}
