package com.sankuai.dz.srcm.aigc.service.autoreply.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> on 2024/11/27 17:18
 */
@Data
public class AutoreplySessionQueryRequest implements Serializable {

    // TODO:筛选条件：1-全部、2-意图匹配、3-意图未匹配、4-用户投诉
    // private Integer filter;

    private String appId;

    private Long startTime;
    private Long endTime;

    // 小助手列表
    private List<String> assistantAccountList;

    // 用户昵称，进行精准搜索
    private String userNickname;

    // 前端从1开始
    private Integer pageNumber;

    private Integer pageSize;

}
