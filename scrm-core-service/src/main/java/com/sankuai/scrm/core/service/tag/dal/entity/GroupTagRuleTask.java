package com.sankuai.scrm.core.service.tag.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: group_tag_rule_task
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GroupTagRuleTask {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: rule_name
     *   说明: 规则名称
     */
    private String ruleName;

    /**
     *   字段: tag_set_serial_no
     *   说明: 标签组序列号
     */
    private String tagSetSerialNo;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: rule
     *   说明: 规则
     */
    private String rule;

    /**
     *   字段: run_type
     *   说明: 执行类型，1-单次执行，2-定期执行
     */
    private Integer runType;

    /**
     *   字段: run_sub_type
     *   说明: 1 每月 2 每周 3 每日
     */
    private Integer runSubType;

    /**
     *   字段: run_interval
     *   说明: 调度间隔 每月时，此字段为某天，每周时，此字段为某周几
     */
    private Integer runInterval;

    /**
     *   字段: run_time
     *   说明: 执行时间
     */
    private String runTime;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: updater
     *   说明: 更新者
     */
    private String updater;

    /**
     *   字段: version
     *   说明: 规则版本
     */
    private Integer version;

    /**
     *   字段: run_count
     *   说明: 执行次数
     */
    private Integer runCount;

    /**
     *   字段: last_run_time
     *   说明: 最近执行时间
     */
    private Date lastRunTime;

    /**
     *   字段: result_url
     *   说明: 预测结果url
     */
    private String resultUrl;

    /**
     *   字段: deleted
     *   说明: 0-未删除, 1-删除
     */
    private Boolean deleted;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}