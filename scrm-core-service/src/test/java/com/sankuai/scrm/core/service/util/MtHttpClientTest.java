package com.sankuai.scrm.core.service.util;

import com.alibaba.fastjson.JSONObject;
import com.dianping.beauty.ibot.exceptions.MtHttpException;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MtHttpClientTest {

    @Mock(lenient = true)
    private CloseableHttpClient httpClient;

    @Mock(lenient = true)
    private CloseableHttpResponse closeableHttpResponse;

    @Mock(lenient = true)
    private StatusLine statusLine;

    @Spy
    @InjectMocks
    private MtHttpClient mtHttpClient;

    @Before
    public void setUp() throws IOException {
    }

    @Test
    public void testRequestHttpPostAndParseResultNormal() throws Throwable {
        String uri = "/test";
        Map<String, Object> postData = new HashMap<>();
        postData.put("key", "value");
        String formData = "formData";
        JSONObject expectedResult = new JSONObject();
        expectedResult.put("key", "value");
        doReturn(expectedResult).when(mtHttpClient).retrieveAndParseResult(any(HttpPost.class), anyString());
        JSONObject result = mtHttpClient.requestHttpPostAndParseResult(uri, postData, formData);
        assertNotNull(result);
        assertEquals("value", result.getString("key"));
        verify(mtHttpClient).retrieveAndParseResult(any(HttpPost.class), eq(formData));
    }

    @Test(expected = Exception.class)
    public void testRequestHttpPostAndParseResultPostDataEmpty() throws Throwable {
        String uri = "/test";
        Map<String, Object> postData = new HashMap<>();
        String formData = "formData";
        doThrow(new MtHttpException(MtHttpException.INVALID_HTTP_STATUS, "Test exception")).when(mtHttpClient).retrieveAndParseResult(any(HttpPost.class), anyString());
        mtHttpClient.requestHttpPostAndParseResult(uri, postData, formData);
    }

    @Test(expected = Exception.class)
    public void testRequestHttpPostAndParseResultUriOrFormDataEmpty() throws Throwable {
        String uri = "";
        Map<String, Object> postData = new HashMap<>();
        postData.put("key", "value");
        String formData = "";
        mtHttpClient.requestHttpPostAndParseResult(uri, postData, formData);
    }

    @Test(expected = Exception.class)
    public void testRequestHttpPostAndParseResultHttpRequestFailed() throws Throwable {
        String uri = "/test";
        Map<String, Object> postData = new HashMap<>();
        postData.put("key", "value");
        String formData = "formData";
        doThrow(new MtHttpException(MtHttpException.INVALID_HTTP_STATUS, "Test exception")).when(mtHttpClient).retrieveAndParseResult(any(HttpPost.class), anyString());
        mtHttpClient.requestHttpPostAndParseResult(uri, postData, formData);
    }
}
