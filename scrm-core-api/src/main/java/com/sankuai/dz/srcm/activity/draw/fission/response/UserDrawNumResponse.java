package com.sankuai.dz.srcm.activity.draw.fission.response;

import com.sankuai.dz.srcm.activity.draw.fission.dto.DrawFissionPrizeInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserDrawNumResponse extends DrawFissionPrizeInfoDTO implements Serializable {
    private long remainDrawNum;
    private String msg;
}
