package com.sankuai.scrm.core.service.dashboard.dal.dto;

import com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 调用ai记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EsFridayIntelligentFollowLog {
    private String id;
    private String userId;
    private String appId;
    private String dateKey;
    private String shopId;
    private String productId;
    private String firstRequest;
    private String firstResponse;
    private String secondRequest;
    private String secondResponse;
    private boolean needSend;
    private String deliveryReason;
    private String failReason;
    private String metaData;
    private String timingReason;
    private String chosenSupplyReason;
    private Long realTimeTaskCode;
    private String realTimeTaskMsg;
    private Boolean realTimeTaskSuccess;

}
