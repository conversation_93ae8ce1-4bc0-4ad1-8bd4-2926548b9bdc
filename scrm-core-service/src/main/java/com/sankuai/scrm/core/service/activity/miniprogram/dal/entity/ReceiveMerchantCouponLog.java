package com.sankuai.scrm.core.service.activity.miniprogram.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: receive_merchant_coupon_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ReceiveMerchantCouponLog {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: activity_id
     *   说明: 活动id
     */
    private Long activityId;

    /**
     *   字段: mt_user_id
     *   说明: 美团userId
     */
    private Long mtUserId;

    /**
     *   字段: union_id
     *   说明: 用户的unionId
     */
    private String unionId;

    /**
     *   字段: mt_city_id
     *   说明: 美团前台城市id
     */
    private Integer mtCityId;

    /**
     *   字段: merchant_coupon_id
     *   说明: 商检券id
     */
    private String merchantCouponId;

    /**
     *   字段: path_param
     *   说明: 活动路径参数
     */
    private String pathParam;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}