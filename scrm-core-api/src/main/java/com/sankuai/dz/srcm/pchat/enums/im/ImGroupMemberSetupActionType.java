package com.sankuai.dz.srcm.pchat.enums.im;

import lombok.Getter;

@Getter
public enum ImGroupMemberSetupActionType {
    SETUP_GROUP_MEMBER_REMARK("1", "设置群成员备注");

    private final String code;

    private final String desc;

    ImGroupMemberSetupActionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImGroupMemberSetupActionType fromCode(String code) {
        if (code==null){
            return null;
        }
        for (ImGroupMemberSetupActionType enumValue : ImGroupMemberSetupActionType.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }
}
