package com.sankuai.dz.srcm.open.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

@Data
@TypeDoc(description = "图文消息")
public class LinkMessageDTO implements Serializable {

    @FieldDoc(description = "链接URL")
    private String url;

    @FieldDoc(description = "链接标题")
    private String title;

    @FieldDoc(description = "缩略图URL")
    private String thumbUrl;

    @FieldDoc(description = "链接描述")
    private String description;
}
