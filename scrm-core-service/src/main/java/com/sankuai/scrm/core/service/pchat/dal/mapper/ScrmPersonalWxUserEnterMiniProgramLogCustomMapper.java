package com.sankuai.scrm.core.service.pchat.dal.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ScrmPersonalWxUserEnterMiniProgramLogCustomMapper {
    List<String> queryLatestUserEnterMiniProgramLogByNickname(@Param("keyword") String keyword, @Param("addTime") Date addTime, @Param("rows") Integer rows, @Param("offset") Integer offset);

    Long countLatestUserEnterMiniProgramLogByNickname(@Param("keyword") String keyword, @Param("addTime") Date addTime);
}