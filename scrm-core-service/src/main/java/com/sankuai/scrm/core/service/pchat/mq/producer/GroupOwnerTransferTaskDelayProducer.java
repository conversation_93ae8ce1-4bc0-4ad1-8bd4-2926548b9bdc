package com.sankuai.scrm.core.service.pchat.mq.producer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IDelayFutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Properties;
import java.util.function.Consumer;

@Service
@Slf4j
public class GroupOwnerTransferTaskDelayProducer implements InitializingBean {
    private IProducerProcessor<?, String> producer;

    public void sendDelayTask(DelayPayload delaySendTask, long delaySec, Consumer successCallback,
            Consumer<Throwable> failureCallback) {
        if (delaySendTask == null) {
            return;
        }
        delaySec = Math.max(delaySec, 5);
        try {
            delaySendTask.setDelaySec(delaySec);
            sendMQDelayTask(delaySendTask.toJsonStr(), delaySec, successCallback, failureCallback);
        } catch (Exception e) {
            log.error("GroupOwnerTransferTaskDelayProducer消息发送失败：", e);
            if (failureCallback != null) {
                failureCallback.accept(e);
            }
        }
    }

    private void sendMQDelayTask(String msg, long delaySec, Consumer successCallback,
            Consumer<Throwable> failureCallback) {
        try {
            log.info("发送消息至mq");
            producer.sendAsyncDelayMessage(msg, delaySec * 1000,
                    new DelayFutureCallback(successCallback, failureCallback));
        } catch (Exception e) {
            log.error("发送MQ延迟任务失败：", e);
            if (failureCallback != null) {
                failureCallback.accept(e);
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        producer = MafkaClient.buildDelayProduceFactory(properties, "scrm.pchat.group.transfer.delay.task");
    }

    private static class DelayFutureCallback implements IDelayFutureCallback {
        private final Consumer successCallback;
        private final Consumer<Throwable> failureCallback;

        public DelayFutureCallback(Consumer successCallback, Consumer<Throwable> failureCallback) {
            this.successCallback = successCallback;
            this.failureCallback = failureCallback;
        }

        @Override
        public void onSuccess(AsyncDelayProducerResult asyncDelayProducerResult) {
            Cat.logMetricForCount("GroupOwnerTransferTaskDelayProducer.sendMQDelayTask.success");
            if (successCallback != null) {
                successCallback.accept(null);
            }
        }

        @Override
        public void onFailure(AsyncDelayProducerResult asyncDelayProducerResult) {
            Cat.logMetricForCount("GroupOwnerTransferTaskDelayProducer.sendMQDelayTask.fail");
            if (failureCallback != null) {
                failureCallback.accept(new Exception("MQ延迟任务发送失败"));
            }
        }
    }

}
