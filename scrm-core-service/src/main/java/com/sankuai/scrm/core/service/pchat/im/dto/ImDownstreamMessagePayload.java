package com.sankuai.scrm.core.service.pchat.im.dto;

import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: sxf
 * @Date: 2024/01/10 16:52
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImDownstreamMessagePayload<T> implements Serializable {

    private String msgId;
    private Integer msgType;// 消息
    private Long timestamp;
    private String robotSerialNo;
    private T data;

    public long getTimestamp() {
        return timestamp == null ? new Date().getTime() : timestamp;
    }

    public String toJsonStr() {
        return JsonUtils.toStr(this);
    }

}
