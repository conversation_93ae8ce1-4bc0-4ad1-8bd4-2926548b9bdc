package com.sankuai.scrm.core.service.infrastructure.acl.corp;

import com.dianping.baby.http.HttpClientUtil;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxOpenIdResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class CorpWxOpenIdConversionAcl {

    private static final String OPENID_CONVERSION_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/convert_to_openid?access_token=%s";

    @Autowired
    private CorpWxAcl corpWxAclService;

    public String convert2OpenId(String corpId, String externalUserId) {
        log.info("CorpWxOpenIdConversionAcl request corpId:{}. externalUserId:{}", corpId, externalUserId);
        if (StringUtils.isEmpty(externalUserId)) {
            throw new IllegalArgumentException("外部联系人userId为空");
        }
        if (StringUtils.isEmpty(corpId)) {
            throw new IllegalArgumentException("CorpId为空");
        }
        String token = corpWxAclService.getTokenByCorpId(corpId);
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("获取企微查询token失败");
        }
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("external_userid", externalUserId);
        try {
            WxOpenIdResponse response = HttpClientUtil.postAsJson(String.format(OPENID_CONVERSION_URL, token), paramMap, Maps.newHashMap(), WxOpenIdResponse.class);
            log.info("CorpWxOpenIdConversionAcl.convert2OpenId response:{}", response);
            if (response == null || !response.isSuccess()) {
                return null;
            }
            return response.getOpenId();
        } catch (IOException e) {
            log.error("CorpWxOpenIdConversionAcl.convertOpenId has exception, corpId:{}, externalUserId:{}", corpId, externalUserId, e);
        }
        return null;
    }
}
