package com.sankuai.dz.srcm.user.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/3/13 19:41
 * @Version v1.0.0
 */
@Data
@TypeDoc(description = "咨询师变更请求数据")
public class ConsultantChangeRequest implements Serializable {

//    @FieldDoc(description = "原咨询师任务id")
//    private Long originConsultantTaskId;

    @FieldDoc(description = "新咨询师任务id")
    private Long newConsultantTaskId;
    private String wxId;

    private String unionId;
    @FieldDoc(description = "直播id")
    private String liveId;
    @FieldDoc(description = "美团userid")
    private Long userId;
}

