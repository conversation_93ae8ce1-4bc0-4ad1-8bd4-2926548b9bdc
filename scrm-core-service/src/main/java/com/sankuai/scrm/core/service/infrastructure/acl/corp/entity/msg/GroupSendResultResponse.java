package com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.msg;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxBaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class GroupSendResultResponse extends WxBaseResponse {

    @JsonProperty("next_cursor")
    private String nextCursor;

    @JsonProperty("send_list")
    private List<GroupSendResult> sendResultList;
}
