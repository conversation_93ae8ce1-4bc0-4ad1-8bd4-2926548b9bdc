package com.sankuai.scrm.core.service.reply.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.reply.domain.ScrmLirenShangjiaAutoReplyDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Properties;

@Slf4j
@Component
public class LirenShangyunCorpSessionMsgConsumer implements InitializingBean, DisposableBean {

    /**
     * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建.
     */
    private static IConsumerProcessor consumer;

    @Resource
    private ScrmLirenShangjiaAutoReplyDomainService scrmLirenShangjiaAutoReplyDomainService;


    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "hotel");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "wechat_message_distribute_dz_liren_auto_reply");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "crmqwtool_wechat_message_distribute");

        consumer.recvMessageWithParallel(String.class, new IMessageListener() {
            @Override
            public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
                try {
                    String body = message.getBody().toString();
                    scrmLirenShangjiaAutoReplyDomainService.autoReply(body);
                } catch (Exception e) {
                    log.error("LirenShangyunCorpSessionMsgConsumer has error,msg is {}", message, e);
                    return ConsumeStatus.RECONSUME_LATER;
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });

    }

    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }
}