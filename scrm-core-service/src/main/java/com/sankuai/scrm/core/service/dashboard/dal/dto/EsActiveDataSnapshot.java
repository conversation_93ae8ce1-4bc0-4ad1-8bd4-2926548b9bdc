package com.sankuai.scrm.core.service.dashboard.dal.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EsActiveDataSnapshot {

    private String id;
    private String corpId;
    private String versionTime;
    private long inGroupSenderCount;
    private long privateSenderCount;
    private long senderCount;
    private long fissionParticipantCount;
    private long drawParticipantCount;
    private long contentRadarParticipantCount;
    private long participantCount;
    //商品页uv
    private long clickProductsNotificationCount;
    //活动页
    private long clickActivityPageNotificationCount;

    private long clickCouponCount;
    //内容页
    private long clickContentPageNotificationCount;
    //合计
    private long clickContentCount;
}
