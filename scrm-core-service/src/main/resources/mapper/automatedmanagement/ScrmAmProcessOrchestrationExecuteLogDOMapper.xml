<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="process_orchestration_id" jdbcType="BIGINT" property="processOrchestrationId" />
    <result column="process_orchestration_version" jdbcType="VARCHAR" property="processOrchestrationVersion" />
    <result column="process_orchestration_node_id" jdbcType="BIGINT" property="processOrchestrationNodeId" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="target_union_id" jdbcType="VARCHAR" property="targetUnionId" />
    <result column="target_id_type" jdbcType="TINYINT" property="targetIdType" />
    <result column="executor_id" jdbcType="VARCHAR" property="executorId" />
    <result column="executor_id_type" jdbcType="TINYINT" property="executorIdType" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="group_member_id" jdbcType="VARCHAR" property="groupMemberId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="task_start_time" jdbcType="TIMESTAMP" property="taskStartTime" />
    <result column="final_check_time" jdbcType="TIMESTAMP" property="finalCheckTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="action_sub_type" jdbcType="INTEGER" property="actionSubType" />
    <result column="action_execution_path" jdbcType="INTEGER" property="actionExecutionPath" />
    <result column="target_mt_user_id" jdbcType="BIGINT" property="targetMtUserId" />
    <result column="resume_time" jdbcType="TIMESTAMP" property="resumeTime" />
    <result column="check_point_batch_id" jdbcType="VARCHAR" property="checkPointBatchId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
    target_id, target_union_id, target_id_type, executor_id, executor_id_type, group_id, 
    group_member_id, member_name, status, remark, task_start_time, final_check_time, 
    add_time, update_time, avatar, action_sub_type, action_execution_path, target_mt_user_id, 
    resume_time, check_point_batch_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_execute_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_execute_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_process_orchestration_execute_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample">
    delete from scrm_a_m_process_orchestration_execute_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_execute_log (app_id, process_orchestration_id, process_orchestration_version, 
      process_orchestration_node_id, target_id, target_union_id, 
      target_id_type, executor_id, executor_id_type, 
      group_id, group_member_id, member_name, 
      status, remark, task_start_time, 
      final_check_time, add_time, update_time, 
      avatar, action_sub_type, action_execution_path, 
      target_mt_user_id, resume_time, check_point_batch_id
      )
    values (#{appId,jdbcType=VARCHAR}, #{processOrchestrationId,jdbcType=BIGINT}, #{processOrchestrationVersion,jdbcType=VARCHAR}, 
      #{processOrchestrationNodeId,jdbcType=BIGINT}, #{targetId,jdbcType=BIGINT}, #{targetUnionId,jdbcType=VARCHAR}, 
      #{targetIdType,jdbcType=TINYINT}, #{executorId,jdbcType=VARCHAR}, #{executorIdType,jdbcType=TINYINT}, 
      #{groupId,jdbcType=VARCHAR}, #{groupMemberId,jdbcType=VARCHAR}, #{memberName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{taskStartTime,jdbcType=TIMESTAMP}, 
      #{finalCheckTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{avatar,jdbcType=VARCHAR}, #{actionSubType,jdbcType=INTEGER}, #{actionExecutionPath,jdbcType=INTEGER}, 
      #{targetMtUserId,jdbcType=BIGINT}, #{resumeTime,jdbcType=TIMESTAMP}, #{checkPointBatchId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_execute_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id,
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version,
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="targetUnionId != null">
        target_union_id,
      </if>
      <if test="targetIdType != null">
        target_id_type,
      </if>
      <if test="executorId != null">
        executor_id,
      </if>
      <if test="executorIdType != null">
        executor_id_type,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupMemberId != null">
        group_member_id,
      </if>
      <if test="memberName != null">
        member_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="taskStartTime != null">
        task_start_time,
      </if>
      <if test="finalCheckTime != null">
        final_check_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="actionSubType != null">
        action_sub_type,
      </if>
      <if test="actionExecutionPath != null">
        action_execution_path,
      </if>
      <if test="targetMtUserId != null">
        target_mt_user_id,
      </if>
      <if test="resumeTime != null">
        resume_time,
      </if>
      <if test="checkPointBatchId != null">
        check_point_batch_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=BIGINT},
      </if>
      <if test="targetUnionId != null">
        #{targetUnionId,jdbcType=VARCHAR},
      </if>
      <if test="targetIdType != null">
        #{targetIdType,jdbcType=TINYINT},
      </if>
      <if test="executorId != null">
        #{executorId,jdbcType=VARCHAR},
      </if>
      <if test="executorIdType != null">
        #{executorIdType,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="groupMemberId != null">
        #{groupMemberId,jdbcType=VARCHAR},
      </if>
      <if test="memberName != null">
        #{memberName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalCheckTime != null">
        #{finalCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="actionSubType != null">
        #{actionSubType,jdbcType=INTEGER},
      </if>
      <if test="actionExecutionPath != null">
        #{actionExecutionPath,jdbcType=INTEGER},
      </if>
      <if test="targetMtUserId != null">
        #{targetMtUserId,jdbcType=BIGINT},
      </if>
      <if test="resumeTime != null">
        #{resumeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkPointBatchId != null">
        #{checkPointBatchId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_process_orchestration_execute_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_process_orchestration_execute_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationId != null">
        process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationVersion != null">
        process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationNodeId != null">
        process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="record.targetId != null">
        target_id = #{record.targetId,jdbcType=BIGINT},
      </if>
      <if test="record.targetUnionId != null">
        target_union_id = #{record.targetUnionId,jdbcType=VARCHAR},
      </if>
      <if test="record.targetIdType != null">
        target_id_type = #{record.targetIdType,jdbcType=TINYINT},
      </if>
      <if test="record.executorId != null">
        executor_id = #{record.executorId,jdbcType=VARCHAR},
      </if>
      <if test="record.executorIdType != null">
        executor_id_type = #{record.executorIdType,jdbcType=TINYINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupMemberId != null">
        group_member_id = #{record.groupMemberId,jdbcType=VARCHAR},
      </if>
      <if test="record.memberName != null">
        member_name = #{record.memberName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStartTime != null">
        task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finalCheckTime != null">
        final_check_time = #{record.finalCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.avatar != null">
        avatar = #{record.avatar,jdbcType=VARCHAR},
      </if>
      <if test="record.actionSubType != null">
        action_sub_type = #{record.actionSubType,jdbcType=INTEGER},
      </if>
      <if test="record.actionExecutionPath != null">
        action_execution_path = #{record.actionExecutionPath,jdbcType=INTEGER},
      </if>
      <if test="record.targetMtUserId != null">
        target_mt_user_id = #{record.targetMtUserId,jdbcType=BIGINT},
      </if>
      <if test="record.resumeTime != null">
        resume_time = #{record.resumeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkPointBatchId != null">
        check_point_batch_id = #{record.checkPointBatchId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_process_orchestration_execute_log
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      target_id = #{record.targetId,jdbcType=BIGINT},
      target_union_id = #{record.targetUnionId,jdbcType=VARCHAR},
      target_id_type = #{record.targetIdType,jdbcType=TINYINT},
      executor_id = #{record.executorId,jdbcType=VARCHAR},
      executor_id_type = #{record.executorIdType,jdbcType=TINYINT},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      group_member_id = #{record.groupMemberId,jdbcType=VARCHAR},
      member_name = #{record.memberName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      task_start_time = #{record.taskStartTime,jdbcType=TIMESTAMP},
      final_check_time = #{record.finalCheckTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      avatar = #{record.avatar,jdbcType=VARCHAR},
      action_sub_type = #{record.actionSubType,jdbcType=INTEGER},
      action_execution_path = #{record.actionExecutionPath,jdbcType=INTEGER},
      target_mt_user_id = #{record.targetMtUserId,jdbcType=BIGINT},
      resume_time = #{record.resumeTime,jdbcType=TIMESTAMP},
      check_point_batch_id = #{record.checkPointBatchId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO">
    update scrm_a_m_process_orchestration_execute_log
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=BIGINT},
      </if>
      <if test="targetUnionId != null">
        target_union_id = #{targetUnionId,jdbcType=VARCHAR},
      </if>
      <if test="targetIdType != null">
        target_id_type = #{targetIdType,jdbcType=TINYINT},
      </if>
      <if test="executorId != null">
        executor_id = #{executorId,jdbcType=VARCHAR},
      </if>
      <if test="executorIdType != null">
        executor_id_type = #{executorIdType,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="groupMemberId != null">
        group_member_id = #{groupMemberId,jdbcType=VARCHAR},
      </if>
      <if test="memberName != null">
        member_name = #{memberName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="taskStartTime != null">
        task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalCheckTime != null">
        final_check_time = #{finalCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="actionSubType != null">
        action_sub_type = #{actionSubType,jdbcType=INTEGER},
      </if>
      <if test="actionExecutionPath != null">
        action_execution_path = #{actionExecutionPath,jdbcType=INTEGER},
      </if>
      <if test="targetMtUserId != null">
        target_mt_user_id = #{targetMtUserId,jdbcType=BIGINT},
      </if>
      <if test="resumeTime != null">
        resume_time = #{resumeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkPointBatchId != null">
        check_point_batch_id = #{checkPointBatchId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO">
    update scrm_a_m_process_orchestration_execute_log
    set app_id = #{appId,jdbcType=VARCHAR},
      process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      target_id = #{targetId,jdbcType=BIGINT},
      target_union_id = #{targetUnionId,jdbcType=VARCHAR},
      target_id_type = #{targetIdType,jdbcType=TINYINT},
      executor_id = #{executorId,jdbcType=VARCHAR},
      executor_id_type = #{executorIdType,jdbcType=TINYINT},
      group_id = #{groupId,jdbcType=VARCHAR},
      group_member_id = #{groupMemberId,jdbcType=VARCHAR},
      member_name = #{memberName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      task_start_time = #{taskStartTime,jdbcType=TIMESTAMP},
      final_check_time = #{finalCheckTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      avatar = #{avatar,jdbcType=VARCHAR},
      action_sub_type = #{actionSubType,jdbcType=INTEGER},
      action_execution_path = #{actionExecutionPath,jdbcType=INTEGER},
      target_mt_user_id = #{targetMtUserId,jdbcType=BIGINT},
      resume_time = #{resumeTime,jdbcType=TIMESTAMP},
      check_point_batch_id = #{checkPointBatchId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_process_orchestration_execute_log
    (app_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
      target_id, target_union_id, target_id_type, executor_id, executor_id_type, group_id, 
      group_member_id, member_name, status, remark, task_start_time, final_check_time, 
      add_time, update_time, avatar, action_sub_type, action_execution_path, target_mt_user_id, 
      resume_time, check_point_batch_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.processOrchestrationId,jdbcType=BIGINT}, 
        #{item.processOrchestrationVersion,jdbcType=VARCHAR}, #{item.processOrchestrationNodeId,jdbcType=BIGINT}, 
        #{item.targetId,jdbcType=BIGINT}, #{item.targetUnionId,jdbcType=VARCHAR}, #{item.targetIdType,jdbcType=TINYINT}, 
        #{item.executorId,jdbcType=VARCHAR}, #{item.executorIdType,jdbcType=TINYINT}, #{item.groupId,jdbcType=VARCHAR}, 
        #{item.groupMemberId,jdbcType=VARCHAR}, #{item.memberName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.taskStartTime,jdbcType=TIMESTAMP}, #{item.finalCheckTime,jdbcType=TIMESTAMP}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.avatar,jdbcType=VARCHAR}, 
        #{item.actionSubType,jdbcType=INTEGER}, #{item.actionExecutionPath,jdbcType=INTEGER}, 
        #{item.targetMtUserId,jdbcType=BIGINT}, #{item.resumeTime,jdbcType=TIMESTAMP}, 
        #{item.checkPointBatchId,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>