package com.sankuai.scrm.core.service.couponIntegration.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Strategist_Coupon_Info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StrategistCouponInfo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: coupon_group_id
     *   说明: 券批次id
     */
    private String couponGroupId;

    /**
     *   字段: app_id
     *   说明: 业务线
     */
    private String appId;

    /**
     *   字段: creation_scene
     *   说明: 创建场景：0-手动上传、1-实时任务、2-定时/周期任务、3-群欢迎语、4-渠道活码欢迎语、5-群/好友裂变活动、6-抽奖裂变活动、7-智能客服
     */
    private Integer creationScene;

    /**
     *   字段: scene_detail
     *   说明: 任务ID
     */
    private String sceneDetail;

    /**
     *   字段: function_module
     *   说明: 功能模块：0-优惠券管理、1-自动化营销、2-群活码、群模版、3-好友活码、4-裂变活动、5-抽奖裂变活动、6-智能客服
     */
    private Integer functionModule;

    /**
     *   字段: status
     *   说明: 状态：1-统计中、2-暂停统计、3-已删除
     */
    private Integer status;

    /**
     *   字段: task_start_time
     *   说明: 任务绑定时间
     */
    private Date taskStartTime;

    /**
     *   字段: create_time
     *   说明: 添加时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}