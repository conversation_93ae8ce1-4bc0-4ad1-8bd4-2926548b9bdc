package com.sankuai.scrm.core.service.aigc.util;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoplist.search.intervention.dto.SequenceDTO;
import com.sankuai.dzshoplist.search.intervention.dto.UserTrackDTO;
import com.sankuai.scrm.core.service.aigc.service.config.UserActionScoreConfig;
import com.sankuai.scrm.core.service.aigc.service.dto.UserActionScoreConfigDTO;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UserActionScoreUtilGetCycleDaysTest {

    @InjectMocks
    private UserActionScoreUtil userActionScoreUtil;

    private Method getCycleDaysMethod;

    private Class<?> userActionClass;

    @BeforeEach
    public void setup() throws Exception {
        // Get the UserAction class
        userActionClass = Class.forName("com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil$UserAction");
        // Get the getCycleDays method
        getCycleDaysMethod = UserActionScoreUtil.class.getDeclaredMethod("getCycleDays", userActionClass, UserActionScoreConfigDTO.class);
        getCycleDaysMethod.setAccessible(true);
    }

    private UserActionScoreConfigDTO createScoreRuleDTO() {
        UserActionScoreConfigDTO dto = new UserActionScoreConfigDTO();
        Map<Long, Integer> cat2CycleDays = new HashMap<>();
        cat2CycleDays.put(1L, 14);
        dto.setCat2CycleDays(cat2CycleDays);
        Map<Long, Integer> cat1CycleDays = new HashMap<>();
        cat1CycleDays.put(1L, 14);
        dto.setCat1CycleDays(cat1CycleDays);
        Map<Long, Integer> cat0CycleDays = new HashMap<>();
        cat0CycleDays.put(1L, 14);
        dto.setCat0CycleDays(cat0CycleDays);
        dto.setDefaultCycleDays(14);
        return dto;
    }

    /**
     * Test when cat2Id is in cat2CycleDays map
     */
    @Test
    public void testGetCycleDaysCat2IdInCat2CycleDays() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(1L);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        assertEquals(14, result);
    }

    /**
     * Test when cat2Id is not in cat2CycleDays map but cat1Id is in cat1CycleDays map
     */
    @Test
    public void testGetCycleDaysCat2IdNotInCat2CycleDaysButCat1IdInCat1CycleDays() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat1Id").invoke(mockAction)).thenReturn(1L);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        assertEquals(14, result);
    }

    /**
     * Test when cat2Id and cat1Id are not in their respective maps but cat0Id is in cat0CycleDays map
     */
    @Test
    public void testGetCycleDaysCat2IdAndCat1IdNotInCat2CycleDaysAndCat1CycleDaysButCat0IdInCat0CycleDays() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat1Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat0Id").invoke(mockAction)).thenReturn(1L);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        assertEquals(14, result);
    }

    /**
     * Test when none of the category IDs are in their respective maps
     */
    @Test
    public void testGetCycleDaysCat2IdAndCat1IdAndCat0IdNotInCat2CycleDaysAndCat1CycleDaysAndCat0CycleDays() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat1Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat0Id").invoke(mockAction)).thenReturn(null);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        assertEquals(14, result);
    }

    /**
     * Test when none of the category IDs are in their respective maps but defaultCycleDays is set to a different value
     */
    @Test
    public void testGetCycleDaysCat2IdAndCat1IdAndCat0IdNotInCat2CycleDaysAndCat1CycleDaysAndCat0CycleDaysButDefaultCycleDaysNotNull() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat1Id").invoke(mockAction)).thenReturn(null);
        when(mockAction.getClass().getMethod("getCat0Id").invoke(mockAction)).thenReturn(null);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        scoreRuleDTO.setDefaultCycleDays(10);
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        assertEquals(10, result);
    }

    /**
     * Test when cat2Id is not in map but has a different value
     */
    @Test
    public void testGetCycleDaysCat2IdNotInMap() throws Throwable {
        // arrange
        Object mockAction = mock(userActionClass);
        // Different from the one in map
        when(mockAction.getClass().getMethod("getCat2Id").invoke(mockAction)).thenReturn(2L);
        when(mockAction.getClass().getMethod("getCat1Id").invoke(mockAction)).thenReturn(1L);
        UserActionScoreConfigDTO scoreRuleDTO = createScoreRuleDTO();
        // act
        int result = (int) getCycleDaysMethod.invoke(userActionScoreUtil, mockAction, scoreRuleDTO);
        // assert
        // Should use cat1Id's value
        assertEquals(14, result);
    }
}
