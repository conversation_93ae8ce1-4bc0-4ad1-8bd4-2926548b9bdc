package com.sankuai.scrm.core.service.pchat.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_Account_Cluster_Handle_Log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxAccountClusterHandleLog {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: handle_type
     *   说明: 操作类型：1:删除实名号、2:更改机器人可用状态、3:增加实名号组、4、修改实名号组名称、5:调整实名号所属分组
     */
    private Integer handleType;

    /**
     *   字段: handler
     *   说明: 处理人的mis号
     */
    private String handler;

    /**
     *   字段: handle_object_type
     *   说明: 操作类型：1:实名号、2实名号组
     */
    private Integer handleObjectType;

    /**
     *   字段: object
     *   说明: 机器人序列号或蔟序列号
     */
    private String object;

    /**
     *   字段: object_origin_info
     *   说明: 操作明显记录
     */
    private String objectOriginInfo;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: tenant_id
     *   说明: 租户id
     */
    private Long tenantId;

    /**
     *   字段: service_type
     *   说明: 1 全案服务 2 SaaS服务
     */
    private Integer serviceType;
}