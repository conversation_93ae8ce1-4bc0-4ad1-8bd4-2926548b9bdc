package com.sankuai.scrm.core.service.user.crane;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.srcm.user.score.BehaviorDTO;
import com.sankuai.dz.srcm.user.score.CalcUserDataHolder;
import com.sankuai.dz.srcm.user.score.EntityMaxMinIdDTO;
import com.sankuai.dz.srcm.user.score.UserTagScoreCalcTaskConfig;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.scrm.core.service.pchat.service.WebcastService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.domain.ScrmUserGrowthYimeiLiveUserTagScoreDomainService;
import com.sankuai.scrm.core.service.user.domain.score.ProductCategoryQueryService;
import com.sankuai.scrm.core.service.user.domain.score.ScoreTraceLogService;
import com.sankuai.scrm.core.service.user.domain.score.data.BehaviorQueryService;
import com.sankuai.scrm.core.service.user.domain.score.strategy.CalcScoreFactory;
import com.sankuai.scrm.core.service.user.domain.tag.NonLiveActionUserDataDomainService;
import com.sankuai.scrm.core.service.user.domain.tag.NonLiveActionUserDataEsDocService;
import com.sankuai.scrm.core.service.user.domain.tag.ProductBehaviorTagService;
import com.sankuai.scrm.core.service.user.domain.tag.UserTagEsStoreDomainService;
import com.sankuai.scrm.core.service.user.util.OneLimiterRetryUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmUserTagScoreCalcTaskTest {

    @Mock(lenient = true)
    private ScrmUserGrowthYimeiLiveUserTagScoreDomainService scrmUserGrowthYimeiLiveUserTagScoreDomainService;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @Mock(lenient = true)
    private UserTagEsStoreDomainService userTagEsStoreDomainService;

    @Mock(lenient = true)
    private Logger log;

    @InjectMocks
    private ScrmUserTagScoreCalcTask scrmUserTagScoreCalcTask;

    private final StoreKey storeKey = new StoreKey("scrm_init_es_user_tag", "offset");

    @Mock(lenient = true)
    private NonLiveActionUserDataDomainService nonLiveActionUserDataDomainService;

    @Mock(lenient = true)
    private NonLiveActionUserDataEsDocService nonLiveActionUserDataEsDocService;
    @Mock(lenient = true)
    private WebcastService webcastService;

    private UserTagScoreCalcTaskConfig config;
    @Mock(lenient = true)
    private CalcScoreFactory calcScoreFactory;
    @Mock(lenient = true)
    private List<BehaviorQueryService> behaviorQueryServiceList;
    @Mock(lenient = true)
    private ScoreTraceLogService scoreTraceLogService;
    @Mock(lenient = true)
    private ProductBehaviorTagService productBehaviorTagService;
    @Mock(lenient = true)
    private ProductCategoryQueryService productCategoryQueryService;
    @Mock(lenient = true)
    private OneLimiterRetryUtils oneLimiterRetryUtils;


    @BeforeEach
    public void setUp() {
        config = new UserTagScoreCalcTaskConfig();
        config.setBatchQuerySize(100);
        config.setOverdueDays(8);
    }

    // 辅助方法 - 创建用户标签
    private ScrmUserGrowthYimeiLiveUserTag createUserTag(Long id, String projectId, String unionId) {
        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setId(id);
        tag.setProjectId(projectId);
        tag.setUnionId(unionId);
        return tag;
    }

    private ScrmUserGrowthYimeiLiveUserTag createUserTag(Long id, String projectId) {
        return createUserTag(id, projectId, null);
    }


    /**
     * 测试配置为空时方法提前返回
     */
    @Test
    public void testCalcUserTagScore_ConfigNull_EarlyReturn() throws Throwable {
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(null);
        // act
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).userTagScoreCalcTaskConfig();
        verifyNoMoreInteractions(scrmUserGrowthYimeiLiveUserTagScoreDomainService);
    }

    /**
     * 测试没有用户标签数据时正常结束
     */
    @Test
    public void testCalcUserTagScore_NoUserTags_CompletesNormally() throws Throwable {
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(Collections.emptyList());
        // act
        when(webcastService.queryUnfinishedWebcastV2(any())).thenReturn(Collections.emptyList());
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt());
    }

    /**
     * 测试单批次数据处理
     */
    @Test
    public void testCalcUserTagScore_SingleBatch_ProcessesCorrectly() throws Throwable {
        // arrange
        ScrmUserGrowthYimeiLiveUserTag tag = createUserTag(1L, "project1");
        tag.setUnionId("unionId1");
        List<ScrmUserGrowthYimeiLiveUserTag> userTags = Collections.singletonList(tag);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(userTags);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByProjectId(eq("project1"), any(), any(), anyInt(), anyInt())).thenReturn(Collections.singletonList(tag)).thenReturn(Collections.emptyList());
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByUserInfo(eq("project1"), eq("unionId1"), isNull())).thenReturn(Collections.singletonList(tag));
        List<LiveRoomInfo> liveRoomInfos = buildLiveRoomInfos();
        when(webcastService.queryUnfinishedWebcastV2(any())).thenReturn(liveRoomInfos);
        // act
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt());
    }

    private List<LiveRoomInfo> buildLiveRoomInfos() {
        List<LiveRoomInfo> liveRoomInfos = new ArrayList<>();
        LiveRoomInfo liveRoomInfo = new LiveRoomInfo();
        liveRoomInfo.setLiveId("project1");
        liveRoomInfos.add(liveRoomInfo);
        return liveRoomInfos;
    }

    /**
     * 测试多批次数据处理
     */
    @Test
    public void testCalcUserTagScore_MultipleBatches_ProcessesAll() throws Throwable {
        // arrange
        config.setBatchQuerySize(2);
        List<ScrmUserGrowthYimeiLiveUserTag> firstBatch = Arrays.asList(createUserTag(1L, "project1", "unionId1"), createUserTag(2L, "project2", "unionId2"));
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(firstBatch);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByProjectId(anyString(), any(), any(), anyInt(), anyInt())).thenReturn(Collections.singletonList(firstBatch.get(0))).thenReturn(Collections.singletonList(firstBatch.get(1))).thenReturn(Collections.emptyList());
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByUserInfo(anyString(), anyString(), isNull())).thenReturn(Collections.singletonList(firstBatch.get(0)));
        // act
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt());
    }

    /**
     * 测试重复项目ID处理
     */
    @Test
    public void testCalcUserTagScore_DuplicateProjectIds_SkipsDuplicates() throws Throwable {
        // arrange
        // 重复项目ID
        List<ScrmUserGrowthYimeiLiveUserTag> // 重复项目ID
                // 重复项目ID
                // 重复项目ID
                userTags = Arrays.asList(createUserTag(1L, "project1", "unionId1"), createUserTag(2L, "project1", "unionId2"));
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(userTags);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByProjectId(eq("project1"), any(), any(), anyInt(), anyInt())).thenReturn(Arrays.asList(userTags.get(0), userTags.get(1))).thenReturn(Collections.emptyList());
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByUserInfo(eq("project1"), anyString(), isNull())).thenReturn(Collections.singletonList(userTags.get(0)));
        when(webcastService.queryUnfinishedWebcastV2(any())).thenReturn(buildLiveRoomInfos());
        // act
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt());
    }

    /**
     * 测试计算天数配置影响时间范围
     */
    @Test
    public void testCalcUserTagScore_CalcDaysAffectsTimeRange() throws Throwable {
        // arrange
        config.setOverdueDays(5);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(Collections.emptyList());
        when(webcastService.queryUnfinishedWebcastV2(any())).thenReturn(Collections.emptyList());
        // act
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt());
    }

    @Test
    public void testCalcUserTagScore_NoUserTags_CompletesNormallyV2() throws Throwable {
        UserTagScoreCalcTaskConfig config = new UserTagScoreCalcTaskConfig();
        config.setQueryLiveWay(UserTagScoreCalcTaskConfig.LIVE_WAY_NOT_END);
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTag(any(), any(), isNull(), eq(Long.MAX_VALUE), anyInt())).thenReturn(Collections.emptyList());
        // act
        when(webcastService.queryUnfinishedWebcastV2(any())).thenReturn(Collections.emptyList());
        scrmUserTagScoreCalcTask.calcUserTagScore();
        // assert
        verify(webcastService, times(1)).queryUnfinishedWebcastV2(any());
    }

    @Test
    public void testCalcOneUserTagScore() {
        List<ScrmUserGrowthYimeiLiveUserTag> userTags = getScrmUserGrowthYimeiLiveUserTags();
        when(calcScoreFactory.calc(any())).thenReturn(1);
        scrmUserTagScoreCalcTask.calcOneUserTagScore(CalcUserDataHolder.<ScrmUserGrowthYimeiLiveUserTag>builder()
                .config(config)
                .userTags(userTags)
                .build());
        verify(calcScoreFactory, times(2)).calc(any());

    }

    private static List<ScrmUserGrowthYimeiLiveUserTag> getScrmUserGrowthYimeiLiveUserTags() {
        List<ScrmUserGrowthYimeiLiveUserTag> userTags = new ArrayList<>();
        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setProjectId("123");
        tag.setUnionId("123");
        tag.setWxId("123");
        userTags.add(tag);
        return userTags;
    }

    @Test
    public void testSaveScore() {

        scrmUserTagScoreCalcTask.saveScore(CalcUserDataHolder.<ScrmUserGrowthYimeiLiveUserTag>builder()
                .config(config)
                .userTags(getScrmUserGrowthYimeiLiveUserTags())
                .taskId("123")
                .build(), 1, 1, 1);
        verify(userTagEsStoreDomainService, times(1)).writeScore(any(), any());
    }

    @Test
    public void testFillProductCategory() {
        List<BehaviorDTO> behaviorDTOS = new ArrayList<>();
        BehaviorDTO behaviorDTO = new BehaviorDTO();
        behaviorDTO.setProductId(123L);
        behaviorDTO.setProductType(1);
        behaviorDTOS.add(behaviorDTO);
        Map<Long, String> productCategoryMap = new HashMap<>();
        productCategoryMap.put(123L, "test");
        when(productCategoryQueryService.queryCategoryByDealGroupIds(anySet(), any())).thenReturn(productCategoryMap);

        scrmUserTagScoreCalcTask.fillProductCategory(behaviorDTOS);
        Assertions.assertEquals(behaviorDTOS.get(0).getProductCategory(), "test");
    }

    @Test
    public void testSerialCalcUserTagScore() {
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByUserInfo(anyString(), anyString(), anyString())).thenReturn(getScrmUserGrowthYimeiLiveUserTags());
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        when(calcScoreFactory.calc(any())).thenReturn(1);
        scrmUserTagScoreCalcTask.serialCalcUserTagScore("123", "123", "123");
        verify(calcScoreFactory, times(2)).calc(any());
    }

    @Test
    public void testSerialCalcLiveScore() {
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.userTagScoreCalcTaskConfig()).thenReturn(config);
        List<EntityMaxMinIdDTO> entityMaxMinIdDTOS = new ArrayList<>();
        EntityMaxMinIdDTO entityMaxMinIdDTO = new EntityMaxMinIdDTO();
        entityMaxMinIdDTO.setMaxId(1L);
        entityMaxMinIdDTO.setMinId(1L);
        entityMaxMinIdDTOS.add(entityMaxMinIdDTO);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCustomerChangeStatByProjectId(any(), any(), any())).thenReturn(entityMaxMinIdDTOS);
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByProjectId(any(), any(), any(), anyInt(), anyInt())).thenReturn(getScrmUserGrowthYimeiLiveUserTags(), new ArrayList<>());
        when(scrmUserGrowthYimeiLiveUserTagScoreDomainService.queryCalcScoreUserTagByUserInfo(any(), any(), any())).thenReturn(getScrmUserGrowthYimeiLiveUserTags());
        scrmUserTagScoreCalcTask.serialCalcLiveScore("123");
        verify(scrmUserGrowthYimeiLiveUserTagScoreDomainService, times(1)).queryCustomerChangeStatByProjectId(any(), any(), any());
    }
}
