package com.sankuai.scrm.core.service.user.domain;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.pchat.domain.BizIdentificationService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.service.LockService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiUserPreferInfo;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserGrowthYimeiUserPreferInfoExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthYimeiUserPreferInfoMapper;
import com.sankuai.scrm.core.service.util.SwitchUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PrivateUserDataHandleDomainServiceUpdateUserPurchasingTagInCurrentConsultantTest {

    @Mock(lenient = true)
    private ScrmUserGrowthYimeiUserPreferInfoMapper yimeiUserPreferInfoMapper;

    @Mock(lenient = true)
    private ScrmPersonalWxGroupManageDomainService wxGroupManageDomainService;

    @Mock(lenient = true)
    private BizIdentificationService bizIdentificationService;

    @Mock(lenient = true)
    private LockService lockService;

    @InjectMocks
    private PrivateUserDataHandleDomainService service;

    private List<ScrmUserGrowthYimeiLiveUserTag> tagList;

    private final long testUserId = 12345L;

    private final String testUnionId = "test-union-id";

    private final long testConsultantTaskId = 67890L;

    private final String testProjectId = "test-project";

    private final int testTagId = 1;

    private final String testWxId = "test-wx-id";

    private final String testAppId = "test-app-id";

    @BeforeEach
    public void setUp() {
        tagList = new ArrayList<>();
        // Create a valid tag with all required fields
        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setUserId(testUserId);
        tag.setUnionId(testUnionId);
        tag.setConsultantTaskId(testConsultantTaskId);
        tag.setProjectId(testProjectId);
        tag.setTagId(testTagId);
        tag.setWxId(testWxId);
        tagList.add(tag);
        // Mock wxGroupManageDomainService behavior
        when(wxGroupManageDomainService.queryGroupMemberWxIdByUnionId(anyString(), anyString())).thenReturn(testWxId);
        // Mock bizIdentificationService behavior
        when(bizIdentificationService.getAppIdByLiveId(anyString())).thenReturn(testAppId);
        // Mock lockService behavior
        doAnswer(invocation -> {
            Consumer<?> hasLockConsumer = invocation.getArgument(1);
            if (hasLockConsumer != null) {
                hasLockConsumer.accept(null);
            }
            return null;
        }).when(lockService).usingLock10s(anyString(), any(), any());
    }

    /**
     * Test when no preference info found - should return early
     */
    @Test
    public void testUpdateUserPurchasingTagInCurrentConsultant_NoPreferenceInfo() throws Throwable {
        // arrange
        when(yimeiUserPreferInfoMapper.selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        service.updateUserPurchasingTagInCurrentConsultant(tagList, testUserId);
        // assert
        verify(yimeiUserPreferInfoMapper).selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class));
        verifyNoMoreInteractions(wxGroupManageDomainService, bizIdentificationService);
    }

    /**
     * Test when preference info list contains null - should return early
     */
    @Test
    public void testUpdateUserPurchasingTagInCurrentConsultant_NullPreferenceInfo() throws Throwable {
        // arrange
        List<ScrmUserGrowthYimeiUserPreferInfo> prefList = new ArrayList<>();
        prefList.add(null);
        when(yimeiUserPreferInfoMapper.selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class))).thenReturn(prefList);
        // act
        service.updateUserPurchasingTagInCurrentConsultant(tagList, testUserId);
        // assert
        verify(yimeiUserPreferInfoMapper).selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class));
        verifyNoMoreInteractions(wxGroupManageDomainService, bizIdentificationService);
    }

    /**
     * Test with valid preference info and tags - should process successfully
     */
    @Test
    public void testUpdateUserPurchasingTagInCurrentConsultant_WithValidPreferenceInfo() throws Throwable {
        // arrange
        ScrmUserGrowthYimeiUserPreferInfo prefInfo = new ScrmUserGrowthYimeiUserPreferInfo();
        prefInfo.setPurchasingLevel("HIGH");
        prefInfo.setInterestProjects("PROJECT1,PROJECT2");
        List<ScrmUserGrowthYimeiUserPreferInfo> prefList = new ArrayList<>();
        prefList.add(prefInfo);
        when(yimeiUserPreferInfoMapper.selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class))).thenReturn(prefList);
        try (MockedStatic<SwitchUtil> switchUtilMockedStatic = mockStatic(SwitchUtil.class);
             MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class);
             MockedStatic<Environment> environmentMockedStatic = mockStatic(Environment.class)) {
            // Mock SwitchUtil behavior
            switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(any())).thenReturn(true);
            // Mock Lion behavior
            environmentMockedStatic.when(Environment::getAppName).thenReturn("test-app");
            lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(true);
            // act
            service.updateUserPurchasingTagInCurrentConsultant(tagList, testUserId);
            // assert
            verify(yimeiUserPreferInfoMapper).selectByExampleWithBLOBs(any(ScrmUserGrowthYimeiUserPreferInfoExample.class));
            verify(bizIdentificationService, times(2)).getAppIdByLiveId(anyString());
            verify(wxGroupManageDomainService, times(2)).queryGroupMemberWxIdByUnionId(anyString(), anyString());
            verify(lockService, atLeast(2)).usingLock10s(anyString(), any(), any());
        }
    }


}
