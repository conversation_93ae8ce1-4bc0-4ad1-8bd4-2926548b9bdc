package com.sankuai.scrm.core.service.workorder.merchant.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PacificCaseBO {

    private String owner;

    private Long caseId;

    private Date updateTime;

    @JsonProperty("工单状态")
    private String status;

}
