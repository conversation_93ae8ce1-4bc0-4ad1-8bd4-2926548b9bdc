package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmPersonalWxSendMQLogExample;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxSendMQLog;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxSendMQLogMapper extends MybatisBLOBsMapper<ScrmPersonalWxSendMQLog, ScrmPersonalWxSendMQLogExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxSendMQLog> list);
}