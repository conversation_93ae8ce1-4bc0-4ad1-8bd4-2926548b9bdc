package com.sankuai.scrm.core.service.dashboard.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionDataNarrowly implements Serializable {
    private String corpName;
    private Long transactionUserCountNarrowly;
    private Long transactionOrderCount;
    private BigDecimal gtv;
    private BigDecimal actualGtv;
    private BigDecimal writeOffGtv;
    private BigDecimal actualCheckGtv;
}
