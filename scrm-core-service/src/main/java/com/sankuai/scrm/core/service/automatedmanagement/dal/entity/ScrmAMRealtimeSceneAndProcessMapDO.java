package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: scrm_a_m_realtime_scene_and_process_map
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAMRealtimeSceneAndProcessMapDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: scene_id
     *   说明: 实时场景主键
     */
    private Long sceneId;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: scene_type
     *   说明: 实时场景类型：0未知 1persona
     */
    private Byte sceneType;

    /**
     *   字段: begin_time
     *   说明: 任务有效期开始时间
     */
    private Date beginTime;

    /**
     *   字段: end_time
     *   说明: 任务有效期结束时间
     */
    private Date endTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}