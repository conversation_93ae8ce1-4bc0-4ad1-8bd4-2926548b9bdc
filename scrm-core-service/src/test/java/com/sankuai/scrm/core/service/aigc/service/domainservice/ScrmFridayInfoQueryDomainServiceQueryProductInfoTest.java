package com.sankuai.scrm.core.service.aigc.service.domainservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationProductItemsProductTypeEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.scrm.core.service.aigc.friday.dto.ScrmFridayDealGroupInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryGeneralProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductInfoService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmFridayInfoQueryDomainServiceQueryProductInfoTest {

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private DealGroupQueryService dealGroupQueryService;

    @Mock
    private ProductInfoService productInfoService;

    @InjectMocks
    private ScrmFridayInfoQueryDomainService scrmFridayInfoQueryDomainService;

    private static final String APP_ID = "test_app_id";

    private static final Long PRODUCT_ID = 123456L;

    private static final String EXPECTED_JSON = "{\"test\":\"value\"}";

    /**
     * Test querying product info for BULK_ORDER type with successful response
     */
    @Test
    public void testQueryProductInfoForBulkOrderTypeSuccess() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        List<DealGroupDTO> dealGroupList = new ArrayList<>();
        DealGroupDTO dealGroupDTO = createMockDealGroupDTO();
        dealGroupList.add(dealGroupDTO);
        queryResult.setList(dealGroupList);
        mockResponse.setData(queryResult);
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(mockResponse);
        try (MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toStr(any(ScrmFridayDealGroupInfoDTO.class))).thenReturn(EXPECTED_JSON);
            // act
            String actualResult = scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
            // assert
            assertEquals(EXPECTED_JSON, actualResult);
        }
    }

    /**
     * Test querying product info for GENERAL type with successful response
     */
    @Test
    public void testQueryProductInfoForGeneralTypeSuccess() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        QueryGeneralProductInfoDTO generalProductInfoDTO = new QueryGeneralProductInfoDTO();
        generalProductInfoDTO.setProductName("Test Product");
        generalProductInfoDTO.setSalePrice("100.00");
        generalProductInfoDTO.setMarketPrice("120.00");
        Map<Long, QueryGeneralProductInfoDTO> productInfoMap = new HashMap<>();
        productInfoMap.put(PRODUCT_ID, generalProductInfoDTO);
        when(productInfoService.batchGetGeneralProductsInfo(anyList())).thenReturn(productInfoMap);
        try (MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toStr(any(QueryGeneralProductInfoDTO.class))).thenReturn(EXPECTED_JSON);
            // act
            String actualResult = scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
            // assert
            assertEquals(EXPECTED_JSON, actualResult);
        }
    }

    /**
     * Test when product type is null or unknown
     */
    @Test
    public void testQueryProductInfoWithUnknownProductType() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.UNKNOWN.getCode());
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
        });
        assertEquals("商品信息查询失败", exception.getMessage());
    }

    /**
     * Test when product info is empty for GENERAL type
     */
    @Test
    public void testQueryProductInfoWithEmptyGeneralProductInfo() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        Map<Long, QueryGeneralProductInfoDTO> productInfoMap = new HashMap<>();
        productInfoMap.put(PRODUCT_ID, null);
        when(productInfoService.batchGetGeneralProductsInfo(anyList())).thenReturn(productInfoMap);
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
        });
        assertEquals("商品信息查询失败", exception.getMessage());
    }

    /**
     * Test when an exception occurs during query for BULK_ORDER type
     */
    @Test
    public void testQueryProductInfoWithExceptionForBulkOrderType() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenThrow(new RuntimeException("Query failed"));
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
        });
        assertEquals("商品信息查询失败", exception.getMessage());
    }

    /**
     * Test when convertToDealGroupInfoDTO throws an exception due to null response
     */
    @Test
    public void testQueryProductInfoWhenConvertToDealGroupInfoDTOThrowsException() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        mockResponse.setData(null);
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(mockResponse);
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
        });
        assertEquals("商品信息查询失败", exception.getMessage());
    }

    /**
     * Test when convertToDealGroupInfoDTO throws an exception due to empty deal group list
     */
    @Test
    public void testQueryProductInfoWhenDealGroupListIsEmpty() throws Throwable {
        // arrange
        when(productManagementService.getProductTypeByProductId(APP_ID, PRODUCT_ID)).thenReturn(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(Collections.emptyList());
        mockResponse.setData(queryResult);
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(mockResponse);
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmFridayInfoQueryDomainService.queryProductInfo(APP_ID, PRODUCT_ID);
        });
        assertEquals("商品信息查询失败", exception.getMessage());
    }

    /**
     * Helper method to create a mock DealGroupDTO
     */
    private DealGroupDTO createMockDealGroupDTO() {
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        // Set basic info
        DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
        basicDTO.setBrandName("Test Brand");
        basicDTO.setTitle("Test Title");
        basicDTO.setTitleDesc("Test Description");
        dealGroupDTO.setBasic(basicDTO);
        // Set category
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setServiceType("Test Category");
        dealGroupDTO.setCategory(categoryDTO);
        // Set price
        PriceDTO priceDTO = new PriceDTO();
        priceDTO.setSalePrice("100.00");
        priceDTO.setMarketPrice("120.00");
        dealGroupDTO.setPrice(priceDTO);
        // Set display shop info
        DealGroupDisplayShopDTO displayShopDTO = new DealGroupDisplayShopDTO();
        displayShopDTO.setDpDisplayShopIds(Arrays.asList(1L, 2L, 3L));
        displayShopDTO.setMtDisplayShopIds(Arrays.asList(4L, 5L));
        dealGroupDTO.setDisplayShopInfo(displayShopDTO);
        // Set regions
        List<DealGroupRegionDTO> regions = new ArrayList<>();
        regions.add(new DealGroupRegionDTO());
        regions.add(new DealGroupRegionDTO());
        regions.add(new DealGroupRegionDTO());
        dealGroupDTO.setRegions(regions);
        // Set tags
        List<DealGroupTagDTO> tags = new ArrayList<>();
        DealGroupTagDTO tag1 = new DealGroupTagDTO();
        tag1.setTagName("Tag1");
        DealGroupTagDTO tag2 = new DealGroupTagDTO();
        tag2.setTagName("Tag2");
        tags.add(tag1);
        tags.add(tag2);
        dealGroupDTO.setTags(tags);
        return dealGroupDTO;
    }
}
