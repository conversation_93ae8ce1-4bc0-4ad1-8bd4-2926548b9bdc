package com.sankuai.dz.srcm.activity.fission.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ParticipateUserInfoListDTO implements Serializable {

    private String userId;
    private String nickName;
    private List<String> followInnerGroupMember;
    private List<String> groupList;
    private Integer completeStatus;
    private Integer stage;
    private Integer inviteNum;
    private Integer effectiveInviteNum;

}
