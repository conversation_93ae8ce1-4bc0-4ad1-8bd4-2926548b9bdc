package com.sankuai.dz.srcm.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户交易视图数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserTradeViewResult implements Serializable {
    
    // ==================== 核心标识字段 ====================
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务链
     */
    private Integer cChain;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 分区日期(yyyy-mm-dd)
     */
    private String partitionDate;
    
    /**
     * 统计口径维度
     */
    private String calDim;
    
    /**
     * 到综BUID（枚举值：1=LifeEvent，2=教育及母婴，3=休娱和丽人，4=医药健康）,-10000代表全部
     */
    private Integer bizlnBuCode;
    
    /**
     * 到综BUID名称
     */
    private String bizlnBuName;

    // ==================== 周期性统计数据 ====================
    
    /**
     * 周期性交易统计数据
     */
    private List<PeriodTradeStat> periodStats;

    // ==================== 最后交易相关信息 ====================
    
    /**
     * 最后美团商户ID
     */
    private Long lastMtShopId;
    
    /**
     * 最后点评商户ID
     */
    private Long lastDpShopId;
    
    /**
     * 最后POI ID
     */
    private Long lastPoiId;
    
    /**
     * 最后订单ID
     */
    private String lastOrderId;
    
    /**
     * 最后一次购买成功时间
     */
    private String lastBuySucTime;
    
    /**
     * 最后一次购买成功日期
     */
    private String lastBuySucDate;
    
    /**
     * 最后一次购买成功日期，距离分区日期的天数
     */
    private Integer last2tdBuySucDays;
    
    /**
     * 最后一笔订单金额（元）
     */
    private BigDecimal lastOrderAmt;
    
    /**
     * 最后一笔实际支付金额（元）
     */
    private BigDecimal lastActualPayAmt;
    
    /**
     * 倒数第二次购买成功时间
     */
    private String penultBuySucTime;
    
    /**
     * 最后商品组PK
     */
    private String lastProductgroupPk;
} 