package com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/15
 */
@Data
public class ScrmCrowdPackUpdateStrategyDetailDTO implements Serializable {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: pack_id
     *   说明: 人群包主键
     */
    private Long packId;

    /**
     *   字段: strategy_version
     *   说明: 更新策略版本，先更新新版本，再删旧版本, 不准备提供回滚能力
     */
    private String strategyVersion;

    /**
     *   字段: filter_field_id
     *   说明: 过滤字段id
     */
    private Long filterFieldId;

    /**
     *   字段: operator_id
     *   说明: 过滤条件运算符id
     */
    private Long operatorId;

    /**
     *   字段: param
     *   说明: 过滤条件参数
     */
    private List<String> param;

    /**
     *   字段: group_id
     *   说明: 条件分组 同一组内为and，不同组间为or
     */
    private Integer groupId;
}
