package com.sankuai.scrm.core.service.infrastructure.acl.dx;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.scrm.core.service.infrastructure.acl.dx.dto.DxRobotAccountDTO;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DxTokenAclService {

    private static final String DX_ROBOT_ACCOUNT_LION_KEY = "com.sankuai.medicalcosmetology.scrm.core.dx.robot.account";

    private static final String TOKEN_CATEGORY = "dx_robot_token";

    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    @Autowired
    private RedisStoreClient redisClient;

    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", timeout = 3000)
    private XmAuthServiceI.Iface xmAuthService;

    public String getToken(String appId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.infrastructure.acl.dx.DxTokenAclService.getToken(java.lang.String)");
        if (StringUtils.isEmpty(appId)) {
            throw new IllegalArgumentException("AppId不能为空");
        }
        DxRobotAccountDTO dxRobotAccount = getDxRobotAccount(appId);
        if (dxRobotAccount == null) {
            throw new RuntimeException("大象机器人配置不存在");
        }
        if (StringUtils.isEmpty(dxRobotAccount.getRobotKey())) {
            throw new RuntimeException("大象机器人账号为空");
        }
        if (StringUtils.isEmpty(dxRobotAccount.getRobotSecret())) {
            throw new RuntimeException("大象机器人密码为空");
        }
        String accessToken;
        try {
            StoreKey storeKey = new StoreKey(TOKEN_CATEGORY, dxRobotAccount.getRobotKey());
            accessToken = redisClient.get(storeKey);
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }
            AppAuthInfo appAuthInfo = new AppAuthInfo();
            appAuthInfo.setAppkey(dxRobotAccount.getRobotKey());
            appAuthInfo.setAppSecret(dxRobotAccount.getRobotSecret());
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            if (resp != null && resp.getStatus() != null && resp.getStatus().getCode() == 0) {
                accessToken = resp.getAccessToken().getToken();
                long expireTime = resp.getAccessToken().getExpireTime();
                int expireSeconds = (int) ((expireTime - FIVE_MINUTES - System.currentTimeMillis()) / 1000);
                redisClient.set(storeKey, accessToken, expireSeconds);
                return accessToken;
            } else {
                throw new RuntimeException("生成开放平台Token失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("获取大象机器人accessToken失败", e);
        }
    }

    private DxRobotAccountDTO getDxRobotAccount(String appId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.infrastructure.acl.dx.DxTokenAclService.getDxRobotAccount(java.lang.String)");
        if (StringUtils.isEmpty(appId)) {
            return null;
        }
        List<DxRobotAccountDTO> accountDTOS = Lion.getList(Environment.getAppName(), DX_ROBOT_ACCOUNT_LION_KEY,
                DxRobotAccountDTO.class);
        return accountDTOS.stream()
                .filter(accountDTO -> appId.equals(accountDTO.getAppId()))
                .findFirst()
                .orElse(null);
    }


}
