//package com.sankuai.scrm.core.service.tag.service;
//
//import static org.junit.Assert.*;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
//import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
//import com.sankuai.dz.srcm.pchat.dto.PagerList;
//import com.sankuai.dz.srcm.pchat.response.IdHolder;
//import com.sankuai.dz.srcm.tag.dto.GroupTagDTO;
//import com.sankuai.dz.srcm.tag.enums.GroupTagStatQueryTypeEnum;
//import com.sankuai.dz.srcm.tag.request.GroupTagDeleteRequest;
//import com.sankuai.dz.srcm.tag.request.GroupTagEditRequest;
//import com.sankuai.dz.srcm.tag.request.GroupTagSaveRequest;
//import com.sankuai.dz.srcm.tag.request.GroupTagStatDetailRequest;
//import com.sankuai.scrm.core.service.tag.dal.entity.GroupTag;
//import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagMapping;
//import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagSet;
//import com.sankuai.scrm.core.service.tag.dal.example.GroupTagExample;
//import com.sankuai.scrm.core.service.tag.dal.example.GroupTagSetExample;
//import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagMapper;
//import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagMappingMapper;
//import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagSetMapper;
//import com.sankuai.scrm.core.service.tag.domain.GroupTagDomainService;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.Date;
//import java.util.List;
//import org.junit.*;
//import org.junit.runner.RunWith;
//import org.junit.runner.RunWith;
//import org.junit.runner.RunWith.*;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class GroupTagServiceImplSaveGroupTagTest {
//
//    @InjectMocks
//    private GroupTagServiceImpl groupTagService;
//
//    @Mock
//    private GroupTagDomainService groupTagDomainService;
//
//    @Mock
//    private GroupTagMapper groupTagMapper;
//
//    @Mock
//    private GroupTagSetMapper groupTagSetMapper;
//
//    @Mock
//    private GroupTagMappingMapper groupTagMappingMapper;
//
//    private GroupTagDeleteRequest request;
//
//    private GroupTagSet groupTagSet;
//
//    @Before
//    public void setUp() {
//        request = new GroupTagDeleteRequest();
//        groupTagSet = new GroupTagSet();
//    }
//
//    private GroupTagSet createMockGroupTagSet() {
//        GroupTagSet groupTagSet = new GroupTagSet();
//        groupTagSet.setId(1L);
//        groupTagSet.setSerialNo("serialNo");
//        groupTagSet.setTagSetName("testSet");
//        groupTagSet.setTagCount(0);
//        groupTagSet.setDeleted(false);
//        groupTagSet.setAddTime(new Date());
//        groupTagSet.setUpdateTime(new Date());
//        return groupTagSet;
//    }
//
//    @Test
//    public void testSaveGroupTagRequestIsNull() throws Throwable {
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(null);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testSaveGroupTagGroupTagSetNameIsEmpty() throws Throwable {
//        GroupTagSaveRequest request = new GroupTagSaveRequest();
//        request.setGroupTagSetName("");
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testSaveGroupTagGroupTagListIsEmpty() throws Throwable {
//        GroupTagSaveRequest request = new GroupTagSaveRequest();
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Collections.emptyList());
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testSaveGroupTagGroupTagListContainsEmptyElement() throws Throwable {
//        GroupTagSaveRequest request = new GroupTagSaveRequest();
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Arrays.asList("test1", ""));
//        // Mock the behavior of groupTagSetMapper to simulate successful insertion
//        when(groupTagSetMapper.insertSelective(any(GroupTagSet.class))).thenAnswer(invocation -> {
//            GroupTagSet groupTagSet = invocation.getArgument(0);
//            groupTagSet.setId(1L);
//            return 1;
//        });
//        // Mock the behavior of groupTagMapper to simulate successful batch insertion
//        when(groupTagMapper.batchInsert(anyList())).thenReturn(1);
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(request);
//        assertEquals("success", response.getMsg());
//        // Verify that groupTagMapper.batchInsert was called with a list containing only one element
//        verify(groupTagMapper).batchInsert(argThat((List<GroupTag> list) -> list.size() == 1 && list.get(0).getTagName().equals("test1")));
//    }
//
//    @Test
//    public void testSaveGroupTagGroupTagSetNameExists() throws Throwable {
//        GroupTagSaveRequest request = new GroupTagSaveRequest();
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Arrays.asList("test1", "test2"));
//        GroupTagSet groupTagSet = new GroupTagSet();
//        groupTagSet.setId(1L);
//        when(groupTagDomainService.queryGroupTagSetByName(anyString())).thenReturn(groupTagSet);
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(request);
//        assertEquals("群标签组名称重复", response.getMsg());
//    }
//
//    @Test
//    public void testSaveGroupTagSuccess() throws Throwable {
//        GroupTagSaveRequest request = new GroupTagSaveRequest();
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Arrays.asList("test1", "test2"));
//        // Simulate no existing set with the same name
//        when(groupTagDomainService.queryGroupTagSetByName(anyString())).thenReturn(null);
//        // Mock the behavior of groupTagSetMapper to simulate successful insertion
//        when(groupTagSetMapper.insertSelective(any(GroupTagSet.class))).thenAnswer(invocation -> {
//            GroupTagSet groupTagSet = invocation.getArgument(0);
//            groupTagSet.setId(1L);
//            return 1;
//        });
//        // Mock the behavior of groupTagMapper to simulate successful batch insertion
//        when(groupTagMapper.batchInsert(anyList())).thenReturn(2);
//        RemoteResponse<IdHolder> response = groupTagService.saveGroupTag(request);
//        assertEquals("success", response.getMsg());
//        assertNotNull(response.getData());
//        assertNotNull(response.getData().getId());
//        assertEquals(Long.valueOf(1L), response.getData().getId());
//        // Verify that the groupTagDomainService.queryGroupTagSetByName was called
//        verify(groupTagDomainService).queryGroupTagSetByName(request.getGroupTagSetName());
//        // Verify that groupTagSetMapper.insertSelective was called
//        verify(groupTagSetMapper).insertSelective(any(GroupTagSet.class));
//        // Verify that groupTagMapper.batchInsert was called with a list of two elements
//        verify(groupTagMapper).batchInsert(argThat((List<GroupTag> list) -> list.size() == 2));
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportRequestIsNull() throws Throwable {
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(null);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportQueryTypeIsNull() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportQueryTypeIsZero() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(0);
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportQueryTypeIsUnknown() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(GroupTagStatQueryTypeEnum.UNKNOWN.getCode());
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportGroupTagStatDetailResponseListIsEmpty() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(GroupTagStatQueryTypeEnum.TAG.getCode());
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(request);
//        assertEquals("没有数据", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailExportNormalCase() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(GroupTagStatQueryTypeEnum.TAG.getCode());
//        // Mocking groupTagDomainService to return a non-empty PagerList<GroupTag>
//        PagerList<GroupTag> nonEmptyPagerList = PagerList.of(1L, Collections.singletonList(new GroupTag()));
//        when(groupTagDomainService.queryGroupTag(any(GroupTagStatDetailRequest.class))).thenReturn(nonEmptyPagerList);
//        RemoteResponse<String> response = groupTagService.groupTagStatDetailExport(request);
//        assertEquals("success", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagRequestIsNull() throws Throwable {
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(null);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetIdIsNull() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(null);
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetNameIsNull() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName(null);
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetNotExist() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Arrays.asList(new GroupTagDTO()));
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(null);
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetDeleted() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("test");
//        request.setGroupTagList(Arrays.asList(new GroupTagDTO()));
//        GroupTagSet groupTagSet = createMockGroupTagSet();
//        groupTagSet.setDeleted(true);
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(groupTagSet);
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetSerialNoNotMatch() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("test");
//        request.setGroupTagSetSerialNo("requestSerialNo");
//        request.setGroupTagList(Arrays.asList(new GroupTagDTO()));
//        GroupTagSet groupTagSet = createMockGroupTagSet();
//        groupTagSet.setSerialNo("differentSerialNo");
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(groupTagSet);
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagSetNameRepeat() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("newName");
//        request.setGroupTagSetSerialNo("serialNo");
//        request.setGroupTagList(Arrays.asList(new GroupTagDTO()));
//        GroupTagSet groupTagSet = createMockGroupTagSet();
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(groupTagSet);
//        when(groupTagDomainService.queryGroupTagSetByName(any())).thenReturn(new GroupTagSet());
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("群标签组名称重复", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagListEmptyAndGroupTagMapNotEmpty() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("test");
//        request.setGroupTagSetSerialNo("serialNo");
//        request.setGroupTagList(Collections.emptyList());
//        GroupTagSet groupTagSet = createMockGroupTagSet();
//        GroupTag groupTag = new GroupTag();
//        groupTag.setSerialNo("tagSerialNo");
//        groupTag.setTagName("existingTag");
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(groupTagSet);
//        when(groupTagDomainService.queryGroupTagByGroupSetSerialNo(any())).thenReturn(Collections.singletonList(groupTag));
//        doNothing().when(groupTagDomainService).updateGroupTagSet(any());
//        doNothing().when(groupTagDomainService).removeGroupTag(any());
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("success", response.getMsg());
//    }
//
//    @Test
//    public void testEditGroupTagGroupTagListNotEmptyAndGroupTagMapNotEmptyAndGroupTagSerialNoInRequest() throws Throwable {
//        GroupTagEditRequest request = new GroupTagEditRequest();
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetName("test");
//        request.setGroupTagSetSerialNo("serialNo");
//        GroupTagDTO groupTagDTO = new GroupTagDTO();
//        groupTagDTO.setGroupTagSerialNo("existingSerialNo");
//        groupTagDTO.setGroupTagName("updatedTag");
//        request.setGroupTagList(Arrays.asList(groupTagDTO));
//        GroupTagSet groupTagSet = createMockGroupTagSet();
//        GroupTag groupTag = new GroupTag();
//        groupTag.setSerialNo("existingSerialNo");
//        groupTag.setTagName("existingTag");
//        when(groupTagDomainService.queryGroupTagSetById(any())).thenReturn(groupTagSet);
//        when(groupTagDomainService.queryGroupTagByGroupSetSerialNo(any())).thenReturn(Collections.singletonList(groupTag));
//        doNothing().when(groupTagDomainService).updateGroupTagSet(any());
//        doNothing().when(groupTagDomainService).updateGroupTag(any());
//        RemoteResponse<IdHolder> response = groupTagService.editGroupTag(request);
//        assertEquals("success", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagRequestIsNull() throws Throwable {
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(null);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagGroupTagSetIdIsNull() throws Throwable {
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagGroupTagSetSerialNoIsEmpty() throws Throwable {
//        request.setGroupTagSetId(1L);
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagGroupTagSetNotExist() throws Throwable {
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetSerialNo("serialNo");
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagGroupTagSetDeleted() throws Throwable {
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetSerialNo("serialNo");
//        groupTagSet.setDeleted(true);
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagGroupTagSetSerialNoNotMatch() throws Throwable {
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetSerialNo("serialNo");
//        groupTagSet.setDeleted(false);
//        groupTagSet.setSerialNo("otherSerialNo");
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("群标签组不存在", response.getMsg());
//    }
//
//    @Test
//    public void testDeleteGroupTagSuccess() throws Throwable {
//        request.setGroupTagSetId(1L);
//        request.setGroupTagSetSerialNo("serialNo");
//        groupTagSet.setDeleted(false);
//        groupTagSet.setSerialNo("serialNo");
//        // Ensure the groupTagSet is returned by the mocked service
//        when(groupTagDomainService.queryGroupTagSetById(anyLong())).thenReturn(groupTagSet);
//        RemoteResponse<Boolean> response = groupTagService.deleteGroupTag(request);
//        assertEquals("success", response.getMsg());
//        assertEquals(true, response.getData());
//    }
//
//    @Test
//    public void testGroupTagStatDetailRequestIsNull() throws Throwable {
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(null);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailQueryTypeIsNull() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailQueryTypeIsZero() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(0);
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailQueryTypeIsUnknown() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(3);
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(request);
//        assertEquals("参数错误", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailQueryTypeIsTag() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(GroupTagStatQueryTypeEnum.TAG.getCode());
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(request);
//        // Adjusted expectation
//        assertEquals("success", response.getMsg());
//    }
//
//    @Test
//    public void testGroupTagStatDetailQueryTypeIsNotTag() throws Throwable {
//        GroupTagStatDetailRequest request = new GroupTagStatDetailRequest();
//        request.setQueryType(GroupTagStatQueryTypeEnum.SET.getCode());
//        PageRemoteResponse response = groupTagService.groupTagStatDetail(request);
//        // Adjusted expectation
//        assertEquals("success", response.getMsg());
//    }
//}
