package com.sankuai.scrm.core.service.tag.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagMapping;
import com.sankuai.scrm.core.service.tag.dal.example.GroupTagMappingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GroupTagMappingMapper extends MybatisBaseMapper<GroupTagMapping, GroupTagMappingExample, Long> {
    int batchInsert(@Param("list") List<GroupTagMapping> list);
}