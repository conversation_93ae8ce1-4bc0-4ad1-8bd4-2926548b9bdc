package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.AbstractConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/5/9
 */
@Component
public class ScrmProcessOrchestrationExecutePlanConverter extends AbstractConverter<ScrmProcessOrchestrationExecutePlanDTO,ScrmAmProcessOrchestrationExecutePlanDO> {

    @Override
    public ScrmProcessOrchestrationExecutePlanDTO convertToDTO(ScrmAmProcessOrchestrationExecutePlanDO resource) {
        if (resource == null) {
            return null;
        }
        ScrmProcessOrchestrationExecutePlanDTO result = new ScrmProcessOrchestrationExecutePlanDTO();
        result.setId(resource.getId());
        result.setProcessOrchestrationId(resource.getProcessOrchestrationId());
        result.setProcessOrchestrationVersion(resource.getProcessOrchestrationVersion());
        result.setProcessOrchestrationType(resource.getProcessOrchestrationType());
        result.setTaskStartTime(resource.getTaskStartTime());
        result.setStatus(resource.getStatus());
        return result;
    }

    @Override
    public ScrmAmProcessOrchestrationExecutePlanDO convertToDO(ScrmProcessOrchestrationExecutePlanDTO resource) {
        if (resource == null) {
            return null;
        }
        ScrmAmProcessOrchestrationExecutePlanDO result = new ScrmAmProcessOrchestrationExecutePlanDO();
        result.setId(resource.getId());
        result.setProcessOrchestrationId(resource.getProcessOrchestrationId());
        result.setProcessOrchestrationVersion(resource.getProcessOrchestrationVersion());
        result.setProcessOrchestrationType(resource.getProcessOrchestrationType());
        result.setTaskStartTime(resource.getTaskStartTime());
        result.setStatus(resource.getStatus());
        return result;
    }
}
