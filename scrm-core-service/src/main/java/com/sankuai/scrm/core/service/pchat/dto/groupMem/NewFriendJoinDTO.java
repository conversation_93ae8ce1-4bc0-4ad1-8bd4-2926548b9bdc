package com.sankuai.scrm.core.service.pchat.dto.groupMem;

import lombok.Data;

/**
 * 4502
 *
 * @Description 新成员入群回调接口（兼容PC）
 * <AUTHOR>
 * @Create On 2023/11/6 17:44
 * @Version v1.0.0
 */
@Data
public class NewFriendJoinDTO {
    /**
     * 群id
     */
    private String vcChatRoomId;

    /**
     * 群编号
     */
    private String vcChatRoomSerialNo;

    /**
     * 群人数
     */
    private Integer nMemberCount;

    /**
     * 1 关注群 0 未关注群
     */
    private Integer nChatRoomIsOpen;

    /**
     * 是否是企业微信群 true:是 false：否
     */


    private Boolean IsEnterpriseChatroom;

    private Member[] members;

}
