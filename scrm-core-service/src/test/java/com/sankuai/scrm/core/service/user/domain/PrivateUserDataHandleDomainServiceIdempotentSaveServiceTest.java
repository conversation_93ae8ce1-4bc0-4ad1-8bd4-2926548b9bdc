package com.sankuai.scrm.core.service.user.domain;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.pchat.service.LockService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthYimeiLiveUserTagMapper;
import com.sankuai.scrm.core.service.user.domain.tag.UserTagEsStoreDomainService;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import static org.mockito.Mockito.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/3/28 14:43
 * @Version v1.0.0
 */
@ExtendWith(MockitoExtension.class)
public class PrivateUserDataHandleDomainServiceIdempotentSaveServiceTest {
    @InjectMocks
    private PrivateUserDataHandleDomainService privateUserDataHandleDomainService;

    @Mock
    private ScrmUserGrowthYimeiLiveUserTagMapper userGrowthYimeiLiveUserTagMapper;

    @Mock
    private LockService lockService;
    @Mock
    private UserTagEsStoreDomainService userTagEsStoreDomainService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试idempotentSave方法，当开关关闭时直接保存标签
     */
    @Test
    public void testIdempotentSaveWithSwitchOff() {
        // arrange
        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        tag.setConsultantTaskId(1L);
        tag.setUserId(1L);
        tag.setTagTypeId(1);
        tag.setTagId(1);
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getBoolean(Environment.getAppName(), "user.tag.idempotent.save.switch", true)).thenReturn(false);
            // act
            invokePrivateMethod(tag);
            // assert
            verify(userGrowthYimeiLiveUserTagMapper, times(1)).insert(tag);
        }
    }

    /**
     * 测试idempotentSave方法，当开关打开时通过锁保存标签
     */
    @Test
    public void testIdempotentSaveWithSwitchOn() {
        // arrange
        ScrmUserGrowthYimeiLiveUserTag tag = new ScrmUserGrowthYimeiLiveUserTag();
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getBoolean(Environment.getAppName(), "user.tag.idempotent.save.switch", true)).thenReturn(true);
            doAnswer(new Answer<Void>() {
                @Override
                public Void answer(InvocationOnMock invocation) throws Throwable {
                    String input = invocation.getArgument(0);
                    // 模拟Lambda表达式的行为
                    System.out.println("Lambda executed with input: " + input);
                    invocation.callRealMethod();
                    return null;
                }
            }).when(lockService).usingLock10s(any(), any(), any());
            // act
            invokePrivateMethod(tag);
            // assert
            verify(lockService, times(1)).usingLock10s(anyString(), eq(null), any());
        }
    }

    private void invokePrivateMethod(ScrmUserGrowthYimeiLiveUserTag tag) {
        try {
            Method method = PrivateUserDataHandleDomainService.class.getDeclaredMethod("idempotentSave", ScrmUserGrowthYimeiLiveUserTag.class);
            method.setAccessible(true);
            method.invoke(privateUserDataHandleDomainService, tag);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
