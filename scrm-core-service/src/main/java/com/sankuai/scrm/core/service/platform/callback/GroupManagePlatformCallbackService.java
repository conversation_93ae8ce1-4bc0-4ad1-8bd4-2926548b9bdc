package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.platform.anno.NTypePlatformCallbackMappingAnno;


/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 17:22
 * @Version v1.0.0
 */
public interface GroupManagePlatformCallbackService extends PlatformCallbackService {

    /**
     * 管理员变动回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4510, desc = "管理员变动回调接口")
    RemoteResponse do4510(String strContext);

    /**
     * 机器人退群自动取消关注群回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4014, desc = "机器人退群自动取消关注群回调接口")
    RemoteResponse do4014(String strContext);

    /**
     * 用户入群申请回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4515, desc = "用户入群申请回调接口")
    RemoteResponse do4515(String strContext);

    /**
     * 机器人被踢出群回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4507, desc = "机器人被踢出群回调接口")
    RemoteResponse do4507(String strContext);

    /**
     * 创建群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4508, desc = "创建群回调接口（兼容PC）")
    RemoteResponse do4508(String strContext);

    /**
     * 设置管理员回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4509, desc = "设置管理员回调接口")
    RemoteResponse do4509(String strContext);

    /**
     * 开启群聊验证回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4011, desc = "开启群聊验证回调接口")
    RemoteResponse do4011(String strContext);

    /**
     * 查询群聊验证回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4013, desc = "查询群聊验证回调接口")
    RemoteResponse do4013(String strContext);

    /**
     * 修改群名称回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4002, desc = "修改群名称回调接口（兼容PC）")
    RemoteResponse do4002(String strContext);

    /**
     * 群内踢人回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4003, desc = "群内踢人回调接口（兼容PC）")
    RemoteResponse do4003(String strContext);

    /**
     * 群内发布公告回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4004, desc = "群内发布公告回调接口（兼容PC）")
    RemoteResponse do4004(String strContext);

    /**
     * 转让群主回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4006, desc = "转让群主回调接口")
    RemoteResponse do4006(String strContext);

    /**
     * 机器人主动退群回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4007, desc = "机器人主动退群回调接口（兼容PC）")
    RemoteResponse do4007(String strContext);

    /**
     * 机器人验证用户入群回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4516, desc = "机器人验证用户入群回调接口")
    RemoteResponse do4516(String strContext);

    /**
     * 获取群二维码回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4008, desc = "获取群二维码回调接口")
    RemoteResponse do4008(String strContext);

    /**
     * 获取群公告回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4005, desc = "获取群公告回调接口（兼容PC）")
    RemoteResponse do4005(String strContext);

    /**
     * 设置群内昵称回调接口（兼容PC）
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4009, desc = "设置群内昵称回调接口（兼容PC）")
    RemoteResponse do4009(String strContext);

    /**
     * 停用群二维码回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4518, desc = "停用群二维码回调接口")
    RemoteResponse do4518(String strContext);

    /**
     * 解散群聊回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4524, desc = "解散群聊回调接口")
    RemoteResponse do4524(String strContext);

    /**
     * 群被解散回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4525, desc = "群被解散回调")
    RemoteResponse do4525(String strContext);

    /**
     * 获取群信息接口（异步）回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4534, desc = "获取群信息接口（异步）回调")
    RemoteResponse do4534(String strContext);

    /**
     * 设置折叠群接口回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4543, desc = "设置折叠群接口回调")
    RemoteResponse do4543(String strContext);

    /**
     * 群聊被停用回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 9283, desc = "群聊被停用回调")
    RemoteResponse do9283(String strContext);

    /**
     * 调用该接口设置仅群主/管理可修改群名称开关回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4551, desc = "调用该接口设置仅群主/管理可修改群名称开关回调")
    RemoteResponse do4551(String strContext);
}

