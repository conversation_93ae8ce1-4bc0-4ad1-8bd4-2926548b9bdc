package com.sankuai.scrm.core.service.tag.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.response.IdHolder;
import com.sankuai.dz.srcm.tag.dto.GroupTagDTO;
import com.sankuai.dz.srcm.tag.enums.GroupTagStatQueryTypeEnum;
import com.sankuai.dz.srcm.tag.request.*;
import com.sankuai.dz.srcm.tag.response.*;
import com.sankuai.dz.srcm.tag.service.GroupTagService;
import com.sankuai.scrm.core.service.group.dal.babymapper.GroupInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.example.GroupInfoEntityExample;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.LoginAccessTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.pchat.utils.UUIDUtil;
import com.sankuai.scrm.core.service.tag.bo.GroupTagUploadResultBo;
import com.sankuai.scrm.core.service.tag.bo.GroupTagUploadTemplateBo;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTag;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagMapping;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagSet;
import com.sankuai.scrm.core.service.tag.dal.example.GroupTagMappingExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagMappingMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagSetMapper;
import com.sankuai.scrm.core.service.tag.domain.GroupTagDomainService;
import com.sankuai.scrm.core.service.tag.domain.GroupTagFileDomainService;
import com.sankuai.scrm.core.service.tag.domain.GroupTagPredTaskDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/10/9 15:22
 * @Version v1.0.0
 */
@Slf4j
@MdpPigeonServer
public class GroupTagServiceImpl implements GroupTagService {
    @Resource
    private GroupTagMapper groupTagMapper;
    @Resource
    private GroupTagSetMapper groupTagSetMapper;
    @Resource
    private GroupTagMappingMapper groupTagMappingMapper;
    @Resource
    private GroupTagDomainService groupTagDomainService;
    @Resource
    private GroupInfoEntityMapper groupInfoEntityMapper;
    @Resource
    private GroupTagPredTaskDomainService groupTagPredTaskDomainService;
    @Resource
    private GroupTagFileDomainService groupTagFileDomainService;
    @Resource
    private LoginAccessTokenAcl loginAccessTokenAcl;
    @Resource
    private CorpAppConfigRepository appConfigRepository;

    @Override
    public RemoteResponse<IdHolder> saveGroupTag(GroupTagSaveRequest request) {
        if (request == null || StringUtils.isBlank(request.getGroupTagSetName()) || CollectionUtil.isEmpty(request.getGroupTagList()) || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }

        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("saveGroupTag,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        List<String> groupTagListStr = request.getGroupTagList().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(groupTagListStr)) {
            return RemoteResponse.fail("参数错误");
        }
        GroupTagSet existGroupTagSet = groupTagDomainService.queryGroupTagSetByName(request.getAppId(), request.getGroupTagSetName());
        if (existGroupTagSet != null) {
            return RemoteResponse.fail("群标签组名称重复");
        }
        GroupTagSet groupTagSet = doSaveGroupTag(loginSsoUserName, request);

        return RemoteResponse.success(IdHolder.of(groupTagSet.getId()));
    }

    private GroupTagSet doSaveGroupTag(String creator, GroupTagSaveRequest request) {
        String groupTagSetSerialNo = UUIDUtil.uuid();
        int tagCount = saveGroupTag(request.getAppId(), creator, groupTagSetSerialNo, request.getGroupTagList());
        return saveGroupTagSet(creator, request, groupTagSetSerialNo, tagCount);
    }

    private int saveGroupTag(String appId, String creator, String groupTagSetSerialNo, List<String> groupTagList) {
        if (CollectionUtil.isEmpty(groupTagList)) {
            log.info("saveGroupTag,groupTagList is empty");
            return 0;
        }
        List<String> groupTagListStr = groupTagList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(groupTagListStr)) {
            log.info("saveGroupTag,groupTagList is empty");
            return 0;
        }
        List<GroupTag> saveGroupTagList = groupTagListStr.stream().map(tag -> {
            GroupTag groupTag = new GroupTag();
            groupTag.setTagName(tag);
            groupTag.setAddTime(new Date());
            groupTag.setUpdateTime(new Date());
            groupTag.setSerialNo(UUIDUtil.uuid());
            groupTag.setDeleted(false);
            groupTag.setTagSetSerialNo(groupTagSetSerialNo);
            groupTag.setCustomerCount(0);
            groupTag.setCreator(creator);
            groupTag.setAppId(appId);
            return groupTag;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(groupTagList)) {
            return groupTagMapper.batchInsert(saveGroupTagList);
        }
        return 0;
    }

    private GroupTagSet saveGroupTagSet(String creator, GroupTagSaveRequest request, String groupTagSetSerialNo, int tagCount) {
        GroupTagSet groupTagSet = new GroupTagSet();
        groupTagSet.setTagSetName(request.getGroupTagSetName());
        groupTagSet.setAddTime(new Date());
        groupTagSet.setUpdateTime(new Date());
        groupTagSet.setSerialNo(groupTagSetSerialNo);
        groupTagSet.setTagCount(tagCount);
        groupTagSet.setDeleted(false);
        groupTagSet.setCustomerCount(0);
        groupTagSet.setCreator(creator);
        groupTagSet.setAppId(request.getAppId());
        groupTagSetMapper.insertSelective(groupTagSet);
        return groupTagSet;
    }

    @Override
    public RemoteResponse<GroupTagUploadResponse> uploadGroupTag(GroupTagUploadRequest request) {
        if (request == null || StringUtils.isBlank(request.getFileKey()) || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("uploadGroupTag,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        List<GroupTagUploadTemplateBo> groupTemplateList = groupTagFileDomainService.parseFile(request.getFileKey());
        if (CollectionUtils.isEmpty(groupTemplateList)) {
            log.error("uploadGroupTag: Excel格式有误，解析有误");
            return RemoteResponse.fail("Excel格式有误，解析有误");
        }

        Map<String, List<GroupTagUploadTemplateBo>> groupTagSetList = groupTemplateList.stream().filter(g -> StringUtils.isNotBlank(g.getGroupTagSetName()) && StringUtils.isNotBlank(g.getGroupTagName())).collect(Collectors.groupingBy(GroupTagUploadTemplateBo::getGroupTagSetName));
        if (groupTagSetList.isEmpty()) {
            return RemoteResponse.fail("Excel格式有误，解析有误");
        }
        List<GroupTagSet> groupTagSets = groupTagDomainService.queryGroupTagSetByNames(request.getAppId(), new ArrayList<>(groupTagSetList.keySet()));
        Map<String, GroupTagSet> groupTagSetMap = groupTagSets.stream().collect(Collectors.toMap(GroupTagSet::getTagSetName, Function.identity(), (a, b) -> a));
        List<GroupTagUploadResultBo> resultBos = new ArrayList<>();
        GroupTagUploadResponse response = new GroupTagUploadResponse();
        response.setFailureCount(0);
        response.setSuccessCount(0);
        groupTagSetList.forEach((groupTagSetName, groupTagList) -> {

            GroupTagUploadResultBo resultBo = new GroupTagUploadResultBo();
            resultBo.setGroupTagSetName(groupTagSetName);
            resultBo.setResult("成功");
            List<String> groupTagNameListStr = Lists.newArrayList();
            groupTagList.forEach(g -> {
                String[] split = g.getGroupTagName().split(",");
                if (split.length > 0) {
                    groupTagNameListStr.addAll(Arrays.asList(split));
                }
            });
            resultBo.setGroupTagName(CollectionUtil.join(groupTagNameListStr, ","));
            resultBos.add(resultBo);
            if (groupTagSetMap.containsKey(groupTagSetName)) {
                log.info("上传的文件包含群组名，不处理");
                resultBo.setResult("失败");
                resultBo.setReason("群标签组名称已存在");
                response.setFailureCount(response.getFailureCount() + 1);
                return;
            }
            GroupTagSaveRequest saveRequest = new GroupTagSaveRequest();
            saveRequest.setGroupTagSetName(groupTagSetName);
            saveRequest.setGroupTagList(groupTagNameListStr);
            saveRequest.setAppId(request.getAppId());
            doSaveGroupTag(loginSsoUserName, saveRequest);
            response.setSuccessCount(response.getSuccessCount() + 1);
        });
        response.setUploadResultFilePath(generateResultUrl(resultBos));
        return RemoteResponse.success(response);
    }

    private String generateResultUrl(List<GroupTagUploadResultBo> resultBos) {

        List<List<String>> head = Lists.newArrayList();
        head.add(Lists.newArrayList("上传结果"));
        head.add(Lists.newArrayList("失败原因"));
        head.add(Lists.newArrayList("群标签组"));
        head.add(Lists.newArrayList("群标签"));
        String fileName = "上传结果明细" + System.currentTimeMillis() + ".xlsx";
        return groupTagDomainService.generalExcelUrl(fileName, head, buildRowData(resultBos));
    }


    private List<List<String>> buildRowData(List<GroupTagUploadResultBo> resultBos) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(resultBos)) {
            return Lists.newArrayList();
        }
        List<List<String>> rowData = Lists.newArrayList();
        resultBos.stream().filter(Objects::nonNull).forEach(record -> {
            List<String> row = Lists.newArrayList();
            row.add(record.getResult());
            row.add(record.getReason());
            row.add(record.getGroupTagSetName());
            row.add(record.getGroupTagName());
            rowData.add(row);
        });
        return rowData;
    }


    @Override
    public RemoteResponse<IdHolder> editGroupTag(GroupTagEditRequest request) {
        if (request == null || request.getGroupTagSetId() == null || StringUtils.isBlank(request.getGroupTagSetName()) || CollectionUtils.isEmpty(request.getGroupTagList()) || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        Set<String> tagSetNameDupSet = new HashSet<>();
        for (GroupTagDTO groupTagDTO : request.getGroupTagList()) {
            if (tagSetNameDupSet.contains(groupTagDTO.getGroupTagName())) {
                return RemoteResponse.fail("群标签名称重复");
            }
            tagSetNameDupSet.add(groupTagDTO.getGroupTagName());
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("editGroupTag,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        GroupTagSet groupTagSet = groupTagDomainService.queryGroupTagSetById(request.getGroupTagSetId());
        if (groupTagSet == null || Boolean.TRUE == groupTagSet.getDeleted() || !groupTagSet.getSerialNo().equals(request.getGroupTagSetSerialNo()) || !request.getAppId().equals(groupTagSet.getAppId())) {
            return RemoteResponse.fail("群标签组不存在");
        }
        if (!groupTagSet.getTagSetName().equals(request.getGroupTagSetName())) {
            GroupTagSet existGroupTagSet = groupTagDomainService.queryGroupTagSetByName(request.getAppId(), request.getGroupTagSetName());
            if (existGroupTagSet != null) {
                return RemoteResponse.fail("群标签组名称重复");
            }
            groupTagSet.setTagSetName(request.getGroupTagSetName());

        }
        AtomicInteger groupTagCount = new AtomicInteger(groupTagSet.getTagCount());

        List<GroupTag> groupTagList = groupTagDomainService.queryGroupTagByGroupSetSerialNo(groupTagSet.getSerialNo());
        Map<String, GroupTag> groupTagMap = groupTagList.stream().collect(Collectors.toMap(GroupTag::getTagName, Function.identity(), (a, b) -> a));
        Map<String, GroupTag> groupTagSerialNoMap = groupTagList.stream().collect(Collectors.toMap(GroupTag::getSerialNo, Function.identity(), (a, b) -> a));

        // 删除群标签
        if (CollectionUtil.isEmpty(request.getGroupTagList()) && CollectionUtil.isNotEmpty(groupTagMap)) {
            groupTagList.forEach(groupTag -> {
                groupTag.setUpdater(loginSsoUserName);
                groupTagDomainService.removeGroupTag(groupTag);
            });
            groupTagCount.set(0);
            groupTagSet.setUpdater(loginSsoUserName);
            groupTagDomainService.updateGroupTagSet(groupTagSet);
            return RemoteResponse.success(IdHolder.of(groupTagSet.getId()));
        } else if (CollectionUtil.isNotEmpty(request.getGroupTagList()) && CollectionUtil.isNotEmpty(groupTagMap)) {
            Map<String, GroupTagDTO> requestGroupTagSerialNoMap = request.getGroupTagList().stream().filter(tag -> StringUtils.isNotBlank(tag.getGroupTagSerialNo())).collect(Collectors.toMap(GroupTagDTO::getGroupTagSerialNo, Function.identity(), (a, b) -> a));
            Set<String> removedTagName = new HashSet<>();
            groupTagMap.forEach((tagName, groupTag) -> {
                if (!requestGroupTagSerialNoMap.containsKey(groupTag.getSerialNo())) {
                    groupTag.setUpdater(loginSsoUserName);
                    groupTagDomainService.removeGroupTag(groupTag);
                    groupTagCount.getAndDecrement();
                    removedTagName.add(tagName);
                }
            });
            if (CollectionUtil.isNotEmpty(removedTagName)) {
                removedTagName.forEach(groupTagMap::remove);
            }
        }
        List<String> newGroupTagList = new ArrayList<>();
        request.getGroupTagList().forEach(tag -> {
            // 新增群标签
            if (StringUtils.isEmpty(tag.getGroupTagSerialNo()) && StringUtils.isNotBlank(tag.getGroupTagName()) && !groupTagMap.containsKey(tag.getGroupTagName())) {
                newGroupTagList.add(tag.getGroupTagName());
                groupTagCount.getAndIncrement();
                return;
            }
            // 修改群标签
            if (StringUtils.isNotBlank(tag.getGroupTagSerialNo()) && groupTagSerialNoMap.containsKey(tag.getGroupTagSerialNo())) {
                GroupTag groupTag = groupTagSerialNoMap.get(tag.getGroupTagSerialNo());
                if (!groupTag.getTagName().equals(tag.getGroupTagName())) {
                    groupTag.setTagName(tag.getGroupTagName());
                    groupTag.setUpdater(loginSsoUserName);
                    groupTagDomainService.updateGroupTag(groupTag);
                }
            }
        });
        saveGroupTag(request.getAppId(), loginSsoUserName, groupTagSet.getSerialNo(), newGroupTagList);
        groupTagSet.setTagCount(groupTagCount.get());
        groupTagSet.setUpdater(loginSsoUserName);
        groupTagDomainService.updateGroupTagSet(groupTagSet);
        return RemoteResponse.success(IdHolder.of(groupTagSet.getId()));
    }


    @Override
    public GroupTagPageResponse<GroupTagResponse> queryGroupTagSet(GroupTagQueryRequest request) {
        if (request == null || StringUtils.isBlank(request.getAppId())) {
            return GroupTagPageResponse.fail("参数错误");
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("queryGroupTagSet,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return GroupTagPageResponse.fail("未登录");
        }
        if (Boolean.TRUE == request.getIsAll()) {
            request.setPageSize(2000);// 最大查询出来2000个
        }

        return queryGroupTagSetV2(request);
//        PagerList<GroupTagSet> pagerList = groupTagDomainService.queryGroupTagSet(request);
//        if (pagerList == null || pagerList.isEmpty()) {
//            return GroupTagPageResponse.success(new ArrayList<>(), 0, true);
//        }
//        List<GroupTagSet> queryTagSet = pagerList.getData();
//        List<String> groupTagSetSerialNos = queryTagSet.stream().map(GroupTagSet::getSerialNo).distinct().collect(Collectors.toList());
//        List<GroupTag> groupTags = groupTagDomainService.queryGroupTag(request.getAppId(), null, groupTagSetSerialNos);
//        // 统计群标签数量 -- 本查询只有keyword，如果查询条件变化，此处需要改动
//        int groupTagCount = (int) groupTagDomainService.countGroupTag(request.getAppId(), request.getKeyword());
//        List<GroupTagResponse> responses = buildGroupTagResponse(queryTagSet, groupTags);
//        GroupTagPageResponse<GroupTagResponse> success = GroupTagPageResponse.success(responses, pagerList.getTotal(), responses.size() < request.getPageSize());
//        success.setGroupTagSetCount((int) pagerList.getTotal());
//        success.setGroupTagCount(groupTagCount);
//        return success;
    }

    private List<GroupTagResponse> buildGroupTagResponse(List<GroupTagSet> groupTagSets, List<GroupTag> groupTags) {
        if (CollectionUtils.isEmpty(groupTagSets)) {
            return new ArrayList<>();
        }
        Map<String, List<GroupTag>> groupTagMap = CollectionUtil.isEmpty(groupTags) ? new HashMap<>() : groupTags.stream().collect(Collectors.groupingBy(GroupTag::getTagSetSerialNo));
        return groupTagSets.stream().map(groupTagSet -> {
            GroupTagResponse response = new GroupTagResponse();
            response.setGroupTagSetId(groupTagSet.getId());
            response.setGroupTagSetName(groupTagSet.getTagSetName());
            response.setGroupTagSetSerialNo(groupTagSet.getSerialNo());

            List<GroupTagDTO> groupTagDTOList = new ArrayList<>();
            if (groupTagMap.containsKey(groupTagSet.getSerialNo())) {
                groupTagDTOList = groupTagMap.get(groupTagSet.getSerialNo()).stream().map(groupTag -> {
                    GroupTagDTO groupTagDTO = new GroupTagDTO();
                    groupTagDTO.setGroupTagId(groupTag.getId());
                    groupTagDTO.setGroupTagName(groupTag.getTagName());
                    groupTagDTO.setGroupTagSerialNo(groupTag.getSerialNo());
                    return groupTagDTO;
                }).collect(Collectors.toList());
            }
            response.setGroupTagList(groupTagDTOList);

            return response;
        }).collect(Collectors.toList());
    }

    public GroupTagPageResponse<GroupTagResponse> queryGroupTagSetV2(GroupTagQueryRequest request) {
        String keyword = request.getKeyword();
        List<GroupTagSet> groupTagSets = groupTagDomainService.queryGroupTagSet(request.getAppId());
        if (CollectionUtils.isEmpty(groupTagSets)) {
            return GroupTagPageResponse.success(new ArrayList<>(), 0, true);
        }
        List<GroupTag> groupTags = groupTagDomainService.queryGroupTag(request.getAppId(), null, null);
        List<GroupTagSet> containedGroupTagSet;
        containedGroupTagSet = StringUtils.isBlank(keyword) ? groupTagSets : groupTagSets.stream().filter(groupTagSet -> StringUtils.contains(groupTagSet.getTagSetName(), keyword)).collect(Collectors.toList());

        List<GroupTag> containedGroupTag;
        if (CollectionUtils.isNotEmpty(containedGroupTagSet)) {
            Set<String> groupTagSetSerialNoSet = containedGroupTagSet.stream().map(GroupTagSet::getSerialNo).collect(Collectors.toSet());
            containedGroupTag = groupTags.stream().filter(groupTag -> groupTagSetSerialNoSet.contains(groupTag.getTagSetSerialNo())).collect(Collectors.toList());
        } else if (CollectionUtils.isNotEmpty(groupTags)) {
            containedGroupTag = StringUtils.isBlank(keyword) ? groupTags : groupTags.stream().filter(groupTag -> StringUtils.contains(groupTag.getTagName(), keyword)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(containedGroupTag)) {
                return GroupTagPageResponse.success(new ArrayList<>(), 0, true);
            }
            Set<String> groupTagSetSerialNos = containedGroupTag.stream().map(GroupTag::getTagSetSerialNo).collect(Collectors.toSet());
            containedGroupTagSet = groupTagSets.stream().filter(groupTagSet -> groupTagSetSerialNos.contains(groupTagSet.getSerialNo())).collect(Collectors.toList());
        } else {
            containedGroupTag = new ArrayList<>();
        }

        int total = containedGroupTagSet.size();
        containedGroupTagSet = containedGroupTagSet.stream()
                .skip((long) (request.getPageNo()) * request.getPageSize()).limit(request.getPageSize()).collect(Collectors.toList());

        List<GroupTagResponse> responses = buildGroupTagResponse(containedGroupTagSet, containedGroupTag);
        GroupTagPageResponse<GroupTagResponse> success = GroupTagPageResponse.success(responses, total, responses.size() < request.getPageSize());
        success.setGroupTagSetCount(total);
        success.setGroupTagCount(containedGroupTag.size());

        return success;
    }

    @Override
    public RemoteResponse<Boolean> deleteGroupTag(GroupTagDeleteRequest request) {
        if (request == null || request.getGroupTagSetId() == null || StringUtils.isBlank(request.getGroupTagSetSerialNo()) || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("deleteGroupTag,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        GroupTagSet groupTagSet = groupTagDomainService.queryGroupTagSetById(request.getGroupTagSetId());
        if (groupTagSet == null || Boolean.TRUE == groupTagSet.getDeleted() || !request.getAppId().equalsIgnoreCase(groupTagSet.getAppId())) {
            return RemoteResponse.fail("群标签组不存在");
        }
        if (!groupTagSet.getSerialNo().equals(request.getGroupTagSetSerialNo())) {
            return RemoteResponse.fail("群标签组不存在");
        }
        groupTagSet.setDeleted(true);
        groupTagSet.setUpdater(loginSsoUserName);
        groupTagDomainService.updateGroupTagSet(groupTagSet);
        groupTagDomainService.removeGroupTagByGroupTagSetSerialNo(loginSsoUserName, groupTagSet.getSerialNo());
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<GroupTagStatBaseResponse> groupTagStatBase(GroupTagStatBaseRequest request) {
        if (request == null || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        GroupTagStatBaseResponse response = new GroupTagStatBaseResponse();
        response.setGroupTagSetCount(groupTagDomainService.countGroupTagSet(request.getAppId()));
        response.setGroupTagCount(groupTagDomainService.countGroupTag(request.getAppId()));
        return RemoteResponse.success(response);
    }

    @Override
    public PageRemoteResponse<GroupTagStatDetailResponse> groupTagStatDetail(GroupTagStatDetailRequest request) {
        if (request == null || request.getQueryType() == null || request.getQueryType() == 0 || StringUtils.isBlank(request.getAppId())) {
            return PageRemoteResponse.fail("参数错误");
        }
        log.info("groupTagStatDetail,param={}", request);
        GroupTagStatQueryTypeEnum groupTagStatQueryTypeEnum = GroupTagStatQueryTypeEnum.fromCode(request.getQueryType());
        if (GroupTagStatQueryTypeEnum.UNKNOWN == groupTagStatQueryTypeEnum) {
            return PageRemoteResponse.fail("参数错误");
        }
        if (GroupTagStatQueryTypeEnum.TAG == groupTagStatQueryTypeEnum) {
            return groupTagStatDetailByTag(request);
        } else {
            return groupTagStatDetailByTagSet(request);
        }
    }

    private PageRemoteResponse<GroupTagStatDetailResponse> groupTagStatDetailByTagSet(GroupTagStatDetailRequest request) {
        PagerList<GroupTagSet> groupTagSetPagerList = groupTagDomainService.queryGroupTagSet(request);
        if (groupTagSetPagerList == null || CollectionUtil.isEmpty(groupTagSetPagerList.getData())) {
            return PageRemoteResponse.success(new ArrayList<>(), 0, true);
        }
        List<GroupTagStatDetailResponse> result = buildGroupTagStatDetailByTagSet(groupTagSetPagerList.getData());
        return PageRemoteResponse.success(result, groupTagSetPagerList.getTotal(), result.size() < request.getPageSize());
    }

    /**
     * 根据群标签组统计群标签组下的群标签信息
     *
     * @param groupTagSets
     * @return
     */
    private List<GroupTagStatDetailResponse> buildGroupTagStatDetailByTagSet(List<GroupTagSet> groupTagSets) {
        List<String> groupTagSetSerialNos = groupTagSets.stream().map(GroupTagSet::getSerialNo).distinct().collect(Collectors.toList());
        List<GroupTagMapping> groupTagMappings = groupTagDomainService.queryGroupTagMappingByGroupTagSetSerialNo(groupTagSetSerialNos);
        Map<String, List<GroupTagMapping>> groupSerialNoWithEntityMap = groupTagMappings.stream().collect(Collectors.groupingBy(GroupTagMapping::getGroupTagSetSerialNo));

        return groupTagSets.stream().map(tagSet -> {
            GroupTagStatDetailResponse response = new GroupTagStatDetailResponse();
            response.setGroupTagSetName(tagSet.getTagSetName());
            response.setGroupTagCount(tagSet.getTagCount());

            if (!groupSerialNoWithEntityMap.containsKey(tagSet.getSerialNo())) {
                response.setGroupCount(0);
            } else {
                List<GroupTagMapping> tempMapping = groupSerialNoWithEntityMap.get(tagSet.getSerialNo());
                if (CollectionUtil.isNotEmpty(tempMapping)) {
                    response.setGroupCount(tempMapping.size());
//                    response.setGroupCount(countGroupTagByGroupId(tempMapping.stream().map(GroupTagMapping::getGroupId).distinct().collect(Collectors.toList())));
                    tempMapping.sort(Comparator.comparing(GroupTagMapping::getUpdateTime).reversed());
                    if (tempMapping.get(0).getUpdateTime() != null) {
                        response.setLatestTime(tempMapping.get(0).getUpdateTime().getTime());
                    }
                }

            }
            return response;
        }).collect(Collectors.toList());
    }

    private int countGroupTagByGroupId(List<String> groupIds) {
        if (CollectionUtil.isEmpty(groupIds)) {
            return 0;
        }
        GroupInfoEntityExample example = new GroupInfoEntityExample();
        GroupInfoEntityExample.Criteria criteria = example.createCriteria();
        criteria.andGroupIdIn(groupIds);
        return (int) groupInfoEntityMapper.countByExample(example);
    }

    private PageRemoteResponse<GroupTagStatDetailResponse> groupTagStatDetailByTag(GroupTagStatDetailRequest request) {
        PagerList<GroupTag> groupTagsPagerList = groupTagDomainService.queryGroupTag(request);
        if (groupTagsPagerList == null || CollectionUtil.isEmpty(groupTagsPagerList.getData())) {
            return PageRemoteResponse.success(new ArrayList<>(), 0, true);
        }
        List<GroupTagStatDetailResponse> result = buildGroupTagStatDetailByTag(groupTagsPagerList.getData());
        return PageRemoteResponse.success(result, groupTagsPagerList.getTotal(), result.size() < request.getPageSize());
    }

    /**
     * 根据标签统计群标签信息
     *
     * @param groupTags
     * @return
     */
    private List<GroupTagStatDetailResponse> buildGroupTagStatDetailByTag(List<GroupTag> groupTags) {
        List<String> groupTagSetSerialNos = groupTags.stream().map(GroupTag::getTagSetSerialNo).distinct().collect(Collectors.toList());
        List<GroupTagSet> groupTagSets = groupTagDomainService.queryGroupTagSetBySerialNo(groupTagSetSerialNos);
        Map<String, GroupTagSet> groupTagSetMap = groupTagSets.stream().collect(Collectors.toMap(GroupTagSet::getSerialNo, Function.identity(), (a, b) -> a));

        List<String> groupTagSerialNos = groupTags.stream().map(GroupTag::getSerialNo).distinct().collect(Collectors.toList());
        List<GroupTagMapping> groupTagMappings = groupTagDomainService.queryGroupTagMappingByGroupTagSerialNo(groupTagSerialNos);
        Map<String, List<GroupTagMapping>> groupTagSerialNoWithEntityMap = groupTagMappings.stream().collect(Collectors.groupingBy(GroupTagMapping::getGroupTagSerialNo));

        return groupTags.stream().map(tag -> {
            GroupTagStatDetailResponse response = new GroupTagStatDetailResponse();
            response.setGroupTagName(tag.getTagName());
            if (groupTagSetMap.containsKey(tag.getTagSetSerialNo())) {
                response.setGroupTagSetName(groupTagSetMap.get(tag.getTagSetSerialNo()).getTagSetName());
            }
            if (!groupTagSerialNoWithEntityMap.containsKey(tag.getSerialNo())) {
                response.setGroupCount(0);
            } else {
                List<GroupTagMapping> tempMapping = groupTagSerialNoWithEntityMap.get(tag.getSerialNo());
                if (CollectionUtil.isNotEmpty(tempMapping)) {
                    response.setGroupCount(tempMapping.size());
//                    response.setGroupCount(countGroupTagByGroupId(tempMapping.stream().map(GroupTagMapping::getGroupId).distinct().collect(Collectors.toList())));
                    tempMapping.sort(Comparator.comparing(GroupTagMapping::getUpdateTime).reversed());
                    if (tempMapping.get(0).getUpdateTime() != null) {
                        response.setLatestTime(tempMapping.get(0).getUpdateTime().getTime());
                    }
                }
            }
            return response;
        }).collect(Collectors.toList());
    }


    @Override
    public RemoteResponse<String> groupTagStatDetailExport(GroupTagStatDetailRequest request) {
        if (request == null || request.getQueryType() == null || request.getQueryType() == 0 || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("groupTagStatDetailExport,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        GroupTagStatQueryTypeEnum groupTagStatQueryTypeEnum = GroupTagStatQueryTypeEnum.fromCode(request.getQueryType());
        if (GroupTagStatQueryTypeEnum.UNKNOWN == groupTagStatQueryTypeEnum) {
            return RemoteResponse.fail("参数错误");
        }
        List<GroupTagStatDetailResponse> result;
        GroupTagStatDetailRequest queryRequest = new GroupTagStatDetailRequest();
        BeanUtil.copyProperties(request, queryRequest);
        queryRequest.setPageNo(1);
        queryRequest.setPageSize(100);
        List<List<String>> head = Lists.newArrayList();
        List<List<String>> data;
        String fileName;
        if (GroupTagStatQueryTypeEnum.TAG == groupTagStatQueryTypeEnum) {
            result = queryGroupTagStatByGroupTagSerialNo(queryRequest);
            fileName = "按标签查看数据" + System.currentTimeMillis() + ".xlsx";
            data = exportGroupTagStatByGroupTagSerialNo(result, head);

        } else {
            result = queryGroupTagStatByGroupTagSetSerialNo(queryRequest);
            fileName = "按标签组查看数据" + System.currentTimeMillis() + ".xlsx";
            data = exportGroupTagStatByGroupTagSetSerialNo(result, head);
        }
        if (CollectionUtil.isEmpty(result)) {
            return RemoteResponse.fail("没有数据");
        }

        return RemoteResponse.success(groupTagDomainService.generalExcelUrl(fileName, head, data));
    }

    private List<List<String>> exportGroupTagStatByGroupTagSetSerialNo(List<GroupTagStatDetailResponse> result, List<List<String>> head) {
        List<List<String>> data;
        head.add(Lists.newArrayList("标签组名称"));
        head.add(Lists.newArrayList("组中标签数量"));
        head.add(Lists.newArrayList("标签组下客户群数"));
        head.add(Lists.newArrayList("标签组最近更新时间"));
        data = result.stream().map(response -> {
            List<String> row = Lists.newArrayList();
            row.add(response.getGroupTagSetName());
            row.add(String.valueOf(response.getGroupTagCount()));
            row.add(String.valueOf(response.getGroupCount()));
            row.add(StringUtils.defaultIfBlank(DateUtil.formatYMdHms(response.getLatestTime()), StringUtils.EMPTY));
            return row;
        }).collect(Collectors.toList());
        return data;
    }

    private List<List<String>> exportGroupTagStatByGroupTagSerialNo(List<GroupTagStatDetailResponse> result, List<List<String>> head) {
        List<List<String>> data;
        head.add(Lists.newArrayList("标签名称"));
        head.add(Lists.newArrayList("标签组名称"));
        head.add(Lists.newArrayList("标签下客户群数"));
        head.add(Lists.newArrayList("标签最后使用时间"));
        data = result.stream().map(response -> {
            List<String> row = Lists.newArrayList();
            row.add(response.getGroupTagName());
            row.add(response.getGroupTagSetName());
            row.add(String.valueOf(response.getGroupCount()));
            row.add(StringUtils.defaultIfBlank(DateUtil.formatYMdHms(response.getLatestTime()), StringUtils.EMPTY));
            return row;
        }).collect(Collectors.toList());
        return data;
    }

    private List<GroupTagStatDetailResponse> queryGroupTagStatByGroupTagSerialNo(GroupTagStatDetailRequest request) {
        log.info("按照标签统计,request={}", request);
        List<GroupTagStatDetailResponse> result = new ArrayList<>();
        int totalLimit = 60000;
        int limitMaxPageNo = totalLimit / request.getPageSize();
        if (totalLimit % request.getPageSize() != 0) {
            limitMaxPageNo++;
        }
        while (true) {
            if (request.getOriginPageNo() > limitMaxPageNo) {
                break;
            }
            PagerList<GroupTag> groupTagsPagerList = groupTagDomainService.queryGroupTag(request);
            if (groupTagsPagerList == null || CollectionUtil.isEmpty(groupTagsPagerList.getData())) {
                break;
            }
            List<GroupTagStatDetailResponse> list = buildGroupTagStatDetailByTag(groupTagsPagerList.getData());
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            result.addAll(list);
            request.nextPage();
        }
        return result;
    }

    private List<GroupTagStatDetailResponse> queryGroupTagStatByGroupTagSetSerialNo(GroupTagStatDetailRequest request) {
        log.info("按照标签组统计,request={}", request);
        List<GroupTagStatDetailResponse> result = new ArrayList<>();
        int totalLimit = 60000;
        int limitMaxPageNo = totalLimit / request.getPageSize();
        if (totalLimit % request.getPageSize() != 0) {
            limitMaxPageNo++;
        }
        while (true) {
            if (request.getOriginPageNo() > limitMaxPageNo) {
                break;
            }
            PagerList<GroupTagSet> pagerList = groupTagDomainService.queryGroupTagSet(request);
            if (pagerList == null || CollectionUtil.isEmpty(pagerList.getData())) {
                break;
            }
            List<GroupTagStatDetailResponse> list = buildGroupTagStatDetailByTagSet(pagerList.getData());
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            result.addAll(list);
            request.nextPage();
        }
        return result;
    }

    @Override
    public RemoteResponse<Boolean> editTagOfSomeGroup(TagOfSomeGroupEditRequest request) {
        if (request == null || StringUtils.isBlank(request.getGroupId()) || CollectionUtil.isEmpty(request.getGroupTagSerialNos()) || StringUtils.isBlank(request.getAppId())) {
            return RemoteResponse.fail("参数错误");
        }
        String loginSsoUserName = loginAccessTokenAcl.currentLoginSsoUserName();
        log.info("editTagOfSomeGroup,param={},loginSsoUserName={}", request, loginSsoUserName);
        if (StringUtils.isBlank(loginSsoUserName)) {
            return RemoteResponse.fail("未登录");
        }
        List<GroupTagMapping> groupTagMappings = groupTagDomainService.queryGroupTagMappingByGroupId(request.getAppId(), request.getGroupId());
        List<GroupTag> groupTags = groupTagDomainService.queryGroupTag(request.getAppId(), "", request.getGroupTagSerialNos());
        Map<String, GroupTag> groupTagMap = groupTags.stream().collect(Collectors.toMap(GroupTag::getSerialNo, Function.identity(), (a, b) -> a));

        Map<String, GroupTagMapping> groupTagMappingMap = groupTagMappings.stream().collect(Collectors.toMap(GroupTagMapping::getGroupTagSerialNo, Function.identity(), (a, b) -> a));
        List<GroupTagMapping> saveList = new ArrayList<>();
        Set<String> requestGroupTagSerialNoSet = new HashSet<>(request.getGroupTagSerialNos());
        for (String groupTagSerialNo : request.getGroupTagSerialNos()) {
            // 已经存在
            if (groupTagMappingMap.containsKey(groupTagSerialNo)) {
                continue;
            }
            // 不存在
            GroupTagMapping groupTagMapping = new GroupTagMapping();
            groupTagMapping.setGroupId(request.getGroupId());
            groupTagMapping.setStatus(true);
            groupTagMapping.setAddTime(new Date());
            groupTagMapping.setUpdateTime(new Date());
            if (groupTagMap.containsKey(groupTagSerialNo)) {
                groupTagMapping.setGroupTagSetSerialNo(groupTagMap.get(groupTagSerialNo).getTagSetSerialNo());
                groupTagMapping.setGroupTagSerialNo(groupTagSerialNo);
                groupTagMapping.setAppId(request.getAppId());
                saveList.add(groupTagMapping);
            }
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            groupTagMappingMapper.batchInsert(saveList);
        }
        // 删除不存在的标签
        Set<String> deleteGroupTagSerialNo = groupTagMappingMap.keySet();
        deleteGroupTagSerialNo.removeAll(requestGroupTagSerialNoSet);
        if (deleteGroupTagSerialNo.size() > 0) {
            GroupTagMappingExample deleteExample = new GroupTagMappingExample();
            deleteExample.createCriteria().andGroupIdEqualTo(request.getGroupId()).andGroupTagSerialNoIn(new ArrayList<>(deleteGroupTagSerialNo));
            GroupTagMapping groupTagMapping = new GroupTagMapping();
            groupTagMapping.setStatus(false);
            groupTagMapping.setUpdateTime(new Date());
            groupTagMappingMapper.updateByExample(groupTagMapping, deleteExample);
        }
        return RemoteResponse.success(true);
    }


}
