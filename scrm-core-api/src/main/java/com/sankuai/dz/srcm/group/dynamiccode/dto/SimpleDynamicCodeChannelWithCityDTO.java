package com.sankuai.dz.srcm.group.dynamiccode.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企微群活码的渠道来源信息
 */
@Data
public class SimpleDynamicCodeChannelWithCityDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    private List<CityVO> cityList;

}
