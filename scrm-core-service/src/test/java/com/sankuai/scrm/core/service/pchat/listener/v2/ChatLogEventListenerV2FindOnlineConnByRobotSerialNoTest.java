package com.sankuai.scrm.core.service.pchat.listener.v2;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.im.Msg;
import com.sankuai.dz.srcm.pchat.response.im.ImMsgListResponse;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxChatLogWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxFriends;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxIMOnlineConn;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxImDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.domain.im.PChatWxImDomainService;
import com.sankuai.scrm.core.service.pchat.domain.im.ent.PrivateLiveEntImMsgService;
import com.sankuai.scrm.core.service.pchat.event.v2.GroupChatLogEventV2;
import com.sankuai.scrm.core.service.pchat.im.ImPushService;
import com.sankuai.scrm.core.service.pchat.thirdparty.consultant.ConsultantService;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ChatLogEventListenerV2FindOnlineConnByRobotSerialNoTest {

    @InjectMocks
    private ChatLogEventListenerV2 chatLogEventListenerV2;

    @Mock
    private ScrmPersonalWxImDomainService personalWxImDomainService;

    @Mock
    private ScrmPersonalWxUserDomainService personalWxUserDomainService;

    @Mock
    private ConsultantService consultantService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private PChatWxImDomainService pChatWxImDomainService;

    @Mock
    private PrivateLiveEntImMsgService privateLiveEntImMsgService;

    @Mock
    private ImPushService imPushService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private List<String> invokeFindOnlineConnByRobotSerialNo(String robotSerialNo) throws Exception {
        Method method = ChatLogEventListenerV2.class.getDeclaredMethod("findOnlineConnByRobotSerialNo", String.class);
        method.setAccessible(true);
        return (List<String>) method.invoke(chatLogEventListenerV2, robotSerialNo);
    }

    private void invokeFillGroupConsultantInfo(ScrmPersonalWxGroupInfoEntity groupInfoEntity, ImMsgListResponse response) throws Exception {
        Method method = ChatLogEventListenerV2.class.getDeclaredMethod("fillGroupConsultantInfo", ScrmPersonalWxGroupInfoEntity.class, ImMsgListResponse.class);
        method.setAccessible(true);
        method.invoke(chatLogEventListenerV2, groupInfoEntity, response);
    }

    /**
     * 测试场景：robotSerialNo 为空
     */
    @Test
    public void testFindOnlineConnByRobotSerialNo_RobotSerialNoIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = "";
        // act
        List<String> result = invokeFindOnlineConnByRobotSerialNo(robotSerialNo);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试场景：robotSerialNo 不为空，但查询到的 onlineConns 列表为空
     */
    @Test
    public void testFindOnlineConnByRobotSerialNo_OnlineConnsIsEmpty() throws Throwable {
        // arrange
        String robotSerialNo = "validRobotSerialNo";
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo(robotSerialNo)).thenReturn(Collections.emptyList());
        // act
        List<String> result = invokeFindOnlineConnByRobotSerialNo(robotSerialNo);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试场景：robotSerialNo 不为空，查询到的 onlineConns 列表不为空，但所有 alias 都为空
     */
    @Test
    public void testFindOnlineConnByRobotSerialNo_AllAliasesAreBlank() throws Throwable {
        // arrange
        String robotSerialNo = "validRobotSerialNo";
        List<ScrmPersonalWxIMOnlineConn> onlineConns = new ArrayList<>();
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(1L, "appId", "", "projectId", 1L, null));
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo(robotSerialNo)).thenReturn(onlineConns);
        // act
        List<String> result = invokeFindOnlineConnByRobotSerialNo(robotSerialNo);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试场景：robotSerialNo 不为空，查询到的 onlineConns 列表不为空，且包含非空 alias
     */
    @Test
    public void testFindOnlineConnByRobotSerialNo_ValidAliasesExist() throws Throwable {
        // arrange
        String robotSerialNo = "validRobotSerialNo";
        List<ScrmPersonalWxIMOnlineConn> onlineConns = new ArrayList<>();
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(1L, "appId", "alias1", "projectId", 1L, null));
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(2L, "appId", "alias2", "projectId", 1L, null));
        // blank alias
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(3L, "appId", "", "projectId", 1L, null));
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo(robotSerialNo)).thenReturn(onlineConns);
        // act
        List<String> result = invokeFindOnlineConnByRobotSerialNo(robotSerialNo);
        // assert
        assertEquals(Lists.newArrayList("alias1", "alias2"), result);
    }

    /**
     * 测试场景：robotSerialNo 不为空，查询到的 onlineConns 列表不为空，且包含重复的非空 alias
     */
    @Test
    public void testFindOnlineConnByRobotSerialNo_DuplicateAliasesExist() throws Throwable {
        // arrange
        String robotSerialNo = "validRobotSerialNo";
        List<ScrmPersonalWxIMOnlineConn> onlineConns = new ArrayList<>();
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(1L, "appId", "alias1", "projectId", 1L, null));
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(2L, "appId", "alias2", "projectId", 1L, null));
        // duplicate alias
        onlineConns.add(new ScrmPersonalWxIMOnlineConn(3L, "appId", "alias1", "projectId", 1L, null));
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo(robotSerialNo)).thenReturn(onlineConns);
        // act
        List<String> result = invokeFindOnlineConnByRobotSerialNo(robotSerialNo);
        // assert
        assertEquals(Lists.newArrayList("alias1", "alias2"), result);
    }

    /**
     * 测试 receiver 为 null 的情况
     */
    @Test
    public void testFillMsgReceiver_ReceiverIsNull() throws Throwable {
        // arrange
        Long senderId = 1L;
        ScrmPersonalWxUserInfo receiver = null;
        ImMsgListResponse response = new ImMsgListResponse();
        // Use reflection to invoke the private method
        Method fillMsgReceiverMethod = ChatLogEventListenerV2.class.getDeclaredMethod("fillMsgReceiver", Long.class, ScrmPersonalWxUserInfo.class, ImMsgListResponse.class);
        fillMsgReceiverMethod.setAccessible(true);
        // act
        fillMsgReceiverMethod.invoke(chatLogEventListenerV2, senderId, receiver, response);
        // assert
        assertNull(response.getReceiver());
    }

    /**
     * 测试 receiver 不为 null，且 friends 为空的情况
     */
    @Test
    public void testFillMsgReceiver_ReceiverIsNotNull_FriendsIsEmpty() throws Throwable {
        // arrange
        Long senderId = 1L;
        ScrmPersonalWxUserInfo receiver = new ScrmPersonalWxUserInfo();
        receiver.setAppId("appId");
        receiver.setId(2L);
        receiver.setHeadimgUrl("avatarUrl");
        receiver.setWxId("wxId");
        receiver.setWxAlias("wxAlias");
        receiver.setNickname("nickname");
        receiver.setSerialNo("serialNo");
        ImMsgListResponse response = new ImMsgListResponse();
        when(personalWxUserDomainService.queryWxFriendRelationByUserIdAndFreId(anyString(), anyLong(), anyLong())).thenReturn(Collections.emptyList());
        // Use reflection to invoke the private method
        Method fillMsgReceiverMethod = ChatLogEventListenerV2.class.getDeclaredMethod("fillMsgReceiver", Long.class, ScrmPersonalWxUserInfo.class, ImMsgListResponse.class);
        fillMsgReceiverMethod.setAccessible(true);
        // act
        fillMsgReceiverMethod.invoke(chatLogEventListenerV2, senderId, receiver, response);
        // assert
        assertNotNull(response.getReceiver());
        assertEquals("avatarUrl", response.getReceiver().getAvatar());
        assertEquals("wxId", response.getReceiver().getWxId());
        assertEquals("wxAlias", response.getReceiver().getWxAlias());
        assertEquals("nickname", response.getReceiver().getWxNickname());
        assertEquals("serialNo", response.getReceiver().getWxSerialNo());
        assertNull(response.getReceiver().getGroupRemarkName());
        assertNull(response.getReceiver().getFriendRemarkName());
        // Initialize isStickie to false when friends list is empty
        assertFalse(Boolean.TRUE.equals(response.getReceiver().getIsStickie()));
    }

    /**
     * 测试 receiver 不为 null，且 friends 不为空的情况
     */
    @Test
    public void testFillMsgReceiver_ReceiverIsNotNull_FriendsIsNotEmpty() throws Throwable {
        // arrange
        Long senderId = 1L;
        ScrmPersonalWxUserInfo receiver = new ScrmPersonalWxUserInfo();
        receiver.setAppId("appId");
        receiver.setId(2L);
        receiver.setHeadimgUrl("avatarUrl");
        receiver.setWxId("wxId");
        receiver.setWxAlias("wxAlias");
        receiver.setNickname("nickname");
        receiver.setSerialNo("serialNo");
        ScrmPersonalWxFriends friend = new ScrmPersonalWxFriends();
        friend.setRemarkName("friendRemark");
        // STICKY
        friend.setStickie("1");
        ImMsgListResponse response = new ImMsgListResponse();
        when(personalWxUserDomainService.queryWxFriendRelationByUserIdAndFreId(anyString(), anyLong(), anyLong())).thenReturn(Collections.singletonList(friend));
        // Use reflection to invoke the private method
        Method fillMsgReceiverMethod = ChatLogEventListenerV2.class.getDeclaredMethod("fillMsgReceiver", Long.class, ScrmPersonalWxUserInfo.class, ImMsgListResponse.class);
        fillMsgReceiverMethod.setAccessible(true);
        // act
        fillMsgReceiverMethod.invoke(chatLogEventListenerV2, senderId, receiver, response);
        // assert
        assertNotNull(response.getReceiver());
        assertEquals("avatarUrl", response.getReceiver().getAvatar());
        assertEquals("wxId", response.getReceiver().getWxId());
        assertEquals("wxAlias", response.getReceiver().getWxAlias());
        assertEquals("nickname", response.getReceiver().getWxNickname());
        assertEquals("serialNo", response.getReceiver().getWxSerialNo());
        assertNull(response.getReceiver().getGroupRemarkName());
        assertEquals("friendRemark", response.getReceiver().getFriendRemarkName());
        assertTrue(Boolean.TRUE.equals(response.getReceiver().getIsStickie()));
    }

    /**
     * 测试正常情况：consultantList 不为空，且转换后的 list 也不为空，consultantService.queryConsultantInfoById 返回非空结果。
     */
    @Test
    public void testFillGroupConsultantInfo_NormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("[{\"consultantTaskId\":1}]");
        ImMsgListResponse response = new ImMsgListResponse();
        List<ConsultantInfoDTO> expectedConsultantList = Collections.singletonList(new ConsultantInfoDTO());
        when(consultantService.queryConsultantInfoById(anyList())).thenReturn(expectedConsultantList);
        // act
        invokeFillGroupConsultantInfo(groupInfoEntity, response);
        // assert
        assertEquals(expectedConsultantList, response.getConsultantList());
    }

    /**
     * 测试边界情况：consultantList 为空字符串。
     */
    @Test
    public void testFillGroupConsultantInfo_EmptyConsultantList() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("");
        ImMsgListResponse response = new ImMsgListResponse();
        // act
        invokeFillGroupConsultantInfo(groupInfoEntity, response);
        // assert
        assertNull(response.getConsultantList());
    }

    /**
     * 测试边界情况：consultantList 不为空，但转换后的 list 为空。
     */
    @Test
    public void testFillGroupConsultantInfo_EmptyConvertedList() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("[]");
        ImMsgListResponse response = new ImMsgListResponse();
        // act
        invokeFillGroupConsultantInfo(groupInfoEntity, response);
        // assert
        assertNull(response.getConsultantList());
    }

    /**
     * 测试边界情况：consultantService.queryConsultantInfoById 返回空结果。
     */
    @Test
    public void testFillGroupConsultantInfo_EmptyQueryResult() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("[{\"consultantTaskId\":1}]");
        ImMsgListResponse response = new ImMsgListResponse();
        when(consultantService.queryConsultantInfoById(anyList())).thenReturn(Collections.emptyList());
        // act
        invokeFillGroupConsultantInfo(groupInfoEntity, response);
        // assert
        assertEquals(Collections.emptyList(), response.getConsultantList());
    }

    /**
     * 测试异常情况：consultantList 格式不正确，导致 JsonUtils.toList 抛出异常。
     */
    @Test
    public void testFillGroupConsultantInfo_InvalidJsonFormat() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("invalid json");
        ImMsgListResponse response = new ImMsgListResponse();
        // act & assert
        try {
            invokeFillGroupConsultantInfo(groupInfoEntity, response);
            fail("Expected RuntimeException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("反序列化失败", e.getCause().getMessage());
        } catch (Exception e) {
            fail("Unexpected exception type: " + e.getClass().getName());
        }
    }

    /**
     * 测试异常情况：consultantService.queryConsultantInfoById 抛出异常。
     */
    @Test
    public void testFillGroupConsultantInfo_QueryConsultantInfoByIdThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setConsultantList("[{\"consultantTaskId\":1}]");
        ImMsgListResponse response = new ImMsgListResponse();
        doThrow(new RuntimeException("Service error")).when(consultantService).queryConsultantInfoById(anyList());
        // act & assert
        try {
            invokeFillGroupConsultantInfo(groupInfoEntity, response);
            fail("Expected RuntimeException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Service error", e.getCause().getMessage());
        } catch (Exception e) {
            fail("Unexpected exception type: " + e.getClass().getName());
        }
    }

    /**
     * 测试在线连接为空的情况
     */
    @Test
    public void testPushGroupChatMsg_NoOnlineConnections() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.emptyList());
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService, never()).pushSession(anyString(), anyList(), any(ImMsgListResponse.class));
    }

    /**
     * 测试群组信息为空的情况
     */
    @Test
    public void testPushGroupChatMsg_GroupInfoEntityIsNull() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(chatLog.getAppId()).thenReturn("appId");
        when(chatLog.getChatRoomSerialNo()).thenReturn("chatRoomSerialNo");
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        onlineConn.setAlias("alias");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.singletonList(onlineConn));
        when(groupManageDomainService.queryGroupByChatroomSerialNo("appId", "chatRoomSerialNo")).thenReturn(null);
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService, never()).pushSession(anyString(), anyList(), any(ImMsgListResponse.class));
    }

    /**
     * 测试机器人用户信息为空的情况
     */
    @Test
    public void testPushGroupChatMsg_RobotUserInfoIsNull() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(chatLog.getAppId()).thenReturn("appId");
        when(chatLog.getChatRoomSerialNo()).thenReturn("chatRoomSerialNo");
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        onlineConn.setAlias("alias");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.singletonList(onlineConn));
        when(groupManageDomainService.queryGroupByChatroomSerialNo("appId", "chatRoomSerialNo")).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryUserByWxSerialNo("robotSerialNo")).thenReturn(null);
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService, never()).pushSession(anyString(), anyList(), any(ImMsgListResponse.class));
    }

    /**
     * 测试机器人成员信息为空的情况
     */
    @Test
    public void testPushGroupChatMsg_RobotMemberIsNull() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
        ScrmPersonalWxUserInfo robotUserInfo = mock(ScrmPersonalWxUserInfo.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(chatLog.getAppId()).thenReturn("appId");
        when(chatLog.getChatRoomSerialNo()).thenReturn("chatRoomSerialNo");
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        onlineConn.setAlias("alias");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.singletonList(onlineConn));
        when(groupManageDomainService.queryGroupByChatroomSerialNo("appId", "chatRoomSerialNo")).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryUserByWxSerialNo("robotSerialNo")).thenReturn(robotUserInfo);
        when(groupManageDomainService.queryGroupMemberByGroupWxSerialNo("appId", "chatRoomSerialNo", "robotSerialNo")).thenReturn(null);
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService, never()).pushSession(anyString(), anyList(), any(ImMsgListResponse.class));
    }

    /**
     * 测试发送者成员信息为空的情况
     */
    @Test
    public void testPushGroupChatMsg_SenderMemberIsNull() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
        ScrmPersonalWxUserInfo robotUserInfo = mock(ScrmPersonalWxUserInfo.class);
        ScrmPersonalWxGroupMemberInfoEntity robotMember = mock(ScrmPersonalWxGroupMemberInfoEntity.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(chatLog.getAppId()).thenReturn("appId");
        when(chatLog.getChatRoomSerialNo()).thenReturn("chatRoomSerialNo");
        when(chatLog.getSenderWxSerialNo()).thenReturn("senderWxSerialNo");
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        onlineConn.setAlias("alias");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.singletonList(onlineConn));
        when(groupManageDomainService.queryGroupByChatroomSerialNo("appId", "chatRoomSerialNo")).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryUserByWxSerialNo("robotSerialNo")).thenReturn(robotUserInfo);
        when(groupManageDomainService.queryGroupMemberByGroupWxSerialNo("appId", "chatRoomSerialNo", "robotSerialNo")).thenReturn(robotMember);
        when(groupManageDomainService.queryGroupMemberByGroupWxSerialNo("appId", "chatRoomSerialNo", "senderWxSerialNo")).thenReturn(null);
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService, never()).pushSession(anyString(), anyList(), any(ImMsgListResponse.class));
    }

    /**
     * 测试正常流程
     */
    @Test
    public void testPushGroupChatMsg_NormalFlow() throws Throwable {
        // arrange
        GroupChatLogEventV2 event = mock(GroupChatLogEventV2.class);
        ScrmPersonalWxChatLogWithBLOBs chatLog = mock(ScrmPersonalWxChatLogWithBLOBs.class);
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
        ScrmPersonalWxUserInfo robotUserInfo = mock(ScrmPersonalWxUserInfo.class);
        ScrmPersonalWxGroupMemberInfoEntity robotMember = mock(ScrmPersonalWxGroupMemberInfoEntity.class);
        ScrmPersonalWxGroupMemberInfoEntity senderMember = mock(ScrmPersonalWxGroupMemberInfoEntity.class);
        ScrmPersonalWxUserInfo sender = mock(ScrmPersonalWxUserInfo.class);
        Msg msg = mock(Msg.class);
        when(event.getChatLogWithBLOBs()).thenReturn(chatLog);
        when(chatLog.getRobotSerialNo()).thenReturn("robotSerialNo");
        when(chatLog.getAppId()).thenReturn("appId");
        when(chatLog.getChatRoomSerialNo()).thenReturn("chatRoomSerialNo");
        when(chatLog.getSenderWxSerialNo()).thenReturn("senderWxSerialNo");
        ScrmPersonalWxIMOnlineConn onlineConn = new ScrmPersonalWxIMOnlineConn();
        onlineConn.setAlias("alias");
        when(personalWxImDomainService.queryOnlineConnByRobotSerialNo("robotSerialNo")).thenReturn(Collections.singletonList(onlineConn));
        when(groupManageDomainService.queryGroupByChatroomSerialNo("appId", "chatRoomSerialNo")).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryUserByWxSerialNo("robotSerialNo")).thenReturn(robotUserInfo);
        when(groupManageDomainService.queryGroupMemberByGroupWxSerialNo("appId", "chatRoomSerialNo", "robotSerialNo")).thenReturn(robotMember);
        when(groupManageDomainService.queryGroupMemberByGroupWxSerialNo("appId", "chatRoomSerialNo", "senderWxSerialNo")).thenReturn(senderMember);
        when(groupManageDomainService.queryUserByWxSerialNo("senderWxSerialNo")).thenReturn(sender);
        when(pChatWxImDomainService.getGroupStickyList(robotUserInfo.getId(), Collections.singletonList(groupInfoEntity.getId()))).thenReturn(Collections.emptyList());
        when(privateLiveEntImMsgService.buildGroupMsg(chatLog)).thenReturn(msg);
        // Use reflection to invoke the private method
        Method pushGroupChatMsgMethod = ChatLogEventListenerV2.class.getDeclaredMethod("pushGroupChatMsg", GroupChatLogEventV2.class);
        pushGroupChatMsgMethod.setAccessible(true);
        pushGroupChatMsgMethod.invoke(chatLogEventListenerV2, event);
        // assert
        verify(imPushService).pushSession(eq("robotSerialNo"), eq(Collections.singletonList("alias")), any(ImMsgListResponse.class));
    }
}
