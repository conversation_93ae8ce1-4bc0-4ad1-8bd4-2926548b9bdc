package com.sankuai.scrm.core.service.infrastructure.acl.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.google.common.collect.Lists;
import com.sankuai.general.order.querycenter.api.dto.QueryOptionDTO;
import com.sankuai.general.order.querycenter.api.dto.UniOrderDTO;
import com.sankuai.general.order.querycenter.api.enums.BizLineEnum;
import com.sankuai.general.order.querycenter.api.enums.QueryParamEnum;
import com.sankuai.general.order.querycenter.api.request.OrderQueryRequest;
import com.sankuai.general.order.querycenter.api.request.SessionContext;
import com.sankuai.general.order.querycenter.api.response.OrderResponse;
import com.sankuai.general.order.querycenter.api.service.OrderQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UnifiedOrderAclService {

    @Resource
    private GetUnifiedOrderService getUnifiedOrderService;
    @Resource
    private OrderQueryService orderQueryService;
    private static final String DISTRIBUTION_BASIC_INFO = "DISTRIBUTION_BASIC_INFO";
    private static final String DISTRIBUTION_TYPE = "distributionType";
    private static final List<String> defaultPrivateLiveList = new ArrayList() {{
        add("PRIVATE_LIVE");
        add("meibo");
    }};


    public long getShopId(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return 0L;
        }
        UnifiedOrderWithId orderInfo = getOrderInfo(orderId);
        if (orderInfo == null) {
            return 0L;
        }
        return orderInfo.getLongMtShopId();
    }

    public UnifiedOrderWithId getOrderInfo(String orderId) {
        try {
            log.info("UnifiedOrderAclService getOrderInfo order id is {}", orderId);
            UnifiedOrderWithId orderInfo = getUnifiedOrderService.getByUnifiedOrderId(orderId);
            log.info("UnifiedOrderAclService getOrderInfo order is {}", orderInfo);
            return orderInfo;
        } catch (Exception e) {
            log.error("UnifiedOrderAclService getOrderInfo has error", e);
            return null;
        }
    }

    public Boolean isPrivateLiveOrder(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return false;
        }
        OrderQueryRequest request = new OrderQueryRequest();
        QueryOptionDTO queryOptionDTO = new QueryOptionDTO();
        // 需要的信息：订单基本信息、订单状态信息、订单商品信息、订单商品明细、订单支付单信息
        List<Integer> queryParams = Lists.newArrayList(QueryParamEnum.ORDER_BASIC_INFO.getCode(), QueryParamEnum.ORDER_STATUS.getCode(),
                QueryParamEnum.ORDER_EXTRA_FIELD.getCode(), QueryParamEnum.ORDER_ITEM.getCode(), QueryParamEnum.ORDER_ITEM_DETAIL.getCode(),
                QueryParamEnum.PAYMENT.getCode(), QueryParamEnum.PAYMENT_DETAIL.getCode());
        queryOptionDTO.setQueryParams(queryParams);
        request.setQueryOption(queryOptionDTO);
        request.setUniOrderId(orderId);
        SessionContext context = new SessionContext();
        context.setBizLine(BizLineEnum.DISPLAY.getCode());
        log.info("UnifiedOrderAclService isPrivateLiveOrder request is {}", request);
        OrderResponse response = orderQueryService.query(request, context);
        log.info("UnifiedOrderAclService isPrivateLiveOrder response is {}", response);
        if (response == null || !response.success() || response.getData() == null) {
            return false;
        }
        UniOrderDTO data = response.getData();
        Map<String, String> orderExtraMap = data.getOrderExtraMap();
        if (orderExtraMap == null) {
            return false;
        }
        String distributionBasicInfo = orderExtraMap.get(DISTRIBUTION_BASIC_INFO);
        if (StringUtils.isBlank(distributionBasicInfo)) {
            return false;
        }
        JSONObject object = JSON.parseObject(distributionBasicInfo);
        String distributionType = (String) object.get(DISTRIBUTION_TYPE);
        if (StringUtils.isBlank(distributionType)) {
            return false;
        }
        List<String> privateLiveSet = Lion.getList(Environment.getAppName(), "private.live.order.distribution.type", String.class, defaultPrivateLiveList);
        return privateLiveSet.contains(distributionType);
    }

}
