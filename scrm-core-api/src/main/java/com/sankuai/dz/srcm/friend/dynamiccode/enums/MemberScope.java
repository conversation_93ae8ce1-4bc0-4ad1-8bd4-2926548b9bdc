package com.sankuai.dz.srcm.friend.dynamiccode.enums;

import lombok.Getter;

@Getter
public enum MemberScope {
    PARTIAL_MEMBER(0, "部分成员"),
    ALL_MEMBER(1, "全部成员");

    private final int code;

    private final String desc;

    MemberScope(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MemberScope getMemberScopeByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberScope scope : values()) {
            if (code.equals(scope.getCode())) {
                return scope;
            }
        }
        return null;
    }
}
