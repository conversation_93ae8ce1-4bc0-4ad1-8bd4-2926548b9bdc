package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmAmProcessOrchestrationDistributorCodeDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationDistributorCodeDO, ScrmAmProcessOrchestrationDistributorCodeDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationDistributorCodeDO> list);
}