package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.adapter.service.ActivityProcessor;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.group.GroupMemberInviteCountDTO;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@PrivateLiveProcessor(wechatType = WeChatType.PERSONAL_WECHAT, appId = PchatConfig.AppID)
public class TuseActivityProcessor implements ActivityProcessor {

    @Resource
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Override
    public List<GroupMemberInviteCountDTO> queryInviteCount(ScrmPersonalWxActivity activity, List<String> wxIdList, List<Long> groupKeyList) {
        return groupManageDomainService.queryGroupMemberAggregateInviteWxCount(wxIdList, groupKeyList);
    }
}
