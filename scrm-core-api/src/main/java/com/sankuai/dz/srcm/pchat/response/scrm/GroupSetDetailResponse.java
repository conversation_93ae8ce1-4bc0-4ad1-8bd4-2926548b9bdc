package com.sankuai.dz.srcm.pchat.response.scrm;


import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.request.Request;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/14 16:14
 * @@Version v1.0.0
 */
@Data
public class GroupSetDetailResponse extends Request {

    /**
     * 群组id
     */
    private Long setId;
    /**
     * 组名对内
     */
    private String setName;

    /**
     * 群名
     */
    private String groupName;

    /**
     * 群公告
     */
    private String groupNotice;

    /**
     * 群数量
     */
    private Integer groupCount;

    /**
     * 群主
     */
    private String groupOwner;

    /**
     * 咨询师信息
     */
    private String consultantInfo;
    private List<ConsultantInfoDTO> consultantlist;

    /**
     * 满员自动续群，默认true
     */
    private Boolean isAutoCreateNewGroup;

    /**
     * 网络直播项目id
     */
    private String webcastId;

    /**
     * 网络直播项目名
     */
    private String webcastName;

    /**
     * 欢迎语
     */
    private List<GroupMsg> groupWelcomeMsg;


    public boolean check() {
        return true;
    }

}