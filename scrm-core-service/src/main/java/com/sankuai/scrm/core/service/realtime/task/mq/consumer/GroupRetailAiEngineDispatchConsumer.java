package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.cat.Cat;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.enums.IntelligentFollowActionType;
import com.sankuai.dz.srcm.friend.dynamiccode.enums.ContactUserActionType;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmIntelligentFollowTaskLogDO;
import com.sankuai.scrm.core.service.aigc.service.dal.example.ScrmIntelligentFollowTaskLogDOExample;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmIntelligentFollowTaskLogDOMapper;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsFridayIntelligentFollowLog;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper;
import com.sankuai.scrm.core.service.data.statistics.dal.entity.ContactUserLogDO;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.request.UserTrackRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.track.result.UserTrackResult;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmFridayIntelligentFollowLogDO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmRealtimeSceneUserRecordDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineFlowLimitProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GroupRetailAiEngineDispatchConsumer implements InitializingBean, DisposableBean {

    @Autowired
    private RealTimeTaskConsumerConfig consumerConfig;

    @Autowired
    private ScrmFridayIntelligentFollowDomainService fridayIntelligentFollowDomainService;

    @Resource
    private GroupRetailAiEngineFlowLimitProducer groupRetailAiEngineFlowLimitProducer;

    @Autowired
    private MtUserCenterAclService mtUserCenterAclService;

    @Autowired
    private PrivateChatDomainService privateChatDomainService;

    @Autowired
    private PoiRelationService poiRelationService;

    @Autowired
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Resource
    private ScrmIntelligentFollowTaskLogDOMapper intelligentFollowTaskLogDOMapper;

    @Resource
    private ScrmRealtimeSceneUserRecordDOMapper realtimeSceneUserRecordDOMapper;

    @Resource
    private ContactUserLogDOMapper contactUserLogDOMapper;

    @Resource
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @Autowired
    private UserTrackAcl userTrackAcl;

    @Autowired
    private RedisStoreClient redisClient;


    private IConsumerProcessor consumer;

    private static final String CATALOG_NAME = "intelligent_follow_task_lock";

    private static final String CAT_TYPE = GroupRetailAiEngineDispatchConsumer.class.getSimpleName();

    private ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        try {
            Cat.logEvent(CAT_TYPE, "GroupRetailAiEngineDispatchConsumer");
            if(StringUtils.isBlank(message.getBody())){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            GroupRetailAiEngineDispatchMessageDTO messageDTO = JsonUtils.toObjectSafe(message.getBody(), GroupRetailAiEngineDispatchMessageDTO.class);
            if(messageDTO.getActionType() == 1){
                // 本段逻辑已迁移到GroupRetailAiEngineABTestRecordMessageConsumer
                scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(messageDTO.getUserId(), messageDTO.getAppId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (false == consumerConfig.checkInEffectiveTime()) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String dateKey = DateUtil.formatYMd(new Date());

            if(false == consumerConfig.isInWhitelist(messageDTO.getUserId(), messageDTO.getAppId())){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Calendar logExpireCalendar = Calendar.getInstance();
            logExpireCalendar.add(Calendar.MINUTE, -10);
            ScrmIntelligentFollowTaskLogDOExample example = new ScrmIntelligentFollowTaskLogDOExample();
            example.createCriteria().andMtUseridEqualTo(messageDTO.getUserId()).andAppIdIn(consumerConfig.getValidAppIds()).andDateKeyEqualTo(dateKey);
            List<ScrmIntelligentFollowTaskLogDO> intelligentFollowTaskLogDOList = intelligentFollowTaskLogDOMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(intelligentFollowTaskLogDOList)) {
                if(consumerConfig.isInWhitelistPure(messageDTO.getUserId()) && intelligentFollowTaskLogDOList.get(0).getUpdateTime().before(logExpireCalendar.getTime())){
                    intelligentFollowTaskLogDOMapper.deleteByPrimaryKey(intelligentFollowTaskLogDOList.get(0).getId());
                }else {
                    log.warn("intelligentFollowTaskLog of today exist");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
            }
            Long bizId = null;
            StoreKey storeKey = new StoreKey(CATALOG_NAME, messageDTO.getUserId(), dateKey);
            if (BooleanUtils.isNotTrue(redisClient.setnx(storeKey, true, 60))) {
                log.warn("intelligentFollowTaskLog of today exist");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            try{
                ScrmIntelligentFollowTaskLogDO intelligentFollowTaskLogDO = new ScrmIntelligentFollowTaskLogDO();
                intelligentFollowTaskLogDO.setExecuteNum(1);
                intelligentFollowTaskLogDO.setDateKey(dateKey);
                intelligentFollowTaskLogDO.setMtUserid(messageDTO.getUserId());
                intelligentFollowTaskLogDO.setAppId(messageDTO.getAppId());
                int insertResult = intelligentFollowTaskLogDOMapper.insertSelective(intelligentFollowTaskLogDO);
                if(insertResult <= 0){
                    Cat.logEvent(CAT_TYPE, "intelligentFollowTaskLog insert failed");
                    log.error("intelligentFollowTaskLog insert failed");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                bizId = intelligentFollowTaskLogDO.getId();
            } catch (Exception e){
                log.error("intelligentFollowTaskLog insert failed", e);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            IntelligentFollowActionTrackDTO intelligentFollowActionTrackDTO = new IntelligentFollowActionTrackDTO();
            intelligentFollowActionTrackDTO.setUserId(messageDTO.getUserId());
            intelligentFollowActionTrackDTO.setAppId(messageDTO.getAppId());
            intelligentFollowActionTrackDTO.setActionList(new ArrayList<>());
            Date now = new Date();
            Date dateSub = DateUtil.dateSub(now, consumerConfig.getDayNum());
            UserTrackRequest mtUserTrack = UserTrackRequest.convert(1, messageDTO.getUserId(), dateSub.getTime(), now.getTime());
            List<UserTrackResult> mtUserTrackResultList = userTrackAcl.queryUserTrack(mtUserTrack);
            Long dpUserId = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(messageDTO.getUserId());
            if (Objects.nonNull(dpUserId)) {
                UserTrackRequest dpUserTrack = UserTrackRequest.convert(2, dpUserId, dateSub.getTime(), now.getTime());
                List<UserTrackResult> dpUserTrackResultList = userTrackAcl.queryUserTrack(dpUserTrack);
                if(CollectionUtils.isNotEmpty(dpUserTrackResultList)){
                    mtUserTrackResultList.addAll(dpUserTrackResultList);
                }
            }
            if(checkIsOrderedInSpecifiedTime(mtUserTrackResultList)){
                log.info("filterOrderedUserTime is not null and track is ordered in specified time, message is {} track is {}",JsonUtils.toStr(message), JsonUtils.toStr(mtUserTrackResultList));
                recordFailLog(messageDTO.getUserId(),bizId,"filterOrderedUserTime is not null and track is ordered in specified time",messageDTO.getAppId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //最近15条，走config
            List<UserTrackResult> filterResult = mtUserTrackResultList.stream()
                    .filter(track -> !consumerConfig.ifExcludeType(String.valueOf(track.getActionType())))
                    .sorted(Comparator.comparing(UserTrackResult::getActionTimestamp).reversed())
                    .limit(consumerConfig.getFootPrintRecordNum())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterResult)) {
                log.info("mtUserTrackResultList is empty");
                recordFailLog(messageDTO.getUserId(),bizId,"mtUserTrackResultList is empty",messageDTO.getAppId());
                Cat.logEvent(CAT_TYPE, "mtUserTrackResultList is empty");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            handleUserTrackResult(filterResult, intelligentFollowActionTrackDTO);
            processActions(messageDTO, intelligentFollowActionTrackDTO);
            setEsFridayIntelligentFollowLog(bizId, messageDTO.getUserId(), messageDTO.getAppId());
            Set<Integer> actionTypeCodes = IntelligentFollowActionType.getInSiteActionTypeCodes();
            if(CollectionUtils.isEmpty(intelligentFollowActionTrackDTO.getActionList())
                    || intelligentFollowActionTrackDTO.getActionList().stream().noneMatch(o->actionTypeCodes.contains(o.getActionType()))){
                log.info("intelligentFollowActionTrackDTO.getActionList() is empty Or contains non inSite action: {}", JsonUtils.toStr(intelligentFollowActionTrackDTO));
                recordFailLog(messageDTO.getUserId(),bizId,"intelligentFollowActionTrackDTO.getActionList() is empty Or contains non inSite action",messageDTO.getAppId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            groupRetailAiEngineFlowLimitProducer.sendMessage(intelligentFollowActionTrackDTO);
            Cat.logEvent(CAT_TYPE, "success");
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "error");
            log.error("GroupRetailAiEngineDispatchConsumer error, message is {}", JsonUtils.toStr(message), e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    private boolean checkIsOrderedInSpecifiedTime( List<UserTrackResult> mtUserTrackResultList) {
        Date filterOrderedUserTime = consumerConfig.getFilterOrderedUserTime();
        if(Objects.isNull(filterOrderedUserTime)){
            return false;
        }
        return mtUserTrackResultList.stream().anyMatch(track ->
                (IntelligentFollowActionType.ORDER.getCode() == track.getActionType()
                        || IntelligentFollowActionType.PAY.getCode() == track.getActionType())
                        && track.getActionTimestamp() > filterOrderedUserTime.getTime());
    }

    public void setEsFridayIntelligentFollowLog(Long bizId,Long userId,String appId){
        EsFridayIntelligentFollowLog fridayLog = EsFridayIntelligentFollowLog
                .builder()
                .id(buildBizId(bizId,userId))
                .appId(appId)
                .userId(userId.toString())
                .dateKey(DateUtil.formatYMd(new Date()))
                .build();
        StoreKey storeKey = new StoreKey("friday_intelligent_follow_log", userId, appId);
        try {
            redisClient.set(storeKey, JsonUtils.toStr(fridayLog), 600);
            log.info("EsFridayIntelligentFollowLog stored to redis with key: {}", userId);
        } catch (Exception e) {
            log.error("Failed to store EsFridayIntelligentFollowLog to redis, key: {}", userId, e);
            Cat.logEvent(CAT_TYPE, "redis_store_error");
        }
    }

    public void recordFailLog(Long userId,Long bizId,String failReason,String appId) {
        Date date = new Date();
        ScrmFridayIntelligentFollowLogDO logDO = ScrmFridayIntelligentFollowLogDO.builder()
                .bizId(buildBizId(bizId,userId))
                .failReason(failReason)
                .userId(userId)
                .appId(appId)
                .needSend(false)
                .dateKey(DateUtil.formatYMd(date))
                .addTime(date)
                .updateTime(date)
                .build();
        fridayIntelligentFollowLogDOMapper.insert(logDO);
    }

    public String buildBizId(Long bizId,Long userId){
        return bizId + "-" + userId;
    }

    private void processActions(GroupRetailAiEngineDispatchMessageDTO messageDTO, IntelligentFollowActionTrackDTO intelligentFollowActionTrackDTO) {
        try {
            Cat.logEvent(CAT_TYPE, "processActions");
            String unionId = mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(messageDTO.getUserId(),
                    GroupDynamicCodeConstants.MX_MINIP_APPID);
            Date addTime = DateUtil.dateSub(new Date(), consumerConfig.getConfigDTO().getDayNum());

            List<ScrmRealtimeSceneUserRecordDO> realtimeSceneUserRecordList = new ArrayList<>();
            List<Integer> sceneIds = consumerConfig.getSceneIdsByAppId(messageDTO.getAppId());
            if (CollectionUtils.isNotEmpty(sceneIds)) {
                ScrmRealtimeSceneUserRecordDOExample realTime = new ScrmRealtimeSceneUserRecordDOExample();
                realTime.createCriteria().andUseridEqualTo(messageDTO.getUserId())
                        .andAppidEqualTo(messageDTO.getAppId()).andSceneidIn(sceneIds).andAddTimeGreaterThanOrEqualTo(
                                DateUtil.dateSub(new Date(), consumerConfig.getConfigDTO().getDayNum()));
                realtimeSceneUserRecordList = realtimeSceneUserRecordDOMapper.selectByExample(realTime);
            }

            List<ContactUserLogDO> contactUserList = Lists.newArrayList();
            List<OperatorHelperPrivateChat> chatList = Lists.newArrayList();
            if (StringUtils.isNotBlank(unionId)) {
                contactUserList = contactUserLogDOMapper.selectByUnionId(unionId, addTime);
                chatList = privateChatDomainService.queryPrivateChatLogForActions(unionId, messageDTO.getAppId(),
                        addTime);
            }

            Map<Integer, List<ContactUserLogDO>> actionTypeGroup = contactUserList.stream()
                    .filter(log -> log.getActionTime() != null)
                    .collect(Collectors.groupingBy(ContactUserLogDO::getActionType));
            List<ContactUserLogDO> add = actionTypeGroup
                    .getOrDefault(ContactUserActionType.EXTERNAL_USER_ADD_FRIEND.getCode(), Lists.newArrayList());
            List<ContactUserLogDO> delete = actionTypeGroup
                    .getOrDefault(ContactUserActionType.EXTERNAL_USER_DELETE_FRIEND.getCode(), Lists.newArrayList());
            realtimeSceneUserRecordList.forEach(r -> {
                IntelligentFollowActionDTO action = new IntelligentFollowActionDTO();
                action.setActionType(IntelligentFollowActionType.ENTER_SHOP_NOT_BUY.getCode());
                action.setActionTime(DateUtil.formatYMdHms(r.getUpdateTime()));
                action.setActionContent(r.getSensorMtPoiId());
                intelligentFollowActionTrackDTO.getActionList().add(action);
            });
            add.forEach(r -> {
                IntelligentFollowActionDTO action = new IntelligentFollowActionDTO();
                action.setActionType(IntelligentFollowActionType.USER_ADD_FRIEND.getCode());
                action.setActionTime(DateUtil.formatYMdHms(r.getActionTime()));
                action.setActionContent(IntelligentFollowActionType.USER_ADD_FRIEND.getDesc());
                intelligentFollowActionTrackDTO.getActionList().add(action);
            });
            delete.forEach(r -> {
                IntelligentFollowActionDTO action = new IntelligentFollowActionDTO();
                action.setActionType(IntelligentFollowActionType.USER_DELETE_FRIEND.getCode());
                action.setActionTime(DateUtil.formatYMdHms(r.getActionTime()));
                action.setActionContent(IntelligentFollowActionType.USER_DELETE_FRIEND.getDesc());
                intelligentFollowActionTrackDTO.getActionList().add(action);
            });

            chatList.forEach(r -> {
                IntelligentFollowActionDTO action = new IntelligentFollowActionDTO();
                action.setActionType(IntelligentFollowActionType.USER_SEND_MSG.getCode());
                action.setActionTime(DateUtil.formatYMdHms(r.getCreatetime()));
                action.setActionContent(r.getContent());
                intelligentFollowActionTrackDTO.getActionList().add(action);
            });
            Cat.logEvent(CAT_TYPE, "processActionsSuccess");
        } catch (Exception e) {
            log.error("processActions error", e);
            Cat.logEvent(CAT_TYPE,"processActionsError");
            throw e;
        }
    }

    private void handleUserTrackResult(List<UserTrackResult> filterResult,IntelligentFollowActionTrackDTO intelligentFollowActionTrackDTO) throws Exception {
        try{
            Cat.logEvent(CAT_TYPE,"handleUserTrackResult");
            for(UserTrackResult userTrackResult : filterResult){
                IntelligentFollowActionDTO action = new IntelligentFollowActionDTO();
                action.setActionType(userTrackResult.getActionType());
                action.setActionTime(DateUtil.formatYMdHms(userTrackResult.getActionTimestamp()));
                action.setActionContent(userTrackResult.getActionContent());
                if(userTrackResult.getActionType() == IntelligentFollowActionType.VIEW_SHOP.getCode() || userTrackResult.getActionType() == IntelligentFollowActionType.ENTER_SHOP_NOT_BUY.getCode()){
                    if(userTrackResult.getPlatForm().equalsIgnoreCase("dp")){
                        List<Long> poiIds = poiRelationService.queryMtByDpIdL(Long.parseLong(userTrackResult.getActionContent()));
                        if(CollectionUtils.isNotEmpty(poiIds)){
                            action.setActionContent(String.valueOf(poiIds.get(0)));
                        }
                    }
                }
                intelligentFollowActionTrackDTO.getActionList().add(action);
            }
            Cat.logEvent(CAT_TYPE,"handleUserTrackResultSuccess");
        }catch (Exception e){
            log.error("handleUserTrackResult error",e);
            Cat.logEvent(CAT_TYPE,"handleUserTrackResultError");
            throw e;
        }
    }

    @Override
    public void destroy() throws Exception {
        if (this.consumer != null) {
            this.consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.group.retail.ai.engine.dispatch.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildCommonConsumerFactory(properties, "scrm.group.retail.ai.engine.dispatch");
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }

}