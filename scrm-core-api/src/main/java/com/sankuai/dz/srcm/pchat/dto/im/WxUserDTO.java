package com.sankuai.dz.srcm.pchat.dto.im;

import lombok.Data;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/15 10:16
 * @Version v1.0.0
 */
@Data
public class WxUserDTO {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: serial_no
     *   说明: 微信序列号
     */
    private String serialNo;

    /**
     *   字段: wx_id
     *   说明: 微信id
     */
    private String wxId;

    /**
     *   字段: nickname
     *   说明: 昵称
     */
    private String nickname;

    /**
     *   字段: avatar
     *   说明: 头像
     */
    private String avatar;

    /**
     *   字段: sex
     *   说明: 性别 0未知 1男 2女
     */
    private String sex;

    /**
     *   字段: creator
     *   说明: 创建者的mis号
     */
    private String creator;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: app_id
     *   说明: app_id，用于区分业务
     */
    private String appId;

    /**
     *   字段: member_type
     *   说明: 成员类型，1-机器人 2 普通用户
     */
    private Byte memberType;

    /**
     *   字段: wx_alias
     *   说明: 微信号
     */
    private String wxAlias;

    /**
     *   字段: union_id
     *   说明: 识别出的在美小的用户unionid
     */
    private String unionId;

    /**
     *   字段: user_id
     *   说明: 识别出的在美团侧的用户userid
     */
    private Long userId;

    /**
     * 1 在线 0 不在线
     */
    private Integer online;


}
