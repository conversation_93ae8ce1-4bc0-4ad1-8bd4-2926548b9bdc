package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.coupon.dto.IssueNextCouponResultDTO;
import com.sankuai.dz.srcm.coupon.request.IssueNextCouponRequest;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import static org.mockito.ArgumentMatchers.*;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueBaseResponse;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.CouponModuleInfoDTO;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.coupon.dto.ValidNextCouponResultDTO;
import com.sankuai.mpmkt.coupon.issue.api.result.CouponIssueValidateDetail;
import com.sankuai.mpmkt.coupon.issue.api.result.CouponIssueValidateResult;
import com.sankuai.scrm.core.service.activity.fission.service.ActivityFissionServiceImpl;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActivSceneCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class NextCouponServiceImplDoIssueCouponInternalTest {

    @Mock
    private ActivityFissionServiceImpl activityFissionService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActivSceneCodeDOMapper scrmAmProcessOrchestrationActivSceneCodeDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper scrmAmProcessOrchestrationDistributorCodeDOMapper;

    @Mock
    private CouponDomainService couponDomainService;

    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;

    @InjectMocks
    private NextCouponServiceImpl nextCouponService;

    private IssueNextCouponRequest validRequest;

    private MktCouponInfoDTO mktCouponInfoDTO;

    /**
     * Use reflection to set the private static final log field in NextCouponServiceImpl
     */
    private static void setLoggerViaReflection(NextCouponServiceImpl target, Logger logger) throws Exception {
        Field logField = null;
        Class<?> clazz = target.getClass();
        while (clazz != null) {
            try {
                logField = clazz.getDeclaredField("log");
                break;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        if (logField == null) {
            throw new IllegalStateException("Logger field not found");
        }
        logField.setAccessible(true);
        // Remove final modifier if present
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(logField, logField.getModifiers() & ~java.lang.reflect.Modifier.FINAL);
        logField.set(target, logger);
    }

    /**
     * Test normal case where issueCouponStrictMode returns successfully
     */
    @Test
    public void testDoIssueCouponInternalNormalCase() throws Throwable {
        // arrange
        IssueNextCouponRequest request = new IssueNextCouponRequest();
        IssueNextCouponResultDTO expectedResult = new IssueNextCouponResultDTO();
        NextCouponServiceImpl spy = Mockito.spy(new NextCouponServiceImpl());
        doReturn(expectedResult).when(spy).issueCouponStrictMode(request);
        // act
        IssueNextCouponResultDTO result = spy.doIssueCouponInternal(request);
        // assert
        assertSame(expectedResult, result);
        verify(spy).issueCouponStrictMode(request);
    }

    /**
     * Test case where issueCouponStrictMode throws an exception, and verify error log
     */
    @Test
    public void testDoIssueCouponInternalExceptionCase() throws Throwable {
        // arrange
        IssueNextCouponRequest request = new IssueNextCouponRequest();
        RuntimeException exception = new RuntimeException("Test exception");
        NextCouponServiceImpl spy = Mockito.spy(new NextCouponServiceImpl());
        doThrow(exception).when(spy).issueCouponStrictMode(request);
        // inject mock logger
        Logger mockLogger = mock(Logger.class);
        setLoggerViaReflection(spy, mockLogger);
        // act
        IssueNextCouponResultDTO result = spy.doIssueCouponInternal(request);
        // assert
        assertNull(result);
        verify(spy).issueCouponStrictMode(request);
        // verify error log
        verify(mockLogger).error(eq("NextCouponServiceImpl doIssueCouponInternal error"), eq(exception));
    }

    /**
     * Test case with null request
     */
    @Test
    public void testDoIssueCouponInternalNullRequest() throws Throwable {
        // arrange
        IssueNextCouponResultDTO expectedResult = new IssueNextCouponResultDTO();
        NextCouponServiceImpl spy = Mockito.spy(new NextCouponServiceImpl());
        doReturn(expectedResult).when(spy).issueCouponStrictMode(null);
        // act
        IssueNextCouponResultDTO result = spy.doIssueCouponInternal(null);
        // assert
        assertSame(expectedResult, result);
        verify(spy).issueCouponStrictMode(null);
    }

    @BeforeEach
    void setUp() {
        validRequest = new IssueNextCouponRequest();
        validRequest.setSceneCode("testScene");
        validRequest.setMtUserId(12345L);
        validRequest.setCouponGroupId("testCouponGroup");
        mktCouponInfoDTO = new MktCouponInfoDTO();
        mktCouponInfoDTO.setCouponId("testCouponGroup");
        mktCouponInfoDTO.setCouponGroupName("Test Coupon");
        mktCouponInfoDTO.setDiscountAmount(new BigDecimal("10.00"));
        mktCouponInfoDTO.setPriceLimit(new BigDecimal("100.00"));
        mktCouponInfoDTO.setValidBeginTime(new Date());
        mktCouponInfoDTO.setValidEndTime(new Date(System.currentTimeMillis() + 86400000));
    }

    @Test
    public void testValidCouponUserHasMultipleExistingCoupons() throws Throwable {
        when(activityFissionService.queryMktCouponInfo(validRequest.getCouponGroupId())).thenReturn(RemoteResponse.success(mktCouponInfoDTO));
        // 创建三条记录，但只有一条在最近一小时内
        List<ScrmSceneCouponRecords> records = new ArrayList<>();
        // 最新的记录 - 在时间窗口内
        ScrmSceneCouponRecords record1 = new ScrmSceneCouponRecords();
        record1.setUnifiedcouponid("unified0");
        record1.setCoupongroupid(validRequest.getCouponGroupId());
        record1.setUserid(validRequest.getMtUserId());
        record1.setCoupongroupname("Test Coupon");
        record1.setBegintime(new Date());
        record1.setEndtime(new Date(System.currentTimeMillis() + 86400000));
        record1.setCouponamount(new BigDecimal("10.00"));
        record1.setPricelimit(new BigDecimal("100.00"));
        // 当前时间
        record1.setAddTime(new Date());
        records.add(record1);
        // 较旧的记录 - 超出时间窗口
        ScrmSceneCouponRecords record2 = new ScrmSceneCouponRecords();
        record2.setUnifiedcouponid("unified1");
        record2.setCoupongroupid(validRequest.getCouponGroupId());
        record2.setUserid(validRequest.getMtUserId());
        record2.setCoupongroupname("Test Coupon");
        record2.setBegintime(new Date());
        record2.setEndtime(new Date(System.currentTimeMillis() + 86400000));
        record2.setCouponamount(new BigDecimal("10.00"));
        record2.setPricelimit(new BigDecimal("100.00"));
        // 61分钟前
        record2.setAddTime(new Date(System.currentTimeMillis() - 61 * 60 * 1000));
        records.add(record2);
        // 更旧的记录 - 超出时间窗口
        ScrmSceneCouponRecords record3 = new ScrmSceneCouponRecords();
        record3.setUnifiedcouponid("unified2");
        record3.setCoupongroupid(validRequest.getCouponGroupId());
        record3.setUserid(validRequest.getMtUserId());
        record3.setCoupongroupname("Test Coupon");
        record3.setBegintime(new Date());
        record3.setEndtime(new Date(System.currentTimeMillis() + 86400000));
        record3.setCouponamount(new BigDecimal("10.00"));
        record3.setPricelimit(new BigDecimal("100.00"));
        // 120分钟前
        record3.setAddTime(new Date(System.currentTimeMillis() - 120 * 60 * 1000));
        records.add(record3);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(records);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setProcessOrchestrationId(1L);
        sceneCodeDO.setProcessOrchestrationNodeId(1L);
        when(scrmAmProcessOrchestrationActivSceneCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneCodeDO));
        ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setDistributorCode("dist123");
        when(scrmAmProcessOrchestrationDistributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        UnifiedCouponIssueBaseResponse<CouponIssueValidateResult> validationResponse = new UnifiedCouponIssueBaseResponse<>();
        validationResponse.setSuccess(false);
        when(couponDomainService.preValidMtCouponAndResponse(eq(validRequest.getMtUserId()), eq(validRequest.getCouponGroupId()), eq(distributorCodeDO.getDistributorCode()))).thenReturn(validationResponse);
        List<UnifiedCouponDTO> unifiedCoupons = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            UnifiedCouponDTO dto = mock(UnifiedCouponDTO.class);
            when(dto.getUnifiedCouponId()).thenReturn("unified" + i);
            // 只有第一个是已使用的
            when(dto.isUsed()).thenReturn(i == 0);
            unifiedCoupons.add(dto);
        }
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> batchResponse = new UnifiedCouponManageResponse<>();
        batchResponse.setSuccess(true);
        batchResponse.setResult(unifiedCoupons);
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(batchResponse);
        RemoteResponse<ValidNextCouponResultDTO> response = nextCouponService.validCoupon(validRequest);
        assertTrue(response.isSuccess());
        ValidNextCouponResultDTO result = response.getData();
        assertFalse(result.isNeverClaimed());
        assertFalse(result.isAcquirable());
        assertNotNull(result.getCouponModuleInfo());
        // 由于时间窗口筛选，应该只有1条记录
        assertEquals(1, result.getCouponModuleInfo().getExistedCouponInfos().size());
        assertTrue(result.getCouponModuleInfo().getExistedCouponInfos().get(0).getAlreadyUsed());
    }

    @Test
    public void testValidCouponNoExistingCouponsValidationFails() throws Throwable {
        when(activityFissionService.queryMktCouponInfo(validRequest.getCouponGroupId())).thenReturn(RemoteResponse.success(mktCouponInfoDTO));
        when(scrmSceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.emptyList());
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setProcessOrchestrationId(1L);
        sceneCodeDO.setProcessOrchestrationNodeId(1L);
        when(scrmAmProcessOrchestrationActivSceneCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneCodeDO));
        ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setDistributorCode("dist123");
        when(scrmAmProcessOrchestrationDistributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        UnifiedCouponIssueBaseResponse<CouponIssueValidateResult> validationResponse = new UnifiedCouponIssueBaseResponse<>();
        validationResponse.setSuccess(false);
        when(couponDomainService.preValidMtCouponAndResponse(eq(validRequest.getMtUserId()), eq(validRequest.getCouponGroupId()), eq(distributorCodeDO.getDistributorCode()))).thenReturn(validationResponse);
        RemoteResponse<ValidNextCouponResultDTO> response = nextCouponService.validCoupon(validRequest);
        assertTrue(response.isSuccess());
        assertEquals("发券预校验失败", response.getMsg());
        ValidNextCouponResultDTO result = response.getData();
        assertTrue(result.isNeverClaimed());
        assertFalse(result.isAcquirable());
    }

    @Test
    public void testValidCouponExceptionOccurs() throws Throwable {
        when(activityFissionService.queryMktCouponInfo(validRequest.getCouponGroupId())).thenThrow(new RuntimeException("Test exception"));
        RemoteResponse<ValidNextCouponResultDTO> response = nextCouponService.validCoupon(validRequest);
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
    }
}
