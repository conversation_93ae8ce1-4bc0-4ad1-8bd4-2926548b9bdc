package com.sankuai.scrm.core.service.pchat.acl.authorization.dto;

import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginAnchorInfoDTO;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import lombok.Data;

import java.util.Map;

/**
 * 权限校验服务
 *
 * <AUTHOR>
 * @date 2024/8/21
 */
@Data
public class AuthorizationCheckRequest {

    /**
     * 权限校验的code
     *
     * @see AuthorityCodeEnum
     */
    private Integer authorityCode;

    /**
     * 操作的直播id
     */
    private String liveId;

    /**
     * 当前的操作人的anchorid
     */
    private Long currentAnchorId;

    /**
     * 当前的操作人
     */
    private Long mainAnchorId;

    private LoginAnchorInfoDTO loginAnchorInfoDTO;
    /**
     * 扩展属性
     */
    private Map<String, String> extMap;

}
