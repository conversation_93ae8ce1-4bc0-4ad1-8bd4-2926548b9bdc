package com.sankuai.scrm.core.service.message.push.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupDomainService;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushLog;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushTask;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushLogDomainService;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChatType;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushSceneType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateRegularMsgPushHandlerTest {

    @InjectMocks
    private PrivateRegularMsgPushHandler handler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private MsgPushLogDomainService msgPushLogDomainService;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Test
    public void canHandle() {
        MsgPushChatType chatType = MsgPushChatType.PRIVATE;
        MsgPushSceneType sceneType = MsgPushSceneType.REGULAR_SCENE;
        boolean result = handler.canHandle(chatType, sceneType);
        assertTrue(result);
    }

    @Test
    public void fillPushChannelReceiverIdSet() {
        MsgPushTask task = new MsgPushTask();
        task.setAppId("appId");
        List<String> receiverIdList = Lists.newArrayList("s1", "s1", "s3");
        Map<String, String> dsPushMap = Maps.newHashMap();
        Map<String, String> wxPushMap = Maps.newHashMap();
        Map<String, String> unablePushMap = Maps.newHashMap();
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(msgPushLogDomainService.queryMsgPushLogList(anyString(), any(), any(), anyList(), any(), any())).thenReturn(Lists.newArrayList(new MsgPushLog()));
        ContactUser contactUser1 = new ContactUser();
        contactUser1.setExternalUserId("s1");
        when(contactUserDomain.queryContactUsersByExternalUserIdList(anyString(), anyList())).thenReturn(Lists.newArrayList(contactUser1));
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAccountId("accountId");
        when(dsAssistantAcl.getAssistantList("appId")).thenReturn(Lists.newArrayList(assistantInfo));
        ContactUser contactUser2 = new ContactUser();
        contactUser2.setExternalUserId("s2");
        when(contactUserDomain.queryContactUsersByExternalUserIdListAndStaffIdList(anyString(), anyList(), anyList())).thenReturn(Lists.newArrayList(contactUser2));
        handler.fillPushChannelReceiverIdSet(task, receiverIdList, dsPushMap, wxPushMap, unablePushMap);
        assertTrue(wxPushMap.containsKey("s1"));
        assertTrue(dsPushMap.containsKey("s2"));
        assertTrue(unablePushMap.containsKey("s3"));
    }
}