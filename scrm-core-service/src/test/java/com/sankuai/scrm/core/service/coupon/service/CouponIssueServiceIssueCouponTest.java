package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.coupon.dto.IssueNextCouponResultDTO;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.coupon.request.IssueNextCouponRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationMockLog;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationMockLogMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponResult;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import java.lang.reflect.Method;
import org.apache.commons.lang3.StringUtils;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.CouponModuleInfoDTO;
import java.util.Collections;
import java.lang.reflect.InvocationTargetException;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;

@ExtendWith(MockitoExtension.class)
class CouponIssueServiceIssueCouponTest {

    @Mock
    private CouponIdempotencyChecker idempotencyChecker;

    @Mock
    private CouponMockHandler mockHandler;

    @Mock
    private NextCouponServiceImpl nextCouponService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationMockLogMapper mockLogMapper;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private SceneDomainService sceneDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @InjectMocks
    private CouponIssueService couponIssueService;

    /**
     * Test case for invalid parameters (null context)
     */
    @Test
    public void testIssueCouponNullContext() throws Throwable {
        // arrange
        CouponRequestContext context = null;
        // act
        CouponResult result = couponIssueService.issueCoupon(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    /**
     * Test case for invalid parameters (missing userId)
     */
    @Test
    public void testIssueCouponMissingUserId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setCouponGroupId("test-group");
        // act
        CouponResult result = couponIssueService.issueCoupon(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    /**
     * Test case for invalid parameters (blank couponGroupId)
     */
    @Test
    public void testIssueCouponBlankCouponGroupId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("");
        // act
        CouponResult result = couponIssueService.issueCoupon(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    /**
     * Test case for already received coupon
     */
    @Test
    public void testIssueCouponAlreadyReceived() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("test-group");
        when(idempotencyChecker.hasUserReceivedCoupon(eq(123L), eq("test-group"))).thenReturn(true);
        // act
        CouponResult result = couponIssueService.issueCoupon(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ALREADY_RECEIVED", result.getErrorCode());
        assertFalse(result.isNewCouponReceived());
        verify(idempotencyChecker).hasUserReceivedCoupon(eq(123L), eq("test-group"));
    }

    /**
     * Test case for mock scenario in process orchestration
     */
    @Test
    public void testIssueCouponMockScenario() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("test-group");
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        when(idempotencyChecker.hasUserReceivedCoupon(eq(123L), eq("test-group"))).thenReturn(false);
        CouponResult mockResult = CouponResult.fail("MOCK_ISSUE", "Mock发券");
        when(mockHandler.handleMockIssue(any(CouponRequestContext.class))).thenReturn(mockResult);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean(eq("test-app"), eq("coupon.distribution.switch.mock"), eq(true))).thenReturn(true);
            // act
            CouponResult result = couponIssueService.issueCoupon(context);
            // assert
            assertFalse(result.isSuccess());
            assertEquals("MOCK_ISSUE", result.getErrorCode());
            assertEquals("Mock发券", result.getErrorMessage());
            verify(mockHandler).handleMockIssue(any(CouponRequestContext.class));
        }
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testIssueCouponExceptionScenario() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("test-group");
        when(idempotencyChecker.hasUserReceivedCoupon(eq(123L), eq("test-group"))).thenThrow(new RuntimeException("Test exception"));
        // act
        CouponResult result = couponIssueService.issueCoupon(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("SYSTEM_ERROR", result.getErrorCode());
        assertEquals("系统异常", result.getErrorMessage());
    }

    private CouponResult invokeValidateRequest(CouponRequestContext context) throws Exception {
        Method method = CouponIssueService.class.getDeclaredMethod("validateRequest", CouponRequestContext.class);
        method.setAccessible(true);
        return (CouponResult) method.invoke(couponIssueService, context);
    }

    @Test
    public void testValidateRequestWithNullContext() throws Throwable {
        // arrange
        CouponRequestContext context = null;
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    @Test
    public void testValidateRequestWithNullUserId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(null);
        context.setCouponGroupId("testCouponGroupId");
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    @Test
    public void testValidateRequestWithNullCouponGroupId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId(null);
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    @Test
    public void testValidateRequestWithEmptyCouponGroupId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("");
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    @Test
    public void testValidateRequestWithBlankCouponGroupId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("   ");
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAM", result.getErrorCode());
        assertEquals("参数错误", result.getErrorMessage());
    }

    @Test
    public void testValidateRequestWithValidParameters() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("testCouponGroupId");
        context.setAppId("testAppId");
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        // act
        CouponResult result = invokeValidateRequest(context);
        // assert
        assertTrue(result.isSuccess());
        assertNull(result.getCouponInfoList());
        assertNull(result.getErrorCode());
        assertNull(result.getErrorMessage());
    }

    private Integer invokePrivateMethod(Byte input) throws Exception {
        Method method = CouponIssueService.class.getDeclaredMethod("getSceneTypeByProcessOrchestrationId", Byte.class);
        method.setAccessible(true);
        return (Integer) method.invoke(couponIssueService, input);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_NullInput() throws Throwable {
        // arrange
        Byte input = null;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.UNKNOWN.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_RealTimeTask() throws Throwable {
        // arrange
        Byte input = (byte) 3;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.REAL_TIME_TASK.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_ScheduledTaskCase4() throws Throwable {
        // arrange
        Byte input = (byte) 4;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.SCHEDULED_TASK.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_ScheduledTaskCase5() throws Throwable {
        // arrange
        Byte input = (byte) 5;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.SCHEDULED_TASK.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_UnknownInput() throws Throwable {
        // arrange
        // any value not 3,4,5
        Byte input = (byte) 99;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.UNKNOWN.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_MinByteValue() throws Throwable {
        // arrange
        Byte input = Byte.MIN_VALUE;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.UNKNOWN.getCode(), result);
    }

    @Test
    public void testGetSceneTypeByProcessOrchestrationId_MaxByteValue() throws Throwable {
        // arrange
        Byte input = Byte.MAX_VALUE;
        // act
        Integer result = invokePrivateMethod(input);
        // assert
        assertEquals(CreationSceneEnum.UNKNOWN.getCode(), result);
    }

    private CouponResult invokePerformNextCouponIssue(CouponRequestContext context) throws Exception {
        Method method = CouponIssueService.class.getDeclaredMethod("performNextCouponIssue", CouponRequestContext.class);
        method.setAccessible(true);
        return (CouponResult) method.invoke(couponIssueService, context);
    }

    private CouponResult invokeBuildCouponResult(IssueNextCouponResultDTO resultDTO) throws Exception {
        Method method = CouponIssueService.class.getDeclaredMethod("buildCouponResult", IssueNextCouponResultDTO.class);
        method.setAccessible(true);
        return (CouponResult) method.invoke(couponIssueService, resultDTO);
    }

    @Test
    public void testPerformNextCouponIssue_SuccessWithNewCoupon() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(true);
        // Create CouponModuleInfoDTO properly
        CouponModuleInfoDTO couponModuleInfo = new CouponModuleInfoDTO();
        List<NextCouponInfoDTO> couponInfos = new ArrayList<>();
        couponInfos.add(new NextCouponInfoDTO());
        couponModuleInfo.setExistedCouponInfos(couponInfos);
        resultDTO.setCouponModuleInfo(couponModuleInfo);
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(resultDTO);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertTrue(result.isSuccess());
        assertTrue(result.isNewCouponReceived());
        assertNotNull(result.getCouponInfoList());
    }

    @Test
    public void testPerformNextCouponIssue_AlreadyReceived() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(false);
        // Create CouponModuleInfoDTO properly
        CouponModuleInfoDTO couponModuleInfo = new CouponModuleInfoDTO();
        List<NextCouponInfoDTO> couponInfos = new ArrayList<>();
        couponInfos.add(new NextCouponInfoDTO());
        couponModuleInfo.setExistedCouponInfos(couponInfos);
        resultDTO.setCouponModuleInfo(couponModuleInfo);
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(resultDTO);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertFalse(result.isSuccess());
        assertFalse(result.isNewCouponReceived());
        assertEquals("ALREADY_RECEIVED", result.getErrorCode());
        assertNotNull(result.getCouponInfoList());
    }

    @Test
    public void testPerformNextCouponIssue_NullResultFromService() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(null);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("ISSUE_FAILED", result.getErrorCode());
        assertEquals("发券失败", result.getErrorMessage());
    }

    @Test
    public void testPerformNextCouponIssue_NullContext() throws Throwable {
        // arrange
        // no setup needed
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokePerformNextCouponIssue(null));
        // The root cause should be a NullPointerException
        Throwable cause = exception.getCause();
        assertTrue(cause instanceof NullPointerException);
    }

    @Test
    public void testPerformNextCouponIssue_WithCouponValidTime() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        context.setCouponValidTime(30);
        context.setSceneCode("TEST_SCENE");
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(true);
        // Create CouponModuleInfoDTO properly
        CouponModuleInfoDTO couponModuleInfo = new CouponModuleInfoDTO();
        List<NextCouponInfoDTO> couponInfos = new ArrayList<>();
        couponInfos.add(new NextCouponInfoDTO());
        couponModuleInfo.setExistedCouponInfos(couponInfos);
        resultDTO.setCouponModuleInfo(couponModuleInfo);
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(resultDTO);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertTrue(result.isSuccess());
        assertTrue(result.isNewCouponReceived());
        assertNotNull(result.getCouponInfoList());
        // Verify that couponValidTime was passed to the request
        verify(nextCouponService).doIssueCouponInternal(argThat(request -> request.getCouponValidTime() != null && request.getCouponValidTime() == 30));
    }

    @Test
    public void testBuildCouponResult_NullCouponModuleInfo() throws Throwable {
        // arrange
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(true);
        // Don't set couponModuleInfo, leaving it null
        // act & assert
        // We expect an NPE when calling buildCouponResult with null couponModuleInfo
        Exception exception = assertThrows(Exception.class, () -> invokeBuildCouponResult(resultDTO));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @Test
    public void testPerformNextCouponIssue_ZeroCouponValidTime() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        context.setCouponValidTime(0);
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(true);
        // Create CouponModuleInfoDTO properly
        CouponModuleInfoDTO couponModuleInfo = new CouponModuleInfoDTO();
        List<NextCouponInfoDTO> couponInfos = new ArrayList<>();
        couponInfos.add(new NextCouponInfoDTO());
        couponModuleInfo.setExistedCouponInfos(couponInfos);
        resultDTO.setCouponModuleInfo(couponModuleInfo);
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(resultDTO);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertTrue(result.isSuccess());
        // Verify that couponValidTime was not set in the request
        verify(nextCouponService).doIssueCouponInternal(argThat(request -> request.getCouponValidTime() == null));
    }

    @Test
    public void testPerformNextCouponIssue_NegativeCouponValidTime() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("GROUP_123");
        context.setCouponValidTime(-10);
        IssueNextCouponResultDTO resultDTO = new IssueNextCouponResultDTO();
        resultDTO.setNewCouponReceived(true);
        // Create CouponModuleInfoDTO properly
        CouponModuleInfoDTO couponModuleInfo = new CouponModuleInfoDTO();
        List<NextCouponInfoDTO> couponInfos = new ArrayList<>();
        couponInfos.add(new NextCouponInfoDTO());
        couponModuleInfo.setExistedCouponInfos(couponInfos);
        resultDTO.setCouponModuleInfo(couponModuleInfo);
        when(nextCouponService.doIssueCouponInternal(any(IssueNextCouponRequest.class))).thenReturn(resultDTO);
        // act
        CouponResult result = invokePerformNextCouponIssue(context);
        // assert
        assertTrue(result.isSuccess());
        // Verify that couponValidTime was not set in the request
        verify(nextCouponService).doIssueCouponInternal(argThat(request -> request.getCouponValidTime() == null));
    }

    @Test
    public void testShouldUseMock_WhenProcessOrchestrationAndMockSwitchTrue() throws Throwable {
        // arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getScene()).thenReturn(CouponSceneEnum.PROCESS_ORCHESTRATION);
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean("test-app", "coupon.distribution.switch.mock", true)).thenReturn(true);
            // act
            boolean result = (boolean) shouldUseMockMethod.invoke(couponIssueService, context);
            // assert
            assertTrue(result);
        }
    }

    @Test
    public void testShouldUseMock_WhenProcessOrchestrationButMockSwitchFalse() throws Throwable {
        // arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getScene()).thenReturn(CouponSceneEnum.PROCESS_ORCHESTRATION);
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean("test-app", "coupon.distribution.switch.mock", true)).thenReturn(false);
            // act
            boolean result = (boolean) shouldUseMockMethod.invoke(couponIssueService, context);
            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testShouldUseMock_WhenNotProcessOrchestrationButMockSwitchTrue() throws Throwable {
        // arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getScene()).thenReturn(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean("test-app", "coupon.distribution.switch.mock", true)).thenReturn(true);
            // act
            boolean result = (boolean) shouldUseMockMethod.invoke(couponIssueService, context);
            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testShouldUseMock_WhenNotProcessOrchestrationAndMockSwitchFalse() throws Throwable {
        // arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getScene()).thenReturn(CouponSceneEnum.NEXT_PLATFORM);
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean("test-app", "coupon.distribution.switch.mock", true)).thenReturn(false);
            // act
            boolean result = (boolean) shouldUseMockMethod.invoke(couponIssueService, context);
            // assert
            assertFalse(result);
        }
    }

    @Test
    public void testShouldUseMock_WhenContextIsNull() throws Throwable {
        // arrange
        CouponRequestContext context = null;
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        // act & assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            shouldUseMockMethod.invoke(couponIssueService, context);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @Test
    public void testShouldUseMock_WhenSceneIsNull() throws Throwable {
        // arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getScene()).thenReturn(null);
        Method shouldUseMockMethod = CouponIssueService.class.getDeclaredMethod("shouldUseMock", CouponRequestContext.class);
        shouldUseMockMethod.setAccessible(true);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBoolean("test-app", "coupon.distribution.switch.mock", true)).thenReturn(true);
            // act
            boolean result = (boolean) shouldUseMockMethod.invoke(couponIssueService, context);
            // assert
            assertFalse(result);
        }
    }

    private String invokeGetDistributorCode(CouponRequestContext context) throws Exception {
        Method method = CouponIssueService.class.getDeclaredMethod("getDistributorCode", CouponRequestContext.class);
        method.setAccessible(true);
        return (String) method.invoke(couponIssueService, context);
    }

    @Test
    public void testGetDistributorCodeSuccess() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(123L);
        context.setProcessOrchestrationNodeId(456L);
        context.setAppId("testApp");
        String expectedCode = "DIST123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(expectedCode);
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertEquals(expectedCode, result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }

    @Test
    public void testGetDistributorCodeNotFound() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(123L);
        context.setProcessOrchestrationNodeId(456L);
        context.setAppId("testApp");
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(null);
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertNull(result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }

    @Test
    public void testGetDistributorCodeException() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(123L);
        context.setProcessOrchestrationNodeId(456L);
        context.setAppId("testApp");
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertNull(result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }

    @Test
    public void testGetDistributorCodeNullContext() throws Throwable {
        // arrange
        // act
        String result = invokeGetDistributorCode(null);
        // assert
        assertNull(result);
        verifyNoInteractions(informationGatheringService);
    }

    @Test
    public void testGetDistributorCodeNullProcessOrchestrationId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(null);
        context.setProcessOrchestrationNodeId(456L);
        context.setAppId("testApp");
        String expectedCode = "DIST123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(expectedCode);
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertEquals(expectedCode, result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }

    @Test
    public void testGetDistributorCodeNullProcessOrchestrationNodeId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(123L);
        context.setProcessOrchestrationNodeId(null);
        context.setAppId("testApp");
        String expectedCode = "DIST123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(expectedCode);
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertEquals(expectedCode, result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }

    @Test
    public void testGetDistributorCodeNullAppId() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setProcessOrchestrationId(123L);
        context.setProcessOrchestrationNodeId(456L);
        context.setAppId(null);
        String expectedCode = "DIST123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(expectedCode);
        // act
        String result = invokeGetDistributorCode(context);
        // assert
        assertEquals(expectedCode, result);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
    }
}
