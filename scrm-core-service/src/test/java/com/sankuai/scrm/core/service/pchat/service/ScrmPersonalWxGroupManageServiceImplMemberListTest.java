package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupEditRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMemberListRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMemberInfoResponse;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.GroupEditionProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupConsultantMapping;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.domain.BizIdentificationService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxGroupManageServiceImplMemberListTest {

    @InjectMocks
    private ScrmPersonalWxGroupManageServiceImpl scrmPersonalWxGroupManageService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private ScrmPersonalWxGroupInfoEntityMapper personalWxGroupInfoEntityMapper;

    private GroupMemberListRequest request;

    private ScrmPersonalWxGroupInfoEntity groupInfoEntity;

    private ScrmPersonalWxGroupConsultantMapping groupConsultantMapping;

    @Mock
    private PrivateLiveAdapterRouter adapterRouter;

    @Mock
    private GroupEditionProcessor processor;

    @Mock
    private BizIdentificationService bizIdentificationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void setUpCommonMocks() {
        request = new GroupMemberListRequest();
        request.setConsultantTaskIds(Arrays.asList(1L, 2L, 3L));
        request.setGroupIds(new ArrayList<>());
        groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setId(1L);
        groupInfoEntity.setProjectId("projectId");
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomWxSerialNo");
        groupConsultantMapping = new ScrmPersonalWxGroupConsultantMapping();
        groupConsultantMapping.setGroupId(1L);
        when(groupManageDomainService.queryGroupById(any())).thenReturn(groupInfoEntity);
        when(groupManageDomainService.queryGroupConsultantMapping(any())).thenReturn(Collections.singletonList(groupConsultantMapping));
        when(groupManageDomainService.queryGroupAndFissionMemberList(any(), any())).thenReturn(new PagerList<>());
    }

    @Test(expected = RuntimeException.class)
    public void testMemberListGroupNotExist() throws Throwable {
        setUpCommonMocks();
        when(groupManageDomainService.queryGroupById(any())).thenReturn(null);
        scrmPersonalWxGroupManageService.memberList(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMemberListNoPermission() throws Throwable {
        setUpCommonMocks();
        groupInfoEntity.setProjectId(null);
        scrmPersonalWxGroupManageService.memberList(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMemberListQueryMemberFail() throws Throwable {
        setUpCommonMocks();
        scrmPersonalWxGroupManageService.memberList(request);
    }
}
