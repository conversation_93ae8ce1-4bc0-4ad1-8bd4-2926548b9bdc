package com.sankuai.dz.srcm.group.dynamiccode.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelConfigRequest;

import java.util.Map;

public interface GroupDynamicCodeChannelConfigService {

    RemoteResponse<?> addPoster(GroupDynamicCodeChannelConfigRequest request);

    RemoteResponse<?> updatePoster(GroupDynamicCodeChannelConfigRequest request);

    RemoteResponse<Map<String, Object>> queryPoster(String appId, Long id);
}
