package com.sankuai.scrm.core.service.infrastructure.acl.wx.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.ResultDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/6/21
 */
@NoArgsConstructor
@Data
public class WxGetMomentTaskResultResponse {

    @JsonProperty("errcode")
    private Integer errcode;

    @JsonProperty("errmsg")
    private String errmsg;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("type")
    private String type;

    @JsonProperty("result")
    private ResultDTO result;
}
