package com.sankuai.dz.srcm.pchat.request.activity;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/18 18:21
 * @Version v1.0.0
 */
@Data
public class ActivityRewardMyDetailRequest extends UnionIdRequest {


    /**
     * 奖品id
     */
    private Long rewardRecordId;

    @Override
    public boolean validParam() {
        this.assertNotNull(rewardRecordId, this.messageNotBlank("rewardRecordId"));

        return super.validParam();
    }


}