package com.sankuai.scrm.core.service.aigc.service.dal.entity;

import lombok.*;

import java.util.Date;

/**
 *
 * 表名: scrm_a_m_marketing_text_version_tag_mapping
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmMarketingTextVersionTagMappingDO {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: app_id
     * 说明: app_id
     */
    private String appId;

    /**
     * 字段: execute_log_id
     * 说明: 执行日志主键
     */
    private Long executeLogId;

    /**
     * 字段: product_id
     * 说明: 商品id
     */
    private Long productId;

    /**
     * 字段: prompt_version
     * 说明: prompt版本
     */
    private Integer promptVersion;

    /**
     * 字段: user_message_action_tag
     * 说明: 命中条件分组中的重点标签。90001-消息接收，90002-消息点击，90003-产生购买
     */
    private Long userMessageActionTag;

    /**
     * 字段: tag_recycle_time
     * 说明: 标签回收时间
     */
    private Date tagRecycleTime;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}