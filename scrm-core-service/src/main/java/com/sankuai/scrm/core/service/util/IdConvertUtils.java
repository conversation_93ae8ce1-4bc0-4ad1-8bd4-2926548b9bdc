package com.sankuai.scrm.core.service.util;

import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class IdConvertUtils {

    @Resource
    private MtUserCenterAclService mtUserCenterAclService;

    @Resource
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;

    public String convertMtUserIdToUnionId(Long mtUserId, String appId) {
        if (mtUserId == null || StringUtils.isBlank(appId)) {
            return null;
        }
        try {
            String unionId = externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(mtUserId, appId);
            if (unionId == null) {
                unionId = mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(mtUserId, "wxde8ac0a21135c07d");
            }
            return unionId;
        } catch (Exception ex) {
            log.error("IdConvertUtils.convertMtUserIdToUnionId error: ", ex);
            return null;
        }
    }
}
