package com.sankuai.scrm.core.service.pchat.adapter.service.ent;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeAndGroupRelationDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupNotice;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupWelcomeMsg;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.service.GroupDynamicCodeAndGroupRelationDataService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.service.GroupDynamicCodeInfoDataService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.adapter.service.GroupManageProcessor;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.util.QRCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@PrivateLiveProcessor(wechatType = WeChatType.ENTERPRISE_WECHAT)
public class EntGroupManageProcessor implements GroupManageProcessor {

    @Autowired
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private GroupDynamicCodeAndGroupRelationDataService dynamicCodeAndGroupRelationDataService;

    @Autowired
    private GroupDynamicCodeInfoDataService dynamicCodeInfoDataService;

    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;

    @Override
    public void dismissGroup(ScrmPersonalWxGroupInfoEntity entity) {
        CorpAppConfig config = appConfigRepository.getConfigByAppId(entity.getAppId());
        if (config == null) {
            return;
        }
        try {
            boolean success = dsCorpGroupAclService.dismissGroup(config.getCorpId(), config.getOrgId(), entity.getGroupId(), entity.getGroupName());
            if (!success) {
                log.error("EnterpriseGroupManageProcessor.dismissGroup fail");
            }
        } catch (Exception e) {
            log.error("EnterpriseGroupManageProcessor.dismissGroup exception", e);
        }
    }

    @Override
    public GroupWelcomeMsg getGroupWelcomeMsg(ScrmPersonalWxGroupInfoEntity entity) {
        GroupWelcomeMsg groupWelcomeMsg = new GroupWelcomeMsg();
        groupWelcomeMsg.setWelcomeMsgId(entity.getWelcomeMsgId());
        GroupMsg groupMsg = new GroupMsg();
        groupMsg.setMsgContent(String.valueOf(entity.getWelcomeMsgId()));
        groupMsg.setMsgType("1");
        groupWelcomeMsg.setContent(Lists.newArrayList(groupMsg));
        return groupWelcomeMsg;
    }

    @Override
    public GroupNotice getGroupNotice(ScrmPersonalWxGroupMsg wxGroupMsg) {
        GroupNotice groupNotice = new GroupNotice();
        groupNotice.setContent(wxGroupMsg.getContent());
        return groupNotice;
    }

    @Override
    public String getGroupQrCode(ScrmPersonalWxGroupInfoEntity groupInfoEntity) {
        if (groupInfoEntity == null) {
            return "群不存在";
        }
        List<GroupDynamicCodeAndGroupRelationDTO> relationDTOList = dynamicCodeAndGroupRelationDataService.queryByGroupId(groupInfoEntity.getGroupId());
        if (CollectionUtils.isEmpty(relationDTOList)) {
            return "不存在群和活码的关联";
        }
        Long dynamicCodeId = relationDTOList.get(0).getDynamicCodeId();
        GroupDynamicCodeInfoDTO codeInfoDTO = dynamicCodeInfoDataService.queryById(dynamicCodeId);
        if (codeInfoDTO == null) {
            return "群活码不存在";
        }
        try {
            String qrCodeUrl = QRCodeUtils.generateQRCodeImage(codeInfoDTO.getQrCode(), 300);
            ScrmPersonalWxGroupInfoEntity record = ScrmPersonalWxGroupInfoEntity.builder()
                    .id(groupInfoEntity.getId())
                    .groupQr(qrCodeUrl)
                    .groupQrRefreshTime(new Date())
                    .build();
            personalWxGroupManageDomainService.updateGroup(record);
            return null;
        } catch (Exception e) {
            log.error("EntGroupManageProcessor.getGroupQrCode generateQRCodeImage has exception, codeInfoDTO={}", codeInfoDTO, e);
            return "生成二维码失败";
        }
    }
}
