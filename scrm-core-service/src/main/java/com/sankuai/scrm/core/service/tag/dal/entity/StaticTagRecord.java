package com.sankuai.scrm.core.service.tag.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: static_tag_record
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StaticTagRecord {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: union_id
     *   说明: 微信unionId
     */
    private String unionId;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: tag_id_list
     *   说明: 静态标签列表
     */
    private String tagIdList;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}