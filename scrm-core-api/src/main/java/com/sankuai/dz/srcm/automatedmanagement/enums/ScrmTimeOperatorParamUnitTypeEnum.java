package com.sankuai.dz.srcm.automatedmanagement.enums;

import java.util.Calendar;

/**
 * 支持的时间操作参数单位
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/17
 */
public enum ScrmTimeOperatorParamUnitTypeEnum {

    UNKNOWN("unknown", 0, "未知"),
    MINUTE("minute", 1, "分钟"),
    HOUR("hour", 2, "小时"),
    DAY("day", 3, "天")
    ;

    private String code;

    private int value;

    private String description;

    ScrmTimeOperatorParamUnitTypeEnum(String code, int value, String description) {
        this.code = code;
        this.value = value;
        this.description = description;
    }

    public static int getCalendarFieldByValue(int value) {
        switch (value) {
            case 1: return Calendar.MINUTE;
            case 2: return Calendar.HOUR_OF_DAY;
            case 3: return Calendar.DAY_OF_MONTH;
            default: throw new RuntimeException("未知时间类型");
        }
    }

    public static ScrmTimeOperatorParamUnitTypeEnum getTypeByValue(int i) {
        for (ScrmTimeOperatorParamUnitTypeEnum productTypeEnum : ScrmTimeOperatorParamUnitTypeEnum.values()) {
            if (productTypeEnum.getValue() == i) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ScrmTimeOperatorParamUnitTypeEnum getTypeByCode(String code) {
        for (ScrmTimeOperatorParamUnitTypeEnum productTypeEnum : ScrmTimeOperatorParamUnitTypeEnum.values()) {
            if (productTypeEnum.getCode().equalsIgnoreCase(code)|| productTypeEnum.name().equalsIgnoreCase(code)) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

}
