package com.sankuai.scrm.core.service.flow.haima;

import com.alibaba.fastjson.JSONObject;
import com.dianping.appkit.constants.BizGroup;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class HaimaFlowEntryConfigService {

    @Autowired
    private HaimaClient haimaClient;

    public <T> List<T> getConfigList(String key, Class<T> clazz) {
        try {
            List<HaimaContent> haimaContentList = getHaimaContentList(key);
            if (CollectionUtils.isEmpty(haimaContentList)) {
                return Lists.newArrayList();
            }
            List<T> configList = new ArrayList<>();
            for (HaimaContent content : haimaContentList) {
                String extJson = content.getExtJson();
                T config = JSONObject.parseObject(extJson, clazz);
                configList.add(config);
            }
            return configList;
        } catch (Exception e) {
            log.error("getXyOrderDetailFlowEntryConfig has exception", e);
            return Lists.newArrayList();
        }
    }

    private List<HaimaContent> getHaimaContentList(String key) {
        HaimaRequest request = new HaimaRequest();
        request.setBizGroup(BizGroup.DIANPING);
        request.setSceneKey(key);

        try {
            HaimaResponse haimaResponse = haimaClient.query(request);
            if (haimaResponse == null || !haimaResponse.isSuccess()) {
                log.error("getHaimaContentList haimaResponse is fail. haimaResponse is {}", haimaResponse);
                return Lists.newArrayList();
            }
            if (CollectionUtils.isEmpty(haimaResponse.getData())
                    || CollectionUtils.isEmpty(haimaResponse.getData().get(0).getContents())) {
                log.error("getHaimaContentList haimaResponse is null. haimaResponse is {}", haimaResponse);
                return Lists.newArrayList();
            }
            return haimaResponse.getData().get(0).getContents();
        } catch (Exception e) {
            log.error("getHaimaContentList has exception", e);
            return Lists.newArrayList();
        }
    }

}
