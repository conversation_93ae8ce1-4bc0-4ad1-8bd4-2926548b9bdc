package com.sankuai.scrm.core.service.abtest.strategy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.user.dto.UserIntentionFinalResult;
import com.sankuai.scrm.core.service.abtest.config.ABTestConfig;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserIntentionScores;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserScoreResult;
import com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link AbstractScrmFridayIntelligentFollowStrategy#buildAndSendABTestRecordMessage}
 */
@ExtendWith(MockitoExtension.class)
class AbstractScrmFridayIntelligentFollowStrategyBuildAndSendABTestRecordMessageTest {

    private TestStrategy strategy;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Mock
    private ABTestConfig abTestConfig;

    @BeforeEach
    void setUp() {
        strategy = new TestStrategy();
        strategy.testRecordMessageProducer = testRecordMessageProducer;
        strategy.abTestConfig = abTestConfig;
    }

    @Test
    void testBuildAndSendABTestRecordMessage_AllFieldsPresent() throws Throwable {
        // arrange
        Long userId = 123L;
        String appId = "app-001";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.81"), new BigDecimal("0.19"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.90"), new BigDecimal("0.50"), new BigDecimal("0.70"), new BigDecimal("0.30"), true);
        Map<Long, BigDecimal> productMap = new LinkedHashMap<>();
        productMap.put(1L, new BigDecimal("5"));
        productMap.put(2L, new BigDecimal("3"));
        Map<Long, BigDecimal> shopMap = new LinkedHashMap<>();
        shopMap.put(10L, new BigDecimal("2"));
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(productMap, shopMap);
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("strategyA");
        ctx.setUniqueName("uniqueA");
        ctx.setRandomValue(42);
        ctx.setRandomValueStart(10);
        ctx.setRandomValueEnd(100);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v1.0");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        assertEquals(userId, dto.getMtUserId());
        assertEquals(appId, dto.getAppId());
        assertEquals("v1.0", dto.getTestVersion());
        assertEquals(0, dto.getStatus());
        assertEquals(0, dto.getOriginValue());
        assertEquals(0, dto.getTestValue());
        assertEquals("strategyA", dto.getStrategyName());
        assertEquals("uniqueA", dto.getUniqueName());
        assertEquals(42, dto.getRandomValue());
        assertEquals(10, dto.getRandomValueStart());
        assertEquals(100, dto.getRandomValueEnd());
        assertEquals(new BigDecimal("0.81"), dto.getIntentionScore());
        assertEquals(new BigDecimal("0.19"), dto.getHesitationScore());
        assertEquals(new BigDecimal("0.90"), dto.getIntentionScoreThreshold());
        assertEquals(new BigDecimal("0.50"), dto.getIntentionScoreLow());
        assertEquals(new BigDecimal("0.70"), dto.getHesitationScoreThreshold());
        assertEquals(new BigDecimal("0.30"), dto.getHesitationScoreLow());
    }

    @Test
    void testBuildAndSendABTestRecordMessage_NullIntentionScores() throws Throwable {
        // arrange
        Long userId = 1L;
        String appId = "appX";
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.9"), new BigDecimal("0.5"), new BigDecimal("0.7"), new BigDecimal("0.3"), true);
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(Collections.singletonMap(1L, new BigDecimal("1")), Collections.singletonMap(2L, new BigDecimal("2")));
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s2");
        ctx.setUniqueName("u2");
        ctx.setRandomValue(5);
        ctx.setRandomValueStart(0);
        ctx.setRandomValueEnd(10);
        when(abTestConfig.getTestVersion(appId)).thenReturn("ver");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, null, actionScores, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        assertNull(dto.getIntentionScore());
        assertNull(dto.getHesitationScore());
    }

    @Test
    void testBuildAndSendABTestRecordMessage_NullHighPotentialUser() throws Throwable {
        // arrange
        Long userId = 2L;
        String appId = "appY";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.6"), new BigDecimal("0.4"),new UserIntentionFinalResult());
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(Collections.singletonMap(3L, new BigDecimal("3")), Collections.singletonMap(4L, new BigDecimal("4")));
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s3");
        ctx.setUniqueName("u3");
        ctx.setRandomValue(7);
        ctx.setRandomValueStart(1);
        ctx.setRandomValueEnd(9);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v2");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, null);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        assertNull(dto.getIntentionScoreThreshold());
        assertNull(dto.getIntentionScoreLow());
        assertNull(dto.getHesitationScoreThreshold());
        assertNull(dto.getHesitationScoreLow());
    }

    @Test
    void testBuildAndSendABTestRecordMessage_NullActionScores() throws Throwable {
        // arrange
        Long userId = 3L;
        String appId = "appZ";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.2"), new BigDecimal("0.8"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.5"), new BigDecimal("0.1"), new BigDecimal("0.6"), new BigDecimal("0.2"), false);
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s4");
        ctx.setUniqueName("u4");
        ctx.setRandomValue(9);
        ctx.setRandomValueStart(2);
        ctx.setRandomValueEnd(8);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v3");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, null, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        assertNull(dto.getItemScoreJson());
    }

    @Test
    void testBuildAndSendABTestRecordMessage_EmptyActionScoreMaps() throws Throwable {
        // arrange
        Long userId = 4L;
        String appId = "appEmpty";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.3"), new BigDecimal("0.7"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.4"), new BigDecimal("0.2"), new BigDecimal("0.5"), new BigDecimal("0.3"), true);
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(Collections.emptyMap(), Collections.emptyMap());
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s5");
        ctx.setUniqueName("u5");
        ctx.setRandomValue(11);
        ctx.setRandomValueStart(3);
        ctx.setRandomValueEnd(7);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v4");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        assertNull(dto.getItemScoreJson());
    }

    @Test
    void testBuildAndSendABTestRecordMessage_OnlyProductScores() throws Throwable {
        // arrange
        Long userId = 5L;
        String appId = "appProd";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.55"), new BigDecimal("0.45"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.6"), new BigDecimal("0.4"), new BigDecimal("0.7"), new BigDecimal("0.5"), true);
        Map<Long, BigDecimal> productMap = new LinkedHashMap<>();
        productMap.put(100L, new BigDecimal("9"));
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(productMap, Collections.emptyMap());
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s6");
        ctx.setUniqueName("u6");
        ctx.setRandomValue(13);
        ctx.setRandomValueStart(4);
        ctx.setRandomValueEnd(6);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v5");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        // Just verify that itemScoreJson is not null when product scores are present
        // The exact content depends on the JSON serialization which may vary
        assertNotNull(dto.getItemScoreJson(), "itemScoreJson should not be null when product scores are present");
    }

    @Test
    void testBuildAndSendABTestRecordMessage_OnlyShopScores() throws Throwable {
        // arrange
        Long userId = 6L;
        String appId = "appShop";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.33"), new BigDecimal("0.67"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.8"), new BigDecimal("0.2"), new BigDecimal("0.9"), new BigDecimal("0.1"), false);
        Map<Long, BigDecimal> shopMap = new LinkedHashMap<>();
        shopMap.put(200L, new BigDecimal("4"));
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(Collections.emptyMap(), shopMap);
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s7");
        ctx.setUniqueName("u7");
        ctx.setRandomValue(15);
        ctx.setRandomValueStart(5);
        ctx.setRandomValueEnd(5);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v6");
        // act
        strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, highPotentialUser);
        // assert
        ArgumentCaptor<GroupRetailAiEngineABTestRecordMessageDTO> captor = ArgumentCaptor.forClass(GroupRetailAiEngineABTestRecordMessageDTO.class);
        verify(testRecordMessageProducer).sendMessage(captor.capture());
        GroupRetailAiEngineABTestRecordMessageDTO dto = captor.getValue();
        // Just verify that itemScoreJson is not null when shop scores are present
        // The exact content depends on the JSON serialization which may vary
        assertNotNull(dto.getItemScoreJson(), "itemScoreJson should not be null when shop scores are present");
    }

    @Test
    void testBuildAndSendABTestRecordMessage_ProducerExceptionIsHandled() throws Throwable {
        // arrange
        Long userId = 7L;
        String appId = "appExc";
        UserIntentionScores intentionScores = new UserIntentionScores(new BigDecimal("0.1"), new BigDecimal("0.9"),new UserIntentionFinalResult());
        UserScoreResult highPotentialUser = new UserScoreResult(new BigDecimal("0.2"), new BigDecimal("0.1"), new BigDecimal("0.3"), new BigDecimal("0.2"), true);
        UserActionScoreUtil.UserActionScoreResult actionScores = new UserActionScoreUtil.UserActionScoreResult(Collections.singletonMap(1L, new BigDecimal("1")), Collections.singletonMap(2L, new BigDecimal("2")));
        ABTestContext ctx = new ABTestContext();
        ctx.setStrategyName("s8");
        ctx.setUniqueName("u8");
        ctx.setRandomValue(20);
        ctx.setRandomValueStart(0);
        ctx.setRandomValueEnd(20);
        when(abTestConfig.getTestVersion(appId)).thenReturn("v7");
        doThrow(new RuntimeException("producer failure")).when(testRecordMessageProducer).sendMessage(any());
        // act & assert
        assertDoesNotThrow(() -> strategy.buildAndSendABTestRecordMessage(userId, appId, intentionScores, actionScores, ctx, highPotentialUser));
        verify(testRecordMessageProducer).sendMessage(any());
    }

    private static class TestStrategy extends AbstractScrmFridayIntelligentFollowStrategy {

        @Override
        public ABTestContext execute(IntelligentFollowActionTrackDTO param, ABTestContext context) {
            return null;
        }

        @Override
        public String getName() {
            return "testStrategy";
        }

        @Override
        public void fillABTestRecordMessageDTO(ABTestContext context, GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO) {
            // no-op for tests
        }
    }
}
