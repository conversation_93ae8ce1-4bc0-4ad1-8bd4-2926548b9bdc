package com.sankuai.scrm.core.service.pchat.domain;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxWelcomeMsgLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxWelcomeMsgLogExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMemberInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxWelcomeMsgLogMapper;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/5/13 14:31
 * @Version v1.0.0
 */

@Slf4j
@Service
public class ScrmPersonalWxMsgDomainService {
    @Resource
    private ScrmPersonalWxWelcomeMsgLogMapper welcomeMsgLogMapper;
    @Resource
    private ScrmPersonalWxGroupMemberInfoEntityMapper wxGroupMemberInfoEntityMapper;

    public ScrmPersonalWxWelcomeMsgLog queryWelcomeMsgLog(Long welcomeMsgId) {
        if (welcomeMsgId == null || welcomeMsgId == 0L) {
            return null;
        }
        ScrmPersonalWxWelcomeMsgLogExample example = new ScrmPersonalWxWelcomeMsgLogExample();
        ScrmPersonalWxWelcomeMsgLogExample.Criteria criteria = example.createCriteria();
        criteria.andWelcomeMsgIdEqualTo(welcomeMsgId);
        example.setOrderByClause("id desc");
        example.limit(1);
        List<ScrmPersonalWxWelcomeMsgLog> welcomeMsgLogs = welcomeMsgLogMapper.selectByExample(example);
        return CollectionUtil.isEmpty(welcomeMsgLogs) ? null : welcomeMsgLogs.get(0);
    }

    public ScrmPersonalWxWelcomeMsgLog saveWelcomeMsgLog(Long welcomeMsgId, Long groupId, Long groupMemberPointer) {
        if (welcomeMsgId == null || welcomeMsgId == 0L || groupMemberPointer == null || groupMemberPointer == 0L) {
            return null;
        }
        ScrmPersonalWxWelcomeMsgLog record = new ScrmPersonalWxWelcomeMsgLog();
        record.setLastMemberPointer(groupMemberPointer);
        record.setUpdateTime(new Date());
        record.setAddTime(new Date());
        record.setWelcomeMsgId(welcomeMsgId);
        record.setLastNotifyTime(new Date());
        record.setGroupId(groupId);
        //record.setSendFrequency();
        welcomeMsgLogMapper.insertSelective(record);
        return record;
    }

    public int updateWelcomeMsgLog(Long id, Long welcomeMsgId, Long groupMemberPointer, Long preGroupMemberPointer) {
        if (id == null || id == 0L || welcomeMsgId == null || welcomeMsgId == 0L || groupMemberPointer == null || groupMemberPointer == 0L) {
            return 0;
        }
        ScrmPersonalWxWelcomeMsgLog record = new ScrmPersonalWxWelcomeMsgLog();
        record.setLastMemberPointer(groupMemberPointer);
        record.setUpdateTime(new Date());
        record.setLastNotifyTime(new Date());


        ScrmPersonalWxWelcomeMsgLogExample example = new ScrmPersonalWxWelcomeMsgLogExample();
        ScrmPersonalWxWelcomeMsgLogExample.Criteria criteria = example.createCriteria();
        criteria.andWelcomeMsgIdEqualTo(welcomeMsgId);
        criteria.andLastMemberPointerEqualTo(preGroupMemberPointer);
        criteria.andIdEqualTo(id);

        return welcomeMsgLogMapper.updateByExampleSelective(record, example);
    }

    public List<ScrmPersonalWxGroupMemberInfoEntity> queryLatestNewGroupMember(String chatroomSerialNo, Long timeTrace) {

        ScrmPersonalWxGroupMemberInfoEntityExample example = new ScrmPersonalWxGroupMemberInfoEntityExample();
        ScrmPersonalWxGroupMemberInfoEntityExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode());
        criteria.andGroupIdEqualTo(chatroomSerialNo);
        criteria.andTimeTraceGreaterThan(timeTrace);
        criteria.andDeletedEqualTo(false);

        example.setOrderByClause("time_trace asc");

        return wxGroupMemberInfoEntityMapper.selectByExample(example);
    }
}
