package com.sankuai.dz.srcm.chat.enums;

public enum WelcomeMsgTypeEnum {

    UNKNOWN(0, "未知类型"),
    ROBOT_WELCOME_MSG(1, "机器人欢迎语"),
    GROUP_WELCOME_MSG(2, "群欢迎语"),
    WE_CHAT_FRIEND_WELCOME_MSG(3, "企微好友欢迎语");



    private int code;

    private String desc;

    WelcomeMsgTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static WelcomeMsgTypeEnum getWelcomeTypeByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        WelcomeMsgTypeEnum[] values = WelcomeMsgTypeEnum.values();
        for (WelcomeMsgTypeEnum value : values) {
            if (code.equals(value.getCode())){
                return value;
            }
        }
        return UNKNOWN;
    }
}
