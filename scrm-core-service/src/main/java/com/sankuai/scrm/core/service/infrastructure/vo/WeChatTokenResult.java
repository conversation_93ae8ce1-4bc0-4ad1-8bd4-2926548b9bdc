package com.sankuai.scrm.core.service.infrastructure.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> on 2023/1/6 5:05 PM
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WeChatTokenResult implements Serializable {
    private int errcode = -1;
    private String errmsg;
    private String access_token;
    private int expires_in = -1;

    public static WeChatTokenResult success(String token){
        return WeChatTokenResult.builder().errcode(0)
                .errmsg("cache")
                .access_token(token)
                .build();
    }

    public static WeChatTokenResult fail(int code, String msg){
        return WeChatTokenResult.builder().errcode(code)
                .errmsg(msg)
                .build();
    }

    public boolean isSuccess(){
        return errcode == 0;
    }
}
