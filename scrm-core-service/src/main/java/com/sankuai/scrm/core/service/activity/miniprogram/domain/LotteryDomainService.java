package com.sankuai.scrm.core.service.activity.miniprogram.domain;

import com.dianping.cat.Cat;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.activity.miniprogram.bo.LotteryActivityBO;
import com.sankuai.scrm.core.service.activity.miniprogram.bo.LotteryDetailBO;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardRecord;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.example.MiniProgramAwardRecordExample;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.example.MiniProgramAwardStatisticExample;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper.MiniProgramAwardRecordMapper;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper.MiniProgramAwardStatisticMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.haima.HaimaAclService;
import com.sankuai.scrm.core.service.order.bo.SkuBO;
import com.sankuai.scrm.core.service.order.bo.UnifiedOrderBO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LotteryDomainService {

    private static final String LOTTERY_ACTIVITY_HAIMA_KEY = "scrm_tmm_lottery_activity";

    @Autowired
    HaimaAclService haimaAclService;

    @Autowired
    private MiniProgramAwardStatisticMapper awardStatisticMapper;

    @Autowired
    private MiniProgramAwardRecordMapper awardRecordMapper;

    public List<LotteryActivityBO> queryLotteryActivityList() {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(LOTTERY_ACTIVITY_HAIMA_KEY);
        List<HaimaConfig> configs = haimaAclService.getOuterConfigList(request);
        if (CollectionUtils.isEmpty(configs)) {
            return Lists.newArrayList();
        }
        return configs.stream().map(config -> {
            try {
                LotteryActivityBO lotteryActivityBO = JsonUtils.toObject(config.getExtJson(), LotteryActivityBO.class);
                if (lotteryActivityBO == null) {
                    return null;
                }
                List<HaimaContent> contents = config.getContents();
                if (CollectionUtils.isNotEmpty(contents)) {
                    List<LotteryDetailBO> lotteryDetailList = contents.stream()
                            .map(content -> JsonUtils.toObject(content.getExtJson(), LotteryDetailBO.class))
                            .collect(Collectors.toList());
                    lotteryActivityBO.setDetailList(lotteryDetailList);
                }
                return lotteryActivityBO;
            } catch (Exception e) {
                log.error("LotteryDomainService.queryLotteryActivityList deserialize fail, config:{}",
                        JsonUtils.toStr(config), e);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public LotteryActivityBO queryLotteryActivity(Integer activityId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService.queryLotteryActivity(java.lang.Integer)");
        if (activityId == null) {
            return null;
        }
        List<LotteryActivityBO> lotteryActivityList = queryLotteryActivityList();
        return lotteryActivityList.stream()
                .filter(lotteryActivityBO -> activityId.equals(lotteryActivityBO.getActivityId()))
                .findFirst()
                .orElse(null);
    }

    public List<String> filterSkuIdList(UnifiedOrderBO orderInfo) {
        if (orderInfo == null || orderInfo.getBuySuccessTime() == null || CollectionUtils.isEmpty(orderInfo.getSkus())) {
            return Lists.newArrayList();
        }
        List<LotteryActivityBO> lotteryActivityList = queryLotteryActivityList();
        Set<String> validSkuIdSet = lotteryActivityList.stream()
                .filter(lotteryActivityBO -> !orderInfo.getBuySuccessTime().before(lotteryActivityBO.getStartTime())
                        && !orderInfo.getBuySuccessTime().after(lotteryActivityBO.getEndTime()))
                .map(LotteryActivityBO::getSkuIdList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        List<String> skuIdList = orderInfo.getSkus().stream()
                .filter(Objects::nonNull)
                .map(SkuBO::getSkuId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        return skuIdList.stream().filter(validSkuIdSet::contains).collect(Collectors.toList());
    }

    public List<MiniProgramAwardStatistic> queryAwardStatisticList(Integer activityId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService.queryAwardStatisticList(java.lang.Integer)");
        if (activityId == null) {
            return Lists.newArrayList();
        }
        MiniProgramAwardStatisticExample example = new MiniProgramAwardStatisticExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        return awardStatisticMapper.selectByExample(example);
    }

    public List<MiniProgramAwardRecord> queryAwardRecord(Integer activityId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService.queryAwardRecord(java.lang.Integer)");
        if (activityId == null) {
            return Lists.newArrayList();
        }
        MiniProgramAwardRecordExample example = new MiniProgramAwardRecordExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        return awardRecordMapper.selectByExample(example);
    }

    public boolean insertAwardStatistic(MiniProgramAwardStatistic record) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService.insertAwardStatistic(com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramAwardStatistic)");
        if (record == null) {
            return false;
        }
        return awardStatisticMapper.insertSelective(record) > 0;
    }

    public boolean batchInsertAwardRecord(List<MiniProgramAwardRecord> recordList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService.batchInsertAwardRecord(java.util.List)");
        if (CollectionUtils.isEmpty(recordList)) {
            return false;
        }
        return awardRecordMapper.batchInsert(recordList) > 0;
    }

}