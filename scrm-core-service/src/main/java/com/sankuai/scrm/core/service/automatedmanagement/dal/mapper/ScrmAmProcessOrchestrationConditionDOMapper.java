package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationConditionDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationConditionDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationConditionDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationConditionDO, ScrmAmProcessOrchestrationConditionDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationConditionDO> list);
}