package com.sankuai.scrm.core.service.abtest.strategy.config.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 消息模板数据结构
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
public class MessageTemplate {
    private String type;
    private String message;
    private Long productId;
    private Long shopId;
    private String couponId;
    private Long couponFullPrice;
    private Long couponAmount;
    private Boolean needNewIssue;
}