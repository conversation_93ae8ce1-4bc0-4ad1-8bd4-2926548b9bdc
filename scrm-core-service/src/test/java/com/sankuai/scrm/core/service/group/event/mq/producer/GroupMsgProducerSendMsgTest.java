package com.sankuai.scrm.core.service.group.event.mq.producer;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupMsgProducerSendMsgTest {

    @Mock
    private IProducerProcessor<?, ?> producer;

    @InjectMocks
    private GroupMsgProducer groupMsgProducer;

    @Test
    public void testSendMsgDataMapIsNull() throws Throwable {
        groupMsgProducer.sendMsg(null);
        verify(producer, never()).sendMessage(any(), anyString());
    }

    @Test
    public void testSendMsgDataMapNotContainsChatId() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        groupMsgProducer.sendMsg(dataMap);
        verify(producer, never()).sendMessage(any(), anyString());
    }

    @Test
    public void testSendMsgProducerResultIsNull() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("ChatId", "test");
        when(producer.sendMessage(any(), anyString())).thenReturn(null);
        groupMsgProducer.sendMsg(dataMap);
        verify(producer, times(3)).sendMessage(any(), anyString());
    }

    @Test
    public void testSendMsgProducerResultIsNotSendOk() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("ChatId", "test");
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(any(), anyString())).thenReturn(producerResult);
        groupMsgProducer.sendMsg(dataMap);
        verify(producer, times(3)).sendMessage(any(), anyString());
    }

    @Test
    public void testSendMsgExceptionOccurs() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("ChatId", "test");
        when(producer.sendMessage(any(), anyString())).thenThrow(new Exception());
        groupMsgProducer.sendMsg(dataMap);
        verify(producer, times(3)).sendMessage(any(), anyString());
    }

    @Test
    public void testSendMsgAllRetriesFail() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("ChatId", "test");
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(any(), anyString())).thenReturn(producerResult);
        groupMsgProducer.sendMsg(dataMap);
        verify(producer, times(3)).sendMessage(any(), anyString());
    }
}
