package com.sankuai.dz.srcm.pchat.request.robot;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.srcm.pchat.enums.robot.ScanQrCodeProtocolTypeEnum;
import com.sankuai.dz.srcm.pchat.enums.robot.ScanQrCodeUserType;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetScanQrCodeRequest implements Serializable {

    private String robotSerialNo;

    /**
     * @see ScanQrCodeUserType
     */
    private int type;

    private String areaCode;

    private String belongClusterId;
    /**
     * @see ScanQrCodeProtocolTypeEnum
     */
    @FieldDoc(description = "登录协议类型 参照 com.sankuai.dz.srcm.pchat.enums.robot.ScanQrCodeProtocolTypeEnum")
    private Integer protocolType;
}
