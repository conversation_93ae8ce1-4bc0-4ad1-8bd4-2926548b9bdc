package com.sankuai.scrm.core.service.infrastructure.acl.leaf;

import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import groovy.util.logging.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IDGenAclService {

    @Autowired
    private IDGen.Iface idGen;

    private static final int INVOKE_RETRY_TIME = 5;

    /**
     * 使用leaf号段模式获取id,超时将进行重试
     */
    public long nextId(String leafKey) throws TException{
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
                Result result = idGen.get(leafKey);
                if (Status.SUCCESS.equals(result.getStatus())) {
                    return result.getId();
                } else {
                    throw new RuntimeException("leaf id生成异常, 异常码:" + result.getId());
                }
            } catch (TException e) {
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    throw e;
                }
            }
        }
        throw new RuntimeException("leaf id生成错误");
    }

    /**
     * 使用leaf snowflake模式获取id,超时将进行重试
     */
    public long nextSnowflakeId(String leafKey) throws TException {
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
                Result result = idGen.getSnowFlake(leafKey);
                if (Status.SUCCESS.equals(result.getStatus())) {
                    return result.getId();
                } else {
                    throw new RuntimeException("leaf id生成异常, 异常码:" + result.getId());
                }
            } catch (TException e) {
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    throw e;
                }
            }
        }
        throw new RuntimeException("leaf id生成错误");
    }
}
