package com.sankuai.scrm.core.service.group.domain;

import com.sankuai.scrm.core.service.group.dal.babymapper.MemberInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupMemberDomainServiceTest {

    @InjectMocks
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private MemberInfoEntityMapper memberInfoEntityMapper;

    /**
     * Tests the getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster method under normal conditions.
     */
    @Test
    public void testGetMemberInfoEntitiesByCorpIdAndUnionIdFromMasterNormal() throws Throwable {
        // Arrange
        String corpId = "corpId";
        String unionId = "unionId";
        List<MemberInfoEntity> expected = Collections.singletonList(new MemberInfoEntity());
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(expected);
        // Act
        List<MemberInfoEntity> actual = groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster(corpId, unionId);
        // Assert
        assertEquals(expected, actual);
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }

    /**
     * Tests the getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster method under boundary conditions.
     */
    @Test
    public void testGetMemberInfoEntitiesByCorpIdAndUnionIdFromMasterBoundary() throws Throwable {
        // Arrange
        String corpId = "corpId";
        String unionId = "unionId";
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        // Act
        List<MemberInfoEntity> actual = groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster(corpId, unionId);
        // Assert
        assertEquals(Collections.emptyList(), actual);
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }

    /**
     * Tests the getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster method under exceptional conditions.
     */
    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntitiesByCorpIdAndUnionIdFromMasterException() throws Throwable {
        // Arrange
        String corpId = "corpId";
        String unionId = "unionId";
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenThrow(new RuntimeException());
        // Act
        groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionIdFromMaster(corpId, unionId);
        // Assert
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 corpId 或 unionId 为空时返回 null
     */
    @Test
    public void testQueryMemberInOrNotInGroupByUnionIdWithEmptyCorpIdOrUnionId() {
        // arrange
        String corpId = "";
        String unionId = "testUnionId";
        // act
        MemberInfoEntity result = groupMemberDomainService.queryMemberInOrNotInGroupByUnionId(corpId, unionId);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况下能够查询到成员信息
     */
    @Test
    public void testQueryMemberInOrNotInGroupByUnionIdWithValidParams() {
        // arrange
        String corpId = "testCorpId";
        String unionId = "testUnionId";
        MemberInfoEntity memberInfoEntity = new MemberInfoEntity();
        List<MemberInfoEntity> memberInfoEntities = new ArrayList<>();
        memberInfoEntities.add(memberInfoEntity);
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(memberInfoEntities);
        // act
        MemberInfoEntity result = groupMemberDomainService.queryMemberInOrNotInGroupByUnionId(corpId, unionId);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试查询不到成员信息时返回 null
     */
    @Test
    public void testQueryMemberInOrNotInGroupByUnionIdWithNoMemberFound() {
        // arrange
        String corpId = "testCorpId";
        String unionId = "testUnionId";
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(new ArrayList<>());
        // act
        MemberInfoEntity result = groupMemberDomainService.queryMemberInOrNotInGroupByUnionId(corpId, unionId);
        // assert
        assertNull(result);
    }
}
