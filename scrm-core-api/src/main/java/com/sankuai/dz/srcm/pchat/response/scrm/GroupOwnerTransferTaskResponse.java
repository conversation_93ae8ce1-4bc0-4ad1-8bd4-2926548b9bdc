package com.sankuai.dz.srcm.pchat.response.scrm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/18
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupOwnerTransferTaskResponse {
    private Long taskId;
    private String liveId;
    private String liveName;
    private String groupId;
    private String groupName;
    private Integer taskStatusCode;
    private String taskStatusDesc;
    private String failReason;
    private Integer operateFromRoleType;
    private String operateFromRoleName;
    private Integer operateToRoleType;
    private String operateToRoleName;
    private String operator;
    private Date createTime;
    private Date completeTime;

}
