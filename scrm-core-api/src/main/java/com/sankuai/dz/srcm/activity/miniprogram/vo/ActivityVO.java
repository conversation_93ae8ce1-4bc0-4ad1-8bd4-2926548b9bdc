package com.sankuai.dz.srcm.activity.miniprogram.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ActivityVO {

    private Long activityId;

    private String banner;

    private String bottomImage;

    private String activityName;

    private Date date;

    private Date startTime;

    private Date endTime;

    private List<Integer> cityIdList;

    private String mtCouponCode;

    private Boolean inGroupCheck;

    private List<ItemCardVO> itemCardList;

    private Boolean hidden;

    private Integer status;

}
