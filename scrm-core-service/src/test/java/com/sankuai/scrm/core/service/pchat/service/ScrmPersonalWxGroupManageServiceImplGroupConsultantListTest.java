package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.SpringConfig;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupOverviewRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupConsultantQueryResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupOverviewResponse;
import com.sankuai.scrm.core.service.pchat.constant.ExceptionMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMemberInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ext.ScrmPersonalWxGroupInfoEntityExtMapper;
import com.sankuai.scrm.core.service.pchat.domain.BizIdentificationService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberTypeEnum;
import com.sankuai.scrm.core.service.pchat.event.GroupMemberRefreshEvent;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmPersonalWxMsgTaskProducer;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxGroupManageServiceImpl;
import com.sankuai.scrm.core.service.pchat.service.im.ImCommonService;
import com.sankuai.scrm.core.service.pchat.service.superadmin.SuperAdminSsoService;
import com.sankuai.scrm.core.service.pchat.thirdparty.consultant.ConsultantService;
import com.sankuai.scrm.core.service.pchat.utils.CollUtils;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.pchat.utils.LazyLoader;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxGroupManageServiceImplGroupConsultantListTest {

    @InjectMocks
    private ScrmPersonalWxGroupManageServiceImpl scrmPersonalWxGroupManageService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private BizIdentificationService bizIdentificationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ScrmPersonalWxGroupInfoEntity createGroupInfo(String chatRoomSerialNo, String projectId) {
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setChatRoomWxSerialNo(chatRoomSerialNo);
        groupInfo.setProjectId(projectId);
        return groupInfo;
    }

    @Test(expected = IllegalArgumentException.class)
    public void testGroupConsultantListGroupNotExist() throws Throwable {
        when(groupManageDomainService.queryGroupById(anyLong())).thenReturn(null);
        scrmPersonalWxGroupManageService.groupConsultantList(1L, 1, 1000);
    }

    /**
     * 测试场景：request.getWebcastIds() 为空
     */
    @Test
    public void testGroupOverviewWithEmptyWebcastIds() throws Throwable {
        // arrange
        GroupOverviewRequest request = new GroupOverviewRequest();
        request.setWebcastIds(Collections.emptyList());
        // act
        RemoteResponse<GroupOverviewResponse> response = scrmPersonalWxGroupManageService.groupOverview(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getGroupCount().intValue());
    }

    /**
     * 测试场景：reSetWebcastIds 后 webcastIds 仍然为空
     */
    @Test
    public void testGroupOverviewWithReSetWebcastIdsStillEmpty() throws Throwable {
        // arrange
        GroupOverviewRequest request = new GroupOverviewRequest();
        request.setWebcastIds(Collections.emptyList());
        // act
        RemoteResponse<GroupOverviewResponse> response = scrmPersonalWxGroupManageService.groupOverview(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getGroupCount().intValue());
    }

    /**
     * 测试场景：bizIdentificationService.getAppIdByLiveId 返回空
     */
    @Test
    public void testGroupOverviewWithEmptyAppId() throws Throwable {
        // arrange
        GroupOverviewRequest request = new GroupOverviewRequest();
        request.setWebcastIds(Collections.singletonList("liveId"));
        when(bizIdentificationService.getAppIdByLiveId("liveId")).thenReturn(null);
        // act
        RemoteResponse<GroupOverviewResponse> response = scrmPersonalWxGroupManageService.groupOverview(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getGroupCount().intValue());
    }

    /**
     * 测试场景：groupManageDomainService.queryGroupList 返回空列表
     */
    @Test
    public void testGroupOverviewWithEmptyGroupList() throws Throwable {
        // arrange
        GroupOverviewRequest request = new GroupOverviewRequest();
        request.setWebcastIds(Collections.singletonList("liveId"));
        when(bizIdentificationService.getAppIdByLiveId("liveId")).thenReturn("appId");
        // act
        RemoteResponse<GroupOverviewResponse> response = scrmPersonalWxGroupManageService.groupOverview(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getGroupCount().intValue());
    }
}
