<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.coupon.dal.mapper.CouponInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="coupon_amount" jdbcType="VARCHAR" property="couponAmount" />
    <result column="coupon_template_id" jdbcType="BIGINT" property="couponTemplateId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, coupon_code, coupon_amount, coupon_template_id, creator, app_id, deleted, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.coupon.dal.example.CouponInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from coupon_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.coupon.dal.example.CouponInfoExample">
    delete from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into coupon_info (coupon_code, coupon_amount, coupon_template_id, 
      creator, app_id, deleted, 
      add_time, update_time)
    values (#{couponCode,jdbcType=VARCHAR}, #{couponAmount,jdbcType=VARCHAR}, #{couponTemplateId,jdbcType=BIGINT}, 
      #{creator,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into coupon_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponCode != null">
        coupon_code,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="couponTemplateId != null">
        coupon_template_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="couponTemplateId != null">
        #{couponTemplateId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.coupon.dal.example.CouponInfoExample" resultType="java.lang.Long">
    select count(*) from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update coupon_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.couponCode != null">
        coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.couponTemplateId != null">
        coupon_template_id = #{record.couponTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update coupon_info
    set id = #{record.id,jdbcType=BIGINT},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      coupon_amount = #{record.couponAmount,jdbcType=VARCHAR},
      coupon_template_id = #{record.couponTemplateId,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo">
    update coupon_info
    <set>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="couponTemplateId != null">
        coupon_template_id = #{couponTemplateId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo">
    update coupon_info
    set coupon_code = #{couponCode,jdbcType=VARCHAR},
      coupon_amount = #{couponAmount,jdbcType=VARCHAR},
      coupon_template_id = #{couponTemplateId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into coupon_info
    (coupon_code, coupon_amount, coupon_template_id, creator, app_id, deleted, add_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.couponCode,jdbcType=VARCHAR}, #{item.couponAmount,jdbcType=VARCHAR}, #{item.couponTemplateId,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.appId,jdbcType=VARCHAR}, #{item.deleted,jdbcType=BIT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>