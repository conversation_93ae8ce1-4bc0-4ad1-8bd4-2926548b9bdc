package com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: friend_channel_dynamic_code
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FriendChannelDynamicCode {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: app_id
     *   说明: 业务线id
     */
    private String appId;

    /**
     *   字段: contact_way_id
     *   说明: 企微联系我服务id
     */
    private Long contactWayId;

    /**
     *   字段: channel_id
     *   说明: 好友渠道码对应的渠道id
     */
    private Long channelId;

    /**
     *   字段: welcome_message_id
     *   说明: 好友渠道码欢迎语id
     */
    private Long welcomeMessageId;

    /**
     *   字段: type
     *   说明: 渠道码类型, 1-单人, 2-多人
     */
    private Integer type;

    /**
     *   字段: friend_code_name
     *   说明: 好友渠道码名称
     */
    private String friendCodeName;

    /**
     *   字段: member_scope
     *   说明: 成员范围：0-部分成员，1-全部成员
     */
    private Integer memberScope;

    /**
     *   字段: creator
     *   说明: 创建人/修改人
     */
    private String creator;

    /**
     *   字段: welcome_msg_type
     *   说明: 渠道码配置欢迎语方式, 1-自定义, 2-欢迎语模版, 3-不配置
     */
    private Integer welcomeMsgType;

    /**
     *   字段: dynamic_code_poster
     *   说明: 渠道码海报
     */
    private String dynamicCodePoster;

    /**
     *   字段: cash_back_scene_id
     *   说明: 返现场景id
     */
    private Long cashBackSceneId;

    /**
     *   字段: is_delete
     *   说明: 状态: false-未删除, true-已删除
     */
    private Boolean isDelete;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: bind_switch
     *   说明: 企微标签绑定状态: false-关闭, true-开启
     */
    private Boolean bindSwitch;
}