package com.sankuai.scrm.core.service.flow.validator;

import com.google.common.collect.Lists;
import com.meituan.nibmp.mem.common.resource.enums.CooperationStatusEnum;
import com.meituan.nibmp.mem.common.resource.enums.MEMTypeEnum;
import com.meituan.nibmp.mem.vaf.query.thrift.BizCooperationService;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoReqDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BatchQueryBizCooperateInfoRespDTO;
import com.meituan.nibmp.mem.vaf.query.thrift.dto.BizCooperateInfoDTO;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.enums.DisplayType;
import com.sankuai.dz.srcm.flow.enums.PlatformType;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.dz.srcm.flowV2.dto.ExtraConfigDTO;
import com.sankuai.dz.srcm.flowV2.dto.ShopDisplayDTO;
import com.sankuai.scrm.core.service.flow.config.BaseFlowEntryConfig;
import com.sankuai.scrm.core.service.flow.config.ShopDetailFlowEntryConfig;
import com.sankuai.scrm.core.service.flow.enums.ValidateType;
import com.sankuai.scrm.core.service.flow.context.FlowEntryContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ShopCooperationValidator implements FlowEntryValidator {

    @Autowired
    private BizCooperationService.Iface bizCooperationService;

    @Override
    public ValidateType getValidateType() {
        return ValidateType.SHOP_COOPERATION;
    }

    @Override
    public boolean needValidate(List<ValidateType> validateTypeList) {
        return CollectionUtils.isNotEmpty(validateTypeList) && validateTypeList.contains(ValidateType.SHOP_COOPERATION);
    }

    @Override
    public boolean validate(FlowEntryContext<?> context) {
        FlowEntryWxMaterialRequest request = context.getRequest();
        if (context.isUseConfigV2()) {
            EntryConfigDetailDTO configV2 = context.getConfigV2();
            ExtraConfigDTO extraConfig = configV2.getExtraConfig();
            List<ShopDisplayDTO> shopDisplayConfigList = extraConfig.getShopDisplayConfig();
            Map<Integer, Integer> shopDisplayMap = shopDisplayConfigList.stream()
                    .collect(Collectors.toMap(ShopDisplayDTO::getPlatformType,
                            ShopDisplayDTO::getDisplayType, (k1, k2) -> k1));
            Integer platform = request.getPlatform();
            Integer displayType = shopDisplayMap.get(platform);
            if (displayType == null) {
                return false;
            } else if (DisplayType.All.getCode() == displayType) {
                return true;
            } else if (DisplayType.COOPERATION.getCode() == displayType) {
                try {
                    return checkShopCooperationStatus(request.getMtShopId());
                } catch (Exception e) {
                    log.error("ShopCooperationValidator.checkShopCooperationStatus has exception", e);
                    return false;
                }
            } else if (DisplayType.NOT_COOPERATION.getCode() == displayType) {
                try {
                    boolean hasCooperation = checkShopCooperationStatus(request.getMtShopId());
                    return !hasCooperation;
                } catch (Exception e) {
                    log.error("ShopCooperationValidator.checkShopCooperationStatus has exception", e);
                    return false;
                }
            }
        } else {
            BaseFlowEntryConfig config = context.getConfig();
            if (config instanceof ShopDetailFlowEntryConfig) {
                ShopDetailFlowEntryConfig shopDetailFlowEntryConfig = (ShopDetailFlowEntryConfig) config;
                List<PlatformType> platformTypeList = shopDetailFlowEntryConfig.getPlatformTypeList();
                PlatformType platformType = PlatformType.getPlatformTypeByCode(request.getPlatform());
                if (CollectionUtils.isEmpty(platformTypeList) || !platformTypeList.contains(platformType)) {
                    return false;
                }
                DisplayType displayTypeOfDpApp = shopDetailFlowEntryConfig.getDisplayTypeOfDpApp();
                DisplayType displayTypeOfMtMinip = shopDetailFlowEntryConfig.getDisplayTypeOfMtMinip();
                DisplayType displayTypeOfMtApp = shopDetailFlowEntryConfig.getDisplayTypeOfMtApp();

                if (DisplayType.DEFAULT.equals(displayTypeOfDpApp) && DisplayType.DEFAULT.equals(displayTypeOfMtMinip) && DisplayType.DEFAULT.equals(displayTypeOfMtApp)) {
                    if (platformType.isMiniProgram()) {
                        return true;
                    }
                    if (platformType.isApp()) {
                        try {
                            boolean hasCooperation = checkShopCooperationStatus(request.getMtShopId());
                            return !hasCooperation;
                        } catch (Exception e) {
                            log.error("ShopCooperationValidator.checkShopCooperationStatus has exception", e);
                            return false;
                        }
                    }
                } else if (platformType.isMiniProgram()) {
                    return shouldDisplay(displayTypeOfMtMinip, request);
                } else if (platformType.isDpApp()) {
                    return shouldDisplay(displayTypeOfDpApp, request);
                } else if (platformType.isMtApp()) {
                    return shouldDisplay(displayTypeOfMtApp, request);
                }
            }
        }
        return false;
    }

    private boolean shouldDisplay(DisplayType displayType, FlowEntryWxMaterialRequest request) {
        if (displayType == null) {
            return false;
        }
        if (DisplayType.All.equals(displayType)) {
            return true;
        } else if (DisplayType.NOT_COOPERATION.equals(displayType)) {
            try {
                boolean hasCooperation = checkShopCooperationStatus(request.getMtShopId());
                return !hasCooperation;
            } catch (Exception e) {
                log.error("ShopCooperationValidator.checkShopCooperationStatus has exception", e);
                return false;
            }
        } else if (DisplayType.COOPERATION.equals(displayType)) {
            try {
                return checkShopCooperationStatus(request.getMtShopId());
            } catch (Exception e) {
                log.error("ShopCooperationValidator.checkShopCooperationStatus has exception", e);
                return false;
            }
        } else if (DisplayType.NOT_DISPLAY.equals(displayType)) {
            return false;
        }
        return false;
    }

    public boolean checkShopCooperationStatus(Long mtShopId) {
        if (mtShopId == null) {
            throw new IllegalArgumentException("美团商户id为空");
        }
        BatchQueryBizCooperateInfoReqDTO reqDTO = new BatchQueryBizCooperateInfoReqDTO();
        reqDTO.setMemType(MEMTypeEnum.SHOP.getCode());
        reqDTO.setBizIdList(Lists.newArrayList(mtShopId));
        reqDTO.setProductClassifyIdList(Lists.newArrayList(81010, 81011));
        try {
            BatchQueryBizCooperateInfoRespDTO respDTO = bizCooperationService.batchQueryBizCooperateInfo(reqDTO);
            if (respDTO == null || respDTO.getCommonResp() == null || !respDTO.getCommonResp().isSuccess() ||
                    MapUtils.isEmpty(respDTO.getBizId2CooperateInfoMap()) || respDTO.getBizId2CooperateInfoMap().get(mtShopId) == null) {
                throw new RuntimeException("BizCooperationService.batchQueryBizCooperateInfo exception");
            }
            BizCooperateInfoDTO bizCooperateInfoDTO = respDTO.getBizId2CooperateInfoMap().get(mtShopId);
            return CooperationStatusEnum.OPENED.getCode() == bizCooperateInfoDTO.getCooperationStatus();
        } catch (TException e) {
            log.error("ShopCooperationValidator.checkShopCooperationStatus has exception, mtShopId:{}", mtShopId, e);
            throw new RuntimeException(e);
        }
    }
}
