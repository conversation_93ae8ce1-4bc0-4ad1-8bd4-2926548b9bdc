package com.sankuai.scrm.core.service.message.push.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushDetail;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushDomainService;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushTaskDomainService;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushResponseCode;
import com.sankuai.scrm.core.service.message.push.request.MsgPushCheckReceiverRequest;
import com.sankuai.scrm.core.service.message.push.request.MsgPushResultRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChatType;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushSceneType;
import com.sankuai.scrm.core.service.message.push.mq.producer.MsgPushRealTimeTaskProducer;
import com.sankuai.scrm.core.service.message.push.mq.producer.MsgPushTaskProducer;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;

@ExtendWith(MockitoExtension.class)
class MsgUnifiedPushServiceImplTest {

    @Mock
    private MsgPushTaskDomainService msgPushTaskDomainService;

    @Mock
    private MsgPushDomainService msgPushDomainService;

    @InjectMocks
    private MsgUnifiedPushServiceImpl msgUnifiedPushService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private MsgPushTaskProducer producer;

    @Mock
    private MsgPushRealTimeTaskProducer realTimeTaskProducer;

    public MsgUnifiedPushServiceImplTest() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryTaskResultFromMaster_Success() throws Throwable {
        MsgPushResultRequest request = new MsgPushResultRequest();
        request.setTaskId(1L);
        request.setOffset(0);
        request.setRows(10);
        MsgPushDetail mockDetail = new MsgPushDetail();
        // Assuming MsgPushDetail has a method to set a task ID
        mockDetail.setTaskId(request.getTaskId());
        when(msgPushTaskDomainService.queryDetailList(anyLong(), anyInt(), anyInt())).thenReturn(Collections.singletonList(mockDetail));
        MsgTaskDetailResultDTO expectedDTO = new MsgTaskDetailResultDTO();
        // Assuming MsgPushDomainService.buildMsgTaskDetailResultDTO correctly converts MsgPushDetail to MsgTaskDetailResultDTO
        when(msgPushDomainService.buildMsgTaskDetailResultDTO(any(MsgPushDetail.class))).thenReturn(expectedDTO);
        MsgPushResponse<List<MsgTaskDetailResultDTO>> response = msgUnifiedPushService.queryTaskResultFromMaster(request);
        assertEquals(MsgPushResponseCode.SUCCESS.getCode(), response.getCode());
        assertEquals(MsgPushResponseCode.SUCCESS.getDesc(), response.getMsg());
        assertEquals(1, response.getData().size());
        verify(msgPushTaskDomainService, times(1)).queryDetailList(anyLong(), anyInt(), anyInt());
        verify(msgPushDomainService, times(1)).buildMsgTaskDetailResultDTO(any(MsgPushDetail.class));
    }

    @Test
    void testQueryTaskResultFromMaster_NullRequest() throws Throwable {
        MsgPushResultRequest request = null;
        MsgPushResponse<List<MsgTaskDetailResultDTO>> response = msgUnifiedPushService.queryTaskResultFromMaster(request);
        assertEquals(MsgPushResponseCode.FAILURE.getCode(), response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    @Test
    void testQueryTaskResultFromMaster_InvalidOffset() throws Throwable {
        MsgPushResultRequest request = new MsgPushResultRequest();
        request.setTaskId(1L);
        request.setOffset(-1);
        request.setRows(10);
        MsgPushResponse<List<MsgTaskDetailResultDTO>> response = msgUnifiedPushService.queryTaskResultFromMaster(request);
        assertEquals(MsgPushResponseCode.FAILURE.getCode(), response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    @Test
    void testQueryTaskResultFromMaster_InvalidRows() throws Throwable {
        MsgPushResultRequest request = new MsgPushResultRequest();
        request.setTaskId(1L);
        request.setOffset(0);
        request.setRows(0);
        MsgPushResponse<List<MsgTaskDetailResultDTO>> response = msgUnifiedPushService.queryTaskResultFromMaster(request);
        assertEquals(MsgPushResponseCode.FAILURE.getCode(), response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test case for null request
     */
    @Test
    public void testQueryReceiverTaskIdNullRequest() {
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(null);
        // assert
        assertFalse(MsgPushResponse.isSuccessResponse(response));
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test case for null receiver in request
     */
    @Test
    public void testQueryReceiverTaskIdNullReceiver() {
        // arrange
        MsgPushCheckReceiverRequest request = new MsgPushCheckReceiverRequest();
        request.setTaskIds(Arrays.asList(1L, 2L));
        request.setReceiver(null);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(request);
        // assert
        assertFalse(MsgPushResponse.isSuccessResponse(response));
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test case for empty taskIds list in request
     */
    @Test
    public void testQueryReceiverTaskIdEmptyTaskIds() {
        // arrange
        MsgPushCheckReceiverRequest request = new MsgPushCheckReceiverRequest();
        request.setTaskIds(Collections.emptyList());
        request.setReceiver("testReceiver");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(request);
        // assert
        assertFalse(MsgPushResponse.isSuccessResponse(response));
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test case for valid request but no matching task found
     */
    @Test
    public void testQueryReceiverTaskIdNoMatchFound() {
        // arrange
        MsgPushCheckReceiverRequest request = new MsgPushCheckReceiverRequest();
        request.setTaskIds(Arrays.asList(1L, 2L));
        request.setReceiver("testReceiver");
        when(msgPushTaskDomainService.queryTaskIdByReceiverAmongTaskIds(anyList(), anyString())).thenReturn(null);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(request);
        // assert
        assertTrue(MsgPushResponse.isSuccessResponse(response));
        assertNull(response.getData());
    }

    /**
     * Test case for valid request with matching task found
     */
    @Test
    public void testQueryReceiverTaskIdMatchFound() {
        // arrange
        MsgPushCheckReceiverRequest request = new MsgPushCheckReceiverRequest();
        request.setTaskIds(Arrays.asList(1L, 2L, 3L));
        request.setReceiver("testReceiver");
        Long expectedTaskId = 2L;
        when(msgPushTaskDomainService.queryTaskIdByReceiverAmongTaskIds(anyList(), anyString())).thenReturn(expectedTaskId);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(request);
        // assert
        assertTrue(MsgPushResponse.isSuccessResponse(response));
        assertEquals(expectedTaskId, response.getData());
    }

    /**
     * Test case for multiple taskIds in request
     */
    @Test
    public void testQueryReceiverTaskIdMultipleTaskIds() {
        // arrange
        MsgPushCheckReceiverRequest request = new MsgPushCheckReceiverRequest();
        request.setTaskIds(Arrays.asList(1L, 2L, 3L, 4L, 5L));
        request.setReceiver("testReceiver");
        Long expectedTaskId = 3L;
        when(msgPushTaskDomainService.queryTaskIdByReceiverAmongTaskIds(anyList(), anyString())).thenReturn(expectedTaskId);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.queryReceiverTaskId(request);
        // assert
        assertTrue(MsgPushResponse.isSuccessResponse(response));
        assertEquals(expectedTaskId, response.getData());
    }

    @Test
    public void testSaveMsgPushTaskNullRequest() throws Throwable {
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(null);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("Request为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskEmptyAppId() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("AppId为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskInvalidAppId() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("invalid");
        when(appConfigRepository.getCorpIdByAppId("invalid")).thenReturn(null);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("AppId无效", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskNullSceneType() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("推送场景为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskNullChatType() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REALTIME_SCENE);
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("推送的聊天类型为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskEmptyReceiverList() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REALTIME_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("接收者为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskEmptyContentList() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REALTIME_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        request.setReceiverIdList(Collections.singletonList("receiver1"));
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("推送内容为空", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskSuccessRealtimeScene() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REALTIME_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        request.setReceiverIdList(Collections.singletonList("receiver1"));
        request.setMsgPushContentDTO(Collections.singletonList(new MsgPushContentDTO()));
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        when(msgPushTaskDomainService.saveMsgPushTask(request)).thenReturn(123L);
        doNothing().when(realTimeTaskProducer).sendMsg(anyString());
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(0, response.getCode());
        assertEquals(123L, response.getData());
        verify(msgPushTaskDomainService).saveMsgPushTask(request);
        verify(realTimeTaskProducer).sendMsg(anyString());
    }

    @Test
    public void testSaveMsgPushTaskSuccessRegularScene() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REGULAR_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        request.setReceiverIdList(Collections.singletonList("receiver1"));
        request.setMsgPushContentDTO(Collections.singletonList(new MsgPushContentDTO()));
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        when(msgPushTaskDomainService.saveMsgPushTask(request)).thenReturn(123L);
        doNothing().when(producer).sendMsg(anyString());
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(0, response.getCode());
        assertEquals(123L, response.getData());
        verify(msgPushTaskDomainService).saveMsgPushTask(request);
        verify(producer).sendMsg(anyString());
    }

    @Test
    public void testSaveMsgPushTaskCreationFailure() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REGULAR_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        request.setReceiverIdList(Collections.singletonList("receiver1"));
        request.setMsgPushContentDTO(Collections.singletonList(new MsgPushContentDTO()));
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        when(msgPushTaskDomainService.saveMsgPushTask(request)).thenReturn(null);
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertEquals("消息推送任务创建失败", response.getMsg());
        assertNull(response.getData());
    }

    @Test
    public void testSaveMsgPushTaskException() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        request.setAppId("valid");
        request.setSceneType(MsgPushSceneType.REGULAR_SCENE);
        request.setChatType(MsgPushChatType.PRIVATE);
        request.setReceiverIdList(Collections.singletonList("receiver1"));
        request.setMsgPushContentDTO(Collections.singletonList(new MsgPushContentDTO()));
        when(appConfigRepository.getCorpIdByAppId("valid")).thenReturn("corp123");
        when(msgPushTaskDomainService.saveMsgPushTask(request)).thenThrow(new RuntimeException("DB error"));
        // act
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(request);
        // assert
        assertEquals(1, response.getCode());
        assertTrue(response.getMsg().contains("消息推送任务创建失败:DB error"));
        assertNull(response.getData());
    }
}
