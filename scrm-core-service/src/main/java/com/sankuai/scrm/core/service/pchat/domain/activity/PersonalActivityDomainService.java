package com.sankuai.scrm.core.service.pchat.domain.activity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.cluster.common.util.AssertUtil;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.dto.WxGroupMemberDTO;
import com.sankuai.dz.srcm.pchat.enums.activity.PersonalActivityStateEnum;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import com.sankuai.dz.srcm.pchat.request.activity.ActivityDetailMyRequest;
import com.sankuai.dz.srcm.pchat.request.activity.ActivityFissionMemberRequest;
import com.sankuai.dz.srcm.pchat.request.activity.ActivityMyListRequest;
import com.sankuai.dz.srcm.pchat.request.activity.ActivityRewardMyListRequest;
import com.sankuai.dz.srcm.pchat.response.activity.*;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.*;
import com.sankuai.scrm.core.service.pchat.dal.activity.example.*;
import com.sankuai.scrm.core.service.pchat.dal.activity.mapper.*;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxInviteRelationDomainService;
import com.sankuai.scrm.core.service.pchat.dto.activity.WinRewardListDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalPushStateEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberStatusEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberTypeEnum;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/19 15:32
 * @Version v1.0.0
 */
@Slf4j
@Service
public class PersonalActivityDomainService {
    @Resource
    private ScrmPersonalWxActivityMapper personalWxActivityMapper;

    @Resource
    private ScrmPersonalWxActivityRuleMapper personalWxActivityRuleMapper;

    @Resource
    private ScrmPersonalWxActivityRuleDetailMapper personalWxActivityRuleDetailMapper;

    @Resource
    private ScrmPersonalWxActivityConfigMapper personalWxActivityConfigMapper;

    @Resource
    private ScrmPersonalWxActivityRuleRewardMapper personalWxActivityRuleRewardMapper;

    @Resource
    private ScrmPersonalWxActivityGroupMappingMapper personalWxActivityGroupMappingMapper;

    @Resource
    private ScrmPersonalWxActivityRewardRecordMapper personalWxActivityRewardRecordMapper;
    @Resource
    private ScrmPersonalWxActivityCustomMapper personalWxActivityCustomMapper;
    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @Autowired
    private ScrmPersonalWxInviteRelationDomainService scrmPersonalWxInviteRelationDomainService;


    @Transactional
    public long saveActivity(ScrmPersonalWxActivity activity) {
        activity.setAddTime(new Date());

        personalWxActivityMapper.insert(activity);
        return activity.getId();
    }

    @Transactional
    public void saveActivityGroupMapping(List<ScrmPersonalWxActivityGroupMapping> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        personalWxActivityGroupMappingMapper.batchInsert(list);
    }

    @Transactional
    public void deleteActivityGroupMapping(List<ScrmPersonalWxActivityGroupMapping> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(list.stream().map(ScrmPersonalWxActivityGroupMapping::getId).collect(Collectors.toList()));
        personalWxActivityGroupMappingMapper.deleteByExample(example);
    }

    @Transactional
    public long saveActivityRule(ScrmPersonalWxActivityRule rule) {
        personalWxActivityRuleMapper.insert(rule);
        return rule.getId();
    }

    public ScrmPersonalWxActivityRule queryActivityRuleByActivityId(Long activityId) {
        ScrmPersonalWxActivityRuleExample example = new ScrmPersonalWxActivityRuleExample();
        ScrmPersonalWxActivityRuleExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);

        List<ScrmPersonalWxActivityRule> scrmPersonalWxActivityRules = personalWxActivityRuleMapper.selectByExample(example);
        return CollectionUtil.isEmpty(scrmPersonalWxActivityRules) ? null : scrmPersonalWxActivityRules.get(0);
    }

    public void updateActivityRuleByPrimary(ScrmPersonalWxActivityRule rule) {
        rule.setUpdateTime(new Date());
        personalWxActivityRuleMapper.updateByPrimaryKeySelective(rule);
    }

    public long saveActivityConfig(ScrmPersonalWxActivityConfig config) {
        config.setAddTime(new Date());
        personalWxActivityConfigMapper.insert(config);
        return config.getId();
    }

    public ScrmPersonalWxActivity queryActivityById(Long activityId) {
        return personalWxActivityMapper.selectByPrimaryKey(activityId);
    }


    public void updateActivityByPrimary(ScrmPersonalWxActivity activity) {
        personalWxActivityMapper.updateByPrimaryKeySelective(activity);
    }

    public List<ScrmPersonalWxActivity> queryActivity(ScrmPersonalWxActivity activityQueryCondition) {

        ScrmPersonalWxActivityExample example = new ScrmPersonalWxActivityExample();
        ScrmPersonalWxActivityExample.Criteria criteria = example.createCriteria();
        if (activityQueryCondition.getId() != null) {
            criteria.andIdEqualTo(activityQueryCondition.getId());
        }
        if (StrUtil.isNotBlank(activityQueryCondition.getProjectId())) {
            criteria.andProjectIdEqualTo(activityQueryCondition.getProjectId());
        }

        AssertUtil.assertNotEmpty(criteria.getAllCriteria(), "the method must have at least one parameter");

        return personalWxActivityMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxActivityGroupMapping> queryActivityGroupMapping(Long activityId) {
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();

        criteria.andActivityIdEqualTo(activityId);
        return personalWxActivityGroupMappingMapper.selectByExample(example);
    }

    public Long saveActivityReward(ScrmPersonalWxActivityRuleReward reward) {
        personalWxActivityRuleRewardMapper.insert(reward);
        return reward.getId();
    }

    public Long saveActivityRuleDetail(ScrmPersonalWxActivityRuleDetail ruleDetail) {
        personalWxActivityRuleDetailMapper.insert(ruleDetail);
        return ruleDetail.getId();
    }

    public List<ScrmPersonalWxActivityRuleDetail> queryActivityRuleDetailByRuleId(Long ruleId) {
        ScrmPersonalWxActivityRuleDetailExample example = new ScrmPersonalWxActivityRuleDetailExample();
        ScrmPersonalWxActivityRuleDetailExample.Criteria criteria = example.createCriteria();
        criteria.andActivityRuleIdEqualTo(ruleId);
        example.setOrderByClause("invite_count asc");
        return personalWxActivityRuleDetailMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxActivityRuleReward> queryActivityRewardByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxActivityRuleRewardExample example = new ScrmPersonalWxActivityRuleRewardExample();
        ScrmPersonalWxActivityRuleRewardExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        return personalWxActivityRuleRewardMapper.selectByExample(example);
    }

    public ScrmPersonalWxActivityRuleReward queryActivityRewardById(Long id) {

        return personalWxActivityRuleRewardMapper.selectByPrimaryKey(id);
    }

    public ScrmPersonalWxActivityConfig queryActivityConfigByActivityId(Long activityId) {
        ScrmPersonalWxActivityConfigExample example = new ScrmPersonalWxActivityConfigExample();
        ScrmPersonalWxActivityConfigExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);

        List<ScrmPersonalWxActivityConfig> scrmPersonalWxActivityConfigs = personalWxActivityConfigMapper.selectByExample(example);
        return CollectionUtil.isEmpty(scrmPersonalWxActivityConfigs) ? null : scrmPersonalWxActivityConfigs.get(0);
    }

    public List<ScrmPersonalWxActivityRuleDetail> queryActivityRuleDetailByIds(List<Long> ruleDetailIds) {
        if (CollectionUtil.isEmpty(ruleDetailIds)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxActivityRuleDetailExample example = new ScrmPersonalWxActivityRuleDetailExample();
        ScrmPersonalWxActivityRuleDetailExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ruleDetailIds);
        return personalWxActivityRuleDetailMapper.selectByExample(example);
    }

    public void updateActivityRuleRewardByPrimary(ScrmPersonalWxActivityRuleReward reward) {
        personalWxActivityRuleRewardMapper.updateByPrimaryKeySelective(reward);
    }

    public void updateActivityRuleDetailByPrimary(ScrmPersonalWxActivityRuleDetail ruleDetail) {
        personalWxActivityRuleDetailMapper.updateByPrimaryKeySelective(ruleDetail);
    }

    public ScrmPersonalWxActivityConfig queryActivityConfigById(Long configId) {
        return personalWxActivityConfigMapper.selectByPrimaryKey(configId);
    }

    public void updateActivityConfigByPrimary(ScrmPersonalWxActivityConfig config) {
        personalWxActivityConfigMapper.updateByPrimaryKeySelective(config);
    }

    public PagerList<ActivityMyListResponse> queryActivityMyList(ActivityMyListRequest request) {
        Long total = personalWxActivityGroupMappingMapper.queryMyActivityListCount(request.getUnionId());
        if (total == null || total == 0) {
            return PagerList.empty();
        }
        int rows = request.getPageSize();
        int offset = request.getPageNo() * rows;
        List<ActivityMyListResponse> activityMyListResponses = personalWxActivityGroupMappingMapper.queryMyActivityList(request.getUnionId(), rows, offset);
        return PagerList.of(total, activityMyListResponses);

    }

    /**
     * description: every one maybe have been joined multi wx group
     *
     * @param request
     * @return
     */
    public List<ActivityMyDetailResponse> queryMyActivityWxDetail(ActivityDetailMyRequest request, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        return personalWxActivityGroupMappingMapper.queryMyActivityDetail(request, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }


    public ScrmPersonalWxActivityRewardRecord queryActivityRewardRecordDetail(Long activityId, String unionId) {
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        criteria.andUnionIdEqualTo(unionId);

        List<ScrmPersonalWxActivityRewardRecord> scrmPersonalWxActivityRewardRecord = personalWxActivityRewardRecordMapper.selectByExample(example);
        return CollectionUtil.isEmpty(scrmPersonalWxActivityRewardRecord) ? null : scrmPersonalWxActivityRewardRecord.get(0);
    }

    public int queryActivityWxInviteCount(Long activityId, String wxId, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        //TODO

        if (switchOpen("queryActivityWxInviteCount")) {
            return myInviteGroupMemberList(activityId, wxId).size();
        }

        Integer count = personalWxActivityGroupMappingMapper.queryActivityWxInviteCount(activityId, wxId, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
        return count == null ? 0 : count;
    }

    private boolean switchOpen(String method) {
        ApiSwitch apiSwitch = Lion.getBean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.api.switches", ApiSwitch.class);
        if (apiSwitch == null) {
            return false;
        }
        if ("queryValidInvitedList".equals(method)) {
            return Boolean.TRUE == apiSwitch.getQueryValidInvitedList();
        } else if ("queryBatchValidInvitedList".equals(method)) {
            return Boolean.TRUE == apiSwitch.getQueryBatchValidInvitedList();
        } else if ("queryActivityWxInviteCount".equals(method)) {
            return Boolean.TRUE == apiSwitch.getQueryActivityWxInviteCount();
        }
        return false;

    }

    private List<ScrmPersonalWxGroupMemberInfoEntity> myInviteGroupMemberList(Long activityId, String wxId) {
        List<ScrmPersonalWxGroupMemberInfoEntity> uniqueWxIdGroupMemberList = groupMemberFirstEnterGroup(activityId, wxId, true);
        List<ScrmPersonalWxGroupMemberInfoEntity> validInviteGroupMemberList = new ArrayList<>();

        for (ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity : uniqueWxIdGroupMemberList) {
            if (wxId.equals(groupMemberInfoEntity.getInviteWxId())) {
                validInviteGroupMemberList.add(groupMemberInfoEntity);
            }
        }

        return validInviteGroupMemberList;
    }

    /**
     * 查询所有用户邀请的，并且包含已经退群的用户
     *
     * @param activityId
     * @param wxId
     * @param needExcludeConsultant
     * @return
     */
    public List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberFirstEnterGroup(Long activityId, String wxId, boolean needExcludeConsultant) {
        return groupMemberFirstEnterGroup(activityId, wxId, needExcludeConsultant, false);
    }

    /**
     * 查询所有用户邀请的，并且包含已经退群的用户的首次邀请人
     *
     * @param activityId
     * @param wxId
     * @param needExcludeConsultant
     * @return
     */
    public List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberFirstEnterGroup(Long activityId, String wxId, boolean needExcludeConsultant, boolean containInvalid) {
        ScrmPersonalWxActivity activity = personalWxActivityMapper.selectByPrimaryKey(activityId);
        if (activity == null || PersonalActivityStateEnum.CREATE.getCode().equals(activity.getState())) {
            return new ArrayList<>();
        }
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntities = queryActivityGroup(activityId);
        if (CollectionUtil.isEmpty(groupInfoEntities)) {
            return new ArrayList<>();
        }
        List<String> allGroupIds = groupInfoEntities.stream().map(ScrmPersonalWxGroupInfoEntity::getChatRoomWxSerialNo).distinct().collect(Collectors.toList());
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupManageDomainService.queryGroupMemberListByWxIds(groupInfoEntities.stream().map(ScrmPersonalWxGroupInfoEntity::getChatRoomWxSerialNo).collect(Collectors.toList()), Collections.singletonList(wxId));
        if (CollectionUtil.isEmpty(groupMemberInfoEntities)) {
            return new ArrayList<>();
        }
        List<String> groupIds;
        if (needExcludeConsultant) {

            groupIds = groupMemberInfoEntities.stream().filter(m -> Objects.equals(PersonalWxGroupMemberTypeEnum.NORMAL.getCode(), m.getMemberType())).map(ScrmPersonalWxGroupMemberInfoEntity::getGroupId).distinct().collect(Collectors.toList());
        } else {
            groupIds = groupMemberInfoEntities.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getGroupId).distinct().collect(Collectors.toList());

        }
        if (CollectionUtil.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        // 我邀请的
        List<ScrmPersonalWxGroupMemberInfoEntity> invitedWxGroupMemberList;
        if (containInvalid) {
            invitedWxGroupMemberList = personalWxGroupManageDomainService.queryAllGroupMemberByInviteWxId(groupIds, wxId, activity.getEndTime());
        } else {
            invitedWxGroupMemberList = personalWxGroupManageDomainService.queryGroupMemberByInviteWxId(groupIds, wxId, activity.getEndTime());
        }
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntities = new ArrayList<>(invitedWxGroupMemberList.stream().collect(Collectors.toMap(ScrmPersonalWxGroupMemberInfoEntity::getWxId, Function.identity(), (u1, u2) -> u1)).values());

        // 我邀请的人 在群中所有的记录
        List<String> invitedWxIds = memberInfoEntities.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getWxId).collect(Collectors.toList());
        List<ScrmPersonalWxGroupMemberInfoEntity> allInvitedWxGroupMemberList = personalWxGroupManageDomainService.queryGroupMemberListByWxIds(allGroupIds, invitedWxIds);
        allInvitedWxGroupMemberList = allInvitedWxGroupMemberList.stream().filter(m -> Objects.equals(PersonalWxGroupMemberStatusEnum.IN_GROUP.getCode(), m.getStatus())).collect(Collectors.toList());
        allInvitedWxGroupMemberList.sort((o1, o2) -> (int) (o1.getAddTime().getTime() - o2.getAddTime().getTime()));

        return new ArrayList<>(allInvitedWxGroupMemberList.stream().collect(Collectors.toMap(ScrmPersonalWxGroupMemberInfoEntity::getWxId, Function.identity(), (u1, u2) -> u1)).values());
    }


    public List<ScrmPersonalWxGroupInfoEntity> queryActivityGroup(Long activityId) {
        List<ScrmPersonalWxActivityGroupMapping> mappings = queryActivityGroupMappingList(activityId);
        if (CollectionUtil.isEmpty(mappings)) {
            return new ArrayList<>();
        }
        List<ScrmPersonalWxGroupInfoEntity> groupInfoEntities = personalWxGroupManageDomainService.queryAllGroupByFamilyCodes(mappings.stream().map(ScrmPersonalWxActivityGroupMapping::getGroupFamilyCode).collect(Collectors.toList()));
        groupInfoEntities = groupInfoEntities.stream().filter(g -> StrUtil.isNotBlank(g.getChatRoomWxSerialNo())).collect(Collectors.toList());
        return groupInfoEntities;
    }

    public List<WinRewardListDTO> queryBatchActivityWxInviteCount(Long activityId, List<String> wxIds, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        if (CollectionUtil.isEmpty(wxIds)) {
            return new ArrayList<>();
        }
        return personalWxActivityGroupMappingMapper.queryBatchActivityWxInviteCount(activityId, wxIds, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }

    public PagerList<ActivityRewardMyListResponse> queryMyActivityRewardRecordList(ActivityRewardMyListRequest request) {
        int rows = request.getPageSize();
        int offset = request.getPageNo() * rows;
        Integer total = personalWxActivityGroupMappingMapper.queryMyActivityRewardRecordCount(request.getUnionId());
        if (total == null || total == 0) {
            return PagerList.empty();
        }
        List<ActivityRewardMyListResponse> activityRewardMyListResponses = personalWxActivityGroupMappingMapper.queryMyActivityRewardRecordList(request.getUnionId(), rows, offset);
        return PagerList.of(total, activityRewardMyListResponses);
    }

    public ScrmPersonalWxActivityRewardRecord queryActivityRewardRecordById(Long rewardRecordId) {
        return personalWxActivityRewardRecordMapper.selectByPrimaryKey(rewardRecordId);
    }

    public ScrmPersonalWxActivity queryActivityByWebcastId(String webcastId) {
        ScrmPersonalWxActivityExample example = new ScrmPersonalWxActivityExample();
        ScrmPersonalWxActivityExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(webcastId);
        List<ScrmPersonalWxActivity> activityList = personalWxActivityMapper.selectByExample(example);
        return CollectionUtil.isEmpty(activityList) ? null : activityList.get(0);
    }

    public List<ScrmPersonalWxActivity> queryValidActivity() {
        ScrmPersonalWxActivityExample example = new ScrmPersonalWxActivityExample();
        ScrmPersonalWxActivityExample.Criteria criteria = example.createCriteria();
        criteria.andStateEqualTo(PersonalActivityStateEnum.PUBLISH.getCode());
        return personalWxActivityMapper.selectByExample(example);
    }

    public long queryActivityRewardCount(Long activityId) {
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        return personalWxActivityRewardRecordMapper.countByExample(example);
    }

    public Long queryActivityGroupMappingCount(Long activityId) {
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        return personalWxActivityGroupMappingMapper.countByExample(example);
    }

    public List<ScrmPersonalWxActivityGroupMapping> queryActivityGroupMappingList(Long activityId) {
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        return personalWxActivityGroupMappingMapper.selectByExample(example);
    }

    public ActivityOverviewResponse queryActivityOverview(Long activityId, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        ActivityOverviewResponse response = new ActivityOverviewResponse();
        response.setMemberCount(personalWxActivityGroupMappingMapper.queryActivityMemberCountOverview(activityId, checkReJoinGroupSwitch, checkLeaveGroupSwitch));
        response.setFissionMemberCount(personalWxActivityGroupMappingMapper.queryActivityFissionMemberCountOverview(activityId, checkReJoinGroupSwitch, checkLeaveGroupSwitch));

        return response;
    }

    public Integer queryActivityMemberCountOverview(Long activityId, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        return personalWxActivityGroupMappingMapper.queryActivityMemberCountOverview(activityId, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }

    public Integer queryActivityFissionMemberCountOverview(Long activityId, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        return personalWxActivityGroupMappingMapper.queryActivityFissionMemberCountOverview(activityId, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }

    public Long queryOngoingActivityWinRewardCount(Long activityId, int firstLevelInviteCount, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        return personalWxActivityGroupMappingMapper.queryOngoingActivityWinRewardCount(activityId, firstLevelInviteCount, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }

    public List<WinRewardListDTO> queryOngoingActivityWinRewardList(Long activityId, int firstLevelInviteCount, String checkReJoinGroupSwitch, String checkLeaveGroupSwitch) {
        return personalWxActivityGroupMappingMapper.queryOngoingActivityWinRewardList(activityId, firstLevelInviteCount, checkReJoinGroupSwitch, checkLeaveGroupSwitch);
    }

    public PagerList<WinRewardListDTO> queryOngoingActivityWinRewardGroupMemberList(Long activityId, List<String> wxIds, PageRequest request) {
        Long total = personalWxActivityGroupMappingMapper.queryOngoingActivityWinRewardGroupMemberCount(activityId, wxIds);
        if (total == null || total == 0) {
            return PagerList.empty();
        }
        int rows = request.getPageSize();
        int offset = request.getPageNo() * request.getPageSize();
        return PagerList.of(total, personalWxActivityGroupMappingMapper.queryOngoingActivityWinRewardGroupMemberList(activityId, wxIds, rows, offset));
    }


    public List<ScrmPersonalWxActivity> queryActivityByIds(List<Long> activityIds) {
        if (CollectionUtil.isEmpty(activityIds)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxActivityExample example = new ScrmPersonalWxActivityExample();
        example.createCriteria().andIdIn(activityIds);
        return personalWxActivityMapper.selectByExample(example);
    }

    public PagerList<ScrmPersonalWxActivity> queryUnFinishedActivity(Date now, int currentPage, int pageSize) {
        ScrmPersonalWxActivityExample example = new ScrmPersonalWxActivityExample();
        ScrmPersonalWxActivityExample.Criteria criteria = example.createCriteria();
        criteria.andStateNotEqualTo(PersonalActivityStateEnum.FINISHED.getCode());
        criteria.andEndTimeLessThanOrEqualTo(now);
        long total = personalWxActivityMapper.countByExample(example);
        if (total == 0) {
            return PagerList.empty();
        }
        example.page(currentPage, pageSize);

        return PagerList.of(total, personalWxActivityMapper.selectByExample(example));
    }

    @Transactional
    public void saveActivityRewardRecords(Long activityId, List<ScrmPersonalWxActivityRewardRecord> rewardRecords) {
        if (CollectionUtil.isEmpty(rewardRecords)) {
            return;
        }
        List<Long> groupMemberId = rewardRecords.stream().map(ScrmPersonalWxActivityRewardRecord::getGroupMemberId).distinct().collect(Collectors.toList());
        List<ScrmPersonalWxActivityRewardRecord> rewardRecordsEntityList = queryActivityRewardRecord(activityId, groupMemberId);
        Set<Long> exitedGroupMemberId = rewardRecordsEntityList.stream().map(ScrmPersonalWxActivityRewardRecord::getGroupMemberId).collect(Collectors.toSet());
        Map<Long, ScrmPersonalWxActivityRewardRecord> existReward = rewardRecordsEntityList.stream().collect(Collectors.toMap(ScrmPersonalWxActivityRewardRecord::getGroupMemberId, Function.identity(), (u1, u2) -> u1));
        List<ScrmPersonalWxActivityRewardRecord> saveList = rewardRecords.stream().filter(r -> !exitedGroupMemberId.contains(r.getGroupMemberId())).collect(Collectors.toList());
        List<ScrmPersonalWxActivityRewardRecord> updatedList = rewardRecords.stream().filter(r -> exitedGroupMemberId.contains(r.getGroupMemberId())).collect(Collectors.toList());
        updateReward(existReward, updatedList);
        if (CollectionUtil.isEmpty(saveList)) {
            return;
        }
        personalWxActivityRewardRecordMapper.batchInsert(saveList);
    }

    private void updateReward(Map<Long, ScrmPersonalWxActivityRewardRecord> existReward, List<ScrmPersonalWxActivityRewardRecord> updatedList) {
        if (CollectionUtil.isEmpty(updatedList)) {
            return;
        }
        updatedList.forEach(r -> {
            ScrmPersonalWxActivityRewardRecord rewardRecord = existReward.get(r.getGroupMemberId());
            if (rewardRecord != null) {
                r.setId(rewardRecord.getId());
                r.setAddTime(rewardRecord.getAddTime());
                updateActivityRewardRecord(r);
            }
        });
    }
    public int updateActivityRewardRecord(ScrmPersonalWxActivityRewardRecord record) {
        record.setUpdateTime(new Date());
        return personalWxActivityRewardRecordMapper.updateByPrimaryKeySelective(record);
    }

    public Long saveActivityRewardRecord(ScrmPersonalWxActivityRewardRecord record) {
        personalWxActivityRewardRecordMapper.insert(record);
        return record.getId();
    }

    public List<ScrmPersonalWxActivityRewardRecord> queryActivityRewardRecord(Long activityId, List<Long> groupMemberId) {
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        if (CollectionUtil.isNotEmpty(groupMemberId)) {
            criteria.andGroupMemberIdIn(groupMemberId);
        }
        return personalWxActivityRewardRecordMapper.selectByExample(example);
    }

    public PagerList<ScrmPersonalWxActivityRewardRecord> queryActivityRewardRecord(Long activityId, PageRequest request) {
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);

        long total = personalWxActivityRewardRecordMapper.countByExample(example);
        if (total == 0) {
            return PagerList.empty();
        }
        example.setOrderByClause("invite_count desc");
        example.page(request.getPageNo(), request.getPageSize());
        return PagerList.of(total, personalWxActivityRewardRecordMapper.selectByExample(example));
    }

    public List<ScrmPersonalWxActivityRewardRecord> queryActivityRewardRecordByActivityIdAndUnionIds(Long activityId, List<String> unionIds) {
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(activityId);
        unionIds.removeIf(Objects::isNull);
        if (CollectionUtil.isNotEmpty(unionIds)) {
            criteria.andUnionIdIn(unionIds);
        } else return null;
        return personalWxActivityRewardRecordMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxActivityGroupMapping> queryActivityGroupMappingByGroupId(Long groupId) {
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();

        criteria.andGroupIdEqualTo(groupId);
        return personalWxActivityGroupMappingMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxActivityGroupMapping> queryActivityGroupMappingByGroupFamilyCode(String groupFamilyCode) {
        ScrmPersonalWxActivityGroupMappingExample example = new ScrmPersonalWxActivityGroupMappingExample();
        ScrmPersonalWxActivityGroupMappingExample.Criteria criteria = example.createCriteria();

        criteria.andGroupFamilyCodeEqualTo(groupFamilyCode);
        return personalWxActivityGroupMappingMapper.selectByExample(example);
    }

    public int deleteActivityRuleDetail(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return 0;
        }
        ScrmPersonalWxActivityRuleDetailExample example = new ScrmPersonalWxActivityRuleDetailExample();
        ScrmPersonalWxActivityRuleDetailExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);

        return personalWxActivityRuleDetailMapper.deleteByExample(example);
    }

    public int deleteActivityRuleReward(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return 0;
        }
        ScrmPersonalWxActivityRuleRewardExample example = new ScrmPersonalWxActivityRuleRewardExample();
        ScrmPersonalWxActivityRuleRewardExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        return personalWxActivityRuleRewardMapper.deleteByExample(example);

    }

    public void updateActivityRewardRecordPushState(List<Long> successPushRewardRecordIds) {
        if (CollectionUtil.isEmpty(successPushRewardRecordIds)) {
            return;
        }
        ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
        ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(successPushRewardRecordIds);
        ScrmPersonalWxActivityRewardRecord rewardRecord = new ScrmPersonalWxActivityRewardRecord();
        rewardRecord.setPushState(PersonalPushStateEnum.SUCCESS.getCode());
        rewardRecord.setPushTime(new Date());
        personalWxActivityRewardRecordMapper.updateByExampleSelective(rewardRecord, example);
    }

    public void updateActivityRewardRecordPushState(Map<Long, String> failPushRewardRecordIdMap) {
        if (CollectionUtil.isEmpty(failPushRewardRecordIdMap)) {
            return;
        }
        failPushRewardRecordIdMap.forEach((key, value) -> {
            ScrmPersonalWxActivityRewardRecordExample example = new ScrmPersonalWxActivityRewardRecordExample();
            ScrmPersonalWxActivityRewardRecordExample.Criteria criteria = example.createCriteria();
            criteria.andIdEqualTo(key);
            ScrmPersonalWxActivityRewardRecord rewardRecord = new ScrmPersonalWxActivityRewardRecord();
            rewardRecord.setPushState(PersonalPushStateEnum.FAILURE.getCode());
            rewardRecord.setPushResult(value);
            rewardRecord.setPushTime(new Date());
            personalWxActivityRewardRecordMapper.updateByExampleSelective(rewardRecord, example);
        });

    }

    public List<ActivityFissionMemberResponse> queryActivityParticipationGroupMember(ScrmPersonalWxActivity activity, ScrmPersonalWxActivityRule rule, ActivityFissionMemberRequest request, List<String> chatroomSerialNos) {
        int rows = request.getPageSize();
        int offset = request.getPageNo() * request.getPageSize();
        return personalWxActivityCustomMapper.queryActivityParticipationGroupMember(SqlUtil.like(request.getKeyword()), DateUtil.formatYMdHms(activity.getEndTime()), rule.getCheckLeaveGroupSwitch(), chatroomSerialNos, rows, offset);
    }

    public Integer queryActivityParticipationGroupMemberCount(ScrmPersonalWxActivity activity, ScrmPersonalWxActivityRule rule, ActivityFissionMemberRequest request, List<String> chatroomSerialNos) {
        // todo
        return personalWxActivityCustomMapper.queryActivityParticipationGroupMemberCount(SqlUtil.like(request.getKeyword()), DateUtil.formatYMdHms(activity.getEndTime()), rule.getCheckLeaveGroupSwitch(), chatroomSerialNos);
    }

    public List<WxGroupMemberDTO> queryValidInvitedList(Long activityId, String wxId, ScrmPersonalWxActivityRule rule) {
        // todo
        if (switchOpen("queryValidInvitedList")) {
            List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = myInviteGroupMemberList(activityId, wxId);
            return buildWxGroupMemberDTO(groupMemberInfoEntities);
        }

        return personalWxActivityCustomMapper.queryValidInvitedList(activityId, wxId, rule.getCheckReJoinGroupSwitch(), rule.getCheckLeaveGroupSwitch());
    }

    public List<WxGroupMemberDTO> queryBatchValidInvitedList(Long activityId, String wxId) {
        // todo
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = groupMemberFirstEnterGroup(activityId, wxId, true);
        return buildWxGroupMemberDTO(groupMemberInfoEntities);
//        return personalWxActivityCustomMapper.queryBatchValidInvitedList(activityId, wxInviteIds, wxIds, rule.getCheckReJoinGroupSwitch(), rule.getCheckLeaveGroupSwitch());
    }

    private List<WxGroupMemberDTO> buildWxGroupMemberDTO(List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities) {
        return groupMemberInfoEntities.stream().map(m -> {
            WxGroupMemberDTO wxGroupMemberDTO = new WxGroupMemberDTO();
            wxGroupMemberDTO.setWxId(m.getWxId());
            wxGroupMemberDTO.setInviteWxId(m.getInviteWxId());
            return wxGroupMemberDTO;
        }).collect(Collectors.toList());
    }

    public List<WxGroupMemberDTO> queryBatchValidInvitedList(Long activityId, String wxId, List<String> wxInviteIds, List<String> wxIds, ScrmPersonalWxActivityRule rule) {
        if (switchOpen("queryBatchValidInvitedList")) {
            return queryBatchValidInvitedList(activityId, wxId);
        }
        return personalWxActivityCustomMapper.queryBatchValidInvitedList(activityId, wxInviteIds, wxIds, rule.getCheckReJoinGroupSwitch(), rule.getCheckLeaveGroupSwitch());
    }

    public List<WinRewardListDTO> queryBatchValidInvitedListParallel(Long activityId, List<String> wxIdList, ScrmPersonalWxActivityRule rule) {
        List<WinRewardListDTO> winRewardListDTOS = new ArrayList<>();
        if (CollectionUtil.isEmpty(wxIdList)) {
            return winRewardListDTOS;
        }
        log.info("queryBatchValidInvitedListParallel start wxIdList is {}", wxIdList);
        List<CompletableFuture<List<WxGroupMemberDTO>>> futureList = new ArrayList<>();
        for (String wxId : wxIdList) {
            CompletableFuture<List<WxGroupMemberDTO>> future = CompletableFuture.supplyAsync(() ->
                            queryValidInvitedList(activityId, wxId, rule)
                    , ThreadPoolUtils.queryBatchValidInvitedListParallelPool);
            futureList.add(future);
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<List<WxGroupMemberDTO>> future : futureList) {
            List<WxGroupMemberDTO> wxGroupMemberDTOList = future.join();
            if (CollectionUtil.isNotEmpty(wxGroupMemberDTOList)) {
                WxGroupMemberDTO wxGroupMemberDTO = wxGroupMemberDTOList.get(0);
                WinRewardListDTO winRewardListDTO = new WinRewardListDTO();
                winRewardListDTO.setWxId(wxGroupMemberDTO.getInviteWxId());
                winRewardListDTO.setInviteCount(wxGroupMemberDTOList.size());
                winRewardListDTOS.add(winRewardListDTO);
            }
        }

        return winRewardListDTOS;
    }

    /**
     * 并行查询多个邀请人的有效邀请人数
     *
     * @param activityId     活动id
     * @param inviteWxIdList 邀请人微信idList
     * @return key为邀请人微信id，value为对应的有效邀请人数
     */
    public Map<String, Integer> queryInviteeCountParallel(Long activityId, List<String> inviteWxIdList) {
        Map<String, Integer> inviteeCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(inviteWxIdList)) {
            return inviteeCountMap;
        }

        List<CompletableFuture<Pair<String, Integer>>> futureList = new ArrayList<>();
        inviteWxIdList.forEach(inviteWxId -> futureList.add(
                CompletableFuture.supplyAsync(() ->
                                scrmPersonalWxInviteRelationDomainService.getInviteeCount2C(activityId, inviteWxId)
                        , ThreadPoolUtils.queryInviteeCountParallelPool)));

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<Pair<String, Integer>> future : futureList) {
            Pair<String, Integer> pair = future.join();
            inviteeCountMap.put(pair.getLeft(), pair.getRight());
        }

        return inviteeCountMap;
    }


}
