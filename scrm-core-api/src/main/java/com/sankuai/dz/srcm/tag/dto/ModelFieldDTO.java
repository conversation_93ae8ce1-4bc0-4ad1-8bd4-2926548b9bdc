package com.sankuai.dz.srcm.tag.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/10/9 11:23
 * @Version v1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFieldDTO implements Serializable {
    private String fieldLabel;
    private String fieldName;
    private Integer fieldType;
    private String fieldTypeLabel;
    private List<ModelFieldOpDTO> fieldOps;
}
