package com.sankuai.dz.srcm.automatedmanagement.enums;

import com.google.common.collect.Sets;

import java.util.Set;

public enum ScrmAmProcessOrchestrationProductItemsProductTypeEnum {

    UNKNOWN(0, "未知"),
    BULK_ORDER(1, "团购"),
    GENERAL(2, "预定");

    public static final Set<Integer> RESERVED_CODE = Sets.newHashSet(
            ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode(),
            ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()
    );

    public static final Set<String> VALID_TYPE_DESC = Sets.newHashSet(
            ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getDesc(),
            ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getDesc()
    );

    private final int code;
    private final String desc;

    ScrmAmProcessOrchestrationProductItemsProductTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getProductDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ScrmAmProcessOrchestrationProductItemsProductTypeEnum value : ScrmAmProcessOrchestrationProductItemsProductTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static ScrmAmProcessOrchestrationProductItemsProductTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ScrmAmProcessOrchestrationProductItemsProductTypeEnum value : ScrmAmProcessOrchestrationProductItemsProductTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
