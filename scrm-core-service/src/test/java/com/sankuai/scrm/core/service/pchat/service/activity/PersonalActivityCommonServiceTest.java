package com.sankuai.scrm.core.service.pchat.service.activity;

import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivity;
import com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityGroupMapping;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class PersonalActivityCommonServiceTest {

    private PersonalActivityCommonService personalActivityCommonService = new PersonalActivityCommonService();

    /**
     * 测试buildActivityGroupMapping方法，正常情况
     */
    @Test
    public void testBuildActivityGroupMappingNormal() {
        // arrange
        String creator = "creator";
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setId(1L);
        activity.setAppId("appId");
        Map<Long, String> idWithFamilyCodeMap = new HashMap<>();
        idWithFamilyCodeMap.put(1L, "familyCode");
        // act
        ScrmPersonalWxActivityGroupMapping result = personalActivityCommonService.buildActivityGroupMapping(creator, activity, idWithFamilyCodeMap.entrySet().iterator().next());
        // assert
        assertNotNull(result);
        assertEquals(activity.getId(), result.getActivityId());
        assertEquals(idWithFamilyCodeMap.keySet().iterator().next(), result.getGroupId());
        assertEquals(idWithFamilyCodeMap.values().iterator().next(), result.getGroupFamilyCode());
        assertEquals(creator, result.getCreator());
        assertEquals(activity.getAppId(), result.getAppId());
    }

    /**
     * 测试buildActivityGroupMapping方法，异常情况，idWithFamilyCodeMap为null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildActivityGroupMappingException() {
        // arrange
        String creator = "creator";
        ScrmPersonalWxActivity activity = new ScrmPersonalWxActivity();
        activity.setId(1L);
        Map<Long, String> idWithFamilyCodeMap = null;
        // act
        personalActivityCommonService.buildActivityGroupMapping(creator, activity, idWithFamilyCodeMap.entrySet().iterator().next());
    }
}
