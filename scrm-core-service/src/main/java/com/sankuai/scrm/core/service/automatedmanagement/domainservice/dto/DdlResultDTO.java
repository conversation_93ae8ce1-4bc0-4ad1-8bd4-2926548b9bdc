package com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/26
 */
@Data
@NoArgsConstructor
public class DdlResultDTO implements Serializable {
    private boolean success = false;
    private String msg;
    private Long Id;
    private Date nextRunTime;
    private boolean willRunInFuture = false;
    private boolean willRunToday = false;

    public DdlResultDTO(boolean success, String msg, Long id) {
        this.success = success;
        this.msg = msg;
        Id = id;
    }
}
