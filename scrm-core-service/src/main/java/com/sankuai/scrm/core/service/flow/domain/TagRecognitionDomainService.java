package com.sankuai.scrm.core.service.flow.domain;

import com.dianping.poi.dto.CoordType;
import com.dianping.poi.mtDto.MtLocationDTO;
import com.dianping.poi.mtRgcService.MtRgcService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.data.ups.thrift.*;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.AreaInfoTypeEnum;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.bean.FrontCityDistrictInfo;
import com.meituan.service.mobile.group.geo.service.AreaService;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.dto.GeoDTO;
import com.sankuai.persona.common.ResponseStatus;
import com.sankuai.scrm.core.service.flow.enums.TagType;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.UserProfileAclService;
import com.sankuai.scrm.core.service.portrait.constant.LabelIdConstant;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.MtPoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class TagRecognitionDomainService {

    @Autowired
    private CityService cityService;

    @Autowired
    private MtPoiService mtPoiService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private MtRgcService mtRgcService;

    @Autowired
    private UserProfileAclService userProfileAclService;

    public Map<String, String> recognizeTagValue(List<TagType> tagTypeList, FlowEntryWxMaterialRequest request) {
        if (CollectionUtils.isEmpty(tagTypeList) || request == null) {
            return Maps.newHashMap();
        }
        Map<String, String> tagMap = Maps.newHashMap();
        tagTypeList.forEach(tagType -> {
            String tagValue = recognizeTagValue(tagType, request);
            if (StringUtils.isNotEmpty(tagValue)) {
                tagMap.put(tagType.getDesc(), tagValue);
            }
        });
        return tagMap;
    }

    public String recognizeTagValue(TagType tagType, FlowEntryWxMaterialRequest request) {
        if (tagType == null || request == null) {
            return null;
        }
        Map<TagType, String> tagMap = Maps.newHashMap();
        if (TagType.CITY.equals(tagType)) {
            return queryCityName(request.getMtCityId());
        }
        if (TagType.BRAND.equals(tagType)) {
            return queryBannerName(request.getMtShopId());
        }
        if (TagType.OCCUPATION.equals(tagType)) {
            return queryOccupationName(request.getUserId());
        }
        if (TagType.BIZ_AREA.equals(tagType)) {
            return queryBizAreaName(request.getMtShopId(), request.getGeoDTO(), request.getUserId());
        }
        return null;
    }

    private String queryCityName(Integer cityId) {
        if (cityId == null) {
            return null;
        }
        CityInfo cityInfo = cityService.getCityById(cityId);
        if (cityInfo == null) {
            return null;
        }
        return cityInfo.getName();
    }

    private String queryBannerName(Long mtShopId) {
        if (mtShopId == null) {
            return null;
        }
        try {
            Map<Long, MtPoiDTO> mtPoiDTOMap = mtPoiService.findPoisById(Lists.newArrayList(mtShopId),
                    Lists.newArrayList("poiBrandId", "poiBrandName"));
            if (MapUtils.isEmpty(mtPoiDTOMap) || mtPoiDTOMap.get(mtShopId) == null) {
                return null;
            }
            MtPoiDTO mtPoiDTO = mtPoiDTOMap.get(mtShopId);
            return mtPoiDTO.getPoiBrandName();
        } catch (TException e) {
            log.error("TagRecognitionDomainService.getBannerName exception, mtShopId:{}", mtShopId, e);
        }
        return null;
    }

    private String queryOccupationName(Long mtUserId) {
        if (mtUserId == null) {
            return null;
        }
        QueryResponse response = userProfileAclService.queryByLabelId(true, Lists.newArrayList(mtUserId),
                Lists.newArrayList(LabelIdConstant.MT_TEN_CROWD_LABEL_ID));
        if (response == null || !ResponseStatus.SUCCESS.equals(response.getStatus()) || CollectionUtils.isEmpty(response.getValue())) {
            return null;
        }
        List<LabelData> labelDataList = response.getValue();
        LabelData tenCrowdLabelData = labelDataList.stream()
                .filter(Objects::nonNull)
                .filter(labelData -> LabelIdConstant.MT_TEN_CROWD_LABEL_ID == labelData.getId())
                .findFirst()
                .orElse(null);
        if (tenCrowdLabelData == null) {
            return null;
        }
        return tenCrowdLabelData.getValue();
    }

    private String queryBizAreaName(Long mtShopId, GeoDTO geo, Long mtUserId) {
        String bizAreaName;
        bizAreaName = queryBizAreaNameByMtShopId(mtShopId);
        if (StringUtils.isNotEmpty(bizAreaName)) {
            return bizAreaName;
        }
        bizAreaName = queryMtAreaIdByLngLat(geo);
        if (StringUtils.isNotEmpty(bizAreaName)) {
            return bizAreaName;
        }
        bizAreaName = queryMtAreaIdByTag(mtUserId);
        if (StringUtils.isNotEmpty(bizAreaName)) {
            return bizAreaName;
        }
        return null;
    }

    private String queryBizAreaNameByMtShopId(Long mtShopId) {
        if (mtShopId == null) {
            return null;
        }
        try {
            Map<Long, MtPoiDTO> mtPoiDTOMap = mtPoiService.findPoisById(Lists.newArrayList(mtShopId),
                    Lists.newArrayList("mtLocationId", "mtBareaId", "mtBareaName", "geo"));
            if (MapUtils.isEmpty(mtPoiDTOMap) || mtPoiDTOMap.get(mtShopId) == null) {
                return null;
            }
            MtPoiDTO mtPoiDTO = mtPoiDTOMap.get(mtShopId);
            if (mtPoiDTO.getMtLocationId() == null || mtPoiDTO.getMtBareaId() == null || mtPoiDTO.getMtBareaId() == 0) {
                return null;
            }
            List<FrontCityDistrictInfo> frontCityDistrictInfos = areaService.getFrontCityDistrictByBareaIdAndLocationId(mtPoiDTO.getMtLocationId(), mtPoiDTO.getMtBareaId());
            if (CollectionUtils.isEmpty(frontCityDistrictInfos)) {
                return null;
            }
            return frontCityDistrictInfos.stream()
                    .map(FrontCityDistrictInfo::getBizArea)
                    .filter(Objects::nonNull)
                    .filter(areaInfo -> AreaInfoTypeEnum.area.equals(areaInfo.getType()))
                    .map(AreaInfo::getName)
                    .findFirst()
                    .orElse(null);
        } catch (TException e) {
            log.error("TagRecognitionDomainService.queryBizAreaNameByMtShopId has exception, mtShopId:{}", mtShopId, e);
        }
        return null;
    }

    private String queryMtAreaIdByLngLat(GeoDTO geo) {
        if (geo == null) {
            return null;
        }
        try {
            MtLocationDTO mtLocationDTO = mtRgcService.getInfoByLngLatCoord(geo.getLng(), geo.getLat(), CoordType.GCJ02);
            if (mtLocationDTO == null || mtLocationDTO.getFrontAreaId() == null) {
                return null;
            }
            return mtLocationDTO.getFrontAreaName();
        } catch (Exception e) {
            log.error("TagRecognitionDomainService.queryMtAreaIdByLngLat has exception, lng:{}, lat:{}", geo.getLng(), geo.getLat(), e);
        }
        return null;
    }

    private String queryMtAreaIdByTag(Long mtUserId) {
        if (mtUserId == null) {
            return null;
        }
        QueryResponse response = userProfileAclService.queryByLabelId(true, Lists.newArrayList(mtUserId), Lists.newArrayList(31717));
        if (response == null || !ResponseStatus.SUCCESS.equals(response.getStatus()) ||
                CollectionUtils.isEmpty(response.getValue()) || response.getValue().get(0) == null) {
            return null;
        }
        LabelData labelData = response.getValue().get(0);
        if (NumberUtils.isParsable(labelData.getValue())) {
            AreaInfo areaInfo = areaService.getById(Integer.parseInt(labelData.getValue()));
            if (areaInfo == null || !AreaInfoTypeEnum.area.equals(areaInfo.getType())) {
                return null;
            }
            return areaInfo.getName();
        }
        return null;
    }
}
