package com.sankuai.scrm.core.service.pchat.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmLiveInfoExampleTest {

    private ScrmLiveInfoExample scrmLiveInfoExample;

    @Before
    public void setUp() {
        scrmLiveInfoExample = new ScrmLiveInfoExample();
    }

    /**
     * 测试 limit 方法，输入正常的非负整数值
     */
    @Test
    public void testLimitNormal() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer rows = 10;
        // act
        ScrmLiveInfoExample result = example.limit(rows);
        // assert
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，输入 null
     */
    @Test
    public void testLimitNull() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer rows = null;
        // act
        ScrmLiveInfoExample result = example.limit(rows);
        // assert
        assertNull(result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为正常值
     */
    @Test
    public void testLimitNormalValue() {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        ScrmLiveInfoExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为边界值
     */
    @Test
    public void testLimitBoundaryValue() {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer offset = 0;
        Integer rows = Integer.MAX_VALUE;
        // act
        ScrmLiveInfoExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为异常值
     */
    @Test
    public void testLimitExceptionValue() {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer offset = null;
        Integer rows = null;
        // act
        ScrmLiveInfoExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 为正常值，rows 为异常值
     */
    @Test
    public void testLimitMixedValue() {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer offset = 10;
        Integer rows = null;
        // act
        ScrmLiveInfoExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmLiveInfoExample.getOredCriteria().size();
        // act
        ScrmLiveInfoExample.Criteria criteria = scrmLiveInfoExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmLiveInfoExample.getOredCriteria().size());
        assertTrue(scrmLiveInfoExample.getOredCriteria().contains(criteria));
    }

    /**
     * 测试 page 方法，正常情况
     */
    @Test
    public void testPageNormal() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer page = 1;
        Integer pageSize = 10;
        // act
        ScrmLiveInfoExample result = example.page(page, pageSize);
        // assert
        assertEquals((long) page * pageSize, (long) result.getOffset());
        assertEquals((long) pageSize, (long) result.getRows());
    }

    /**
     * 测试 page 方法，边界情况
     */
    @Test
    public void testPageBoundary() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer page = 0;
        Integer pageSize = 10;
        // act
        ScrmLiveInfoExample result = example.page(page, pageSize);
        // assert
        assertEquals((long) page * pageSize, (long) result.getOffset());
        assertEquals((long) pageSize, (long) result.getRows());
    }

    /**
     * 测试 page 方法，异常情况
     * Note: Adjusted to expect NullPointerException due to the current implementation.
     */
    @Test(expected = NullPointerException.class)
    public void testPageException() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        Integer page = null;
        Integer pageSize = 10;
        // act
        example.page(page, pageSize);
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmLiveInfoExample.Criteria criteria = scrmLiveInfoExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmLiveInfoExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmLiveInfoExample.Criteria criteria1 = scrmLiveInfoExample.createCriteria();
        ScrmLiveInfoExample.Criteria criteria2 = scrmLiveInfoExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmLiveInfoExample.getOredCriteria().size());
    }

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmLiveInfoExample example = new ScrmLiveInfoExample();
        // act
        ScrmLiveInfoExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 clear 方法
     */
    @Test
    public void testClear() {
        // arrange
        scrmLiveInfoExample.setOrderByClause("test");
        scrmLiveInfoExample.setDistinct(true);
        scrmLiveInfoExample.setRows(10);
        scrmLiveInfoExample.setOffset(10);
        ScrmLiveInfoExample.Criteria criteria = scrmLiveInfoExample.createCriteria();
        scrmLiveInfoExample.getOredCriteria().add(criteria);
        // act
        scrmLiveInfoExample.clear();
        // assert
        assertTrue(scrmLiveInfoExample.getOredCriteria().isEmpty());
        assertNull(scrmLiveInfoExample.getOrderByClause());
        assertFalse(scrmLiveInfoExample.isDistinct());
        assertNull(scrmLiveInfoExample.getRows());
        assertNull(scrmLiveInfoExample.getOffset());
    }
}
