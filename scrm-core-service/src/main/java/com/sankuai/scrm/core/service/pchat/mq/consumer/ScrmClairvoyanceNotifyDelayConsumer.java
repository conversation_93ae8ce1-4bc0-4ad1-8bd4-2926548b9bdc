package com.sankuai.scrm.core.service.pchat.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.pchat.mq.handle.ScrmClairvoyanceNotifyDelayHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/7/5 14:22
 * @Version v1.0.0
 */
@Slf4j
@Component
public class ScrmClairvoyanceNotifyDelayConsumer extends AbstractConsumer {
    @Resource
    private ScrmClairvoyanceNotifyDelayHandle scrmClairvoyanceNotifyDelayHandle;

    @Override
    protected ConsumeStatus messageHandle(MafkaMessage message, MessagetContext context) {
        return scrmClairvoyanceNotifyDelayHandle.messageHandle(message);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.init("daozong", "com.sankuai.medicalcosmetology.scrm.core"
                , "scrm.clairvoyance.notify.delay.task.consumer", "scrm.clairvoyance.notify.delay.task");
    }
}
