package com.sankuai.dz.srcm.automatedmanagement.enums;

/**
 * 流程编排行为类型
 *
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/17
 */
public enum ScrmProcessOrchestrationContentSupplyTypeEnum {

    UNKNOWN("unknown", 0, "未知"),
    AUTOMATIC_PRODUCT_PROMOTION("automaticProductPromotion", 1, "自动推品"),
    MANUAL_PRODUCT_PROMOTION("manualProductPromotion", 2, "手动推品"),
    CUSTOMIZED_PRODUCT_PROMOTION("customizedProductPromotion", 3, "自定义推品"),
    COUPON_PROMOTION("couponPromotion", 4, "发券")
    ;

    private String code;

    private Integer value;

    private String description;

    ScrmProcessOrchestrationContentSupplyTypeEnum(String code, int value, String description) {
        this.code = code;
        this.value = value;
        this.description = description;
    }

    public static ScrmProcessOrchestrationContentSupplyTypeEnum getTypeByValue(int i) {
        for (ScrmProcessOrchestrationContentSupplyTypeEnum productTypeEnum : ScrmProcessOrchestrationContentSupplyTypeEnum
                .values()) {
            if (productTypeEnum.getValue() == i) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ScrmProcessOrchestrationContentSupplyTypeEnum getTypeByCode(String code) {
        for (ScrmProcessOrchestrationContentSupplyTypeEnum productTypeEnum : ScrmProcessOrchestrationContentSupplyTypeEnum
                .values()) {
            if (productTypeEnum.getCode().equalsIgnoreCase(code) || productTypeEnum.name().equalsIgnoreCase(code)) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public Integer getValue() {
        return value;
    }

    public byte getValueByte() {
        return value.byteValue();
    }

    public String getDescription() {
        return description;
    }
}
