package com.sankuai.scrm.core.service.pchat.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/3 19:21
 * @Version v1.0.0
 */
@Service
@Slf4j
public class ScrmGroupMemberStatePushProducer extends AbstractProducer implements InitializingBean {
    @Override
    public String getProducerName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet("daozong", "com.sankuai.medicalcosmetology.scrm.core", "scrm.pchat.group.user.state.change");
    }
}
