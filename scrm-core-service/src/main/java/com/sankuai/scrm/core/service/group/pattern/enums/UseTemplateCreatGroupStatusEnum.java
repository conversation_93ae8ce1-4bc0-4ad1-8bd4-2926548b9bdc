package com.sankuai.scrm.core.service.group.pattern.enums;


public enum UseTemplateCreatGroupStatusEnum {

    INITIALIZE_SUCCESS(1, "初始化成功"),

    INITIALIZE_FAIL(2, "初始化失败"),
    CHECKING(3, "检测中"),
    EXECUTE_SUCCESS(4, "执行成功"),
    EXECUTE_FAIL(5, "执行失败");


    public final int code;

    public final String desc;

    UseTemplateCreatGroupStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(int code) {
        for (UseTemplateCreatGroupStatusEnum status : UseTemplateCreatGroupStatusEnum.values()) {
            if (status.code == code) {
                return status.desc;
            }
        }
        return null;
    }

}
