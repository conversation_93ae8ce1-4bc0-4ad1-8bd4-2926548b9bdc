package com.sankuai.scrm.core.service.data.statistics.dal.babymapper;

import com.sankuai.dz.srcm.data.statistics.dto.EntireStatisticsIndicatorDTO;
import com.sankuai.dz.srcm.data.statistics.dto.ScrmStatisticsIndicatorDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface MemberInfoEntityDOMapper {

    //按日: 结果可计算在群人数环比, 新增在群人数环比
    ScrmStatisticsIndicatorDTO countGroupMemberCountForDay(@Param("corpId") String corpId,
                                                           @Param("previousDay") Date previousDay, @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                           @Param("previousDayLastWeek") Date previousDayLastWeek, @Param("startDayLastWeek") Date startDayLastWeek, @Param("endDayLastWeek") Date endDayLastWeek,
                                                           @Param("previousDayLastMonth") Date previousDayLastMonth, @Param("startDayOfLastMonth") Date startDayOfLastMonth, @Param("endDayOfLastMonth") Date endDayOfLastMonth);

    //按周或月: 不在群环比
    ScrmStatisticsIndicatorDTO countGroupMemberCount(@Param("corpId") String corpId, @Param("status") Integer status,
                                                     @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                     @Param("lastPeriodStart") Date lastPeriodStart, @Param("lastPeriodEnd") Date lastPeriodEnd,
                                                     @Param("beforeLastPeriodStart") Date beforeLastPeriodStart, @Param("beforeLastPeriodEnd") Date beforeLastPeriodEnd);

    //按日情况下整体统计: 查询社群和好友的重复计数包括新增的,
    EntireStatisticsIndicatorDTO statisticsRepeatCountForDay(@Param("corpId") String corpId,
                                                                @Param("previousDay") Date previousDay, @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                                @Param("previousDayLastWeek") Date previousDayLastWeek, @Param("startDayLastWeek") Date startDayLastWeek, @Param("endDayLastWeek") Date endDayLastWeek,
                                                                @Param("previousDayLastMonth") Date previousDayLastMonth, @Param("startDayOfLastMonth") Date startDayOfLastMonth, @Param("endDayOfLastMonth") Date endDayOfLastMonth);


    //整体统计:  周, 月,自定义, 流失好友数的重复计数
    EntireStatisticsIndicatorDTO statisticsRepeatCount(@Param("corpId") String corpId, @Param("memberStatus") Integer memberStatus, @Param("friendStatus") Integer friendStatus,
                                                          @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                          @Param("lastPeriodStart") Date lastPeriodStart, @Param("lastPeriodEnd") Date lastPeriodEnd,
                                                          @Param("beforeLastPeriodStart") Date beforeLastPeriodStart, @Param("beforeLastPeriodEnd") Date beforeLastPeriodEnd);


    long countExistGroupMemberNum(@Param("corpId")String corpId,  @Param("endDate") Date endDate);

   long countRepeatUserNumForEverDay(@Param("corpId")String corpId,   @Param("endDate") Date endDate);





}
