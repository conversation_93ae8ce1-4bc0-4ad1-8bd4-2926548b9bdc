package com.sankuai.scrm.core.service.message.task.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_Friend_MsgTaskLog
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmFriendMsgTaskLog {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: task_id
     *   说明: 周期任务id
     */
    private Long taskId;

    /**
     *   字段: app_id
     *   说明: 业务线的appid
     */
    private String appId;

    /**
     *   字段: title
     *   说明: 群发任务的标题
     */
    private String title;

    /**
     *   字段: status
     *   说明: 状态，1为发送中，2为已完成，3为已取消
     */
    private Integer status;

    /**
     *   字段: all_size
     *   说明: 整体数量
     */
    private Integer allSize;

    /**
     *   字段: fail_size
     *   说明: 失败数量
     */
    private Integer failSize;

    /**
     *   字段: next_send_time
     *   说明: 下次发送时间
     */
    private Date nextSendTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}