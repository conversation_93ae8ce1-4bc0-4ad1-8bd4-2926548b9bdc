package com.sankuai.scrm.core.service.pchat.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/4 14:28
 * @Version v1.0.0
 */
@Data
public class ScrmPersonalWxGroupMemberInfoDTO {
    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: wx_id
     * 说明: 在微信群内的wxid
     */
    private String wxId;

    /**
     * 字段: user_serial_no
     * 说明: 用户在群内的序列id
     */
    private String userSerialNo;

    /**
     * 字段: union_id
     * 说明: 识别出的在美小的用户unionid
     */
    private String unionId;

    /**
     * 字段: user_id
     * 说明: 识别出的在美团侧的用户userid
     */
    private Long userId;

    /**
     * 字段: group_id
     * 说明: 群id
     */
    private String groupId;

    /**
     * 字段: join_type
     * 说明: 入群方式
     */
    private Integer joinType;

    /**
     * 字段: invite_wx_id
     * 说明: 邀请者微信群内的wxid
     */
    private String inviteWxId;

    /**
     * 字段: member_nick_name
     * 说明: 成员昵称
     */
    private String memberNickName;

    /**
     * 字段: wx_nickname
     * 说明: 微信昵称
     */
    private String wxNickname;

    /**
     * 字段: avatar
     * 说明: 头像
     */
    private String avatar;

    /**
     * 字段: member_type
     * 说明: 成员类型，1-咨询师，2-普通成员
     */
    private Byte memberType;

    /**
     * 群备注
     */
    private String groupRemark;

    /**
     * 字段: status
     * 说明: 群成员状态 0:在群里 1离开群
     */
    private Byte status;

    /**
     * 字段: state
     * 说明: 企业自定义的state参数，用于区分不同的入群渠道
     */
    private String state;

    /**
     * 字段: enter_time
     * 说明: 进群时间
     */
    private Date enterTime;

    /**
     * 字段: deleted
     * 说明: 是否删除 0 未删除
     */
    private Boolean deleted;

    /**
     * 字段: add_time
     * 说明: 创建时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;

    /**
     * 微信号
     */
    private String wxAlias;
    /**
     * 群名称
     */
    private String groupName;
    /**
     * 直播id
     */
    private String projectId;
    private Long groupEntityId;
    private int rank=0;
    private int originRank=0;
}
