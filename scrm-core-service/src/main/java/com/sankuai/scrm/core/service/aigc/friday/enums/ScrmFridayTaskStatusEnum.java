package com.sankuai.scrm.core.service.aigc.friday.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> on 2024/08/23 15:05
 */
@Getter
public enum ScrmFridayTaskStatusEnum {

    UNKNOWN(0, "未知"),
    /**
     * 待执行：任务刚创建、任务重试失败（重试次数小于最大重试次数）
     */
    TO_BE_EXECUTED(1, "待执行"),
    RUNNING(2, "执行中"),
    /**
     * 重试失败：任务重试失败（重试次数大于等于最大重试次数）
     */
    RETRY_FAILED(3, "重试失败"),
    /**
     * OpenAI接口调用出错时会进行重试，直到达到最大重试次数
     * 业务出错后不会进行重试
     */
    RUNTIME_ERROR(4, "业务出错"),
    SUCCESS(5, "请求成功"),;

    private final Integer code;

    private final String desc;

    ScrmFridayTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ScrmFridayTaskStatusEnum fromCode(Integer code) {
        for (ScrmFridayTaskStatusEnum enumValue : ScrmFridayTaskStatusEnum.values()) {
            if (Objects.equals(enumValue.code, code)) {
                return enumValue;
            }
        }
        return ScrmFridayTaskStatusEnum.UNKNOWN;
    }

}
