package com.sankuai.scrm.core.service.group.creation.domain;

import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.scrm.core.service.group.creation.context.GroupCreationContext;
import com.sankuai.scrm.core.service.group.template.dao.bo.GroupBatchCreateTaskDetail;
import com.sankuai.scrm.core.service.group.template.dao.bo.GroupBatchCreateTaskOverview;
import com.sankuai.scrm.core.service.group.template.domain.GroupCreateTaskDetailDomainService;
import com.sankuai.scrm.core.service.group.template.domain.GroupCreateTaskOverviewDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeLocalService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupCreationDomainServiceCreatePrivateLiveGroupDynCodeTest {

    @InjectMocks
    private GroupCreationDomainService groupCreationDomainService;

    @Mock
    private GroupCreateTaskOverviewDomainService taskOverviewDomainService;

    @Mock
    private GroupDynamicCodeLocalService groupDynamicCodeLocalService;

    private GroupBatchCreateTaskDetail detail;

    private GroupBatchCreateTaskOverview overview;

    private GroupDynamicCodeInfoDTO code;

    @Before
    public void setUp() {
        detail = new GroupBatchCreateTaskDetail();
        detail.setTaskId(1L);
        detail.setLiveGroupId(1L);
        detail.setGroupId("groupId");
        detail.setGroupName("groupName");
        overview = new GroupBatchCreateTaskOverview();
        overview.setChannelId(1L);
        overview.setAppId("appId");
        code = new GroupDynamicCodeInfoDTO();
        code.setName("私域直播群" + detail.getLiveGroupId());
        code.setChannelId(overview.getChannelId());
        code.setAppId(overview.getAppId());
        code.setMtCityId(-1L);
        code.setGroupIdList(java.util.Collections.singletonList(detail.getGroupId()));
        code.setOwnerIsRobot(true);
        code.setGroupAutoIncrement(true);
        code.setGroupNamePrefix(detail.getGroupName());
        code.setGroupNameInitialSeq(1);
    }

    @Test
    public void testCreatePrivateLiveGroupDynCodeDetailIsNull() throws Throwable {
        GroupDynamicCodeInfoDTO result = groupCreationDomainService.createPrivateLiveGroupDynCode(null, true);
        assertNull(result);
    }

    @Test
    public void testCreatePrivateLiveGroupDynCodeOverviewIsNull() throws Throwable {
        when(taskOverviewDomainService.queryById(detail.getTaskId())).thenReturn(null);
        GroupDynamicCodeInfoDTO result = groupCreationDomainService.createPrivateLiveGroupDynCode(detail, true);
        assertNull(result);
    }

    @Test
    public void testCreatePrivateLiveGroupDynCodeCreateCodeIsNull() throws Throwable {
        when(taskOverviewDomainService.queryById(detail.getTaskId())).thenReturn(overview);
        when(groupDynamicCodeLocalService.createCode(code)).thenReturn(null);
        GroupDynamicCodeInfoDTO result = groupCreationDomainService.createPrivateLiveGroupDynCode(detail, true);
        assertNull(result);
    }
}
