<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.message.task.dal.mapper.ScrmFriendCycleMsgTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="send_object_type" jdbcType="INTEGER" property="sendObjectType" />
    <result column="send_strategy_type" jdbcType="INTEGER" property="sendStrategyType" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="send_end_time" jdbcType="TIMESTAMP" property="sendEndTime" />
    <result column="special_end_cycle" jdbcType="VARCHAR" property="specialEndCycle" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creater" jdbcType="VARCHAR" property="creater" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    <result column="send_object" jdbcType="LONGVARCHAR" property="sendObject" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, title, send_object_type, send_strategy_type, send_time, send_end_time, 
    special_end_cycle, status, creater, add_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    send_object, content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendCycleMsgTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Scrm_Friend_CycleMsgTask
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendCycleMsgTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_Friend_CycleMsgTask
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Scrm_Friend_CycleMsgTask
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_Friend_CycleMsgTask
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendCycleMsgTaskExample">
    delete from Scrm_Friend_CycleMsgTask
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_Friend_CycleMsgTask (app_id, title, send_object_type, 
      send_strategy_type, send_time, send_end_time, 
      special_end_cycle, status, creater, 
      add_time, update_time, send_object, 
      content)
    values (#{appId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{sendObjectType,jdbcType=INTEGER}, 
      #{sendStrategyType,jdbcType=INTEGER}, #{sendTime,jdbcType=TIMESTAMP}, #{sendEndTime,jdbcType=TIMESTAMP}, 
      #{specialEndCycle,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{creater,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{sendObject,jdbcType=LONGVARCHAR}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_Friend_CycleMsgTask
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="sendObjectType != null">
        send_object_type,
      </if>
      <if test="sendStrategyType != null">
        send_strategy_type,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="sendEndTime != null">
        send_end_time,
      </if>
      <if test="specialEndCycle != null">
        special_end_cycle,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="sendObject != null">
        send_object,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="sendObjectType != null">
        #{sendObjectType,jdbcType=INTEGER},
      </if>
      <if test="sendStrategyType != null">
        #{sendStrategyType,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendEndTime != null">
        #{sendEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="specialEndCycle != null">
        #{specialEndCycle,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendObject != null">
        #{sendObject,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendCycleMsgTaskExample" resultType="java.lang.Long">
    select count(*) from Scrm_Friend_CycleMsgTask
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_Friend_CycleMsgTask
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.sendObjectType != null">
        send_object_type = #{record.sendObjectType,jdbcType=INTEGER},
      </if>
      <if test="record.sendStrategyType != null">
        send_strategy_type = #{record.sendStrategyType,jdbcType=INTEGER},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sendEndTime != null">
        send_end_time = #{record.sendEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.specialEndCycle != null">
        special_end_cycle = #{record.specialEndCycle,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.creater != null">
        creater = #{record.creater,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sendObject != null">
        send_object = #{record.sendObject,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update Scrm_Friend_CycleMsgTask
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      send_object_type = #{record.sendObjectType,jdbcType=INTEGER},
      send_strategy_type = #{record.sendStrategyType,jdbcType=INTEGER},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      send_end_time = #{record.sendEndTime,jdbcType=TIMESTAMP},
      special_end_cycle = #{record.specialEndCycle,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      creater = #{record.creater,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      send_object = #{record.sendObject,jdbcType=LONGVARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_Friend_CycleMsgTask
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      send_object_type = #{record.sendObjectType,jdbcType=INTEGER},
      send_strategy_type = #{record.sendStrategyType,jdbcType=INTEGER},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      send_end_time = #{record.sendEndTime,jdbcType=TIMESTAMP},
      special_end_cycle = #{record.specialEndCycle,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      creater = #{record.creater,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    update Scrm_Friend_CycleMsgTask
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sendObjectType != null">
        send_object_type = #{sendObjectType,jdbcType=INTEGER},
      </if>
      <if test="sendStrategyType != null">
        send_strategy_type = #{sendStrategyType,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendEndTime != null">
        send_end_time = #{sendEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="specialEndCycle != null">
        special_end_cycle = #{specialEndCycle,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendObject != null">
        send_object = #{sendObject,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    update Scrm_Friend_CycleMsgTask
    set app_id = #{appId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      send_object_type = #{sendObjectType,jdbcType=INTEGER},
      send_strategy_type = #{sendStrategyType,jdbcType=INTEGER},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      send_end_time = #{sendEndTime,jdbcType=TIMESTAMP},
      special_end_cycle = #{specialEndCycle,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creater = #{creater,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      send_object = #{sendObject,jdbcType=LONGVARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask">
    update Scrm_Friend_CycleMsgTask
    set app_id = #{appId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      send_object_type = #{sendObjectType,jdbcType=INTEGER},
      send_strategy_type = #{sendStrategyType,jdbcType=INTEGER},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      send_end_time = #{sendEndTime,jdbcType=TIMESTAMP},
      special_end_cycle = #{specialEndCycle,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creater = #{creater,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_Friend_CycleMsgTask
    (app_id, title, send_object_type, send_strategy_type, send_time, send_end_time, special_end_cycle, 
      status, creater, add_time, update_time, send_object, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.sendObjectType,jdbcType=INTEGER}, 
        #{item.sendStrategyType,jdbcType=INTEGER}, #{item.sendTime,jdbcType=TIMESTAMP}, 
        #{item.sendEndTime,jdbcType=TIMESTAMP}, #{item.specialEndCycle,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.creater,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.sendObject,jdbcType=LONGVARCHAR}, 
        #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>