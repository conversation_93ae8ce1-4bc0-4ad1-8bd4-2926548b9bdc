package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;


import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CrowdPackPersonaRedisService {
    private static final int REDIS_BATCH_SIZE = 50000; // Redis批处理大小
    private static final String SCRM_PERSONA_USER_CATEGORY = "scrm_persona_user_id_category";

    @Autowired
    private RedisStoreClient redisClient;

    public boolean setTodayPersonaUserIdSet(long packId, Set<Long> mtUserIdSet){
        String date = LocalDate.now().toString();
        return setPersonaUserIdSet(packId, mtUserIdSet, date);
    }

    private boolean setPersonaUserIdSet(long packId, Set<Long> mtUserIdSet, String date) {
        if (CollectionUtils.isEmpty(mtUserIdSet)) {
            return false;
        }

        List<Long> userIdList = new ArrayList<>(mtUserIdSet);

        try {
            // 计算需要分多少批
            int totalSize = userIdList.size();
            int batchCount = (totalSize + REDIS_BATCH_SIZE - 1) / REDIS_BATCH_SIZE;

            // 存储批次信息
            StoreKey batchInfoKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_info");
            redisClient.set(batchInfoKey, String.valueOf(batchCount), 2 * 24 * 60 * 60);

            // 分批存储数据
            for (int i = 0; i < batchCount; i++) {
                int startIndex = i * REDIS_BATCH_SIZE;
                int endIndex = Math.min(startIndex + REDIS_BATCH_SIZE, totalSize);

                List<Long> batchUserIds = userIdList.subList(startIndex, endIndex);
                String jsonString = JsonUtils.toStr(batchUserIds);

                StoreKey batchKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_" + i);
                redisClient.set(batchKey, jsonString, 2 * 24 * 60 * 60);
            }

            log.info("Successfully stored persona user set for packId: {}, date: {}, total size: {}, batch count: {}",
                    packId, date, totalSize, batchCount);
            return true;

        } catch (Exception e) {
            log.error("Failed to store persona user set for packId: {}, date: {}, size: {}",
                    packId, date, userIdList.size(), e);
            return false;
        }
    }

    public Set<Long> getTodayPersonaUserIdSet(long packId) {
        String date = LocalDate.now().toString();
        return getPersonaUserIdSetByDate(packId, date);
    }

    public Set<Long> getYesterdayPersonaUserIdSet(long packId) {
        String date = LocalDate.now().minusDays(1).toString();
        return getPersonaUserIdSetByDate(packId, date);
    }

    private Set<Long> getPersonaUserIdSetByDate(long packId, String date) {
        try {
            // 获取批次信息
            StoreKey batchInfoKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_info");
            String batchCountStr = redisClient.get(batchInfoKey);

            if (StringUtils.isBlank(batchCountStr)) {
                return new HashSet<>();
            }

            int batchCount = Integer.parseInt(batchCountStr);
            Set<Long> allUserIds = new HashSet<>();

            // 分批读取数据
            for (int i = 0; i < batchCount; i++) {
                StoreKey batchKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_" + i);
                String jsonString = redisClient.get(batchKey);

                if (StringUtils.isNotBlank(jsonString)) {
                    List<Long> batchUserIds = JsonUtils.toList(jsonString, Long.class);
                    if (CollectionUtils.isNotEmpty(batchUserIds)) {
                        allUserIds.addAll(batchUserIds);
                    }
                }
            }

            log.info("Successfully retrieved persona user set for packId: {}, date: {}, total size: {}, batch count: {}",
                    packId, date, allUserIds.size(), batchCount);
            return allUserIds;

        } catch (Exception e) {
            log.error("Failed to retrieve persona user set for packId: {}, date: {}", packId, date, e);
            return new HashSet<>();
        }
    }

    public boolean isTodayPersonaUserExist(long packId) {
        String date = LocalDate.now().toString();
        StoreKey batchInfoKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_info");
        return redisClient.exists(batchInfoKey);
    }

    public boolean deleteYesterdayPersonaUserIdSet(long packId) {
        String date = LocalDate.now().minusDays(1).toString();
        return deletePersonaUserIdSetByDate(packId, date);
    }

    private boolean deletePersonaUserIdSetByDate(long packId, String date) {
        try {
            // 获取批次信息
            StoreKey batchInfoKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_info");
            String batchCountStr = redisClient.get(batchInfoKey);

            if (StringUtils.isNotBlank(batchCountStr)) {
                int batchCount = Integer.parseInt(batchCountStr);

                // 删除所有批次数据
                for (int i = 0; i < batchCount; i++) {
                    StoreKey batchKey = new StoreKey(SCRM_PERSONA_USER_CATEGORY, packId, date, "batch_" + i);
                    redisClient.delete(batchKey);
                }
            }

            // 删除批次信息
            redisClient.delete(batchInfoKey);

            log.info("Successfully deleted persona user set for packId: {}, date: {}", packId, date);
            return true;

        } catch (Exception e) {
            log.error("Failed to delete persona user set for packId: {}, date: {}", packId, date, e);
            return false;
        }
    }

}