package com.sankuai.scrm.core.docker;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;

import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolUtils {

    public static final ThreadPoolExecutor SCRM_CALC_USER_TAG_SCORE_LIVE_EXECUTOR_DOCKER = Rhino
            .newThreadPool("ScrmCalcUsrTagScoreLiveTaskDocker-Pool",
                    DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(5).withMaxQueueSize(2000))
            .getExecutor();

    public static final ThreadPoolExecutor SCRM_CALC_USER_TAG_SCORE_USER_EXECUTOR_DOCKER = Rhino
            .newThreadPool("ScrmCalcUsrTagScoreTaskDocker-Pool",
                    DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(5).withMaxQueueSize(2000))
            .getExecutor();

}
