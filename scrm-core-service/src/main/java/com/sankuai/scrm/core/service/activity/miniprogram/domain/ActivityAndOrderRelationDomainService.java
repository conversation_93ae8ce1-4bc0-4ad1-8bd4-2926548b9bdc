package com.sankuai.scrm.core.service.activity.miniprogram.domain;

import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.ActivityAndOrderRelation;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.example.ActivityAndOrderRelationExample;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.mapper.ActivityAndOrderRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityAndOrderRelationDomainService {

    @Resource
    private ActivityAndOrderRelationMapper activityAndOrderRelationMapper;

    public boolean saveActivityAndOrderRelation(ActivityAndOrderRelation relation) {
        if (ObjectUtils.isEmpty(relation) || relation.getOrderId() == null || relation.getActivityId() == null) {
            return false;
        }

        return activityAndOrderRelationMapper.insertSelective(relation) > 0;
    }

    public List<ActivityAndOrderRelation> batchQueryActivityOrderRelation(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return Lists.newArrayList();
        }
        ActivityAndOrderRelationExample example = new ActivityAndOrderRelationExample();
        example.createCriteria().andActivityIdIn(activityIds);
        return activityAndOrderRelationMapper.selectByExample(example);
    }
    public List<ActivityAndOrderRelation> batchQueryRelationByPathParam(List<String> pathParams) {
        if (CollectionUtils.isEmpty(pathParams)) {
            return Lists.newArrayList();
        }
        ActivityAndOrderRelationExample example = new ActivityAndOrderRelationExample();
        example.createCriteria().andPathParamIn(pathParams);
        return activityAndOrderRelationMapper.selectByExample(example);
    }



    public List<Long> queryOrderIdsByActivityId(Long activityId) {
        if (activityId == null) {
            return Lists.newArrayList();
        }
        ActivityAndOrderRelationExample example = new ActivityAndOrderRelationExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        List<ActivityAndOrderRelation> orderRelations = activityAndOrderRelationMapper.selectByExample(example);
        return orderRelations.stream().map(ActivityAndOrderRelation::getOrderId).collect(Collectors.toList());
    }

    public List<ActivityAndOrderRelation> queryRelationByPathParam(String pathParam) {
        if (StringUtils.isBlank(pathParam)) {
            return Lists.newArrayList();
        }
        ActivityAndOrderRelationExample example = new ActivityAndOrderRelationExample();
        example.createCriteria().andPathParamEqualTo(pathParam);
        return activityAndOrderRelationMapper.selectByExample(example);
    }

}
