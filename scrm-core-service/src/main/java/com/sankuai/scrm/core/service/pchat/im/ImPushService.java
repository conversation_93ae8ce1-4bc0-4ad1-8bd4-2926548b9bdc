package com.sankuai.scrm.core.service.pchat.im;

import com.sankuai.dz.srcm.pchat.response.im.*;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMemberInfoResponse;
import com.sankuai.scrm.core.service.pchat.im.dto.ImPushMsgStateDTO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/11 15:07
 * @Version v1.0.0
 */
public interface ImPushService {
    /**
     * 推送-会话消息(与消息查询结构一致)
     */
    void pushSession(String robotSerialNo, List<String> alias, ImMsgListResponse message);

    /**
     * 私聊好友信息
     *
     * @param robotSerialNo
     * @param alias
     * @param message
     */
    void pushPrivateSession(String robotSerialNo, List<String> alias, FriendSessionList message);

    /**
     * 推送-消息成功失败推送
     */
    void pushMsgState(String robotSerialNo, List<String> alias, ImPushMsgStateDTO message);

    /**
     * 推送-联系人(与联系人结构一致)
     */
    void pushContacts(String robotSerialNo, List<String> alias, ImFriendListResponse message);

    /**
     * 推送-机器人状态(与机器人结构一致)
     */
    void pushRobotState(String robotSerialNo, List<String> alias, ImRobotListResponse message);

    /**
     * 推送-新机器人(与机器人结构一致)
     */
    void pushRobot(String robotSerialNo, List<String> alias, ImRobotListResponse message);

    /**
     * 推送-群(与群结构一致)
     */
    void pushGroup(String robotSerialNo, List<String> alias, ImGroupListResponse message);

    /**
     * 推送-群成员(与群成员结构一致)
     */
    void pushGroupMember(String robotSerialNo, List<String> alias, GroupMemberInfoResponse message);

    /**
     * 推送群编辑信息
     *
     * @param alias
     * @param response
     */
    void pushGroupSetup(String robotSerialNo, List<String> alias, ImGroupSetupResponse response);
}
