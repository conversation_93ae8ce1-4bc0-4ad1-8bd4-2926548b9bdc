package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsFridayIntelligentFollowLog;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmFridayIntelligentFollowLogDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmFridayIntelligentFollowLogDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.aigc.service.enums.AISceneABTestStatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.AISceneExecuteDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.mockito.*;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class ScrmIntelligentFollowResultDelayConsumerWriteLogTest {

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @InjectMocks
    private ScrmIntelligentFollowResultDelayConsumer consumer;

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private AISceneExecuteDomainService aiSceneExecuteDomainService;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    private Method recvMessageMethod;

    /**
     * Helper method to invoke the private writeLog method using reflection
     */
    private void invokeWriteLogMethod(IntelligentFollowResultDTO mqMessageDTO, StepExecuteResultDTO stepExecuteResultDTO) throws Throwable {
        Method writeLogMethod = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredMethod("writeLog", IntelligentFollowResultDTO.class, StepExecuteResultDTO.class);
        writeLogMethod.setAccessible(true);
        writeLogMethod.invoke(consumer, mqMessageDTO, stepExecuteResultDTO);
    }

    /**
     * Test when stepExecuteResultDTO is null, method should return immediately
     */
    @Test
    public void testWriteLogWhenStepResultIsNull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        StepExecuteResultDTO stepExecuteResultDTO = null;
        // act
        invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
        // assert
        verifyNoInteractions(dashBoardESWriteDomainService);
        verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
    }

    /**
     * Test normal case where both ES and DB updates succeed
     */
    @Test
    public void testWriteLogSuccessfullyUpdatesBothESAndDB() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        // act
        invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
        verify(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
    }

    /**
     * Test when ES update fails, should catch exception and log error
     * The method should not continue to DB update as the exception is caught at the method level
     */
    @Test
    public void testWriteLogWhenESUpdateFails() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            doThrow(new RuntimeException("ES update failed")).when(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(), anyString());
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
            // The method should NOT try to update DB after ES failure
            // because the exception is caught at the method level
            verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
            // Verify Cat.logEvent was called
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
        }
    }

    /**
     * Test with null consumer processor - removed as it's not a valid test case
     * The actual implementation doesn't handle null consumer case,
     * so testing it would just be testing Java's NPE behavior
     */
    /**
     * Test when DB update fails, should catch exception and log error
     */
    @Test
    public void testWriteLogWhenDBUpdateFails() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            doThrow(new RuntimeException("DB update failed")).when(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(), any());
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
            verify(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
            // Verify Cat.logEvent was called
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
        }
    }

    /**
     * Test when mqMessageDTO is null, should handle gracefully
     */
    @Test
    public void testWriteLogWhenMqMessageDTOIsNull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = null;
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            // Should catch NPE and log error
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
            // No interactions with services due to NPE
            verifyNoInteractions(dashBoardESWriteDomainService);
            verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        recvMessageMethod = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    @Test
    public void testRecvMessageNullBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(message, times(3)).getBody();
    }

    @Test
    public void testRecvMessageJsonParseFailed() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(message, atLeast(2)).getBody();
    }

    @Test
    public void testRecvMessageInvalidProcessId() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.getProcessIdByAppId(anyString())).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(consumerConfig).getProcessIdByAppId("testApp");
        verify(aiSceneExecuteDomainService, never()).runRealTimeTask(anyLong(), anyString(), any());
    }


    @Test
    public void testRecvMessageUnionIdBlank() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setProcessId(100L);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn(null);
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        when(aiSceneExecuteDomainService.runRealTimeTask(anyLong(), anyString(), any())).thenReturn(stepResult);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(mtUserCenterAclService).getUnionIdByUserIdFromMtUserCenter(123L, "wxde8ac0a21135c07d");
        verify(aiSceneExecuteDomainService).runRealTimeTask(100L, null, dto);
    }

    @Test
    public void testRecvMessageTaskExecutionException() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setProcessId(100L);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("union123");
        when(aiSceneExecuteDomainService.runRealTimeTask(anyLong(), anyString(), any())).thenThrow(new RuntimeException("Task execution failed"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiSceneExecuteDomainService).runRealTimeTask(100L, "union123", dto);
    }

    @Test
    public void testRecvMessageNoNeedSendMsg() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setNeedSendMsg(false);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // processId will be set to 0L
        when(consumerConfig.getProcessIdByAppId(anyString())).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiSceneExecuteDomainService, never()).runRealTimeTask(anyLong(), anyString(), any());
    }

    @Test
    public void testRecvMessageWithProcessIdInMessage() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setProcessId(300L);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.getProcessIdByAppId(anyString())).thenReturn(100L);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("union123");
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        when(aiSceneExecuteDomainService.runRealTimeTask(anyLong(), anyString(), any())).thenReturn(stepResult);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiSceneExecuteDomainService).runRealTimeTask(300L, "union123", dto);
    }

    @Test
    public void testRecvMessageSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        dto.setAppId("testApp");
        dto.setUserId(123L);
        dto.setProcessId(100L);
        dto.setEsId("es123");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("union123");
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        when(aiSceneExecuteDomainService.runRealTimeTask(anyLong(), anyString(), any())).thenReturn(stepResult);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(), eq("es123"));
    }


}
