package com.sankuai.scrm.core.service.user.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import java.lang.reflect.Method;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.srcm.user.dto.PeriodSearchStat;
import com.sankuai.dz.srcm.user.dto.UserSearchViewResult;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.MockedStatic;

// 启用 Mockito 支持
@ExtendWith(MockitoExtension.class)
public class UserSearchViewConverterToInteger1Test {

    /**
     * 使用反射调用私有方法 toInteger
     */
    private Integer invokePrivateToInteger(Integer input) throws Exception {
        Method method = UserIntendVisitViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        method.setAccessible(true);
        return (Integer) method.invoke(null, input);
    }

    /**
     * 测试 toInteger 方法，当输入为 null 时，返回 0
     */
    @Test
    public void testToInteger_WhenInputIsNull_ReturnsZero() throws Throwable {
        // Arrange
        Integer input = null;
        Integer expected = 0;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为非 null 整数时，返回原值
     */
    @Test
    public void testToInteger_WhenInputIsNotNull_ReturnsSameValue() throws Throwable {
        // Arrange
        Integer input = 42;
        Integer expected = 42;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为 0 时，返回 0
     */
    @Test
    public void testToInteger_WhenInputIsZero_ReturnsZero() throws Throwable {
        // Arrange
        Integer input = 0;
        Integer expected = 0;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 toInteger 方法，当输入为负数时，返回原值
     */
    @Test
    public void testToInteger_WhenInputIsNegative_ReturnsSameValue() throws Throwable {
        // Arrange
        Integer input = -10;
        Integer expected = -10;
        // Act
        Integer result = invokePrivateToInteger(input);
        // Assert
        assertEquals(expected, result);
    }

    @Test
    void testConvertToUserSearchViewList_NullInput_ReturnsNull() throws Throwable {
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(null);
        assertNull(result, "Should return null for null input");
    }

    @Test
    void testConvertToUserSearchViewList_EmptyList_ReturnsNull() throws Throwable {
        List<UserSearchViewResult> emptyList = Collections.emptyList();
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(emptyList);
        assertNull(result, "Should return null for empty list");
    }

    @Test
    void testConvertToUserSearchViewList_SingleValidItem_ReturnsConvertedList() throws Throwable {
        UserSearchViewResult inputItem = new UserSearchViewResult();
        inputItem.setUserId(1001L);
        inputItem.setAppId("testApp");
        List<UserSearchViewResult> inputList = Collections.singletonList(inputItem);
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(inputList);
        assertNotNull(result, "Result list should not be null");
        assertEquals(1, result.size(), "Should have one converted item");
        assertEquals(1001L, result.get(0).getUserId(), "User ID should match");
        assertEquals("testApp", result.get(0).getAppId(), "App ID should match");
    }

    @Test
    void testConvertToUserSearchViewList_MultipleValidItems_ReturnsConvertedList() throws Throwable {
        UserSearchViewResult item1 = new UserSearchViewResult();
        item1.setUserId(1001L);
        UserSearchViewResult item2 = new UserSearchViewResult();
        item2.setUserId(1002L);
        List<UserSearchViewResult> inputList = Arrays.asList(item1, item2);
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(inputList);
        assertNotNull(result, "Result list should not be null");
        assertEquals(2, result.size(), "Should have two converted items");
        assertEquals(1001L, result.get(0).getUserId(), "First user ID should match");
        assertEquals(1002L, result.get(1).getUserId(), "Second user ID should match");
    }

    @Test
    void testConvertToUserSearchViewList_ListWithNullItem_ReturnsListWithNull() throws Throwable {
        UserSearchViewResult validItem = new UserSearchViewResult();
        validItem.setUserId(1001L);
        List<UserSearchViewResult> inputList = Arrays.asList(validItem, null);
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(inputList);
        assertNotNull(result, "Result list should not be null");
        assertEquals(2, result.size(), "Should have two items in result");
        assertEquals(1001L, result.get(0).getUserId(), "Valid item should be converted");
        assertNull(result.get(1), "Null item should remain null");
    }

    @Test
    void testConvertToUserSearchViewList_VerifyStreamProcessing() throws Throwable {
        // 由于要测试的方法是静态的，且内部调用了另一个静态方法，
        // 这种情况下不适合使用Mockito进行mock测试。
        // 改为验证实际的转换行为
        UserSearchViewResult item1 = new UserSearchViewResult();
        item1.setUserId(1001L);
        UserSearchViewResult item2 = new UserSearchViewResult();
        item2.setUserId(1002L);
        List<UserSearchViewResult> inputList = Arrays.asList(item1, item2);
        List<UserSearchView> result = UserSearchViewConverter.convertToUserSearchViewList(inputList);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1001L, result.get(0).getUserId());
        assertEquals(1002L, result.get(1).getUserId());
    }
}
