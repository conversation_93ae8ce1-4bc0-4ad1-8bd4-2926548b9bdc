package com.sankuai.scrm.core.service.aigc.service.impl;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplySessionDO;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.ScrmIntelligentReplyDomainService;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.entity.FriendChatMessageContext;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.entity.IntelligentReply;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.domainservice.entity.IntelligentReplyContent;
import com.sankuai.scrm.core.service.friend.autoreply.intelligence.repository.ScrmFriendAutoreplySessionRepository;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoreplyServiceImplTest {

    @InjectMocks
    private ScrmAutoreplyServiceImpl scrmAutoreplyService;

    @Mock
    private ScrmIntelligentReplyDomainService replyDomainService;
    @Mock
    private ScrmFriendAutoreplySessionRepository sessionRepository;

    /**
     * 测试场景：当智能回复成功生成且有回复内容时，记录助手消息
     */
    @Test
    public void testMockGenerateReplyIntelligentReplySuccess() throws Throwable {
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class);
                MockedStatic<Environment> environmentMockedStatic = mockStatic(Environment.class)) {
            environmentMockedStatic.when(Environment::getAppName).thenReturn("test-app");
            mockedLion.when(
                    () -> Lion.getList(eq("test-app"), eq("intelligence.autoreply.mock.messages"), eq(String.class)))
                    .thenReturn(Collections.singletonList("testMessage"));
            mockedLion.when(() -> Lion.getString(eq("test-app"), eq("autoreply.ending.session.content")))
                    .thenReturn("Ending Session Content");

            // arrange
            String message = "测试消息";
            ScrmFriendAutoreplySessionDO sessionDO = new ScrmFriendAutoreplySessionDO();
            sessionDO.setId(1L);

            when(sessionRepository.getSessionMessagesForceMaster(anyLong()))
                    .thenReturn(Collections.singletonList(new ScrmFriendAutoreplyMessageLogDO()));
            when(sessionRepository.recordFriendMessage(any())).thenAnswer(invocation -> {
                FriendChatMessageContext context = (FriendChatMessageContext)invocation.getArguments()[0];
                return FriendChatMessageContext.builder().friendMessage(context.getFriendMessage()).sessionDO(sessionDO)
                        .friendMessageLogDO(new ScrmFriendAutoreplyMessageLogDO()).build();
            });
            when(replyDomainService.generateIntelligentReply(anyString(), any(), anyList()))
                    .thenReturn(new IntelligentReply(IntelligentReply.ReplyTypeEnum.REPLY, Collections
                            .singletonList(new IntelligentReplyContent(ContentTypeTEnum.TEXT, "回复内容", false))));

            // act
            scrmAutoreplyService.mockGenerateReply(message);

            // assert
            verify(sessionRepository, times(1)).recordAssistantMessage(any(FriendChatMessageContext.class));
        }
    }
}