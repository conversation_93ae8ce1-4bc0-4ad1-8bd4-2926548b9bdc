package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/8/12
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
public class InvokeDetailKeyObject implements Serializable {
    /**
     *   字段: type
     *   说明: moment 朋友圈  message 消息
     */
    private String type;

    /**
     *   字段: content_type
     *   说明: 内容类型
     */
    private Byte contentType;

    /**
     *   字段: content_sub_type
     *   说明: 内容子类型
     */
    private Byte contentSubType;

    private Long nodeId;
}
