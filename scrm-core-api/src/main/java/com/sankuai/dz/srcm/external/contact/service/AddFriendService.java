package com.sankuai.dz.srcm.external.contact.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.external.contact.dto.ScrmMobileAddFriendTaskDTO;
import com.sankuai.dz.srcm.external.contact.request.MobileAddFriendRealTimeRequest;
import com.sankuai.dz.srcm.external.contact.request.QueryMobileAddFriendRealTimeByIdRequest;
import com.sankuai.dz.srcm.external.contact.request.QueryPageMobileAddFriendRealTimeRequest;

public interface AddFriendService {
    //主动添加用户为好友接口
    RemoteResponse<Long> createMobileAddFriendRealTime(MobileAddFriendRealTimeRequest request);

    //根据任务id查询结果接口
    RemoteResponse<ScrmMobileAddFriendTaskDTO> queryMobileAddFriendRealTimeById(QueryMobileAddFriendRealTimeByIdRequest request);

    //分页查询主动添加用户为好友任务
    PageRemoteResponse<ScrmMobileAddFriendTaskDTO> queryPageMobileAddFriendRealTime(QueryPageMobileAddFriendRealTimeRequest request);
}
