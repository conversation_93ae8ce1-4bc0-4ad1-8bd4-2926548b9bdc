package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import lombok.*;

import java.util.Date;

/**
 *
 *   表名: scrm_a_m_process_orchestration_product_items
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmProcessOrchestrationProductItemsDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: product_id
     *   说明: 商品id
     */
    private Long productId;

    /**
     *   字段: dp_product_id
     *   说明: 点评商品id
     */
    private Long dpProductId;

    /**
     *   字段: product_type
     *   说明: 商品类型 0未知 1团购 2泛商品
     */
    private Integer productType;

    /**
     *   字段: app_id
     *   说明: 业务id
     */
    private String appId;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}