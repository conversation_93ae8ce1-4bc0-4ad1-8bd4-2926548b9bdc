package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import lombok.*;

import java.util.Date;

/**
 *
 *   表名: scrm_a_m_process_orchestration_product_activ_page
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmProcessOrchestrationProductActivityPageDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: tag_id
     *   说明: 对应scrm_a_m_process_orchestration_product_tags主键, 每个新建活动页都作为一个特殊标签录入标签表
     */
    private Long tagId;

    /**
     *   字段: app_id
     *   说明: 业务id
     */
    private String appId;

    /**
     *   字段: activity_title
     *   说明: 活动页标题
     */
    private String activityTitle;

    /**
     *   字段: mini_program_title
     *   说明: 小程序标题
     */
    private String miniProgramTitle;

    /**
     *   字段: mini_program_origin_app_id
     *   说明: 原始小程序appid
     */
    private String miniProgramOriginAppId;

    /**
     *   字段: mini_program_app_id
     *   说明: 小程序appid
     */
    private String miniProgramAppId;

    /**
     *   字段: page_path
     *   说明: 小程序页面路径
     */
    private String pagePath;

    /**
     *   字段: thumb_pic_url
     *   说明: 小程序封面
     */
    private String thumbPicUrl;

    /**
     *   字段: back_ground_pic_url
     *   说明: 活动页背景图
     */
    private String backGroundPicUrl;

    /**
     *   字段: related_product_ids
     *   说明: 活动页关联商品id
     */
    private String relatedProductIds;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}