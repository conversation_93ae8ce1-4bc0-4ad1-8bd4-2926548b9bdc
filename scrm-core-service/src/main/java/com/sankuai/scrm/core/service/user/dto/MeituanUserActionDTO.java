package com.sankuai.scrm.core.service.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/15 16:39
 * @Version v1.0.0
 */
@Data
public class MeituanUserActionDTO implements Serializable {

    /**
     * 日期分区字段，格式为datekey(yyyymmdd)
     */
    private String dt;
    /**
     *
     */
    @JsonProperty("user_id")
    private Long userId;
    /**
     * 用户行为
     */
    private String act;
    /**
     * 搜索关键词
     */
    @JsonProperty("search_keyword")
    private String searchKeyword;
    /**
     * 商品一级分类名称
     */
    @JsonProperty("product_cat_name")
    private String productCatName;
    /**
     * 商品一级分类ID
     */
    @JsonProperty("product_cat_id")
    private Long productCatId;
    /**
     * 行为发生时间
     */
    @JsonProperty("act_time")
    private String actTime;

    /**
     * 品类行为次数
     */
    @JsonProperty("action_times")
    private Integer actionTimes;
}
