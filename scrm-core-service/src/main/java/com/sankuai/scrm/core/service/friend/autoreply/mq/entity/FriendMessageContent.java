package com.sankuai.scrm.core.service.friend.autoreply.mq.entity;

import lombok.Data;

import java.util.List;

/**
 * see <a href="https://km.sankuai.com/page/1282364080#id-MessageContent">MessageContent</a>
 *
 * <AUTHOR> on 2024/10/29 14:23
 */
@Data
public class FriendMessageContent {

    // 消息类型。暂时只考虑：1-文本、2-图片、3-小程序、4-图文链接
    private Integer contentType;
    // 消息内容。【文本】或【图片】消息类型时生效
    private String content;

    // 小程序标题/图文标题（【小程序】或【图文链接】消息类型时生效）
    private String title;

    // 小程序相关字段
    // 小程序appId
    private String appId;
    // 小程序原始appId
    private String originId;
    // 小程序名称
    private String appName;
    // 小程序页面路径
    private String pagePath;

    // 图文链接相关字段
    // 图文描述
    private String desc;
    // 图文标题
    private String linkUrl;

    // 被@的用户昵称（【文本】消息类型时生效）
    private List<String> wasAt;

    // --------------
    // |云PC通道时生效|
    // --------------
    // 【文本】消息类型时生效
    private DeepseaFriendMessageText text;
    // 【图片】消息类型时生效
    private DeepseaFriendMessageImage image;
    // 【小程序】消息类型时生效
    private DeepseaFriendMessageMiniProgram miniProgram;
    // 【图文链接】消息类型时生效
    private DeepseaFriendMessageLink link;

}
