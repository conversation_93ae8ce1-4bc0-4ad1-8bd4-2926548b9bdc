package com.sankuai.scrm.core.service.user.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.user.dto.UserIntendVisitViewResult;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import com.dianping.frog.sdk.util.StringUtils;
import com.sankuai.dz.srcm.user.dto.UserSearchViewResult;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;

class ScrmUserSSViewServiceImplGetUserIntendVisitViewByAppId1Test {

    private ScrmUserSSViewServiceImpl service;

    @Mock
    private PorosRestHighLevelClient porosRestHighLevelClient;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() throws Exception {
        closeable = MockitoAnnotations.openMocks(this);
        service = new ScrmUserSSViewServiceImpl();
        // Use reflection to set the private field
        Field field = ScrmUserSSViewServiceImpl.class.getDeclaredField("porosRestHighLevelClient");
        field.setAccessible(true);
        field.set(service, porosRestHighLevelClient);
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }

    /**
     * 测试正常情况：所有参数都有效，ES查询返回结果
     */
    @Test
    void testGetUserIntendVisitViewByAppIdSuccess() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = "testApp";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        SearchHit searchHit = mock(SearchHit.class);
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("userId", userId);
        sourceMap.put("cChain", cChain);
        sourceMap.put("appId", appId);
        sourceMap.put("partitionDate", "2023-01-01");
        sourceMap.put("calDim", "daily");
        sourceMap.put("visitType", "intend");
        sourceMap.put("userPk", "userPk123");
        sourceMap.put("bizlnBuCode", 2001);
        sourceMap.put("bizlnBuName", "测试业务");
        sourceMap.put("lastVisitDate", "2023-01-01");
        sourceMap.put("lastVisitTime", "10:00:00");
        sourceMap.put("last2tdVisitDays", 5);
        sourceMap.put("lastdayVisitPv", 100L);
        sourceMap.put("lastdayVisitSec", new BigDecimal("300.5"));
        sourceMap.put("lastdayVisitProductPv", 50L);
        sourceMap.put("lastdayVisitProductAmt", new BigDecimal("1000.00"));
        sourceMap.put("lastdayVisitProductAvgAmt", new BigDecimal("20.00"));
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[] { searchHit });
        when(searchHit.getSourceAsMap()).thenReturn(sourceMap);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertFalse(result.getData().isEmpty());
        assertEquals(1, result.getData().size());
        UserIntendVisitViewResult dto = result.getData().get(0);
        assertEquals(userId, dto.getUserId());
        assertEquals(cChain, dto.getCChain());
        assertEquals(appId, dto.getAppId());
        assertEquals("2023-01-01", dto.getPartitionDate());
        assertEquals("daily", dto.getCalDim());
        assertEquals("intend", dto.getVisitType());
        assertEquals("userPk123", dto.getUserPk());
        assertEquals(Integer.valueOf(2001), dto.getBizlnBuCode());
        assertEquals("测试业务", dto.getBizlnBuName());
        assertEquals("2023-01-01", dto.getLastVisitDate());
        assertEquals("10:00:00", dto.getLastVisitTime());
        assertEquals(Integer.valueOf(5), dto.getLast2tdVisitDays());
        assertEquals(Long.valueOf(100), dto.getLastdayVisitPv());
        assertEquals(new BigDecimal("300.5"), dto.getLastdayVisitSec());
        assertEquals(Long.valueOf(50), dto.getLastdayVisitProductPv());
        assertEquals(new BigDecimal("1000.00"), dto.getLastdayVisitProductAmt());
        assertEquals(new BigDecimal("20.00"), dto.getLastdayVisitProductAvgAmt());
    }

    /**
     * 测试异常情况：userId为null
     */
    @Test
    void testGetUserIntendVisitViewByAppIdNullUserId() throws Throwable {
        // arrange
        Long userId = null;
        Integer cChain = 1001;
        String appId = "testApp";
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户ID不能为空", result.getMsg());
        assertNull(result.getData());
    }

    /**
     * 测试正常情况：userId不为null但cChain为null
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithNullCChain() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = null;
        String appId = "testApp";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试正常情况：userId和cChain不为null但appId为null
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithNullAppId() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = null;
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试异常情况：ES查询抛出IOException
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithIOException() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = "testApp";
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenThrow(new IOException("ES查询异常"));
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMsg().contains("根据appId查询用户意向访问画像失败"));
        assertNull(result.getData());
    }

    /**
     * 测试边界情况：ES查询返回空结果
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithEmptyResult() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = "testApp";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试边界情况：ES查询返回的hit中source为null
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithNullSource() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = "testApp";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        SearchHit searchHit = mock(SearchHit.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[] { searchHit });
        when(searchHit.getSourceAsMap()).thenReturn(null);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试边界情况：converter转换返回null的情况
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithConverterReturnNull() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        String appId = "testApp";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        SearchHit searchHit = mock(SearchHit.class);
        // 构造一个会导致转换失败的数据
        Map<String, Object> sourceMap = new HashMap<>();
        // 类型不匹配，会导致转换失败
        sourceMap.put("userId", "invalid");
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[] { searchHit });
        when(searchHit.getSourceAsMap()).thenReturn(sourceMap);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        // After examining the code more carefully, we can see that the convertToUserIntendVisitViewDTO method
        // catches exceptions but still returns a DTO with null values, which is then added to the result list.
        // So we should expect 1 item in the list, not 0.
        assertEquals(1, result.getData().size());
        // Verify that the returned DTO has null values for the fields that would be affected by the conversion error
        UserIntendVisitViewResult dto = result.getData().get(0);
        assertNull(dto.getUserId());
    }

    /**
     * 测试边界情况：appId为空字符串
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithEmptyAppId() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        // 空字符串
        String appId = "";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试边界情况：appId只包含空格
     */
    @Test
    void testGetUserIntendVisitViewByAppIdWithBlankAppId() throws Throwable {
        // arrange
        Long userId = 12345L;
        Integer cChain = 1001;
        // 只包含空格
        String appId = "   ";
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.getHits()).thenReturn(new SearchHit[0]);
        when(porosRestHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        // act
        RemoteResponse<List<UserIntendVisitViewResult>> result = service.getUserIntendVisitViewByAppId(userId, cChain, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void testGetUserSearchViewByIdWithNullUserPk() throws Throwable {
        // arrange
        String userPk = null;
        String appId = "testAppId";
        Integer bizlnBuCode = 1001;
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户主键不能为空", result.getMsg());
        assertNull(result.getData());
    }

    @Test
    void testGetUserSearchViewByIdWithBlankAppId() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "";
        Integer bizlnBuCode = 1001;
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户主键不能为空", result.getMsg());
        assertNull(result.getData());
    }

    @Test
    void testGetUserSearchViewByIdWithNullBizlnBuCode() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "testAppId";
        Integer bizlnBuCode = null;
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户主键不能为空", result.getMsg());
        assertNull(result.getData());
    }

    @Test
    void testGetUserSearchViewByIdWhenDocumentNotFound() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "testAppId";
        Integer bizlnBuCode = 1001;
        GetResponse getResponse = mock(GetResponse.class);
        when(getResponse.isExists()).thenReturn(false);
        when(porosRestHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class))).thenReturn(getResponse);
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getData());
        verify(porosRestHighLevelClient).get(any(GetRequest.class), any(RequestOptions.class));
    }

    @Test
    void testGetUserSearchViewByIdSuccessfully() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "testAppId";
        Integer bizlnBuCode = 1001;
        GetResponse getResponse = mock(GetResponse.class);
        when(getResponse.isExists()).thenReturn(true);
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("userId", 12345L);
        sourceMap.put("cChain", 1);
        sourceMap.put("appId", "testAppId");
        sourceMap.put("partitionDate", "2023-01-01");
        sourceMap.put("calDim", "daily");
        sourceMap.put("bizlnBuCode", 1001);
        sourceMap.put("sourceInfo", "testSource");
        sourceMap.put("isDirect", 1);
        sourceMap.put("itemType", "testItemType");
        sourceMap.put("lastSearchDate", "2023-01-01");
        sourceMap.put("lastSearchToNowDays", 1);
        sourceMap.put("lastdaySearchKeywordCnt", 10L);
        sourceMap.put("lastdayClickQv", 5L);
        sourceMap.put("lastdayQv", 8L);
        sourceMap.put("lastdayClickItemCnt", 3L);
        sourceMap.put("lastdayViewItemCnt", 7L);
        when(getResponse.getSourceAsMap()).thenReturn(sourceMap);
        when(porosRestHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class))).thenReturn(getResponse);
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(12345L, result.getData().getUserId());
        assertEquals("testAppId", result.getData().getAppId());
        verify(porosRestHighLevelClient).get(any(GetRequest.class), any(RequestOptions.class));
    }

    @Test
    void testGetUserSearchViewByIdWithIOException() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "testAppId";
        Integer bizlnBuCode = 1001;
        when(porosRestHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class))).thenThrow(new IOException("Elasticsearch connection failed"));
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMsg().contains("根据ID查询用户搜索画像失败"));
        assertNull(result.getData());
        verify(porosRestHighLevelClient).get(any(GetRequest.class), any(RequestOptions.class));
    }

    @Test
    void testGetUserSearchViewByIdWithConversionException() throws Throwable {
        // arrange
        String userPk = "testUserPk";
        String appId = "testAppId";
        Integer bizlnBuCode = 1001;
        GetResponse getResponse = mock(GetResponse.class);
        when(getResponse.isExists()).thenReturn(true);
        // 模拟在convertToUserSearchViewDTO方法中抛出异常的情况
        // 这里我们模拟getSourceAsMap返回一个会引发异常的数据结构
        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("userId", new Object() {

            @Override
            public String toString() {
                throw new RuntimeException("Conversion error");
            }
        });
        when(getResponse.getSourceAsMap()).thenReturn(sourceMap);
        when(porosRestHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class))).thenReturn(getResponse);
        // act
        RemoteResponse<UserSearchViewResult> result = service.getUserSearchViewById(userPk, appId, bizlnBuCode);
        // assert
        assertNotNull(result);
        // 方法内部捕获了转换异常，仍然返回成功但数据为null
        assertTrue(result.isSuccess());
        // 在实际实现中，如果convertToUserSearchViewDTO抛出异常，会返回null
        assertNull(result.getData());
        verify(porosRestHighLevelClient).get(any(GetRequest.class), any(RequestOptions.class));
    }
}
