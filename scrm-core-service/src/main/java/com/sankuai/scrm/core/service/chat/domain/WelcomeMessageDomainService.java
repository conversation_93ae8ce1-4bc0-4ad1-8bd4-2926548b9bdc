package com.sankuai.scrm.core.service.chat.domain;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.chat.dto.MessageDTO;
import com.sankuai.dz.srcm.chat.enums.WelcomeMsgTypeEnum;
import com.sankuai.scrm.core.service.chat.dal.entity.WelcomeMessage;
import com.sankuai.scrm.core.service.chat.dal.example.WelcomeMessageExample;
import com.sankuai.scrm.core.service.chat.dal.mapper.WelcomeMessageMapper;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class WelcomeMessageDomainService {

    @Autowired
    private WelcomeMessageMapper welcomeMessageMapper;

    public int countWelcomeMessage(String appId, String creator, String searchWord, WelcomeMsgTypeEnum welcomeMsgType) {
        if (StringUtils.isEmpty(appId) || welcomeMsgType == null) {
            return 0;
        }
        WelcomeMessageExample example = new WelcomeMessageExample();
        WelcomeMessageExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andTypeEqualTo(welcomeMsgType.getCode())
                .andStatusEqualTo(true);
        if (StringUtils.isNotEmpty(creator)) {
            criteria.andCreatorEqualTo(creator);
        }
        if (StringUtils.isNotEmpty(searchWord)) {
            criteria.andMsgListLike('%' + searchWord + '%');
        }
        return (int) welcomeMessageMapper.countByExample(example);
    }

    public WelcomeMessage queryWelcomeMessage(Long id) {
        if (id == null) {
            return null;
        }
        return welcomeMessageMapper.selectByPrimaryKey(id);
    }

    public WelcomeMessage queryWelcomeMessage(String appId, String account, WelcomeMsgTypeEnum welcomeMsgType) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(account) || welcomeMsgType == null) {
            return null;
        }
        WelcomeMessageExample example = new WelcomeMessageExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAccountEqualTo(account)
                .andTypeEqualTo(welcomeMsgType.getCode())
                .andStatusEqualTo(true);
        List<WelcomeMessage> welcomeMessageList = welcomeMessageMapper.selectByExample(example);
        return welcomeMessageList.stream().findFirst().orElse(null);
    }

    public List<WelcomeMessage> queryWelcomeMessageList(String appId, String creator, String searchWord, WelcomeMsgTypeEnum welcomeMsgType, int offset, int rows) {
        if (StringUtils.isEmpty(appId) || welcomeMsgType == null || offset < 0 || rows <= 0) {
            return Lists.newArrayList();
        }
        WelcomeMessageExample example = new WelcomeMessageExample();
        WelcomeMessageExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andTypeEqualTo(welcomeMsgType.getCode())
                .andStatusEqualTo(true);
        if (StringUtils.isNotEmpty(creator)) {
            criteria.andCreatorEqualTo(creator);
        }
        if (StringUtils.isNotEmpty(searchWord)) {
            criteria.andMsgListLike('%' + searchWord + '%');
        }
        example.setOrderByClause("update_time DESC");
        example.setOffset(offset);
        example.setRows(rows);
        return welcomeMessageMapper.selectByExample(example);
    }

    public List<WelcomeMessage> queryFriendWelcomeMsgList(String appId, Integer pageSize, Integer pageNum) {
        if (StringUtils.isBlank(appId) || pageSize < 0 || pageNum < 0) {
            return Collections.emptyList();
        }
        WelcomeMessageExample example = buildFriendWelcomeMsgExample(appId);
        example.page(pageNum - 1, pageSize);
        return welcomeMessageMapper.selectByExample(example);
    }

    public long countFriendWelcomeMsg(String appId) {
        WelcomeMessageExample example = buildFriendWelcomeMsgExample(appId);
        return welcomeMessageMapper.countByExample(example);
    }


    private WelcomeMessageExample buildFriendWelcomeMsgExample(String appId) {
        WelcomeMessageExample example = new WelcomeMessageExample();
        WelcomeMessageExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId).andTypeEqualTo(WelcomeMsgTypeEnum.WE_CHAT_FRIEND_WELCOME_MSG.getCode()).andStatusEqualTo(true);
        example.setOrderByClause("update_time DESC");
        return example;
    }

    public boolean createWelcomeMessage(String appId, String creator, String account, List<MessageDTO> msgList, WelcomeMsgTypeEnum welcomeMsgType) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(creator) || StringUtils.isEmpty(account) ||
                CollectionUtils.isEmpty(msgList) || WelcomeMsgTypeEnum.UNKNOWN.equals(welcomeMsgType)) {
            return false;
        }
        try {
            String msgListStr = JsonUtils.toStr(msgList);
            WelcomeMessage record = WelcomeMessage.builder()
                    .appId(appId)
                    .creator(creator)
                    .account(account)
                    .msgList(msgListStr)
                    .type(welcomeMsgType.getCode())
                    .status(true)
                    .build();
            return welcomeMessageMapper.insertSelective(record) > 0;
        } catch (Exception e) {
            log.error("WelcomeMessageDomainService.createWelcomeMessage fail", e);
        }
        return false;
    }

    public Long createWelcomeMessage(String appId, String welcomeMsgName, String creator, String account, String msg,
                                     WelcomeMsgTypeEnum welcomeMsgType) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(welcomeMsgName) || StringUtils.isEmpty(creator)
                || StringUtils.isEmpty(account) || StringUtils.isEmpty(msg)) {
            return null;
        }
        WelcomeMessage record = WelcomeMessage.builder()
                .appId(appId)
                .name(welcomeMsgName)
                .creator(creator)
                .account(account)
                .msgList(msg)
                .type(welcomeMsgType.getCode())
                .status(true)
                .build();
        int row = welcomeMessageMapper.insertSelective(record);
        return row > 0 ? record.getId() : null;
    }

    public boolean updateWelcomeMessage(Long id, String account, List<MessageDTO> msgList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.chat.domain.WelcomeMessageDomainService.updateWelcomeMessage(java.lang.Long,java.lang.String,java.util.List)");
        if (id == null || StringUtils.isEmpty(account) || CollectionUtils.isEmpty(msgList)) {
            return false;
        }
        try {
            String msgListStr = JsonUtils.toStr(msgList);
            WelcomeMessage record = WelcomeMessage.builder()
                    .id(id)
                    .account(account)
                    .msgList(msgListStr)
                    .build();
            return welcomeMessageMapper.updateByPrimaryKeySelective(record) > 0;
        } catch (Exception e) {
            log.error("WelcomeMessageDomainService.updateWelcomeMessage fail", e);
        }
        return false;
    }

    public boolean updateWelcomeMessage(Long id, String name, String account, String msg) {
        if (id == null || StringUtils.isEmpty(name) || StringUtils.isEmpty(account) || StringUtils.isEmpty(msg)) {
            return false;
        }
        WelcomeMessage record = WelcomeMessage.builder()
                .id(id)
                .name(name)
                .account(account)
                .msgList(msg)
                .build();
        return welcomeMessageMapper.updateByPrimaryKeySelective(record) > 0;
    }

    public boolean deleteWelcomeMessage(Long id) {
        if (id == null) {
            return false;
        }
        WelcomeMessage record = WelcomeMessage.builder()
                .id(id)
                .status(false)
                .build();
        return welcomeMessageMapper.updateByPrimaryKeySelective(record) > 0;
    }
}
