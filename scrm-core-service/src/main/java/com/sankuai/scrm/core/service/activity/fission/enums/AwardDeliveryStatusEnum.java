package com.sankuai.scrm.core.service.activity.fission.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/3/18 14:16
 * @Version v1.0.0
 */
@Getter
public enum AwardDeliveryStatusEnum {
    IS("1", "已发放"),
    NOT("0", "未发放"),
    ONGOING("2", "发放中"),
    FAIL("3", "发放失败"),
    ;


    private String code;
    private String desc;

    AwardDeliveryStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AwardDeliveryStatusEnum fromCode(String code) {
        for (AwardDeliveryStatusEnum enumValue : AwardDeliveryStatusEnum.values()) {
            if (Objects.equals(enumValue.code, code)) {
                return enumValue;
            }
        }
        return null;
    }

}
