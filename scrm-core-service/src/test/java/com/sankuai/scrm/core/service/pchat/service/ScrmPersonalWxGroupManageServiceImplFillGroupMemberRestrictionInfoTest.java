package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupListRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupListResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMemberInfoResponse;
import com.sankuai.scrm.core.service.pchat.bo.PersonalWxMemberCountStat;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmRestrictionGroup;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmRestrictionUser;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmRestrictionUserProcess;
import com.sankuai.scrm.core.service.pchat.domain.BizIdentificationService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionGroupDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionIdentificationDomainService;
import com.sankuai.scrm.core.service.pchat.dto.RestrictionUserDTO;
import com.sankuai.scrm.core.service.pchat.enums.RestrictionReasonTypeEnum;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxGroupManageServiceImplFillGroupMemberRestrictionInfoTest {

    @Mock
    private ScrmRestrictionGroupDomainService restrictionGroupDomainService;

    @Mock
    private ScrmRestrictionGroupServiceImpl restrictionGroupService;

    private Method fillGroupMemberRestrictionInfoMethod;

    @InjectMocks
    private ScrmPersonalWxGroupManageServiceImpl scrmPersonalWxGroupManageService;

    @Mock
    private BizIdentificationService bizIdentificationService;

    @Mock
    private WebcastService webcastService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private ScrmRestrictionIdentificationDomainService scrmRestrictionIdentificationDomainService;

    @Before
    public void setUp() throws Exception {
        fillGroupMemberRestrictionInfoMethod = ScrmPersonalWxGroupManageServiceImpl.class.getDeclaredMethod("fillGroupMemberRestrictionInfo", String.class, List.class, Map.class);
        fillGroupMemberRestrictionInfoMethod.setAccessible(true);
    }

    private void invokePrivateMethod(String projectId, List<GroupMemberInfoResponse> responses, Map<String, ScrmRestrictionUser> restrictionUserMap) throws Exception {
        fillGroupMemberRestrictionInfoMethod.invoke(scrmPersonalWxGroupManageService, projectId, responses, restrictionUserMap);
    }

    private GroupMemberInfoResponse createGroupMemberInfoResponse(String wxId, boolean isRestricted) {
        GroupMemberInfoResponse response = new GroupMemberInfoResponse();
        response.setMemberWxId(wxId);
        response.setIsRestrictedUser(isRestricted);
        return response;
    }

    private ScrmRestrictionUser createRestrictionUser(Integer reasonType) {
        ScrmRestrictionUser restrictionUser = new ScrmRestrictionUser();
        restrictionUser.setRestrictedReasonType(reasonType);
        return restrictionUser;
    }

    private ScrmRestrictionUserProcess createRestrictionUserProcess(String wxId, String type, String reason, String remark) {
        ScrmRestrictionUserProcess process = new ScrmRestrictionUserProcess();
        process.setWxId(wxId);
        process.setProcessType(type);
        process.setProcessReason(reason);
        process.setProcessRemark(remark);
        return process;
    }

    private RestrictionUserDTO createRestrictionUserDTO(int projectCount, int groupCount) {
        RestrictionUserDTO dto = new RestrictionUserDTO();
        dto.setProjectCount(projectCount);
        dto.setGroupCount(groupCount);
        return dto;
    }

    private ScrmPersonalWxGroupInfoEntity createMockGroupEntity() {
        ScrmPersonalWxGroupInfoEntity entity = new ScrmPersonalWxGroupInfoEntity();
        entity.setId(1L);
        entity.setGroupName("TestGroup");
        entity.setOwnerWxId("wxId1");
        entity.setMemberCount(10);
        entity.setStatus(1);
        entity.setChatRoomWxSerialNo("chatRoom1");
        entity.setAppId("testAppId");
        entity.setProjectId("projectId1");
        return entity;
    }

    private PagerList<ScrmPersonalWxGroupMemberInfoEntity> createMockGroupMemberInfoEntities() {
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setGroupId("chatRoom1");
        return PagerList.of(1L, Collections.singletonList(memberInfo));
    }

    /**
     * 测试群成员列表查询，当请求参数为空时
     */
    private PersonalWxMemberCountStat createMockMemberCountStat() {
        PersonalWxMemberCountStat stat = new PersonalWxMemberCountStat();
        stat.setGroupId(1L);
        stat.setMemberInGroupCount(10);
        stat.setRobotMemberCount(2);
        stat.setConsultantMemberCount(3);
        stat.setGroupSeedMemberCount(1);
        return stat;
    }

    private void setupCommonMocks(String appId) {
        when(bizIdentificationService.getAppIdByLiveId(anyString())).thenReturn(appId);
        when(webcastService.isSaasService(anyString())).thenReturn(false);
        when(scrmRestrictionIdentificationDomainService.whetherHaveRestrictedUserInGroup(anyString())).thenReturn(false);
        when(groupManageDomainService.queryGroupMemberByGroupSerialNo(anyString(), anyList(), any(), any())).thenReturn(createMockGroupMemberInfoEntities());
        when(webcastService.queryWebcastNameByIds(anyList())).thenReturn(new HashMap<>());
        List<PersonalWxMemberCountStat> memberCountStats = new ArrayList<>();
        memberCountStats.add(createMockMemberCountStat());
        when(groupManageDomainService.memberCountStatByGroup(any())).thenReturn(memberCountStats);
    }

    /**
     * 测试正常情况：所有参数都不为空，且查询结果也不为空
     */
    @Test
    public void testFillGroupMemberRestrictionInfo_NormalCase() throws Throwable {
        // arrange
        String projectId = "project1";
        // Create response
        GroupMemberInfoResponse response = createGroupMemberInfoResponse("wx1", true);
        List<GroupMemberInfoResponse> groupMemberInfoResponses = Collections.singletonList(response);
        // Create restriction user
        ScrmRestrictionUser restrictionUser = createRestrictionUser(RestrictionReasonTypeEnum.JOINING_GROUP_ABNORMAL.getCode());
        Map<String, ScrmRestrictionUser> restrictionUserMap = new HashMap<>();
        restrictionUserMap.put("wx1", restrictionUser);
        // Create process
        ScrmRestrictionUserProcess process = createRestrictionUserProcess("wx1", "type1", "reason1", "remark1");
        List<ScrmRestrictionUserProcess> processes = Collections.singletonList(process);
        // Create DTO
        RestrictionUserDTO dto = createRestrictionUserDTO(2, 5);
        Map<String, RestrictionUserDTO> restrictionUserDTOMap = new HashMap<>();
        restrictionUserDTOMap.put("wx1", dto);
        // Mock service calls
        when(restrictionGroupDomainService.queryRestrictionUserProcesses(null, projectId, Collections.singletonList("wx1"))).thenReturn(processes);
        when(restrictionGroupService.restrictionUserGroupProjectStat(Collections.singletonList("wx1"))).thenReturn(restrictionUserDTOMap);
        // act
        invokePrivateMethod(projectId, groupMemberInfoResponses, restrictionUserMap);
        // assert
        assertEquals("跨2场直播进群数量5个", response.getRestrictedReason());
        assertEquals(1, response.getProcessAction().size());
        assertEquals("type1", response.getProcessAction().get(0).getProcessType());
        assertEquals("reason1", response.getProcessAction().get(0).getProcessingReason());
        assertEquals("remark1", response.getProcessAction().get(0).getProcessingRemark());
    }

    /**
     * 测试边界情况：groupMemberInfoResponses 为空
     */
    @Test
    public void testFillGroupMemberRestrictionInfo_EmptyGroupMemberInfoResponses() throws Throwable {
        // arrange
        String projectId = "project1";
        List<GroupMemberInfoResponse> groupMemberInfoResponses = new ArrayList<>();
        Map<String, ScrmRestrictionUser> restrictionUserMap = new HashMap<>();
        // act
        invokePrivateMethod(projectId, groupMemberInfoResponses, restrictionUserMap);
        // assert
        assertTrue(groupMemberInfoResponses.isEmpty());
        verifyNoInteractions(restrictionGroupDomainService, restrictionGroupService);
    }

    /**
     * 测试边界情况：restrictionUserMap 为空
     */
    @Test
    public void testFillGroupMemberRestrictionInfo_EmptyRestrictionUserMap() throws Throwable {
        // arrange
        String projectId = "project1";
        GroupMemberInfoResponse response = createGroupMemberInfoResponse("wx1", true);
        List<GroupMemberInfoResponse> groupMemberInfoResponses = Collections.singletonList(response);
        Map<String, ScrmRestrictionUser> restrictionUserMap = new HashMap<>();
        // act
        invokePrivateMethod(projectId, groupMemberInfoResponses, restrictionUserMap);
        // assert
        assertNull(response.getRestrictedReason());
        assertNull(response.getProcessAction());
        verifyNoInteractions(restrictionGroupDomainService, restrictionGroupService);
    }

    /**
     * 测试异常情况：queryRestrictionUserProcesses 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testFillGroupMemberRestrictionInfo_QueryRestrictionUserProcessesThrowsException() throws Throwable {
        // arrange
        String projectId = "project1";
        GroupMemberInfoResponse response = createGroupMemberInfoResponse("wx1", true);
        List<GroupMemberInfoResponse> groupMemberInfoResponses = Collections.singletonList(response);
        ScrmRestrictionUser restrictionUser = createRestrictionUser(RestrictionReasonTypeEnum.JOINING_GROUP_ABNORMAL.getCode());
        Map<String, ScrmRestrictionUser> restrictionUserMap = new HashMap<>();
        restrictionUserMap.put("wx1", restrictionUser);
        when(restrictionGroupDomainService.queryRestrictionUserProcesses(null, projectId, Collections.singletonList("wx1"))).thenThrow(new RuntimeException("Test Exception"));
        // act
        try {
            invokePrivateMethod(projectId, groupMemberInfoResponses, restrictionUserMap);
        } catch (InvocationTargetException e) {
            throw e.getTargetException();
        }
    }

    /**
     * 测试异常情况：restrictionUserGroupProjectStat 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testFillGroupMemberRestrictionInfo_RestrictionUserGroupProjectStatThrowsException() throws Throwable {
        // arrange
        String projectId = "project1";
        GroupMemberInfoResponse response = createGroupMemberInfoResponse("wx1", true);
        List<GroupMemberInfoResponse> groupMemberInfoResponses = Collections.singletonList(response);
        ScrmRestrictionUser restrictionUser = createRestrictionUser(RestrictionReasonTypeEnum.JOINING_GROUP_ABNORMAL.getCode());
        Map<String, ScrmRestrictionUser> restrictionUserMap = new HashMap<>();
        restrictionUserMap.put("wx1", restrictionUser);
        List<ScrmRestrictionUserProcess> processes = Collections.singletonList(createRestrictionUserProcess("wx1", "type1", "reason1", "remark1"));
        when(restrictionGroupDomainService.queryRestrictionUserProcesses(null, projectId, Collections.singletonList("wx1"))).thenReturn(processes);
        when(restrictionGroupService.restrictionUserGroupProjectStat(Collections.singletonList("wx1"))).thenThrow(new RuntimeException("Test Exception"));
        // act
        try {
            invokePrivateMethod(projectId, groupMemberInfoResponses, restrictionUserMap);
        } catch (InvocationTargetException e) {
            throw e.getTargetException();
        }
    }

    /**
     * Test case: Empty webcastIds and groupParentId should return empty result
     */
    @Test
    public void testGroupList_EmptyInput_ReturnEmptyResult() throws Throwable {
        // arrange
        GroupListRequest request = new GroupListRequest();
        request.setWebcastIds(new ArrayList<>());
        request.setGroupParentId("");
        // act
        PageRemoteResponse<GroupListResponse> response = scrmPersonalWxGroupManageService.groupList(request);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
    }

    /**
     * Test case: Valid webcastId without restriction groups
     */
    @Test
    public void testGroupList_ValidWebcastId_NoRestrictionGroups() throws Throwable {
        // arrange
        String appId = "testAppId";
        GroupListRequest request = new GroupListRequest();
        request.setWebcastIds(Collections.singletonList("liveId1"));
        request.setPageNo(1);
        request.setPageSize(10);
        request.setAppId(appId);
        setupCommonMocks(appId);
        when(restrictionGroupDomainService.queryRestrictionGroupByProjectId(anyString())).thenReturn(new ArrayList<>());
        List<ScrmPersonalWxGroupInfoEntity> groupList = Collections.singletonList(createMockGroupEntity());
        PagerList<ScrmPersonalWxGroupInfoEntity> pagerList = PagerList.of(1L, groupList);
        when(groupManageDomainService.queryGroupListAndTotal(any(), any(), any())).thenReturn(pagerList);
        when(groupManageDomainService.queryUserListByWxIds(anyList())).thenReturn(new ArrayList<>());
        // act
        PageRemoteResponse<GroupListResponse> response = scrmPersonalWxGroupManageService.groupList(request);
        // assert
        assertNotNull(response);
        assertEquals(1, response.getData().size());
    }

    /**
     * Test case: Valid webcastId with restriction groups
     */
    @Test
    public void testGroupList_ValidWebcastId_WithRestrictionGroups() throws Throwable {
        // arrange
        String appId = "testAppId";
        GroupListRequest request = new GroupListRequest();
        request.setWebcastIds(Collections.singletonList("liveId1"));
        request.setPageNo(1);
        request.setPageSize(10);
        request.setAppId(appId);
        setupCommonMocks(appId);
        ScrmRestrictionGroup restrictionGroup = new ScrmRestrictionGroup();
        restrictionGroup.setChatRoomWxSerialNo("chatRoom1");
        List<ScrmRestrictionGroup> restrictionGroups = Collections.singletonList(restrictionGroup);
        when(restrictionGroupDomainService.queryRestrictionGroupByProjectId(anyString())).thenReturn(restrictionGroups);
        List<ScrmPersonalWxGroupInfoEntity> groupList = Collections.singletonList(createMockGroupEntity());
        PagerList<ScrmPersonalWxGroupInfoEntity> pagerList = PagerList.of(1L, groupList);
        when(groupManageDomainService.queryGroupListAndTotal(any(), any(), any())).thenReturn(pagerList);
        when(groupManageDomainService.queryUserListByWxIds(anyList())).thenReturn(new ArrayList<>());
        // act
        PageRemoteResponse<GroupListResponse> response = scrmPersonalWxGroupManageService.groupList(request);
        // assert
        assertNotNull(response);
        assertEquals(1, response.getData().size());
    }

    /**
     * Test case: Empty result with valid request
     */
    @Test
    public void testGroupList_ValidRequest_EmptyResult() throws Throwable {
        // arrange
        String appId = "testAppId";
        GroupListRequest request = new GroupListRequest();
        request.setWebcastIds(Collections.singletonList("liveId1"));
        request.setPageNo(1);
        request.setPageSize(10);
        request.setAppId(appId);
        setupCommonMocks(appId);
        when(restrictionGroupDomainService.queryRestrictionGroupByProjectId(anyString())).thenReturn(new ArrayList<>());
        when(groupManageDomainService.queryGroupListAndTotal(any(), any(), any())).thenReturn(PagerList.empty());
        // act
        PageRemoteResponse<GroupListResponse> response = scrmPersonalWxGroupManageService.groupList(request);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
    }

    /**
     * Test case: Request with user info mapping
     */
    @Test
    public void testGroupList_WithUserInfoMapping() throws Throwable {
        // arrange
        String appId = "testAppId";
        GroupListRequest request = new GroupListRequest();
        request.setWebcastIds(Collections.singletonList("liveId1"));
        request.setPageNo(1);
        request.setPageSize(10);
        request.setAppId(appId);
        setupCommonMocks(appId);
        when(restrictionGroupDomainService.queryRestrictionGroupByProjectId(anyString())).thenReturn(new ArrayList<>());
        ScrmPersonalWxGroupInfoEntity groupEntity = createMockGroupEntity();
        List<ScrmPersonalWxGroupInfoEntity> groupList = Collections.singletonList(groupEntity);
        PagerList<ScrmPersonalWxGroupInfoEntity> pagerList = PagerList.of(1L, groupList);
        when(groupManageDomainService.queryGroupListAndTotal(any(), any(), any())).thenReturn(pagerList);
        ScrmPersonalWxUserInfo userInfo = new ScrmPersonalWxUserInfo();
        userInfo.setWxId("wxId1");
        userInfo.setNickname("testUser");
        when(groupManageDomainService.queryUserListByWxIds(anyList())).thenReturn(Collections.singletonList(userInfo));
        // act
        PageRemoteResponse<GroupListResponse> response = scrmPersonalWxGroupManageService.groupList(request);
        // assert
        assertNotNull(response);
        assertEquals(1, response.getData().size());
        GroupListResponse groupResponse = response.getData().get(0);
        assertNotNull(groupResponse);
    }
}
