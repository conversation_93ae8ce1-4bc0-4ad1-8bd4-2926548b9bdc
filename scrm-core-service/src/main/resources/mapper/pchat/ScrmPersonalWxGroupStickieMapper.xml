<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupStickieMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupStickie">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="robot_id" jdbcType="BIGINT" property="robotId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="stickie" jdbcType="CHAR" property="stickie" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, robot_id, group_id, remark, add_time, update_time, app_id, stickie
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupStickieExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_GroupStickie
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_GroupStickie
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_GroupStickie
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupStickieExample">
    delete from Scrm_PersonalWx_GroupStickie
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupStickie">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_GroupStickie (robot_id, group_id, remark, 
      add_time, update_time, app_id, 
      stickie)
    values (#{robotId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{appId,jdbcType=VARCHAR}, 
      #{stickie,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupStickie">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_GroupStickie
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="robotId != null">
        robot_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="stickie != null">
        stickie,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="robotId != null">
        #{robotId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="stickie != null">
        #{stickie,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupStickieExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_GroupStickie
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_GroupStickie
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.robotId != null">
        robot_id = #{record.robotId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.stickie != null">
        stickie = #{record.stickie,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_GroupStickie
    set id = #{record.id,jdbcType=BIGINT},
      robot_id = #{record.robotId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      app_id = #{record.appId,jdbcType=VARCHAR},
      stickie = #{record.stickie,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupStickie">
    update Scrm_PersonalWx_GroupStickie
    <set>
      <if test="robotId != null">
        robot_id = #{robotId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="stickie != null">
        stickie = #{stickie,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupStickie">
    update Scrm_PersonalWx_GroupStickie
    set robot_id = #{robotId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      app_id = #{appId,jdbcType=VARCHAR},
      stickie = #{stickie,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_GroupStickie
    (robot_id, group_id, remark, add_time, update_time, app_id, stickie)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.robotId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.stickie,jdbcType=CHAR})
    </foreach>
  </insert>
</mapper>