package com.sankuai.scrm.core.service.aigc.service.dal.entity;

import lombok.*;

import java.util.Date;

/**
 *
 * 表名: scrm_autoreply_appid_agent
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAutoreplyAppidAgentDO {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: app_id
     * 说明: 主体appId
     */
    private String appId;

    /**
     * 字段: xiaomei_app_id
     * 说明: 小美租户appId
     */
    private String xiaomeiAppId;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}