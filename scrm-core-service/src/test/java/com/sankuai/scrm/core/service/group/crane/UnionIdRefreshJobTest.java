package com.sankuai.scrm.core.service.group.crane;

import cn.hutool.core.bean.BeanUtil;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxGroupAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxGroupMemberDetail;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UnionIdRefreshJobTest {

    @InjectMocks
    private UnionIdRefreshJob unionIdRefreshJob;

    @Mock
    private CorpAppConfigRepository appConfigRepository;
    @Mock
    private GroupMemberDomainService groupMemberDomainService;
    @Mock
    private CorpWxGroupAcl corpWxGroupAcl;


    /**
     * 测试 configs 为空的情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithEmptyConfigs() {
        // arrange
        when(appConfigRepository.getConfigs()).thenReturn(Collections.emptyList());

        // act
        unionIdRefreshJob.fillGroupMemberUnionId();

        // assert
        verify(appConfigRepository, times(1)).getConfigs();
        verifyNoMoreInteractions(appConfigRepository);
    }

    /**
     * 测试 configs 非空，但没有 corpId 的情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithConfigsNoCorpId() {
        // arrange
        CorpAppConfig configWithoutCorpId = new CorpAppConfig();
        when(appConfigRepository.getConfigs()).thenReturn(Collections.singletonList(configWithoutCorpId));

        // act
        unionIdRefreshJob.fillGroupMemberUnionId();

        // assert
        verify(appConfigRepository, times(1)).getConfigs();
        // 由于没有 corpId，理论上不会有进一步的行为，但是这个测试用例主要是为了保证代码的完整性和未来可能的变化
    }

    /**
     * 测试 configs 非空，且包含多个 corpId 的情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithMultipleCorpIds() {
        // arrange
        CorpAppConfig config1 = new CorpAppConfig();
        config1.setCorpId("ww9549b0976e58e955");
        CorpAppConfig config2 = new CorpAppConfig();
        config2.setCorpId("ww6155e0a24f071a89");
        List<CorpAppConfig> configs = Arrays.asList(config1, config2);
        when(appConfigRepository.getConfigs()).thenReturn(configs);

        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getInt(any(), any(), any())).thenReturn(0);
            List<MemberInfoEntity> memberInfoEntities = new ArrayList<>();
            MemberInfoEntity memberInfoEntity = new MemberInfoEntity();
            memberInfoEntity.setCorpId("ww9549b0976e58e955");
//            memberInfoEntity.setUnionId("unionId");
            memberInfoEntity.setId(1);
            memberInfoEntity.setGroupId("groupId");
            memberInfoEntity.setGroupMemberId("userId");
            memberInfoEntities.add(memberInfoEntity);
            when(groupMemberDomainService.queryUnionIdIsNullRecords(any(), any(), any(), any())).thenReturn(memberInfoEntities, Collections.emptyList());
            WxGroupDetail wxGroupDetail = new WxGroupDetail();
            wxGroupDetail.setGroupId("groupId");
            wxGroupDetail.setOwner("owner");
            List<WxGroupMemberDetail> wxMemberList = new ArrayList<>();
            WxGroupMemberDetail wxGroupMemberDetail = new WxGroupMemberDetail();
            wxGroupMemberDetail.setUnionId("unionId");
            wxGroupMemberDetail.setGroupMemberId("userId");
            wxMemberList.add(wxGroupMemberDetail);
            wxGroupDetail.setMemberList(wxMemberList);
            when(corpWxGroupAcl.getWxGroupDetail(anyString(), anyString(), anyBoolean())).thenReturn(wxGroupDetail);

            MemberInfoEntity copyMember = BeanUtil.copyProperties(memberInfoEntity, MemberInfoEntity.class);
            copyMember.setUnionId(null);
            when(groupMemberDomainService.queryMembersByGroupId(any(), any())).thenReturn(Collections.singletonList(copyMember));
            when(groupMemberDomainService.updateGroupMemberUnionId(any())).thenReturn(true);
            // act
            unionIdRefreshJob.fillGroupMemberUnionId();

            // assert
            verify(groupMemberDomainService, times(1)).updateGroupMemberUnionId(any());
        }
    }


    /**
     * 测试空的groupMemberIdWithUnionIdMap情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithEmptyMap() {
        // arrange
        Map<String, String> groupMemberIdWithUnionIdMap = new HashMap<>();

        // act
        unionIdRefreshJob.fillGroupMemberUnionId(groupMemberIdWithUnionIdMap);

        // assert
        verify(groupMemberDomainService, never()).queryUnionIdIsNullRecords(anyList());
    }

    /**
     * 测试groupMemberIdWithUnionIdMap不为空，但查询结果为空的情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithNonEmptyMapButNoResults() {
        // arrange
        Map<String, String> groupMemberIdWithUnionIdMap = new HashMap<>();
        groupMemberIdWithUnionIdMap.put("member1", "unionId1");
        when(groupMemberDomainService.queryUnionIdIsNullRecords(anyList())).thenReturn(Arrays.asList());

        // act
        unionIdRefreshJob.fillGroupMemberUnionId(groupMemberIdWithUnionIdMap);

        // assert
        verify(groupMemberDomainService, times(1)).queryUnionIdIsNullRecords(anyList());
        verify(groupMemberDomainService, never()).updateGroupMemberUnionId(any(MemberInfoEntity.class));
    }

    /**
     * 测试groupMemberIdWithUnionIdMap不为空，且查询到结果的情况
     */
    @Test
    public void testFillGroupMemberUnionIdWithResults() {
        // arrange
        Map<String, String> groupMemberIdWithUnionIdMap = new HashMap<>();
        groupMemberIdWithUnionIdMap.put("member1", "unionId1");
        List<MemberInfoEntity> memberInfoEntities = Arrays.asList(new MemberInfoEntity(1, null, 0, "member1", "name1", "nickName1", "avatar1", (byte) 1, (byte) 1, "state1", null, "groupId1", "corpId1", 1L, "corpName1", false, null, null));
        when(groupMemberDomainService.queryUnionIdIsNullRecords(anyList())).thenReturn(memberInfoEntities);

        // act
        unionIdRefreshJob.fillGroupMemberUnionId(groupMemberIdWithUnionIdMap);

        // assert
        verify(groupMemberDomainService, times(1)).queryUnionIdIsNullRecords(anyList());
        verify(groupMemberDomainService, times(1)).updateGroupMemberUnionId(any(MemberInfoEntity.class));
    }

}