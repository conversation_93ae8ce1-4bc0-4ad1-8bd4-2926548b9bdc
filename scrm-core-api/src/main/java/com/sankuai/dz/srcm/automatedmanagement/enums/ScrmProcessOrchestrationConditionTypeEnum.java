package com.sankuai.dz.srcm.automatedmanagement.enums;

/**
 * 流程编排条件类型
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/17
 */
public enum ScrmProcessOrchestrationConditionTypeEnum {

    UNKNOWN("unknown", 0, "未知"),
    CONDITION_BRANCH("conditionType", 1, "条件分支"),
    DELAY_BRANCH("delayType", 2, "延时分支"),
    SPLIT_BRANCH("splitType", 3, "分流分支"),

    ;

    private String code;

    private int value;

    private String description;

    ScrmProcessOrchestrationConditionTypeEnum(String code, int value, String description) {
        this.code = code;
        this.value = value;
        this.description = description;
    }

    public static ScrmProcessOrchestrationConditionTypeEnum getTypeByValue(int i) {
        for (ScrmProcessOrchestrationConditionTypeEnum productTypeEnum : ScrmProcessOrchestrationConditionTypeEnum.values()) {
            if (productTypeEnum.getValue() == i) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ScrmProcessOrchestrationConditionTypeEnum getTypeByCode(String code) {
        for (ScrmProcessOrchestrationConditionTypeEnum productTypeEnum : ScrmProcessOrchestrationConditionTypeEnum.values()) {
            if (productTypeEnum.getCode().equalsIgnoreCase(code)|| productTypeEnum.name().equalsIgnoreCase(code)) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
