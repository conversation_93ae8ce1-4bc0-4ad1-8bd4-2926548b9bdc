<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.order.dal.mapper.OrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.order.dal.entity.OrderInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unified_order_id" jdbcType="VARCHAR" property="unifiedOrderId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="mt_user_id" jdbcType="BIGINT" property="mtUserId" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="buy_success_time" jdbcType="TIMESTAMP" property="buySuccessTime" />
    <result column="has_refund" jdbcType="BIT" property="hasRefund" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="mt_city_id" jdbcType="INTEGER" property="mtCityId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unified_order_id, sku_id, mt_user_id, mobile_no, buy_success_time, has_refund, 
    refund_time, paid_amount, mt_city_id, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.order.dal.example.OrderInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.order.dal.example.OrderInfoExample">
    delete from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.order.dal.entity.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info (unified_order_id, sku_id, mt_user_id, 
      mobile_no, buy_success_time, has_refund, 
      refund_time, paid_amount, mt_city_id, 
      add_time, update_time)
    values (#{unifiedOrderId,jdbcType=VARCHAR}, #{skuId,jdbcType=VARCHAR}, #{mtUserId,jdbcType=BIGINT}, 
      #{mobileNo,jdbcType=VARCHAR}, #{buySuccessTime,jdbcType=TIMESTAMP}, #{hasRefund,jdbcType=BIT}, 
      #{refundTime,jdbcType=TIMESTAMP}, #{paidAmount,jdbcType=VARCHAR}, #{mtCityId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.order.dal.entity.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        unified_order_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="mtUserId != null">
        mt_user_id,
      </if>
      <if test="mobileNo != null">
        mobile_no,
      </if>
      <if test="buySuccessTime != null">
        buy_success_time,
      </if>
      <if test="hasRefund != null">
        has_refund,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="mtCityId != null">
        mt_city_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="mtUserId != null">
        #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="mobileNo != null">
        #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="buySuccessTime != null">
        #{buySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasRefund != null">
        #{hasRefund,jdbcType=BIT},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="mtCityId != null">
        #{mtCityId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.order.dal.example.OrderInfoExample" resultType="java.lang.Long">
    select count(*) from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unifiedOrderId != null">
        unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.mtUserId != null">
        mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      </if>
      <if test="record.mobileNo != null">
        mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="record.buySuccessTime != null">
        buy_success_time = #{record.buySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hasRefund != null">
        has_refund = #{record.hasRefund,jdbcType=BIT},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.mtCityId != null">
        mt_city_id = #{record.mtCityId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_info
    set id = #{record.id,jdbcType=BIGINT},
      unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      sku_id = #{record.skuId,jdbcType=VARCHAR},
      mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      buy_success_time = #{record.buySuccessTime,jdbcType=TIMESTAMP},
      has_refund = #{record.hasRefund,jdbcType=BIT},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      mt_city_id = #{record.mtCityId,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.order.dal.entity.OrderInfo">
    update order_info
    <set>
      <if test="unifiedOrderId != null">
        unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="mtUserId != null">
        mt_user_id = #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="mobileNo != null">
        mobile_no = #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="buySuccessTime != null">
        buy_success_time = #{buySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasRefund != null">
        has_refund = #{hasRefund,jdbcType=BIT},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="mtCityId != null">
        mt_city_id = #{mtCityId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.order.dal.entity.OrderInfo">
    update order_info
    set unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      mt_user_id = #{mtUserId,jdbcType=BIGINT},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      buy_success_time = #{buySuccessTime,jdbcType=TIMESTAMP},
      has_refund = #{hasRefund,jdbcType=BIT},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      mt_city_id = #{mtCityId,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into order_info
    (unified_order_id, sku_id, mt_user_id, mobile_no, buy_success_time, has_refund, refund_time, 
      paid_amount, mt_city_id, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.unifiedOrderId,jdbcType=VARCHAR}, #{item.skuId,jdbcType=VARCHAR}, #{item.mtUserId,jdbcType=BIGINT}, 
        #{item.mobileNo,jdbcType=VARCHAR}, #{item.buySuccessTime,jdbcType=TIMESTAMP}, #{item.hasRefund,jdbcType=BIT}, 
        #{item.refundTime,jdbcType=TIMESTAMP}, #{item.paidAmount,jdbcType=VARCHAR}, #{item.mtCityId,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>