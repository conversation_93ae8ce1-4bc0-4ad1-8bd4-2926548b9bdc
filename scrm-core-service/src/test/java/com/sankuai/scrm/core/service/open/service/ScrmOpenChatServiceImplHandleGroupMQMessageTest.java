package com.sankuai.scrm.core.service.open.service;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.chat.mq.msg.GroupChatMsg;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenChattingMessage;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenGroupBriefed;
import com.sankuai.scrm.core.service.chat.mq.msg.OpenGroupMemberBriefed;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.open.domain.ScrmOpenChatDomainService;
import com.sankuai.scrm.core.service.open.producer.ScrmOpenEntGroupMessageProducer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmOpenChatServiceImplHandleGroupMQMessageTest {

    @InjectMocks
    private ScrmOpenChatDomainService scrmOpenChatDomainService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private ScrmOpenEntGroupMessageProducer scrmOpenEntGroupMessageProducer;

    private GroupChatMsg groupChatMsg;

    @BeforeEach
    void setUp() {
        groupChatMsg = new GroupChatMsg();
        groupChatMsg.setCorpId("testCorpId");
        OpenGroupMemberBriefed member = new OpenGroupMemberBriefed();
        member.setUserId("testUserId");
        groupChatMsg.setMember(member);
        OpenGroupBriefed group = new OpenGroupBriefed();
        group.setGroupId("testGroupId");
        groupChatMsg.setGroup(group);
        OpenChattingMessage message = new OpenChattingMessage();
        message.setMessageType("TEXT");
        message.setTextContent("Test message content");
        groupChatMsg.setMessage(message);
        groupChatMsg.setMessageTime(System.currentTimeMillis() / 1000);
    }

    /**
     * Test case for null message input
     */
    @Test
    public void testHandleGroupMQMessage_NullMessage() throws Throwable {
        // arrange
        // act
        scrmOpenChatDomainService.handleGroupMQMessage(null);
        // assert
        verifyNoInteractions(appConfigRepository, dsAssistantAcl, scrmOpenEntGroupMessageProducer);
    }

    /**
     * Test case for blank appId
     */
    @Test
    public void testHandleGroupMQMessage_BlankAppId() throws Throwable {
        // arrange
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn("");
        // act
        scrmOpenChatDomainService.handleGroupMQMessage(groupChatMsg);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("testCorpId");
        verifyNoMoreInteractions(appConfigRepository, dsAssistantAcl, scrmOpenEntGroupMessageProducer);
    }

    /**
     * Test case for appId not in configuration list
     */
    @Test
    public void testHandleGroupMQMessage_AppIdNotInConfigList() throws Throwable {
        // arrange
        when(appConfigRepository.getAppIdByCorpId(eq("testCorpId"))).thenReturn("testAppId");
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("testApp");
            lionMock.when(() -> Lion.getList(eq("testApp"), eq("scrm_open_need_handle_message"), eq(String.class), anyList())).thenReturn(Arrays.asList("otherAppId"));
            // act
            scrmOpenChatDomainService.handleGroupMQMessage(groupChatMsg);
        }
        // assert
        verify(appConfigRepository).getAppIdByCorpId(eq("testCorpId"));
        verifyNoMoreInteractions(appConfigRepository, dsAssistantAcl, scrmOpenEntGroupMessageProducer);
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testHandleGroupMQMessage_ExceptionHandling() throws Throwable {
        // arrange
        when(appConfigRepository.getAppIdByCorpId(eq("testCorpId"))).thenThrow(new RuntimeException("Test exception"));
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            scrmOpenChatDomainService.handleGroupMQMessage(groupChatMsg);
            // assert
            catMock.verify(() -> Cat.logError(eq("handleGroupMQMessage.error"), any(RuntimeException.class)));
        }
    }
}
