package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupInfoEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxGroupInfoEntityMapper extends MybatisBLOBsMapper<ScrmPersonalWxGroupInfoEntity, ScrmPersonalWxGroupInfoEntityExample, Long> {
    int batchInsert(@Param("list") List<com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntityWithBLOBs> list);
}