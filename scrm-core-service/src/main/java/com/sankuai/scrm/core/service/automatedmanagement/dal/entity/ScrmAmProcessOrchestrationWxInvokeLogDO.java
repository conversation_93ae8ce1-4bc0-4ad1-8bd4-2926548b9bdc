package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import lombok.*;

import java.util.Date;

/**
 *
 *   表名: scrm_a_m_process_orchestration_wx_invoke_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmProcessOrchestrationWxInvokeLogDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: process_orchestration_node_id
     *   说明: 对应节点id
     */
    private Long processOrchestrationNodeId;

    /**
     *   字段: executor_id
     *   说明: 执行者Id
     */
    private String executorId;

    /**
     *   字段: jobid
     *   说明: wx任务Id, 根据状态类型不同语意不同
     */
    private String jobid;

    /**
     *   字段: type
     *   说明: moment 朋友圈  message 消息
     */
    private String type;

    /**
     *   字段: status
     *   说明: 任务状态（新建任务/新建失败/未发布/已发布)
     */
    private Byte status;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}