package com.sankuai.scrm.core.service.group.dal.babymapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.GroupInfoEntityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GroupInfoEntityMapper extends MybatisBLOBsMapper<GroupInfoEntity, GroupInfoEntityExample, Integer> {
    int batchInsert(@Param("list") List<GroupInfoEntity> list);
}