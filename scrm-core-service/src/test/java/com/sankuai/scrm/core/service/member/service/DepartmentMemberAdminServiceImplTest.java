package com.sankuai.scrm.core.service.member.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.member.dto.DepartmentUserListResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.StoreCacheAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxDepartmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxStaffAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxDepartmentInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxDepartmentStaffDetail;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.member.dao.mapper.DepartmentMemberInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/10/8
 */
@ExtendWith(MockitoExtension.class)
public class DepartmentMemberAdminServiceImplTest{

    @Mock(lenient = true)
    private CorpWxDepartmentAcl corpWxDepartmentAcl;

    @Mock(lenient = true)
    private CorpWxStaffAcl corpWxStaffAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private DepartmentMemberInfoMapper departmentMemberInfoMapper;

    @InjectMocks
    private DepartmentMemberAdminServiceImpl departmentMemberAdminService;

    @Mock(lenient = true)
    private StoreCacheAcl storeCacheAcl;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试appId为空的情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdAppIdEmpty() {
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("", "userIds");
        assertEquals("参数异常，appId为空", response.getMsg());
    }

    /**
     * 测试userIds为空的情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdUserIdsEmpty() {
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "");
        assertEquals("参数异常，userIds为空", response.getMsg());
    }

    /**
     * 测试appId不存在的情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdAppIdNotExist() {
        when(appConfigRepository.getConfigByAppId(any())).thenReturn(null);
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "userIds");
        assertEquals("参数异常，appId不存在", response.getMsg());
    }

    /**
     * 测试部门列表为空的情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdDepartmentListEmpty() {
        CorpAppConfig config = new CorpAppConfig();
        when(appConfigRepository.getConfigByAppId(any())).thenReturn(config);
        when(corpWxDepartmentAcl.getDepartmentList(any())).thenReturn(new ArrayList<>());
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "userIds");
        assertEquals("查询部分数据为空", response.getMsg());
    }

    /**
     * 测试userIdMap为空的情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdUserIdMapEmpty() {
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId("corpId");
        List<WxDepartmentInfo> departmentList = Arrays.asList(new WxDepartmentInfo());
        when(appConfigRepository.getConfigByAppId(any())).thenReturn(config);
        when(corpWxDepartmentAcl.getDepartmentList(any())).thenReturn(departmentList);
        when(corpWxDepartmentAcl.getDepartmentMemberList(any(), anyInt())).thenReturn(new ArrayList<>());
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "nonExistingUserId");
        assertNull(response.getData().getMemberList());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdNormal() {
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId("corpId");
        List<WxDepartmentInfo> departmentList = Arrays.asList(new WxDepartmentInfo());
        WxDepartmentStaffDetail staffDetail = new WxDepartmentStaffDetail();
        staffDetail.setUserId("userId");
        List<WxDepartmentStaffDetail> staffDetailList = Arrays.asList(staffDetail);

        when(appConfigRepository.getConfigByAppId(any())).thenReturn(config);
        when(corpWxDepartmentAcl.getDepartmentList(any())).thenReturn(departmentList);
        when(corpWxDepartmentAcl.getDepartmentMemberList(any(), anyInt())).thenReturn(staffDetailList);

        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "userId");
        assertNotNull(response.getData());
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testBatchGetDepartmentUserListByUserIdException() {
        when(appConfigRepository.getConfigByAppId(any())).thenThrow(new RuntimeException());
        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "userIds");
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testBatchGetDepartmentUserListByUserId(){
        List<WxDepartmentInfo> departmentList = new ArrayList<>();
        WxDepartmentInfo wxDepartmentInfo = new WxDepartmentInfo();
        wxDepartmentInfo.setId(1);
        wxDepartmentInfo.setName("name");
        wxDepartmentInfo.setOrder(1);
        wxDepartmentInfo.setNameEn("nameEn");
        wxDepartmentInfo.setParentId(0);
        wxDepartmentInfo.setDepartmentLeaderList(new ArrayList<>());
        departmentList.add(wxDepartmentInfo);
        when(storeCacheAcl.getFromCache(any(),any())).thenReturn(departmentList);
        WxDepartmentStaffDetail staffDetail = new WxDepartmentStaffDetail();
        staffDetail.setUserId("userId");
        staffDetail.setName("name");
        List<WxDepartmentStaffDetail> staffDetailList = Arrays.asList(staffDetail);
        when(corpWxDepartmentAcl.getDepartmentMemberList(any(),anyInt())).thenReturn(staffDetailList);

        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId("corpId");
        config.setAppId("appId");
        when(appConfigRepository.getConfigByAppId(any())).thenReturn(config);

        RemoteResponse<DepartmentUserListResponse> response = departmentMemberAdminService.batchGetDepartmentUserListByUserId("appId", "userIds");
        assertNotNull(response.getData());
    }

    @Test
    void getId2Size_WhenCacheMiss() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // given
        String corpId = "testCorpId";
        List<Integer> departmentIds = Arrays.asList(1, 2);

        when(storeCacheAcl.getFromCache("wxDepartmentId2sizeCategory", corpId))
                .thenReturn(null);

        List<WxDepartmentStaffDetail> dept1Members = Arrays.asList(
                new WxDepartmentStaffDetail(),
                new WxDepartmentStaffDetail()
        );
        List<WxDepartmentStaffDetail> dept2Members = Arrays.asList(
                new WxDepartmentStaffDetail(),
                new WxDepartmentStaffDetail(),
                new WxDepartmentStaffDetail()
        );

        when(corpWxDepartmentAcl.getDepartmentMemberList(corpId, 1))
                .thenReturn(dept1Members);
        when(corpWxDepartmentAcl.getDepartmentMemberList(corpId, 2))
                .thenReturn(dept2Members);

        // when
        Method getId2Size = departmentMemberAdminService.getClass().getDeclaredMethod("getId2Size", String.class, List.class);
        getId2Size.setAccessible(true);
        Map<Integer, Integer> result = (Map<Integer, Integer>) getId2Size.invoke(departmentMemberAdminService, corpId, departmentIds);

        // then
        assertEquals(2, result.size());
    }
}