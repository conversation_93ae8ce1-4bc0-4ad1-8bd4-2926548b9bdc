package com.sankuai.scrm.core.service.pchat.service.robot;

import com.sankuai.scrm.core.service.pchat.service.superadmin.SuperAdminSsoService;
import com.sankuai.scrm.core.service.pchat.service.superadmin.SSOUseInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxRobotLoginDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxAccountClusterHandleLogDomainService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.pchat.enums.robot.RobotValidEnum;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dz.srcm.pchat.request.robot.EditRobotValidStatusRequest;
import org.mockito.MockedStatic;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRobotLoginServiceImplEditRobotValidStatusEnumTest {

    @InjectMocks
    private ScrmRobotLoginServiceImpl scrmRobotLoginService;

    @Mock
    private ScrmPersonalWxRobotLoginDomainService loginDomainService;

    @Mock
    private ScrmPersonalWxAccountClusterHandleLogDomainService handleLogDomainService;

    private EditRobotValidStatusRequest request;

    private SSOUseInfo loginUser;

    @Before
    public void setUp() {
        request = new EditRobotValidStatusRequest();
        request.setRobotSerialNo("robot123");
        request.setValid(RobotValidEnum.VALID.getCode());
        loginUser = new SSOUseInfo();
        loginUser.setLogin("user123");
    }

    @Test
    public void testEditRobotValidStatusNormal() throws Throwable {
        try (MockedStatic<SuperAdminSsoService> mockedStatic = mockStatic(SuperAdminSsoService.class)) {
            mockedStatic.when(SuperAdminSsoService::getLoginUser).thenReturn(loginUser);
            when(loginDomainService.editRobotValidStatus(anyString(), anyInt())).thenReturn(true);
            RemoteResponse<Boolean> response = scrmRobotLoginService.editRobotValidStatus(request);
            assertTrue(response.getData());
            assertEquals("success", response.getMsg());
        }
    }

    @Test
    public void testEditRobotValidStatusEditFailed() throws Throwable {
        try (MockedStatic<SuperAdminSsoService> mockedStatic = mockStatic(SuperAdminSsoService.class)) {
            mockedStatic.when(SuperAdminSsoService::getLoginUser).thenReturn(loginUser);
            when(loginDomainService.editRobotValidStatus(anyString(), anyInt())).thenReturn(false);
            RemoteResponse<Boolean> response = scrmRobotLoginService.editRobotValidStatus(request);
            assertFalse(response.getData());
            // Change the expected message to match the actual behavior
            assertEquals("success", response.getMsg());
        }
    }
}
