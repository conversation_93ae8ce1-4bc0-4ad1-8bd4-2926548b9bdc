package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxAccountClusterHandleLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxAccountClusterHandleLogExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ScrmPersonalWxAccountClusterHandleLogMapper extends MybatisBaseMapper<ScrmPersonalWxAccountClusterHandleLog, ScrmPersonalWxAccountClusterHandleLogExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxAccountClusterHandleLog> list);
}