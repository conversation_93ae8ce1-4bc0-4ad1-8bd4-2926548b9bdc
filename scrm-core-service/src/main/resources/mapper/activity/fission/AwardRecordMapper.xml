<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.activity.fission.dal.mapper.AwardRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="phone" jdbcType="CHAR" property="phone" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="assistant_account" jdbcType="VARCHAR" property="assistantAccount" />
    <result column="add_status" jdbcType="BIT" property="addStatus" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="stage" jdbcType="INTEGER" property="stage" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delivery_status" jdbcType="CHAR" property="deliveryStatus" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="prize_id" jdbcType="BIGINT" property="prizeId" />
    <result column="notify_status" jdbcType="CHAR" property="notifyStatus" />
    <result column="notify_time" jdbcType="TIMESTAMP" property="notifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, union_id, phone, receiver, address, assistant_account, add_status, activity_id, 
    stage, add_time, update_time, delivery_status, delivery_time, prize_id, notify_status, 
    notify_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.example.AwardRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from award_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from award_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from award_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.example.AwardRecordExample">
    delete from award_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into award_record (union_id, phone, receiver, 
      address, assistant_account, add_status, 
      activity_id, stage, add_time, 
      update_time, delivery_status, delivery_time, 
      prize_id, notify_status, notify_time
      )
    values (#{unionId,jdbcType=VARCHAR}, #{phone,jdbcType=CHAR}, #{receiver,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{assistantAccount,jdbcType=VARCHAR}, #{addStatus,jdbcType=BIT}, 
      #{activityId,jdbcType=BIGINT}, #{stage,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deliveryStatus,jdbcType=CHAR}, #{deliveryTime,jdbcType=TIMESTAMP}, 
      #{prizeId,jdbcType=BIGINT}, #{notifyStatus,jdbcType=CHAR}, #{notifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into award_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unionId != null">
        union_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="assistantAccount != null">
        assistant_account,
      </if>
      <if test="addStatus != null">
        add_status,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="prizeId != null">
        prize_id,
      </if>
      <if test="notifyStatus != null">
        notify_status,
      </if>
      <if test="notifyTime != null">
        notify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=CHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="assistantAccount != null">
        #{assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="addStatus != null">
        #{addStatus,jdbcType=BIT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=CHAR},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prizeId != null">
        #{prizeId,jdbcType=BIGINT},
      </if>
      <if test="notifyStatus != null">
        #{notifyStatus,jdbcType=CHAR},
      </if>
      <if test="notifyTime != null">
        #{notifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.example.AwardRecordExample" resultType="java.lang.Long">
    select count(*) from award_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update award_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=CHAR},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.assistantAccount != null">
        assistant_account = #{record.assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.addStatus != null">
        add_status = #{record.addStatus,jdbcType=BIT},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.stage != null">
        stage = #{record.stage,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=CHAR},
      </if>
      <if test="record.deliveryTime != null">
        delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.prizeId != null">
        prize_id = #{record.prizeId,jdbcType=BIGINT},
      </if>
      <if test="record.notifyStatus != null">
        notify_status = #{record.notifyStatus,jdbcType=CHAR},
      </if>
      <if test="record.notifyTime != null">
        notify_time = #{record.notifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update award_record
    set id = #{record.id,jdbcType=BIGINT},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=CHAR},
      receiver = #{record.receiver,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      assistant_account = #{record.assistantAccount,jdbcType=VARCHAR},
      add_status = #{record.addStatus,jdbcType=BIT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      stage = #{record.stage,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delivery_status = #{record.deliveryStatus,jdbcType=CHAR},
      delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      prize_id = #{record.prizeId,jdbcType=BIGINT},
      notify_status = #{record.notifyStatus,jdbcType=CHAR},
      notify_time = #{record.notifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord">
    update award_record
    <set>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=CHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="assistantAccount != null">
        assistant_account = #{assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="addStatus != null">
        add_status = #{addStatus,jdbcType=BIT},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="stage != null">
        stage = #{stage,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=CHAR},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="prizeId != null">
        prize_id = #{prizeId,jdbcType=BIGINT},
      </if>
      <if test="notifyStatus != null">
        notify_status = #{notifyStatus,jdbcType=CHAR},
      </if>
      <if test="notifyTime != null">
        notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.activity.fission.dal.entity.AwardRecord">
    update award_record
    set union_id = #{unionId,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=CHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      assistant_account = #{assistantAccount,jdbcType=VARCHAR},
      add_status = #{addStatus,jdbcType=BIT},
      activity_id = #{activityId,jdbcType=BIGINT},
      stage = #{stage,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delivery_status = #{deliveryStatus,jdbcType=CHAR},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      prize_id = #{prizeId,jdbcType=BIGINT},
      notify_status = #{notifyStatus,jdbcType=CHAR},
      notify_time = #{notifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into award_record
    (union_id, phone, receiver, address, assistant_account, add_status, activity_id, 
      stage, add_time, update_time, delivery_status, delivery_time, prize_id, notify_status, 
      notify_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.unionId,jdbcType=VARCHAR}, #{item.phone,jdbcType=CHAR}, #{item.receiver,jdbcType=VARCHAR}, 
        #{item.address,jdbcType=VARCHAR}, #{item.assistantAccount,jdbcType=VARCHAR}, #{item.addStatus,jdbcType=BIT}, 
        #{item.activityId,jdbcType=BIGINT}, #{item.stage,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deliveryStatus,jdbcType=CHAR}, #{item.deliveryTime,jdbcType=TIMESTAMP}, 
        #{item.prizeId,jdbcType=BIGINT}, #{item.notifyStatus,jdbcType=CHAR}, #{item.notifyTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>