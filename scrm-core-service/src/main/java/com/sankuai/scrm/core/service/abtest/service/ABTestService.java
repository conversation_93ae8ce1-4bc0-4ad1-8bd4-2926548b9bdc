package com.sankuai.scrm.core.service.abtest.service;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.enums.SplitType;
import com.sankuai.scrm.core.service.abtest.config.ABTestConfig;
import com.sankuai.scrm.core.service.abtest.config.dto.RangeStrategyItem;
import com.sankuai.scrm.core.service.abtest.config.dto.PercentageStrategyItem;
import com.sankuai.scrm.core.service.abtest.strategy.ABTestStrategy;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.time.LocalDateTime;

import java.time.temporal.ChronoUnit;
import java.time.Duration;
import org.springframework.stereotype.Service;

/**
 * AB测试服务类
 * 负责处理AB测试的核心逻辑，包括用户分流、策略选择、随机数生成等
 * 支持多种分流方式：区间分流、百分比分流、不关心用户ID分流
 * 
 * @param <P> 策略执行参数类型
 * <AUTHOR>
 * @since 1.0.0
 */
//@MdpPigeonServer(url = "com.sankuai.scrm.core.service.abtest.service.ABTestService")
@Slf4j
@Service
public class ABTestService<P>{

    @Autowired
    private ABTestConfig abTestConfig;

    @Autowired
    private List<ABTestStrategy<P>> strategies;

    @Autowired
    private RedisStoreClient redisClient;

    /**
     * 区间分流随机数缓存分类
     * 用于Redis中存储区间分流的用户随机数
     */
    private static final String ABTEST_RANGE_RANDOM_CATEGORY = "abtest_user_range_random";

    /**
     * 百分比分流随机数缓存分类
     * 用于Redis中存储百分比分流的用户随机数
     */
    private static final String ABTEST_PERCENTAGE_RANDOM_CATEGORY = "abtest_user_percentage_random";

    /**
     * 默认策略名称
     * 当找不到匹配策略时使用的兜底策略
     */
    private static final String DO_NOTHING_STRATEGY_NAME = "DoNothingStrategy";

    /**
     * 处理AB测试逻辑
     * 根据请求参数执行相应的AB测试策略
     * 
     * @param request AB测试处理请求，包含用户信息、分流类型、策略名称等
     * @return AB测试上下文，包含执行结果和状态信息
     */
    public ABTestContext handle(ABTestHandleRequest<P> request) {
        try {
            ABTestContext context = prepareContext(request);
            if (StringUtils.isNotBlank(request.getStrategyName())) {
                return executeNamedStrategy(request, context);
            }
            Integer randomValue = generateRandomValue(request);
            context.setRandomValue(randomValue);
            ABTestStrategy<P> strategy = selectStrategy(request, randomValue, context);
            return executeStrategy(strategy, request.getParam(), context);
        } catch (Exception e) {
            Cat.logEvent("ABTestService.handle", "Exception");
            log.error("[ABTestService] Exception in handle, userId:{}, appId:{},errorMsg:{}", request.getUserId(), request.getAppId(),e.getMessage(), e);
            return request.getContext();
        }
    }

    /**
     * 准备AB测试上下文
     * 初始化或获取AB测试上下文，设置基本参数
     * 
     * @param request AB测试处理请求
     * @return 初始化后的AB测试上下文
     */
    private ABTestContext prepareContext(ABTestHandleRequest<P> request) {
        ABTestContext context = Optional.ofNullable(request.getContext()).orElse(new ABTestContext());
        context.setAppId(request.getAppId());
        return context;
    }

    /**
     * 执行指定名称的策略
     * 当请求中指定了策略名称时，直接执行该策略
     * 
     * @param request AB测试处理请求
     * @param context AB测试上下文
     * @return 执行结果上下文
     */
    private ABTestContext executeNamedStrategy(ABTestHandleRequest<P> request, ABTestContext context) {
        context.setStrategyName(request.getStrategyName());
        context.setRandomValue(0);
        context.setRandomValueStart(0);
        context.setRandomValueEnd(0);
        context.setAbTestStatus(request.getAbTestStatus());
        ABTestStrategy<P> strategy = getStrategyByType(request.getStrategyName());
        if (strategy != null) {
            // log.info("[ABTestService] executeNamedStrategy strategy:{}", strategy.getName());
            return strategy.execute(request.getParam(), context);
        }
        return context;
    }

    /**
     * 生成随机数值
     * 根据分流类型生成或获取用户的随机数值
     * 
     * @param request AB测试处理请求
     * @return 生成的随机数值
     */
    private Integer generateRandomValue(ABTestHandleRequest<P> request) {
        SplitType splitType = request.getSplitType();
        Long userId = request.getUserId();
        String appId = request.getAppId();
        if(isTestEnvOrRcEnv()){
            return abTestConfig.getTestRandomValue(appId);
        }
        Integer randomValue;
        switch (splitType) {
            case RANGE:
                randomValue = getOrGenerateRandomValue(userId, appId);
                // log.info("[ABTestService] RANGE split, randomValue:{}", randomValue);
                break;
            case PERCENTAGE:
                randomValue = getOrGeneratePercentageRandomValue(userId, appId);
                // log.info("[ABTestService] PERCENTAGE split, randomValue:{}", randomValue);
                break;
            case DOT_CARE_USER_ID:
                randomValue = generateDotCareUserIdRandomValue(appId);
                // log.info("[ABTestService] DOT_CARE_USER_ID split, randomValue:{}", randomValue);
                break;
            default:
                throw new IllegalArgumentException("Unknown split type: " + splitType);
        }
        return randomValue;
    }

    /**
     * 选择执行策略
     * 根据随机数值和分流类型选择合适的AB测试策略
     * 
     * @param request AB测试处理请求
     * @param randomValue 随机数值
     * @param context AB测试上下文
     * @return 选择的策略，如果没找到则返回DoNothingStrategy
     */
    private ABTestStrategy<P> selectStrategy(ABTestHandleRequest<P> request, Integer randomValue, ABTestContext context) {
        SplitType splitType = request.getSplitType();
        String appId = request.getAppId();
        Long userId = request.getUserId();
        ABTestStrategy<P> strategy = null;
        switch (splitType) {
            case RANGE:
                strategy = findStrategyByRange(randomValue, appId, context);
                break;
            case PERCENTAGE:
            case DOT_CARE_USER_ID:
                strategy = findStrategyByPercentage(randomValue, appId, context);
                break;
            default:
                log.info("[ABTestService] Unknown splitType: {} for userId: {}", splitType, userId);
                break;
        }
        if (strategy == null) {
            // log.info("[ABTestService] No strategy matched for userId: {}，use doNothing strategy.", userId);
            context.setRandomValueStart(0);
            context.setRandomValueEnd(0);
            context.setUniqueName("doNothing");
            context.setAbTestStatus(0);
            context.setProcessId(null);
            strategy = getStrategyByType(DO_NOTHING_STRATEGY_NAME);
        }
        return strategy;
    }

    /**
     * 执行策略
     * 执行选定的AB测试策略并返回结果
     * 
     * @param strategy 要执行的策略
     * @param param 策略执行参数
     * @param context AB测试上下文
     * @return 执行结果上下文
     */
    private ABTestContext executeStrategy(ABTestStrategy<P> strategy, P param, ABTestContext context) {
        if (strategy != null) {
            context.setStrategyName(strategy.getName());
            // log.info("[ABTestService] Executing strategy:{}", strategy.getName());
            return strategy.execute(param, context);
        }
        return context;
    }

    /**
     * 判断是否是测试环境或者RC环境
     * 
     * @return true表示是测试环境或RC环境，false表示是生产环境
     */
    private boolean isTestEnvOrRcEnv() {
        return Environment.isProductEnv() && StringUtils.isNotBlank(Environment.getCell()) || Environment.isTestEnv();
    }

    /**
     * 获取或生成userId对应的随机数，缓存一天
     * 随机数上限取配置值
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户的随机数值
     */
    private Integer getOrGenerateRandomValue(Long userId,String appId) {
        if (userId == null) {
            // log.info("[ABTestService] getOrGenerateRandomValue: userId is null");
            return null;
        }
        String dateKey = DateUtil.formatYMd(new Date());
        StoreKey storeKey = new StoreKey(ABTEST_RANGE_RANDOM_CATEGORY,appId,dateKey, userId);
        Integer randomValue = redisClient.get(storeKey);
        if (randomValue == null) {
            Integer bound = abTestConfig.getRangeMaxValueFromConfig(appId);
            if (bound == null) {
                // log.info("[ABTestService] getOrGenerateRandomValue: bound is null");
                throw new IllegalArgumentException("bound is null");
            }
            randomValue = ThreadLocalRandom.current().nextInt(bound);
            // log.info("[ABTestService] Generated random value: {} for userId: {}", randomValue, userId);
            int expireSeconds = getSecondsUntilNextMidnight();
            redisClient.set(storeKey, randomValue, expireSeconds);
        }
        return randomValue;
    }

    /**
     * 计算当前时间距离第二天凌晨的秒数
     * 
     * @return 距离第二天凌晨的秒数
     */
    private int getSecondsUntilNextMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextMidnight = now.plusDays(1).truncatedTo(ChronoUnit.DAYS);
        long seconds = Duration.between(now, nextMidnight).getSeconds();
        return (int) seconds;
    }

    /**
     * 根据区间范围查找策略
     * 
     * @param randomValue 随机数值
     * @param appId 应用ID
     * @param context AB测试上下文
     * @return 匹配的策略，如果没找到则返回null
     */
    private ABTestStrategy<P> findStrategyByRange(Integer randomValue,String appId,ABTestContext context) {
        List<RangeStrategyItem> items = new ArrayList<>(abTestConfig.getRangeStrategyConfigItems(appId));
        items.sort(Comparator.comparingInt(RangeStrategyItem::getOrder));
        for (RangeStrategyItem item : items) {
            if (randomValue >= item.getRangeStart() && randomValue < item.getRangeEnd()) {
                context.setRandomValueStart(item.getRangeStart());
                context.setRandomValueEnd(item.getRangeEnd());
                context.setUniqueName(item.getUniqueName());
                context.setAbTestStatus(item.getAbTestStatus());
                context.setProcessId(item.getProcessId());
                return getStrategyByType(item.getStrategyName());
            }
        }
       return null;
    }

    /**
     * 根据百分比查找策略
     * 
     * @param randomValue 随机数值
     * @param appId 应用ID
     * @param context AB测试上下文
     * @return 匹配的策略，如果没找到则返回null
     */
    private ABTestStrategy<P> findStrategyByPercentage(Integer randomValue,String appId,ABTestContext context) {
        List<PercentageStrategyItem> items = new ArrayList<>(abTestConfig.getPercentageStrategies(appId));
        items.sort(Comparator.comparingInt(PercentageStrategyItem::getOrder));
        for (PercentageStrategyItem item : items) {
            if (randomValue < item.getNumerator()) {
                context.setRandomValueStart(item.getNumerator());
                context.setRandomValueEnd(abTestConfig.getPercentageMaxValueFromConfig(appId));
                context.setUniqueName(item.getUniqueName());
                context.setAbTestStatus(item.getAbTestStatus());
                context.setProcessId(item.getProcessId());
                return getStrategyByType(item.getStrategyName());
            }
        }
        return null;
    }

    /**
     * 生成随机数,不缓存
     * 用于不关心用户ID的分流场景
     * @return 生成的随机数值
     */
    private Integer generateDotCareUserIdRandomValue(String appId){
        Integer denominatorMax = abTestConfig.getPercentageMaxValueFromConfig(appId);
        if (denominatorMax == null) {
            log.info("[ABTestService] generateDotCareUserIdRandomValue: denominatorMax is null");
            throw new IllegalArgumentException("denominatorMax is null");
        }
        return ThreadLocalRandom.current().nextInt(denominatorMax);
    }

    /**
     * 百分比分流随机数缓存，和范围分流类似，按userId缓存一天
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户的随机数值
     */
    private Integer getOrGeneratePercentageRandomValue(Long userId,String appId) {
        if (userId == null) {
            return null;
        }
        String dateKey = DateUtil.formatYMd(new Date());
        StoreKey storeKey = new StoreKey(ABTEST_PERCENTAGE_RANDOM_CATEGORY, appId,dateKey,userId);
        Integer rand = redisClient.get(storeKey);
        Integer denominatorMax = abTestConfig.getPercentageMaxValueFromConfig(appId);
        if (denominatorMax == null) {
            // log.info("[ABTestService] getOrGeneratePercentageRandomValue: denominatorMax is null");
            throw new IllegalArgumentException("denominatorMax is null");
        }
        if (rand == null) {
            rand = ThreadLocalRandom.current().nextInt(denominatorMax);
            int expireSeconds = getSecondsUntilNextMidnight();
            redisClient.set(storeKey, rand, expireSeconds);
        }
        return rand;
    }

    /**
     * 根据策略名称获取策略实例
     * 
     * @param strategyName 策略名称
     * @return 对应的策略实例，如果没找到则返回null
     */
    private ABTestStrategy<P> getStrategyByType(String strategyName) {
        for (ABTestStrategy<P> strategy : strategies) {
            if (strategy.getName().equals(strategyName)) {
                return strategy;
            }
        }
        return null;
    }
}