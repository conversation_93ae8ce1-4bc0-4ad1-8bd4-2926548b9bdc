package com.sankuai.scrm.core.service.aigc.service.repository;

import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmAutoreplyAppidAgentDO;
import com.sankuai.scrm.core.service.aigc.service.dal.example.ScrmAutoreplyAppidAgentDOExample;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmAutoreplyAppidAgentDOMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoreplyAgentRepositoryTest {

    private final String businessCode = "businessCode";
    private final String appId = "testAppId";
    private final String xiaomeiAppId = "testXiaomeiAppId";

    @InjectMocks
    private ScrmAutoreplyAgentRepository scrmAutoreplyAgentRepository;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmAutoreplyAppidAgentDOMapper autoreplyAppidAgentDOMapper;

    @Before
    public void setUp() {
        when(autoreplyAppidAgentDOMapper.updateByExampleSelective(any(ScrmAutoreplyAppidAgentDO.class), any(ScrmAutoreplyAppidAgentDOExample.class))).thenReturn(1);
    }

    @Test
    public void queryXiaomeiAppIdByBusinessCodeTest() {
        // arrange
        ScrmAutoreplyAppidAgentDO expected = new ScrmAutoreplyAppidAgentDO();
        when(autoreplyAppidAgentDOMapper.selectByExample(any(ScrmAutoreplyAppidAgentDOExample.class)))
                .thenReturn(Collections.singletonList(expected));
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode(businessCode);
        config.setAppId(appId);
        when(corpAppConfigRepository.getConfigByBusinessCode(eq(businessCode))).thenReturn(config);
        // act
        ScrmAutoreplyAppidAgentDO result = scrmAutoreplyAgentRepository.queryXiaomeiAppIdByBusinessCode(businessCode);
        // assert
        assertSame(expected, result);
    }

    /**
     * 测试bindXiaomeiAppId方法，当appId已经存在于数据库中时
     */
    @Test
    public void testBindXiaomeiAppId_AppIdExists() {
        // arrange
        ScrmAutoreplyAppidAgentDO agentDO = new ScrmAutoreplyAppidAgentDO();
        agentDO.setAppId(appId);
        agentDO.setXiaomeiAppId(xiaomeiAppId);
        when(autoreplyAppidAgentDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(agentDO));
        // act
        scrmAutoreplyAgentRepository.bindXiaomeiAppId(appId, xiaomeiAppId);
        // assert
        verify(autoreplyAppidAgentDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试bindXiaomeiAppId方法，当appId不存在于数据库中时
     */
    @Test
    public void testBindXiaomeiAppId_AppIdNotExists() {
        // arrange
        when(autoreplyAppidAgentDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        scrmAutoreplyAgentRepository.bindXiaomeiAppId(appId, xiaomeiAppId);
        // assert
        verify(autoreplyAppidAgentDOMapper, times(1)).insertSelective(any());
    }

    /**
     * 测试 updateXiaomeiAppId 方法，正常情况
     */
    @Test
    public void testUpdateXiaomeiAppIdNormal() {
        // arrange
        // act
        scrmAutoreplyAgentRepository.updateXiaomeiAppId(appId, xiaomeiAppId);
        // assert
        verify(autoreplyAppidAgentDOMapper, times(1)).updateByExampleSelective(any(ScrmAutoreplyAppidAgentDO.class),
                any(ScrmAutoreplyAppidAgentDOExample.class));
    }

    /**
     * 测试 updateXiaomeiAppId 方法，异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testUpdateXiaomeiAppIdException() {
        // arrange
        when(autoreplyAppidAgentDOMapper.updateByExampleSelective(any(ScrmAutoreplyAppidAgentDO.class), any(ScrmAutoreplyAppidAgentDOExample.class))).thenThrow(new RuntimeException());
        // act
        scrmAutoreplyAgentRepository.updateXiaomeiAppId(appId, xiaomeiAppId);
        // assert
        verify(autoreplyAppidAgentDOMapper, times(1)).updateByExampleSelective(any(ScrmAutoreplyAppidAgentDO.class), any(ScrmAutoreplyAppidAgentDOExample.class));
    }

    /**
     * Test queryXiaomeiAppId method when appId is null
     */
    @Test(expected = RuntimeException.class)
    public void testQueryXiaomeiAppIdWhenAppIdIsNull() throws Throwable {
        // arrange
        String appId = null;
        // act
        scrmAutoreplyAgentRepository.queryXiaomeiAppId(appId);
        // Since the method under test throws a RuntimeException when appId is null,
        // there is no need for an assert statement here.
    }

    /**
     * Test queryXiaomeiAppId method when appId is valid but no record in database
     */
    @Test
    public void testQueryXiaomeiAppIdWhenAppIdIsValidButNoRecord() throws Throwable {
        // arrange
        String appId = "testAppId";
        when(autoreplyAppidAgentDOMapper.selectByExample(any(ScrmAutoreplyAppidAgentDOExample.class)))
                .thenReturn(Collections.emptyList());
        // act
        ScrmAutoreplyAppidAgentDO result = scrmAutoreplyAgentRepository.queryXiaomeiAppId(appId);
        // assert
        assertNull(result);
    }

    /**
     * Test queryXiaomeiAppId method when appId is valid and record exists in database
     */
    @Test
    public void testQueryXiaomeiAppIdWhenAppIdIsValidAndRecordExists() throws Throwable {
        // arrange
        String appId = "testAppId";
        ScrmAutoreplyAppidAgentDO expected = new ScrmAutoreplyAppidAgentDO();
        when(autoreplyAppidAgentDOMapper.selectByExample(any(ScrmAutoreplyAppidAgentDOExample.class)))
                .thenReturn(Collections.singletonList(expected));
        // act
        ScrmAutoreplyAppidAgentDO result = scrmAutoreplyAgentRepository.queryXiaomeiAppId(appId);
        // assert
        assertSame(expected, result);
    }
}