package com.sankuai.scrm.core.service.payment.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: payment_scene
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PaymentScene {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: scene_type
     *   说明: 付款场景类型
     */
    private Integer sceneType;

    /**
     *   字段: payment_way
     *   说明: 付款方式
     */
    private Integer paymentWay;

    /**
     *   字段: relation_id
     *   说明: 场景关联的id
     */
    private Long relationId;

    /**
     *   字段: money_cent
     *   说明: 金额，单位分
     */
    private Integer moneyCent;

    /**
     *   字段: deleted
     *   说明: 是否删除
     */
    private Boolean deleted;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}