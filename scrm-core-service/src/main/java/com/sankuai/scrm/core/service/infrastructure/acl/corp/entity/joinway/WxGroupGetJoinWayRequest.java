package com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.joinway;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业微信客户群"加入群聊"管理 - 获取客户群进群方式配置接口请求
 * https://developer.work.weixin.qq.com/document/path/92229#%E8%8E%B7%E5%8F%96%E5%AE%A2%E6%88%B7%E7%BE%A4%E8%BF%9B%E7%BE%A4%E6%96%B9%E5%BC%8F%E9%85%8D%E7%BD%AE
 */
@Data
public class WxGroupGetJoinWayRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("config_id")
    private String configId;
}
