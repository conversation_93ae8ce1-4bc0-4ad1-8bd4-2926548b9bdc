package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/17 19:14
 * @Version v1.0.0
 */
@Getter
public enum PersonalWxGroupMemberStatusEnum {

    IN_GROUP((byte)0, "在群"),
    LEAVE_GROUP((byte)1, "不在群"),
    KICK_OUT_GROUP((byte)2, "已踢出群"),

    ;

    private final Byte code;

    private final String desc;

    PersonalWxGroupMemberStatusEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PersonalWxGroupMemberStatusEnum fromCode(Byte code) {
        for (PersonalWxGroupMemberStatusEnum enumValue : PersonalWxGroupMemberStatusEnum.values()) {
            if (enumValue.code == code) {
                return enumValue;
            }
        }
        return null;
    }
}
