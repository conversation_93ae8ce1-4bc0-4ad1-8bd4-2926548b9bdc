package com.sankuai.scrm.core.service.abtest.strategy;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.user.dto.UserIntentionFinalResult;
import com.sankuai.dzshoplist.search.intervention.dto.UserTrackDTO;
import com.sankuai.scrm.core.service.abtest.config.ABTestConfig;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserIntentionScores;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserScoreDTO;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserScoreResult;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowContentDTO;
import com.sankuai.scrm.core.service.aigc.service.dto.intelligent.follow.FridayIntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.util.UserActionScoreUtil;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutorLimitDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.coupon.service.CouponSelectorService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.request.UserTrackRequest;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.user.predict.service.UserIntentionComprehensiveServiceImpl;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Component
@Slf4j
public abstract class AbstractScrmFridayIntelligentFollowStrategy implements ABTestStrategy<IntelligentFollowActionTrackDTO> {


    @Autowired
    protected ScrmFridayIntelligentFollowDomainService scrmFridayIntelligentFollowDomainService;

    @Autowired
    protected GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Autowired
    protected RealTimeTaskConsumerConfig consumerConfig;

    @Autowired
    protected MtUserCenterAclService mtUserCenterAclService;

    @Autowired
    protected ABTestConfig abTestConfig;

    @Autowired
    protected UserActionScoreUtil userActionScoreUtil;

    @Autowired
    protected CouponSelectorService couponSelectorService;

    @Autowired
    protected UserTrackAcl userTrackAcl;

    @Resource
    protected ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @Autowired
    protected RedisStoreClient redisClient;

    @Autowired
    private ExecuteManagementService executeManagementService;

    @Autowired
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Autowired
    protected UserIntentionComprehensiveServiceImpl userIntentionComprehensiveService;

    protected IntelligentFollowResultDTO buildIntelligentFollowResultDTO(FridayIntelligentFollowResultDTO followResultDTO) {
        if (followResultDTO == null) {
            return null;
        }
        if(!followResultDTO.isNeedSendMsg()){
            return null;
        }
        IntelligentFollowResultDTO resultDTO = new IntelligentFollowResultDTO();
        resultDTO.setNeedSendMsg(followResultDTO.isNeedSendMsg());
        resultDTO.setSendTime(followResultDTO.getSendTime());
        resultDTO.setText(followResultDTO.getContent().getSendText());
        resultDTO.setShopId(followResultDTO.getContent().getShopId());
        resultDTO.setProductId(followResultDTO.getContent().getProductId());
        return resultDTO;
    }

    /**
     * 构建ABTest记录消息DTO并发送消息
     * @param context
     */
    protected void buildABTestRecordMessageDTOAndSendMessage(ABTestContext context){
        if (abTestConfig.getAppIds().contains(context.getAppId())){
            String dateKey = DateUtil.formatYMd(new Date());
            GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO = new GroupRetailAiEngineABTestRecordMessageDTO();
            testRecordMessageDTO.setTestVersion(abTestConfig.getTestVersion(context.getAppId()));
            testRecordMessageDTO.setStrategyName(context.getStrategyName());
            testRecordMessageDTO.setRandomValue( context.getRandomValue() );
            testRecordMessageDTO.setRandomValueStart( context.getRandomValueStart() );
            testRecordMessageDTO.setRandomValueEnd(context.getRandomValueEnd());
            testRecordMessageDTO.setUniqueName(context.getUniqueName());
            testRecordMessageDTO.setMtUserId(context.getUserId());
            testRecordMessageDTO.setDateKey(dateKey);
            testRecordMessageDTO.setAppId(context.getAppId());
            testRecordMessageDTO.setStatus(context.getAbTestStatus());
            testRecordMessageDTO.setActionType(0);
            fillABTestRecordMessageDTO(context,testRecordMessageDTO);
            testRecordMessageProducer.sendMessage(testRecordMessageDTO);
        }
    }

    /**
     * 构建ABTest记录消息DTO并发送消息
     * @param context
     */
    protected void buildABTestRecordMessageDTOAndSendMessage(ABTestContext context, String strategyName){
        if (abTestConfig.getAppIds().contains(context.getAppId())){
            String dateKey = DateUtil.formatYMd(new Date());
            GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO = new GroupRetailAiEngineABTestRecordMessageDTO();
            testRecordMessageDTO.setTestVersion(abTestConfig.getTestVersion(context.getAppId()));
            testRecordMessageDTO.setStrategyName(strategyName);
            testRecordMessageDTO.setRandomValue( context.getRandomValue() );
            testRecordMessageDTO.setRandomValueStart( context.getRandomValueStart() );
            testRecordMessageDTO.setRandomValueEnd(context.getRandomValueEnd());
            testRecordMessageDTO.setUniqueName(context.getUniqueName());
            testRecordMessageDTO.setMtUserId(context.getUserId());
            testRecordMessageDTO.setDateKey(dateKey);
            testRecordMessageDTO.setAppId(context.getAppId());
            testRecordMessageDTO.setStatus(context.getAbTestStatus());
            testRecordMessageDTO.setActionType(0);
            fillABTestRecordMessageDTO(context,testRecordMessageDTO);
            testRecordMessageProducer.sendMessage(testRecordMessageDTO);
        }
    }

    /**
     * 获取美团用户ID
     * @param platform
     * @param userId
     * @return
     */
    protected Long getMtUserId(PlatformEnum platform, Long userId){
        if(PlatformEnum.DP.equals(platform)){
            Long mtUserIdByDpUserId = mtUserCenterAclService.getMtUserIdByDpUserId(userId);
            if(null == mtUserIdByDpUserId){
                return null;
            }
            return mtUserIdByDpUserId;
        }
        return userId;
    }

    /**
     * 获取店铺信息
     */
    protected MtPoiDTO getShopInfo(Long shopId) {
        try {
            return scrmFridayIntelligentFollowDomainService.getMtPoiDTO(shopId);
        } catch (Exception e) {
            log.error("Failed to get shop info for shopId: {}", shopId, e);
            return null;
        }
    }

    /**
     * 获取商品信息
     * 通过构造临时的FridayIntelligentFollowResultDTO来调用现有方法
     */
    protected ProductInfoDTO getProductInfo(Long productId) {
        try {
            // 构造临时的FridayIntelligentFollowResultDTO对象
            FridayIntelligentFollowResultDTO tempFollowResultDTO = new FridayIntelligentFollowResultDTO();
            FridayIntelligentFollowContentDTO contentDTO = new FridayIntelligentFollowContentDTO();
            contentDTO.setProductId(productId);
            tempFollowResultDTO.setContent(contentDTO);

            // 调用ScrmFridayIntelligentFollowDomainService的getProductInfoDTO方法
            return scrmFridayIntelligentFollowDomainService.getProductInfoDTO(tempFollowResultDTO);
        } catch (Exception e) {
            log.error("Failed to get product info for productId: {}", productId, e);
            return null;
        }
    }

    /**
     * 判断是否为高潜力用户
     */
    protected UserScoreResult isHighPotentialUser(UserIntentionScores scores, List<UserScoreDTO> userScoreList) {
        for (UserScoreDTO userScoreDTO : userScoreList) {
            if (isScoreInRange(scores.getIntentionScore(), userScoreDTO.getIntentionScoreLow(), userScoreDTO.getIntentionScoreHigh())
                    && isScoreInRange(scores.getHesitationScore(), userScoreDTO.getHesitationScoreLow(), userScoreDTO.getHesitationScoreHigh())) {
                return new UserScoreResult(
                        userScoreDTO.getIntentionScoreHigh(),
                        userScoreDTO.getIntentionScoreLow(),
                        userScoreDTO.getHesitationScoreHigh(),
                        userScoreDTO.getHesitationScoreLow(),
                        true
                );
            }
        }
        //没命中范围，默认9999.99
        return new UserScoreResult(new BigDecimal("9999.99"), new BigDecimal("9999.99"), new BigDecimal("9999.99"), new BigDecimal("9999.99"), false);
    }

    /**
     * 判断分数是否在指定范围内
     */
    protected boolean isScoreInRange(BigDecimal score, BigDecimal low, BigDecimal high) {
        return score.compareTo(low) >= 0 && score.compareTo(high) < 0;
    }

    /**
     * 构建并发送AB测试记录消息，包含新增的评分字段
     */
    protected void buildAndSendABTestRecordMessage(Long userId, String appId,
                                                 UserIntentionScores intentionScores,
                                                 UserActionScoreUtil.UserActionScoreResult actionScores,ABTestContext abTestContext,UserScoreResult highPotentialUser) {
        try {
            GroupRetailAiEngineABTestRecordMessageDTO recordMessage = new GroupRetailAiEngineABTestRecordMessageDTO();

            // 设置基础字段
            recordMessage.setMtUserId(userId);
            recordMessage.setAppId(appId);
            recordMessage.setTestVersion(abTestConfig.getTestVersion(appId));
            recordMessage.setStatus(0);
            recordMessage.setOriginValue(0);
            recordMessage.setTestValue(0);
            recordMessage.setDateKey(DateUtil.formatYMd(new Date()));
            recordMessage.setActionType(0);
            recordMessage.setStrategyName(abTestContext.getStrategyName());
            recordMessage.setUniqueName(abTestContext.getUniqueName());
            recordMessage.setRandomValue(abTestContext.getRandomValue() );
            recordMessage.setRandomValueStart( abTestContext.getRandomValueStart());
            recordMessage.setRandomValueEnd(abTestContext.getRandomValueEnd());
            // 设置新增的评分字段
            if (intentionScores != null) {
                recordMessage.setIntentionScore(intentionScores.getIntentionScore());
                recordMessage.setHesitationScore(intentionScores.getHesitationScore());
                recordMessage.setUserScoreDetail(JSON.toJSONString(intentionScores.getUserIntentionFinalResult()));
            }
            // 设置阈值字段
            if (highPotentialUser != null) {
                recordMessage.setIntentionScoreThreshold(highPotentialUser.getIntentionScoreHigh());
                recordMessage.setIntentionScoreLow(highPotentialUser.getIntentionScoreLow());
                recordMessage.setHesitationScoreThreshold(highPotentialUser.getHesitationScoreHigh());
                recordMessage.setHesitationScoreLow(highPotentialUser.getHesitationScoreLow());
            }
            // 设置商品/店铺评分JSON字段
            if (actionScores != null) {
                Map<String, Object> itemScoreMap = new HashMap<>();
                // 添加商品评分（取前10个，按评分从大到小排序）
                if (!actionScores.getProductScores().isEmpty()) {
                    Map<String, BigDecimal> productScores = new HashMap<>();
                    actionScores.getProductScores().entrySet().stream()
                            .sorted(Map.Entry.<Long, BigDecimal>comparingByValue().reversed())
                            .limit(10)
                            .forEach(entry -> productScores.put(entry.getKey().toString(), entry.getValue()));
                    itemScoreMap.put("products", productScores);
                }
                // 添加店铺评分（取前10个，按评分从大到小排序）
                if (!actionScores.getShopScores().isEmpty()) {
                    Map<String, BigDecimal> shopScores = new HashMap<>();
                    actionScores.getShopScores().entrySet().stream()
                            .sorted(Map.Entry.<Long, BigDecimal>comparingByValue().reversed())
                            .limit(10)
                            .forEach(entry -> shopScores.put(entry.getKey().toString(), entry.getValue()));
                    itemScoreMap.put("shops", shopScores);
                }
                // 转换为JSON字符串
                if (!itemScoreMap.isEmpty()) {
                    recordMessage.setItemScoreJson(JsonUtils.toStr(itemScoreMap));
                }
            }
            // 发送消息
            testRecordMessageProducer.sendMessage(recordMessage);
            // log.info("AB test record message sent successfully for userId: {}, appId: {}", userId, appId);
        } catch (Exception e) {
            Cat.logEvent(getName(), "buildAndSendABTestRecordMessage.error");
            log.error("Failed to build and send AB test record message for userId: {}, appId: {}", userId, appId, e);
        }
    }

    /**
     * 获取用户意向评分
     */
    protected UserIntentionScores getUserIntentionScores(Long userId,String appId) {
        try {
            UserIntentionFinalResult userIntentionFinalResult = userIntentionComprehensiveService.evaluateUserComprehensively("mt_" + userId, appId);
            if (userIntentionFinalResult == null) {
                return null;
            }
            if (userIntentionFinalResult.getHesitationScore() == null || userIntentionFinalResult.getIntentionScore() == null) {
                return null;
            }
            // log.info("userIntentionFinalResult: {}", JSON.toJSONString(userIntentionFinalResult));
            return new UserIntentionScores(userIntentionFinalResult.getIntentionScore(),userIntentionFinalResult.getHesitationScore(),userIntentionFinalResult);
        } catch (Exception e) {
            Cat.logEvent(getName(), "getUserIntentionScores.error");
            log.error("Failed to get user intention scores for userId: {}", userId, e);
            return null;
        }
    }

    /**
     * 获取用户行为分数
     */
    protected UserActionScoreUtil.UserActionScoreResult getUserActionScores(Long userId) {
        try {
            Date now = new Date();
            Date dateSub = DateUtil.dateSub(now, consumerConfig.getDayNum());
            UserTrackRequest convert = UserTrackRequest.convert(1, userId, dateSub.getTime(), now.getTime());
            UserTrackDTO userTrack = userTrackAcl.queryUserTrackByRequest(convert);
            UserActionScoreUtil.UserActionScoreResult shopAndProductScore = userActionScoreUtil.getShopAndProductScore(userTrack);
            return shopAndProductScore;
        } catch (Exception e) {
            Cat.logEvent(getName(), "getUserActionScores.error");
            log.error("Failed to get user action scores for userId: {}", userId, e);
            return null;
        }
    }

    protected boolean checkIsContactableUser(Long processOrchestrationId, String appId, Long userId) {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = processOrchestrationReadDomainService.getScrmAmProcessOrchestrationInfoDO(processOrchestrationId, appId, null);
        List<ScrmAmProcessOrchestrationExecutorLimitDO> limitDOS = null;
        if(processOrchestrationInfoDO != null){
            limitDOS = processOrchestrationReadDomainService.queryExecutorLimitDOList(processOrchestrationInfoDO);
        }
        if(CollectionUtils.isEmpty(limitDOS)){
            return false;
        }
        Set<String> executors = limitDOS.stream().map(ScrmAmProcessOrchestrationExecutorLimitDO::getExecutorId).collect(Collectors.toSet());
        List<String> appIds = new ArrayList<>();
        appIds.add(appId);
        List<ContactUser> contactUsers = executeManagementService.getContactUserByMtUserId(userId, appIds);
        if(CollectionUtils.isEmpty(contactUsers)){
            return false;
        }
        return contactUsers.stream().anyMatch(contactUser -> executors.contains(contactUser.getStaffId()));
    }


    public abstract void fillABTestRecordMessageDTO(ABTestContext context,GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO);

}
