package com.sankuai.scrm.core.service.data.statistics.domain;

import com.sankuai.dz.srcm.data.statistics.dto.*;
import com.sankuai.dz.srcm.data.statistics.enums.*;
import com.sankuai.dz.srcm.friend.dynamiccode.enums.*;
import com.sankuai.dz.srcm.data.statistics.request.FriendOperatorDataOverviewOverview;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.*;
import com.sankuai.scrm.core.service.data.statistics.dal.entity.ScrmHistoryDataStatisticsIndicatorInfo;
import com.sankuai.scrm.core.service.data.statistics.dal.example.ScrmHistoryDataStatisticsIndicatorInfoExample;
import com.sankuai.scrm.core.service.data.statistics.dal.mapper.ScrmHistoryDataStatisticsIndicatorInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
@Service
public class FriendOperatorDataStatisticsDomainService {

    @Resource
    private ContactUserToDoMapper contactUserToDoMapper;
    @Resource
    private ContactUserLogDOMapper contactUserLogDOMapper;
    @Resource
    private GroupUserLogDOMapper groupUserLogDOMapper;
    @Resource
    private MemberInfoEntityDOMapper memberInfoDOMapper;
    @Resource
    private ScrmHistoryDataStatisticsIndicatorInfoMapper historyStatisticsIndicatorInfoMapper;

    public List<WeChatFriendStatisticsDTO> friendOperatorDataOverviewForFriend(FriendOperatorDataOverviewOverview request, CorpAppConfig config) {
        String corpId = config.getCorpId();
        String appId = config.getAppId();
        //日期
        Date startDate = getDate(request.getStartTime(), true);
        Date endDate = getDate(request.getEndTime(), false);

        //获取startDate在上一周的日期范围 精确到0:0:0
        Date startDayLastWeek = DateUtils.addWeeks(startDate, -1);
        Date endDayLastWeek = DateUtils.addWeeks(endDate, -1);
        //获取endDate上一月相同的日期范围, 若为2号就是上个月的2号的23:59:59
        Date startDayLastMonth = DateUtils.addMonths(startDate, -1);
        Date endDayLastMonth = DateUtils.addMonths(endDate, -1);

        if (request.getDateType() == FriendChannelCodeStatisticsType.DAY.getCode()) {
            //个人号总数, 总好友人数, 新增好友数, 总好友人次, 新增好友人次
            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            Date startDayBeforeLast = DateUtils.addDays(startDayLastMonth, -1);
            FriendStatisticsIndicatorDTO indicatorDTO = getFriendIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, startDayBeforeLast, endDayLastMonth);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, endDayLastMonth);
            //流失好友人次
            FriendStatisticsIndicatorDTO lostFriendTimesDTO = getContactUserLogs(corpId, ContactUserActionType.EXTERNAL_USER_DELETE_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, endDayLastMonth);
            //个人号总数
            long currentAccountNum = indicatorDTO.getCurrentAccountNum();//1
            String totalAccountNumWeekLinkRatio = calculateLinkRatio(currentAccountNum, indicatorDTO.getLastPeriodAccountNum());
            String totalAccountNumMonthLinkRatio = calculateLinkRatio(currentAccountNum, indicatorDTO.getBeforeLastPeriodAccountNum());
            //总好友数
            long currentFriendNum = indicatorDTO.getCurrentPeriodFriendNum(); //2
            String friendNumWeekLinkRatio = calculateLinkRatio(currentFriendNum, indicatorDTO.getLastPeriodFriendNum());
            String friendNumMonthLinkRatio = calculateLinkRatio(currentFriendNum, indicatorDTO.getBeforeLastPeriodFriendNum());
            //新增好友数
            long currentAddFriendNum = currentFriendNum - indicatorDTO.getStartDayFriendNum(); //3
            long lastWeekAddFriendNum = indicatorDTO.getLastPeriodFriendNum() - indicatorDTO.getStartDayLastPeriodFriendNum();
            long lastMonthAddFriendNum = indicatorDTO.getBeforeLastPeriodFriendNum() - indicatorDTO.getStartDayBeforeLastPeriodFriendNum();
            String addFriendNumWeekLinkRatio = calculateLinkRatio(currentAddFriendNum, lastWeekAddFriendNum);
            String addFriendNumMonthLinkRatio = calculateLinkRatio(currentAddFriendNum, lastMonthAddFriendNum);
            //总好友次数
            long currentFriendTimes = indicatorDTO.getCurrentPeriodFriendTimes(); //4
            String friendTimesWeekLinkRatio = calculateLinkRatio(currentFriendTimes, indicatorDTO.getLastPeriodFriendTimes());
            String friendTimesMonthLinkRatio = calculateLinkRatio(currentFriendTimes, indicatorDTO.getBeforeLastPeriodFriendTimes());
            //新增好友次数
            long currentAddFriendTimes = currentFriendTimes - indicatorDTO.getStartDayCurrentFriendTimes(); //5
            long lastWeekAddFriendTimes = indicatorDTO.getLastPeriodFriendTimes() - indicatorDTO.getStartDayLastPeriodFriendTimes();
            long lastMonthAddFriendTimes = indicatorDTO.getBeforeLastPeriodFriendTimes() - indicatorDTO.getStartDayBeforeLastPeriodFriendTimes();
            String addFriendTimesWeekLinkRatio = calculateLinkRatio(currentAddFriendTimes, lastWeekAddFriendTimes);
            String addFriendTimesMonthLinkRatio = calculateLinkRatio(currentAddFriendTimes, lastMonthAddFriendTimes);
            //流失好友数
            long currentLostFriendNum = lostFriendNumDTO.getCurrentPeriodFriendNum(); //6
            String lostFriendNumWeekLinkRatio = calculateLinkRatio(currentLostFriendNum, lostFriendNumDTO.getLastPeriodFriendNum());
            String lostFriendNumMonthLinkRatio = calculateLinkRatio(currentLostFriendNum, lostFriendNumDTO.getBeforeLastPeriodFriendNum());

            //好友流失率
            String lostFriendRatio = calculateRatio(currentLostFriendNum, currentFriendNum); //7
            String lastWeekLostFriendRatio = calculateRatio(lostFriendNumDTO.getLastPeriodFriendNum(), indicatorDTO.getLastPeriodFriendNum());
            String lastMonthLostFriendRatio = calculateRatio(lostFriendNumDTO.getBeforeLastPeriodFriendNum(), indicatorDTO.getBeforeLastPeriodFriendNum());
            String lostFriendRatioWeekLinkRatio = calculateLostRatio(lostFriendRatio, lastWeekLostFriendRatio); //z
            String lostFriendRatioMonthLinkRatio = calculateLostRatio(lostFriendRatio, lastMonthLostFriendRatio); //y

            //流失好友次数
            long currentLostFriendTimes = lostFriendTimesDTO.getCurrentPeriodFriendTimes(); //8
            String lostFriendTimesWeekLinkRatio = calculateLinkRatio(currentLostFriendTimes, lostFriendTimesDTO.getLastPeriodFriendTimes());
            String lostFriendTimesMonthLinkRatio = calculateLinkRatio(currentLostFriendTimes, lostFriendTimesDTO.getBeforeLastPeriodFriendTimes());

            List<WeChatFriendStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.PERSONAL_ACCOUNT_TOTAL.getDesc(), String.valueOf(currentAccountNum), totalAccountNumWeekLinkRatio, totalAccountNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(currentFriendNum), friendNumWeekLinkRatio, friendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentFriendTimes), friendTimesWeekLinkRatio, friendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentAddFriendNum), addFriendNumWeekLinkRatio, addFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentAddFriendTimes), addFriendTimesWeekLinkRatio, addFriendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentLostFriendNum), lostFriendNumWeekLinkRatio, lostFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentLostFriendTimes), lostFriendTimesWeekLinkRatio, lostFriendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_RATION.getDesc(), lostFriendRatio, lostFriendRatioWeekLinkRatio, lostFriendRatioMonthLinkRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.WEEK.getCode()) {
            //若startDate和当前日期相等, 则startDate和endDate都减一, 若不相等, 将endDate日期设置为当前日期
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }

            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            FriendStatisticsIndicatorDTO indicatorDTO = getFriendIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);
            //流失好友人次
            FriendStatisticsIndicatorDTO lostFriendTimesDTO = getContactUserLogs(corpId, ContactUserActionType.EXTERNAL_USER_DELETE_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);
            //个人号总数
            long totalAccountNum = indicatorDTO.getCurrentAccountNum(); //1
            long lastWeekAccountNum = indicatorDTO.getLastPeriodAccountNum();
            String totalAccountNumWeekLinkRatio = calculateLinkRatio(totalAccountNum, lastWeekAccountNum);
            //总好友数
            long currentFriendNum = indicatorDTO.getCurrentPeriodFriendNum(); //2
            long lastWeekFriendNum = indicatorDTO.getLastPeriodFriendNum();
            String friendNumWeekLinkRatio = calculateLinkRatio(currentFriendNum, lastWeekFriendNum);
            //新增好友数
            long currentAddFriendNum = currentFriendNum - indicatorDTO.getStartDayFriendNum();  //3
            long lastWeekAddFriendNum = lastWeekFriendNum - indicatorDTO.getStartDayLastPeriodFriendNum();
            String addFriendNumWeekLinkRatio = calculateLinkRatio(currentAddFriendNum, lastWeekAddFriendNum);
            //流失好友数
            long currentLostFriendNum = lostFriendNumDTO.getCurrentPeriodFriendNum(); //4
            String lostFriendNumWeekLinkRatio = calculateLinkRatio(currentLostFriendNum, lostFriendNumDTO.getLastPeriodFriendNum());
            //好友流失率
            String lostFriendRatio = calculateRatio(currentLostFriendNum, currentFriendNum); //5
            String lastWeekLostFriendRatio = calculateRatio(lostFriendNumDTO.getLastPeriodFriendNum(), lastWeekFriendNum);
            String lostFriendRatioWeekLinkRatio = calculateLostRatio(lostFriendRatio, lastWeekLostFriendRatio); //z
            //总好友次数
            long currentFriendTimes = indicatorDTO.getCurrentPeriodFriendTimes(); //6
            String friendTimesWeekLinkRatio = calculateLinkRatio(currentFriendTimes, indicatorDTO.getLastPeriodFriendTimes());
            //新增好友次数
            long currentAddFriendTimes = currentFriendTimes - indicatorDTO.getStartDayCurrentFriendTimes();  //7
            long lastWeekAddFriendTimes = indicatorDTO.getLastPeriodFriendTimes() - indicatorDTO.getStartDayLastPeriodFriendTimes();
            String addFriendTimesWeekLinkRatio = calculateLinkRatio(currentAddFriendTimes, lastWeekAddFriendTimes);
            //流失好友次数
            long currentLostFriendTimes = lostFriendTimesDTO.getCurrentPeriodFriendTimes(); //8
            String lostFriendTimesWeekLinkRatio = calculateLinkRatio(currentLostFriendTimes, lostFriendTimesDTO.getLastPeriodFriendTimes());

            List<WeChatFriendStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.PERSONAL_ACCOUNT_TOTAL.getDesc(), String.valueOf(totalAccountNum), totalAccountNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(currentFriendNum), friendNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentFriendTimes), friendTimesWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentAddFriendNum), addFriendNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentAddFriendTimes), addFriendTimesWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentLostFriendNum), lostFriendNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentLostFriendTimes), lostFriendTimesWeekLinkRatio, ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_RATION.getDesc(), lostFriendRatio, lostFriendRatioWeekLinkRatio, ""));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.MONTH.getCode()) {
            //若startDate和当前日期相等, 则startDate和endDate都减一, 若不相等, 将endDate日期设置为当前日期
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }
            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastMonth, -1);
            FriendStatisticsIndicatorDTO indicatorDTO = getFriendIndicator(appId, startDay, endDate, startDayLast, endDayLastMonth, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);
            //流失好友人次
            FriendStatisticsIndicatorDTO lostFriendTimesDTO = getContactUserLogs(corpId, ContactUserActionType.EXTERNAL_USER_DELETE_FRIEND.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);
            //个人号总数
            //个人号总数
            long totalAccountNum = indicatorDTO.getCurrentAccountNum(); //1
            long lastMonthAccountNum = indicatorDTO.getLastPeriodAccountNum();
            String totalAccountNumMonthLinkRatio = calculateLinkRatio(totalAccountNum, lastMonthAccountNum);
            //总好友数
            long currentFriendNum = indicatorDTO.getCurrentPeriodFriendNum(); //2
            long lastMonthFriendNum = indicatorDTO.getLastPeriodFriendNum();
            String friendNumMonthLinkRatio = calculateLinkRatio(currentFriendNum, lastMonthFriendNum);
            //新增好友数
            long currentAddFriendNum = currentFriendNum - indicatorDTO.getStartDayFriendNum(); //3
            long lastMonthAddFriendNum = lastMonthFriendNum - indicatorDTO.getStartDayLastPeriodFriendNum();
            String addFriendNumMonthLinkRatio = calculateLinkRatio(currentAddFriendNum, lastMonthAddFriendNum);
            //流失好友数
            long currentLostFriendNum = lostFriendNumDTO.getCurrentPeriodFriendNum(); //4
            String lostFriendNumMonthLinkRatio = calculateLinkRatio(currentLostFriendNum, lostFriendNumDTO.getLastPeriodFriendNum());
            //好友流失率
            String lostFriendRatio = calculateRatio(currentLostFriendNum, currentFriendNum); //5
            String lastMonthLostFriendRatio = calculateRatio(lostFriendNumDTO.getLastPeriodFriendNum(), lastMonthFriendNum);
            String lostFriendRatioMonthLinkRatio = calculateLostRatio(lostFriendRatio, lastMonthLostFriendRatio); //z
            //总好友次数
            long currentFriendTimes = indicatorDTO.getCurrentPeriodFriendTimes(); //6
            String friendTimesMonthLinkRatio = calculateLinkRatio(currentFriendTimes, indicatorDTO.getLastPeriodFriendTimes());
            //新增好友次数
            long currentAddFriendTimes = currentFriendTimes - indicatorDTO.getStartDayCurrentFriendTimes();  //7
            long lastMonthAddFriendTimes = indicatorDTO.getLastPeriodFriendTimes() - indicatorDTO.getStartDayLastPeriodFriendTimes();
            String addFriendTimesMonthLinkRatio = calculateLinkRatio(currentAddFriendTimes, lastMonthAddFriendTimes);
            //流失好友次数
            long currentLostFriendTimes = lostFriendTimesDTO.getCurrentPeriodFriendTimes(); //8
            String lostFriendTimesMonthLinkRatio = calculateLinkRatio(currentLostFriendTimes, lostFriendTimesDTO.getLastPeriodFriendTimes());

            List<WeChatFriendStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.PERSONAL_ACCOUNT_TOTAL.getDesc(), String.valueOf(totalAccountNum), "", totalAccountNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(currentFriendNum), "", friendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentFriendTimes), "", friendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentAddFriendNum), "", addFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentAddFriendTimes), "", addFriendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentLostFriendNum), "", lostFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentLostFriendTimes), "", lostFriendTimesMonthLinkRatio));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_RATION.getDesc(), lostFriendRatio, "", lostFriendRatioMonthLinkRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.USER_DEFINED.getCode()) {

            Date startDay = DateUtils.addDays(startDate, -1);
            //获取startDate之前时间间隔与startDate和endDate时间间隔相同的日期
            long interval = endDate.getTime() - startDate.getTime();
            Date dateBeforeStartDate = new Date(startDate.getTime() - interval);

            FriendStatisticsIndicatorDTO indicatorDTO = getFriendIndicator(appId, startDay, endDate, null, null, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);
            //流失好友人次
            FriendStatisticsIndicatorDTO lostFriendTimesDTO = getContactUserLogs(corpId, ContactUserActionType.EXTERNAL_USER_DELETE_FRIEND.getCode(), startDate, endDate, null, null, null, null);

            //个人号总数
            long totalAccountNum = indicatorDTO.getCurrentAccountNum(); //1
            //总好友数
            long currentFriendNum = indicatorDTO.getCurrentPeriodFriendNum(); //2
            //新增好友数
            long currentAddFriendNum = currentFriendNum - indicatorDTO.getStartDayFriendNum(); //3
            //流失好友数
            long currentLostFriendNum = lostFriendNumDTO.getCurrentPeriodFriendNum(); //4
            //好友流失率
            String lostFriendRatio = calculateRatio(currentLostFriendNum, currentFriendNum); //5
            //总好友次数
            long currentFriendTimes = indicatorDTO.getCurrentPeriodFriendTimes(); //6
            //新增好友次数
            long currentAddFriendTimes = currentFriendTimes - indicatorDTO.getStartDayCurrentFriendTimes();  //7
            //流失好友次数
            long currentLostFriendTimes = lostFriendTimesDTO.getCurrentPeriodFriendTimes(); //8 //8
            List<WeChatFriendStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.PERSONAL_ACCOUNT_TOTAL.getDesc(), String.valueOf(totalAccountNum), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(currentFriendNum), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.TOTAL_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentFriendTimes), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentAddFriendNum), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.ADD_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentAddFriendTimes), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentLostFriendNum), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentLostFriendTimes), "", ""));
            resultList.add(buildResultDTOForFriend(FriendStatisticsIndicatorType.LOST_FRIEND_RATION.getDesc(), lostFriendRatio, "", ""));
            return resultList;
        }
        return Collections.emptyList();
    }

    private Date handleEndDate() {
        Date endDate;
        endDate = new Date();
        LocalDateTime localDateTime = endDate.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        endDate = Date.from(endOfDay.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
        endDate = DateUtils.addDays(endDate, -1);
        return endDate;
    }


    public List<ScrmUserStatisticsDTO> friendOperatorDataOverviewForScrm(FriendOperatorDataOverviewOverview request, CorpAppConfig config) {
        String corpId = config.getCorpId();
        String appId = request.getAppId();
        //日期
        Date startDate = getDate(request.getStartTime(), true);
        Date endDate = getDate(request.getEndTime(), false);

        Date startDayLastWeek = DateUtils.addWeeks(startDate, -1);
        Date endDayLastWeek = DateUtils.addWeeks(endDate, -1);
        //获取endDate上一月相同的日期范围, 若为2号就是上个月的2号的23:59:59
        Date startDayLastMonth = DateUtils.addMonths(startDate, -1);
        Date endDayLastMonth = DateUtils.addMonths(endDate, -1);

        if (request.getDateType() == FriendChannelCodeStatisticsType.DAY.getCode()) {
            //退群人数
            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            Date startDayBeforeLast = DateUtils.addDays(startDayLastMonth, -1);
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, endDayLastMonth);
            //退群用户人次
            ScrmStatisticsIndicatorDTO exitGroupUserTimes = getGroupUserLogs(corpId, GroupUserLogType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, endDayLastMonth);

            ScrmStatisticsIndicatorDTO indicatorDTO = getScrmIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, startDayBeforeLast, endDayLastMonth);

            //总群数
            long totalGroupNum = indicatorDTO.getCurrentPeriodTotalGroupNum();
            String totalGroupNumWeekLinkRatio = calculateLinkRatio(totalGroupNum, indicatorDTO.getLastPeriodTotalGroupNum());
            String totalGroupNumMonthLinkRatio = calculateLinkRatio(totalGroupNum, indicatorDTO.getBeforeLastPeriodTotalGroupNum());
            //增量
            long currentAddGroupNum = totalGroupNum - indicatorDTO.getCurrentStartDateGroupNum();
            long lastAddGroupNumOfWeek = indicatorDTO.getLastPeriodTotalGroupNum() - indicatorDTO.getLastPeriodStartDateGroupNum();
            long lastAddGroupNumOfMonth = indicatorDTO.getBeforeLastPeriodTotalGroupNum() - indicatorDTO.getBeforeLastPeriodStartDateGroupNum();
            String addGroupNumWeekLinkRatio = calculateLinkRatio(currentAddGroupNum, lastAddGroupNumOfWeek);
            String addGroupNumMonthLinkRatio = calculateLinkRatio(currentAddGroupNum, lastAddGroupNumOfMonth);
            //在群人数
            long existGroupUserNum = indicatorDTO.getCurrentGroupUserNum();
            String inGroupUserNumWeekLinkRatio = calculateLinkRatio(existGroupUserNum, indicatorDTO.getLastPeriodGroupUserNum());
            String inGroupUserNumMonthLinkRatio = calculateLinkRatio(existGroupUserNum, indicatorDTO.getBeforeLastPeriodGroupUserNum());
            //增量
            long currentAddInGroupUserNum = existGroupUserNum - indicatorDTO.getCurrentStartDateGroupUserNum();
            long addInGroupUserNumOfWeek = indicatorDTO.getLastPeriodGroupUserNum() - indicatorDTO.getLastPeriodStartDateGroupUserNum();
            long addInGroupUserNumOfMonth = indicatorDTO.getBeforeLastPeriodGroupUserNum() - indicatorDTO.getBeforeLastPeriodStartDateGroupUserNum();
            String addInGroupUserNumWeekLinkRatio = calculateLinkRatio(currentAddInGroupUserNum, addInGroupUserNumOfWeek);
            String addInGroupUserNumMonthLinkRatio = calculateLinkRatio(currentAddInGroupUserNum, addInGroupUserNumOfMonth);
            //退群人数
            String exitGroupNumWeekLinkRatio = calculateLinkRatio(exitGroupUsers.getCurrentGroupUserNum(), exitGroupUsers.getLastPeriodGroupUserNum());
            String exitGroupNumMonthLinkRatio = calculateLinkRatio(exitGroupUsers.getCurrentGroupUserNum(), exitGroupUsers.getBeforeLastPeriodGroupUserNum());
            //在群人次
            long currentGroupUserTimes = indicatorDTO.getCurrentGroupUserTimes();
            String inGroupUserTimesWeekLinkRatio = calculateLinkRatio(currentGroupUserTimes, indicatorDTO.getLastPeriodGroupUserTimes());
            String inGroupUserTimesMonthLinkRatio = calculateLinkRatio(currentGroupUserTimes, indicatorDTO.getBeforeLastPeriodGroupUserTimes());
            //增量
            long currentAddInGroupUserTimes = currentGroupUserTimes - indicatorDTO.getCurrentPeriodStartDateGroupUserTimes();
            long currentAddInGroupUserTimesOfWeek = indicatorDTO.getLastPeriodGroupUserTimes() - indicatorDTO.getLastPeriodStartDateGroupUserTimes();
            long currentAddInGroupUserTimesOfMonth = indicatorDTO.getBeforeLastPeriodGroupUserTimes() - indicatorDTO.getBeforeLastPeriodStartDateGroupUserTimes();
            String addInGroupUserTimesWeekLinkRatio = calculateLinkRatio(currentAddInGroupUserTimes, currentAddInGroupUserTimesOfWeek);
            String addInGroupUserTimesMonthLinkRatio = calculateLinkRatio(currentAddInGroupUserTimes, currentAddInGroupUserTimesOfMonth);
            //退群人次
            String exitGroupTimesWeekLinkRatio = calculateLinkRatio(exitGroupUserTimes.getCurrentGroupUserTimes(), exitGroupUserTimes.getLastPeriodGroupUserTimes());
            String exitGroupTimesMonthLinkRatio = calculateLinkRatio(exitGroupUserTimes.getCurrentGroupUserTimes(), exitGroupUserTimes.getBeforeLastPeriodGroupUserTimes());
            //构建
            List<ScrmUserStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.TOTAL_GROUP_NUM.getDesc(), String.valueOf(totalGroupNum), totalGroupNumWeekLinkRatio, totalGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_NUM.getDesc(), String.valueOf(currentAddGroupNum), addGroupNumWeekLinkRatio, addGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_NUM.getDesc(), String.valueOf(existGroupUserNum), inGroupUserNumWeekLinkRatio, inGroupUserNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_NUM.getDesc(), String.valueOf(currentAddInGroupUserNum), addInGroupUserNumWeekLinkRatio, addInGroupUserNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_NUM.getDesc(), String.valueOf(exitGroupUsers.getCurrentGroupUserNum()), exitGroupNumWeekLinkRatio, exitGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentGroupUserTimes), inGroupUserTimesWeekLinkRatio, inGroupUserTimesMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentAddInGroupUserTimes), addInGroupUserTimesWeekLinkRatio, addInGroupUserTimesMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(exitGroupUserTimes.getCurrentGroupUserTimes()), exitGroupTimesWeekLinkRatio, exitGroupTimesMonthLinkRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.WEEK.getCode()) {
            //识别日期
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }
            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            ScrmStatisticsIndicatorDTO indicatorDTO = getScrmIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, null, null);

            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);
            //退群用户人次
            ScrmStatisticsIndicatorDTO exitGroupUserTimes = getGroupUserLogs(corpId, GroupUserLogType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);

            //总群数
            long totalGroupNum = indicatorDTO.getCurrentPeriodTotalGroupNum();
            String totalGroupNumWeekLinkRatio = calculateLinkRatio(totalGroupNum, indicatorDTO.getLastPeriodTotalGroupNum());

            //增量
            long currentAddGroupNum = totalGroupNum - indicatorDTO.getCurrentStartDateGroupNum();
            long lastAddGroupNumOfWeek = indicatorDTO.getLastPeriodTotalGroupNum() - indicatorDTO.getLastPeriodStartDateGroupNum();
            String addGroupNumWeekLinkRatio = calculateLinkRatio(currentAddGroupNum, lastAddGroupNumOfWeek);

            //在群人数
            long existGroupUserNum = indicatorDTO.getCurrentGroupUserNum();
            String inGroupUserNumWeekLinkRatio = calculateLinkRatio(existGroupUserNum, indicatorDTO.getLastPeriodGroupUserNum());

            //增量
            long currentAddInGroupUserNum = existGroupUserNum - indicatorDTO.getCurrentStartDateGroupUserNum();
            long addInGroupUserNumOfWeek = indicatorDTO.getLastPeriodGroupUserNum() - indicatorDTO.getLastPeriodStartDateGroupUserNum();
            String addInGroupUserNumWeekLinkRatio = calculateLinkRatio(currentAddInGroupUserNum, addInGroupUserNumOfWeek);

            //退群人数
            String exitGroupNumWeekLinkRatio = calculateLinkRatio(exitGroupUsers.getCurrentGroupUserNum(), exitGroupUsers.getLastPeriodGroupUserNum());
            //在群人次
            long currentGroupUserTimes = indicatorDTO.getCurrentGroupUserTimes();
            String inGroupUserTimesWeekLinkRatio = calculateLinkRatio(currentGroupUserTimes, indicatorDTO.getLastPeriodGroupUserTimes());
            //增量
            long currentAddInGroupUserTimes = currentGroupUserTimes - indicatorDTO.getCurrentPeriodStartDateGroupUserTimes();
            long currentAddInGroupUserTimesOfWeek = indicatorDTO.getLastPeriodGroupUserTimes() - indicatorDTO.getLastPeriodStartDateGroupUserTimes();
            String addInGroupUserTimesWeekLinkRatio = calculateLinkRatio(currentAddInGroupUserTimes, currentAddInGroupUserTimesOfWeek);
            //退群人次
            String exitGroupTimesWeekLinkRatio = calculateLinkRatio(exitGroupUserTimes.getCurrentGroupUserTimes(), exitGroupUserTimes.getLastPeriodGroupUserTimes());
            List<ScrmUserStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.TOTAL_GROUP_NUM.getDesc(), String.valueOf(totalGroupNum), totalGroupNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_NUM.getDesc(), String.valueOf(currentAddGroupNum), addGroupNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_NUM.getDesc(), String.valueOf(existGroupUserNum), inGroupUserNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_NUM.getDesc(), String.valueOf(currentAddInGroupUserNum), addInGroupUserNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_NUM.getDesc(), String.valueOf(exitGroupUsers.getCurrentGroupUserNum()), exitGroupNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentGroupUserTimes), inGroupUserTimesWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentAddInGroupUserTimes), addInGroupUserTimesWeekLinkRatio, ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(exitGroupUserTimes.getCurrentGroupUserTimes()), exitGroupTimesWeekLinkRatio, ""));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.MONTH.getCode()) {
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }

            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastMonth, -1);
            ScrmStatisticsIndicatorDTO indicatorDTO = getScrmIndicator(appId, startDay, endDate, startDayLast, endDayLastMonth, null, null);

            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);
            //退群用户人次
            ScrmStatisticsIndicatorDTO exitGroupUserTimes = getGroupUserLogs(corpId, GroupUserLogType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);

            //总群数
            long totalGroupNum = indicatorDTO.getCurrentPeriodTotalGroupNum();
            String totalGroupNumMonthLinkRatio = calculateLinkRatio(totalGroupNum, indicatorDTO.getLastPeriodTotalGroupNum());
            //增量
            long currentAddGroupNum = totalGroupNum - indicatorDTO.getCurrentStartDateGroupNum();
            long lastAddGroupNumOfMonth = indicatorDTO.getLastPeriodTotalGroupNum() - indicatorDTO.getLastPeriodStartDateGroupNum();
            String addGroupNumMonthLinkRatio = calculateLinkRatio(currentAddGroupNum, lastAddGroupNumOfMonth);
            //在群人数
            long existGroupUserNum = indicatorDTO.getCurrentGroupUserNum();
            String inGroupUserNumMonthLinkRatio = calculateLinkRatio(existGroupUserNum, indicatorDTO.getLastPeriodGroupUserNum());
            //增量
            long currentAddInGroupUserNum = existGroupUserNum - indicatorDTO.getCurrentStartDateGroupUserNum();
            long addInGroupUserNumOfMonth = indicatorDTO.getLastPeriodGroupUserNum() - indicatorDTO.getLastPeriodStartDateGroupUserNum();
            String addInGroupUserNumMonthLinkRatio = calculateLinkRatio(currentAddInGroupUserNum, addInGroupUserNumOfMonth);
            //退群人数
            String exitGroupNumMonthLinkRatio = calculateLinkRatio(exitGroupUsers.getCurrentGroupUserNum(), exitGroupUsers.getLastPeriodGroupUserNum());
            //在群人次
            long currentGroupUserTimes = indicatorDTO.getCurrentGroupUserTimes();
            String inGroupUserTimesMonthLinkRatio = calculateLinkRatio(currentGroupUserTimes, indicatorDTO.getLastPeriodGroupUserTimes());
            //增量
            long currentAddInGroupUserTimes = currentGroupUserTimes - indicatorDTO.getCurrentPeriodStartDateGroupUserTimes();
            long currentAddInGroupUserTimesOfMonth = indicatorDTO.getLastPeriodGroupUserTimes() - indicatorDTO.getLastPeriodStartDateGroupUserTimes();
            String addInGroupUserTimesMonthLinkRatio = calculateLinkRatio(currentAddInGroupUserTimes, currentAddInGroupUserTimesOfMonth);
            //退群人次
            String exitGroupTimesMonthLinkRatio = calculateLinkRatio(exitGroupUserTimes.getCurrentGroupUserTimes(), exitGroupUserTimes.getLastPeriodGroupUserTimes());

            List<ScrmUserStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.TOTAL_GROUP_NUM.getDesc(), String.valueOf(totalGroupNum), "", totalGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_NUM.getDesc(), String.valueOf(currentAddGroupNum), "", addGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_NUM.getDesc(), String.valueOf(existGroupUserNum), "", inGroupUserNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_NUM.getDesc(), String.valueOf(currentAddInGroupUserNum), "", addInGroupUserNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_NUM.getDesc(), String.valueOf(exitGroupUsers.getCurrentGroupUserNum()), "", exitGroupNumMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentGroupUserTimes), "", inGroupUserTimesMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentAddInGroupUserTimes), "", addInGroupUserTimesMonthLinkRatio));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(exitGroupUserTimes.getCurrentGroupUserTimes()), "", exitGroupTimesMonthLinkRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.USER_DEFINED.getCode()) {
            Date startDay = DateUtils.addDays(startDate, -1);
            //获取startDate之前时间间隔与startDate和endDate时间间隔相同的日期
            long interval = endDate.getTime() - startDate.getTime();
            Date dateBeforeStartDate = new Date(startDate.getTime() - interval);
            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);
            //退群用户人次
            ScrmStatisticsIndicatorDTO exitGroupUserTimes = getGroupUserLogs(corpId, GroupUserLogType.EXIT_GROUP.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);

            ScrmStatisticsIndicatorDTO indicatorDTO = getScrmIndicator(appId, startDay, endDate, null, null, null, null);

            //总群数
            long totalGroupNum = indicatorDTO.getCurrentPeriodTotalGroupNum();
            //增量
            long currentAddGroupNum = totalGroupNum - indicatorDTO.getCurrentStartDateGroupNum();
            //在群人数
            long existGroupUserNum = indicatorDTO.getCurrentGroupUserNum();
            //增量
            long currentAddInGroupUserNum = existGroupUserNum - indicatorDTO.getCurrentStartDateGroupUserNum();
            //退群人数
            long exitGroupUserNum = exitGroupUsers.getCurrentGroupUserNum();
            //在群人次
            long currentGroupUserTimes = indicatorDTO.getCurrentGroupUserTimes();
            //增量
            long currentAddInGroupUserTimes = currentGroupUserTimes - indicatorDTO.getCurrentPeriodStartDateGroupUserTimes();
            //退群人次
            long exitGroupUserFrequency = exitGroupUserTimes.getCurrentGroupUserTimes();
            List<ScrmUserStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.TOTAL_GROUP_NUM.getDesc(), String.valueOf(totalGroupNum), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_NUM.getDesc(), String.valueOf(currentAddGroupNum), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_NUM.getDesc(), String.valueOf(existGroupUserNum), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_NUM.getDesc(), String.valueOf(currentAddInGroupUserNum), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_NUM.getDesc(), String.valueOf(exitGroupUserNum), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.IN_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentGroupUserTimes), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.ADD_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(currentAddInGroupUserTimes), "", ""));
            resultList.add(buildResultDTOForScrm(ScrmStatisticsIndicatorType.QUIT_GROUP_USER_FREQUENCY.getDesc(), String.valueOf(exitGroupUserFrequency), "", ""));
            return resultList;
        }

        return Collections.emptyList();
    }

    public List<FriendOperatorEntireStatisticsDTO> friendOperatorDataOverviewForEntire(FriendOperatorDataOverviewOverview request, CorpAppConfig config) {
        String corpId = config.getCorpId();
        String appId = request.getAppId();

        //日期
        Date startDate = getDate(request.getStartTime(), true);
        Date endDate = getDate(request.getEndTime(), false);

        Date startDayLastWeek = DateUtils.addWeeks(startDate, -1);
        Date endDayLastWeek = DateUtils.addWeeks(endDate, -1);
        //获取endDate上一月相同的日期范围, 若为2号就是上个月的2号的23:59:59
        Date startDayLastMonth = DateUtils.addMonths(startDate, -1);
        Date endDayLastMonth = DateUtils.addMonths(endDate, -1);
        if (request.getDateType() == FriendChannelCodeStatisticsType.DAY.getCode()) {

            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            Date startDayBeforeLast = DateUtils.addDays(startDayLastMonth, -1);
            EntireStatisticsIndicatorDTO indicatorDTO = getEntireIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, startDayBeforeLast, endDayLastMonth);
            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, startDayLastMonth);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, startDayLastMonth);
            //流失和退群对应的重复计数
            EntireStatisticsIndicatorDTO lostNumRepeatCount = getEntireRepeatCount(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, startDayLastMonth, startDayLastMonth);

            //总客户数
            long totalUserNum = indicatorDTO.getTotalUserNum();
            String totalFriendNumWeekLinkRatio = calculateLinkRatio(totalUserNum, indicatorDTO.getLastPeriodTotalUserNum()); //z
            String totalFriendNumMonthLinkRatio = calculateLinkRatio(totalUserNum, indicatorDTO.getBeforeLastPeriodTotalUserNum()); //y
            //新增用户数
            long currentEntireAddFriendNum = totalUserNum - indicatorDTO.getTotalStartDateUserNum();
            long lastWeekEntireAddFriendNum = indicatorDTO.getLastPeriodTotalUserNum() - indicatorDTO.getLastPeriodStartDateTalkUserNum();
            long lastMonthEntireAddFriendNum = indicatorDTO.getBeforeLastPeriodTotalUserNum() - indicatorDTO.getBeforeLastPeriodStartDateTalkUserNum();
            String entireAddFriendNumWeekLinkRatio = calculateLinkRatio(currentEntireAddFriendNum, lastWeekEntireAddFriendNum); //z
            String entireAddFriendNumMonthLinkRatio = calculateLinkRatio(currentEntireAddFriendNum, lastMonthEntireAddFriendNum);//y
            //流失好友数=退群+流失好友数
            long currentEntireLostFriendNum = exitGroupUsers.getCurrentGroupUserNum() + lostFriendNumDTO.getCurrentPeriodFriendNum() - lostNumRepeatCount.getCurrentPeriodRepeatNum(); //3
            long lastWeekEntireLostFriendNum = exitGroupUsers.getLastPeriodGroupUserNum() + lostFriendNumDTO.getLastPeriodFriendNum() - lostNumRepeatCount.getLastPeriodRepeatNum();
            long lastMonthEntireLostFriendNum = exitGroupUsers.getBeforeLastPeriodGroupUserNum() + lostFriendNumDTO.getBeforeLastPeriodFriendNum() - lostNumRepeatCount.getBeforeLastPeriodRepeatNum();
            String entireLostFriendNumWeekRatio = calculateLinkRatio(currentEntireLostFriendNum, lastWeekEntireLostFriendNum);
            String entireLostFriendNumMonthRatio = calculateLinkRatio(currentEntireLostFriendNum, lastMonthEntireLostFriendNum);
            //发言用户数
            long currentTalkUserNum = indicatorDTO.getCurrentTalkUserNum();
            String talkUserNumWeekRatio = calculateLinkRatio(currentTalkUserNum, indicatorDTO.getLastPeriodTalkUserNum());
            String talkUserNumMonthRatio = calculateLinkRatio(currentTalkUserNum, indicatorDTO.getBeforeLastPeriodTalkUserNum());
            //发言用户人次
            long currentTalkUserTimes = indicatorDTO.getCurrentTalkUserTimes();//5
            String talkUserTimesWeekRatio = calculateLinkRatio(currentTalkUserTimes, indicatorDTO.getLastPeriodTalkUserTimes());
            String talkUserTimesMonthRatio = calculateLinkRatio(currentTalkUserTimes, indicatorDTO.getBeforeLastPeriodTalkUserTimes());

            List<FriendOperatorEntireStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(totalUserNum), totalFriendNumWeekLinkRatio, totalFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentEntireAddFriendNum), entireAddFriendNumWeekLinkRatio, entireAddFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentEntireLostFriendNum), entireLostFriendNumWeekRatio, entireLostFriendNumMonthRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_NUM.getDesc(), String.valueOf(currentTalkUserNum), talkUserNumWeekRatio, talkUserNumMonthRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ACTIVITY_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentTalkUserNum), talkUserNumWeekRatio, talkUserNumMonthRatio));//暂时与发言用户数相同
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_FREQUENCY.getDesc(), String.valueOf(currentTalkUserTimes), talkUserTimesWeekRatio, talkUserTimesMonthRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.WEEK.getCode()) {
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }

            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastWeek, -1);
            EntireStatisticsIndicatorDTO indicatorDTO = getEntireIndicator(appId, startDay, endDate, startDayLast, endDayLastWeek, null, null);
            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);
            //流失和退群重复计数
            EntireStatisticsIndicatorDTO lostNumRepeatCount = getEntireRepeatCount(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastWeek, endDayLastWeek, null, null);

            //总客户数
            long totalUserNum = indicatorDTO.getTotalUserNum();
            String totalFriendNumWeekLinkRatio = calculateLinkRatio(totalUserNum, indicatorDTO.getLastPeriodTotalUserNum()); //z
            //新增用户数
            long currentEntireAddFriendNum = totalUserNum - indicatorDTO.getTotalStartDateUserNum();
            long lastWeekEntireAddFriendNum = indicatorDTO.getLastPeriodTotalUserNum() - indicatorDTO.getLastPeriodStartDateTalkUserNum();
            String entireAddFriendNumWeekLinkRatio = calculateLinkRatio(currentEntireAddFriendNum, lastWeekEntireAddFriendNum); //z
            //流失好友数=退群+流失好友数
            long currentEntireLostFriendNum = exitGroupUsers.getCurrentGroupUserNum() + lostFriendNumDTO.getCurrentPeriodFriendNum() - lostNumRepeatCount.getCurrentPeriodRepeatNum(); //3
            long lastWeekEntireLostFriendNum = exitGroupUsers.getLastPeriodGroupUserNum() + lostFriendNumDTO.getLastPeriodFriendNum() - lostNumRepeatCount.getLastPeriodRepeatNum();
            String entireLostFriendNumWeekRatio = calculateLinkRatio(currentEntireLostFriendNum, lastWeekEntireLostFriendNum);

            //发言用户数
            long currentTalkUserNum = indicatorDTO.getCurrentTalkUserNum();
            String talkUserNumWeekRatio = calculateLinkRatio(currentTalkUserNum, indicatorDTO.getLastPeriodTalkUserNum());
            //发言用户人次
            long currentTalkUserTimes = indicatorDTO.getCurrentTalkUserTimes();//5
            String talkUserTimesWeekRatio = calculateLinkRatio(currentTalkUserTimes, indicatorDTO.getLastPeriodTalkUserTimes());

            List<FriendOperatorEntireStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(totalUserNum), totalFriendNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentEntireAddFriendNum), entireAddFriendNumWeekLinkRatio, ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentEntireLostFriendNum), entireLostFriendNumWeekRatio, ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_NUM.getDesc(), String.valueOf(currentTalkUserNum), talkUserNumWeekRatio, ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ACTIVITY_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentTalkUserNum), talkUserNumWeekRatio, ""));//暂时与发言用户数相同
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_FREQUENCY.getDesc(), String.valueOf(currentTalkUserTimes), talkUserTimesWeekRatio, ""));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.MONTH.getCode()) {
            if (DateUtils.isSameDay(startDate, new Date())) {
                startDate = DateUtils.addDays(startDate, -1);
                endDate = handleEndDate();
            } else if (endDate.after(new Date())) {  //结束时间大于当前时间
                endDate = handleEndDate();
            }

            Date startDay = DateUtils.addDays(startDate, -1);
            Date startDayLast = DateUtils.addDays(startDayLastMonth, -1);
            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);
            //流失和退群重复计数
            EntireStatisticsIndicatorDTO lostNumRepeatCount = getEntireRepeatCount(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, startDayLastMonth, endDayLastMonth, null, null);

            EntireStatisticsIndicatorDTO indicatorDTO = getEntireIndicator(appId, startDay, endDate, startDayLast, endDayLastMonth, null, null);

            //总客户数
            long totalUserNum = indicatorDTO.getTotalUserNum();
            String totalFriendNumMonthLinkRatio = calculateLinkRatio(totalUserNum, indicatorDTO.getLastPeriodTotalUserNum()); //y
            //新增用户数
            long currentEntireAddFriendNum = totalUserNum - indicatorDTO.getTotalStartDateUserNum();
            long lastMonthEntireAddFriendNum = indicatorDTO.getLastPeriodTotalUserNum() - indicatorDTO.getLastPeriodStartDateTalkUserNum();
            String entireAddFriendNumMonthLinkRatio = calculateLinkRatio(currentEntireAddFriendNum, lastMonthEntireAddFriendNum);//y

            //流失好友数=退群+流失好友数
            long currentEntireLostFriendNum = exitGroupUsers.getCurrentGroupUserNum() + lostFriendNumDTO.getCurrentPeriodFriendNum() - lostNumRepeatCount.getCurrentPeriodRepeatNum();
            long lastMonthEntireLostFriendNum = exitGroupUsers.getLastPeriodGroupUserNum() + lostFriendNumDTO.getLastPeriodFriendNum() - lostNumRepeatCount.getLastPeriodRepeatNum();
            String entireLostFriendNumMonthRatio = calculateLinkRatio(currentEntireLostFriendNum, lastMonthEntireLostFriendNum);
            //发言用户数
            long currentTalkUserNum = indicatorDTO.getCurrentTalkUserNum();
            String talkUserNumMonthRatio = calculateLinkRatio(currentTalkUserNum, indicatorDTO.getLastPeriodTalkUserNum());
            //发言用户人次
            long currentTalkUserTimes = indicatorDTO.getCurrentTalkUserTimes();//5
            String talkUserTimesMonthRatio = calculateLinkRatio(currentTalkUserTimes, indicatorDTO.getLastPeriodTalkUserTimes());

            List<FriendOperatorEntireStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(totalUserNum), "", totalFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentEntireAddFriendNum), "", entireAddFriendNumMonthLinkRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentEntireLostFriendNum), "", entireLostFriendNumMonthRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_NUM.getDesc(), String.valueOf(currentTalkUserNum), "", talkUserNumMonthRatio));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ACTIVITY_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentTalkUserNum), "", talkUserNumMonthRatio));//暂时与发言用户数相同
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_FREQUENCY.getDesc(), String.valueOf(currentTalkUserTimes), "", talkUserTimesMonthRatio));
            return resultList;
        } else if (request.getDateType() == FriendChannelCodeStatisticsType.USER_DEFINED.getCode()) {
            Date startDay = DateUtils.addDays(startDate, -1);
            //获取startDate之前时间间隔与startDate和endDate时间间隔相同的日期
            long interval = endDate.getTime() - startDate.getTime();
            Date dateBeforeStartDate = new Date(startDate.getTime() - interval);

            EntireStatisticsIndicatorDTO indicatorDTO = getEntireIndicator(appId, startDay, endDate, null, null, null, null);
            //退群人数
            ScrmStatisticsIndicatorDTO exitGroupUsers = getMemberInfos(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);
            //流失好友数
            FriendStatisticsIndicatorDTO lostFriendNumDTO = getContactUsers(corpId, ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);
            //流失和退群重复计数
            EntireStatisticsIndicatorDTO lostNumRepeatCount = getEntireRepeatCount(corpId, GroupMemberStatusType.EXIT_GROUP.getCode(), ContactUserStatus.NOT_FRIEND.getCode(), startDate, endDate, dateBeforeStartDate, startDate, null, null);

            //总客户数
            long totalUserNum = indicatorDTO.getTotalUserNum();
            //新增用户数
            long currentEntireAddFriendNum = totalUserNum - indicatorDTO.getTotalStartDateUserNum();
            //流失好友数=退群+流失好友数
            long currentEntireLostFriendNum = exitGroupUsers.getCurrentGroupUserNum() + lostFriendNumDTO.getCurrentPeriodFriendNum() - lostNumRepeatCount.getCurrentPeriodRepeatNum(); //3
            //发言用户数
            long currentTalkUserNum = indicatorDTO.getCurrentTalkUserNum(); //4
            //发言用户人次
            long currentTalkUserTimes = indicatorDTO.getCurrentTalkUserTimes();//5
            List<FriendOperatorEntireStatisticsDTO> resultList = new ArrayList<>();
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.TOTAL_FRIEND_NUM.getDesc(), String.valueOf(totalUserNum), "", ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ADD_FRIEND_NUM.getDesc(), String.valueOf(currentEntireAddFriendNum), "", ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.LOST_FRIEND_NUM.getDesc(), String.valueOf(currentEntireLostFriendNum), "", ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_NUM.getDesc(), String.valueOf(currentTalkUserNum), "", ""));
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.ACTIVITY_FRIEND_FREQUENCY.getDesc(), String.valueOf(currentTalkUserNum), "", ""));//暂时与发言用户数相同
            resultList.add(buildResultDTOForEntire(EntireStatisticsIndicatorType.SESSION_USER_FREQUENCY.getDesc(), String.valueOf(currentTalkUserTimes), "", ""));
            return resultList;
        }
        return Collections.emptyList();
    }

    private EntireStatisticsIndicatorDTO getEntireIndicator(String appId, Date startDate, Date endDate, Date startDayLast, Date endDayLast, Date startDayBeforeLast, Date endDayBeforeLast) {
        EntireStatisticsIndicatorDTO indicatorDTO = new EntireStatisticsIndicatorDTO();
        fillIndicator(appId, endDate,
                (historyDataIndicator, dto) -> {
                    dto.setTotalUserNum(historyDataIndicator.getTotalUserNum());
                    dto.setCurrentTalkUserNum(historyDataIndicator.getTalkUserNum());
                    dto.setCurrentTalkUserTimes(historyDataIndicator.getTalkUserFrequency());
                },
                indicatorDTO);
        //用与计算增量
        fillIndicator(appId, startDate,
                (historyDataIndicator, dto) -> {
                    dto.setTotalStartDateUserNum(historyDataIndicator.getTotalUserNum());
                    dto.setCurrentStartDateTalkUserNum(historyDataIndicator.getTalkUserNum());
                    dto.setCurrentStartDateTalkUserTimes(historyDataIndicator.getTalkUserFrequency());
                },
                indicatorDTO);
        if (ObjectUtils.isNotEmpty(endDayLast)) {
            fillIndicator(appId, endDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setLastPeriodTotalUserNum(historyDataIndicator.getTotalUserNum());
                        dto.setLastPeriodTalkUserNum(historyDataIndicator.getTalkUserNum());
                        dto.setLastPeriodTalkUserTimes(historyDataIndicator.getTalkUserFrequency());
                    },
                    indicatorDTO);
        }
        if (ObjectUtils.isNotEmpty(startDayLast)) {
            //用于计算增量
            fillIndicator(appId, startDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setLastPeriodStartDateTotalUserNum(historyDataIndicator.getTotalGroupNum());
                        dto.setLastPeriodStartDateTalkUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setLastPeriodStartDateTalkUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }
        if (ObjectUtils.isNotEmpty(endDayBeforeLast)) {
            fillIndicator(appId, endDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setBeforeLastPeriodTotalUserNum(historyDataIndicator.getTotalUserNum());
                        dto.setBeforeLastPeriodTalkUserNum(historyDataIndicator.getTalkUserNum());
                        dto.setBeforeLastPeriodTalkUserTimes(historyDataIndicator.getTalkUserFrequency());
                    },
                    indicatorDTO);
        }
        if (ObjectUtils.isNotEmpty(startDayBeforeLast)) {
            //用于计算增量
            fillIndicator(appId, startDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setBeforeLastPeriodStartDateTotalUserNum(historyDataIndicator.getTotalGroupNum());
                        dto.setLastPeriodStartDateTalkUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setLastPeriodStartDateTalkUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }
        return indicatorDTO;
    }


    private ScrmStatisticsIndicatorDTO getScrmIndicator(String appId, Date startDate, Date endDate, Date startDayLast, Date endDayLast, Date startDayBeforeLast, Date endDayBeforeLast) {
        ScrmStatisticsIndicatorDTO indicatorDTO = new ScrmStatisticsIndicatorDTO();
        fillIndicator(appId, endDate,
                (historyDataIndicator, dto) -> {
                    dto.setCurrentPeriodTotalGroupNum(historyDataIndicator.getTotalGroupNum());
                    dto.setCurrentGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                    dto.setCurrentGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                },
                indicatorDTO);
        //用与计算增量
        fillIndicator(appId, startDate,
                (historyDataIndicator, dto) -> {
                    dto.setCurrentStartDateGroupNum(historyDataIndicator.getTotalGroupNum());
                    dto.setCurrentStartDateGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                    dto.setCurrentPeriodStartDateGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                },
                indicatorDTO);

        if (ObjectUtils.isNotEmpty(endDayLast)) {
            fillIndicator(appId, endDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setLastPeriodTotalGroupNum(historyDataIndicator.getTotalGroupNum());
                        dto.setLastPeriodGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setLastPeriodGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }

        //用于计算增量
        if (ObjectUtils.isNotEmpty(startDayLast)) {
            fillIndicator(appId, startDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setLastPeriodStartDateGroupNum(historyDataIndicator.getTotalGroupNum());
                        dto.setLastPeriodStartDateGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setLastPeriodStartDateGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }

        if (ObjectUtils.isNotEmpty(endDayBeforeLast)) {
            fillIndicator(appId, endDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setBeforeLastPeriodTotalGroupNum(historyDataIndicator.getTotalGroupNum());
                        dto.setBeforeLastPeriodGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setLastPeriodGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }

        //用于计算增量
        if (ObjectUtils.isNotEmpty(startDayBeforeLast)) {
            fillIndicator(appId, startDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setBeforeLastPeriodStartDateGroupNum(historyDataIndicator.getTotalGroupNum());
                        dto.setBeforeLastPeriodStartDateGroupUserNum(historyDataIndicator.getExistGroupMemberNum());
                        dto.setBeforeLastPeriodStartDateGroupUserTimes(historyDataIndicator.getExistGroupMemberFrequency());
                    },
                    indicatorDTO);
        }
        return indicatorDTO;
    }

    private FriendStatisticsIndicatorDTO getFriendIndicator(String appId, Date startDate, Date endDate, Date startDayLast, Date endDayLast, Date startDayBeforeLast, Date endDayBeforeLast) {
        FriendStatisticsIndicatorDTO indicatorDTO = new FriendStatisticsIndicatorDTO();
        fillIndicator(appId, endDate,
                (historyDataIndicator, dto) -> {
                    dto.setCurrentAccountNum(historyDataIndicator.getPersonalAccountNum());
                    dto.setCurrentPeriodFriendNum(historyDataIndicator.getTotalFriendNum());
                    dto.setCurrentPeriodFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                },
                indicatorDTO);
        //用与计算增量
        fillIndicator(appId, startDate,
                (historyDataIndicator, dto) -> {
                    dto.setStartDayFriendNum(historyDataIndicator.getTotalFriendNum());
                    dto.setStartDayCurrentFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                },
                indicatorDTO);
        if (ObjectUtils.isNotEmpty(endDayLast)) {
            fillIndicator(appId, endDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setLastPeriodAccountNum(historyDataIndicator.getPersonalAccountNum());
                        dto.setLastPeriodFriendNum(historyDataIndicator.getTotalFriendNum());
                        dto.setLastPeriodFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                    },
                    indicatorDTO);
        }

        //用于计算增量
        if (ObjectUtils.isNotEmpty(startDayLast)) {
            fillIndicator(appId, startDayLast,
                    (historyDataIndicator, dto) -> {
                        dto.setStartDayLastPeriodFriendNum(historyDataIndicator.getTotalFriendNum());
                        dto.setStartDayLastPeriodFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                    },
                    indicatorDTO);
        }
        if (ObjectUtils.isNotEmpty(endDayBeforeLast)) {
            fillIndicator(appId, endDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setBeforeLastPeriodAccountNum(historyDataIndicator.getPersonalAccountNum());
                        dto.setBeforeLastPeriodFriendNum(historyDataIndicator.getTotalFriendNum());
                        dto.setBeforeLastPeriodFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                    },
                    indicatorDTO);
        }
        //用与计算增量
        if (ObjectUtils.isNotEmpty(startDayBeforeLast)) {
            fillIndicator(appId, startDayBeforeLast,
                    (historyDataIndicator, dto) -> {
                        dto.setStartDayBeforeLastPeriodFriendNum(historyDataIndicator.getTotalFriendNum());
                        dto.setStartDayBeforeLastPeriodFriendTimes(historyDataIndicator.getTotalFriendFrequency());
                    },
                    indicatorDTO);
        }
        return indicatorDTO;
    }


    public Date getDate(long dateTime, boolean isStartTime) {
        // 获取开始时间戳并转换为LocalDateTime类型
        if (isStartTime) {
            LocalDateTime startDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(dateTime), ZoneId.of("Asia/Shanghai"));
            // 将开始日期的时分秒设置为00:00:00
            startDateTime = startDateTime.withHour(0).withMinute(0).withSecond(0);
            // 转换回Date类型
            return Date.from(startDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
        } else {
            // 获取结束时间戳并转换为LocalDateTime类型
            LocalDateTime endDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(dateTime), ZoneId.of("Asia/Shanghai"));
            // 将结束日期的时分秒设置为23:59:59
            endDateTime = endDateTime.withHour(23).withMinute(59).withSecond(59);
            // 转换回Date类型
            return Date.from(endDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
        }
    }

    //适用三种类
    private <T> void fillIndicator(String appId, Date date,
                                   BiConsumer<ScrmHistoryDataStatisticsIndicatorInfo, T> fillHistoryDataFunc,
                                   T indicatorDTO) {
        List<ScrmHistoryDataStatisticsIndicatorInfo> historyDataIndicators = getHistoryDataIndicators(appId, date);
        if (CollectionUtils.isNotEmpty(historyDataIndicators)) {
            ScrmHistoryDataStatisticsIndicatorInfo historyDataIndicator = historyDataIndicators.get(0);
            fillHistoryDataFunc.accept(historyDataIndicator, indicatorDTO);
        }
    }



    public List<ScrmHistoryDataStatisticsIndicatorInfo> getHistoryDataIndicators(String appId, Date date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(date);
            Date statisticsDate = sdf.parse(formattedDate);
            ScrmHistoryDataStatisticsIndicatorInfoExample historyIndicatorInfoExample = new ScrmHistoryDataStatisticsIndicatorInfoExample();
            historyIndicatorInfoExample.createCriteria().andAppIdEqualTo(appId).andStatisticsDateEqualTo(statisticsDate);
            return historyStatisticsIndicatorInfoMapper.selectByExample(historyIndicatorInfoExample);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    private FriendOperatorEntireStatisticsDTO buildResultDTOForEntire(String indicatorType, String indicator, String weekLinkRation, String monthLinkRation) {
        FriendOperatorEntireStatisticsDTO dto = new FriendOperatorEntireStatisticsDTO();
        dto.setIndicatorType(indicatorType);
        dto.setIndicator(indicator);
        dto.setWeekLinkRatio(weekLinkRation);
        dto.setMothLinkRation(monthLinkRation);
        return dto;
    }


    public ScrmStatisticsIndicatorDTO getGroupUserLogs(String corpId, Integer status,
                                                       Date startDate, Date endDate,
                                                       Date lastPeriodStart, Date lastPeriodEnd,
                                                       Date beforeLastPeriodStart, Date beforeLastPeriodEnd) {
        return groupUserLogDOMapper.countInGroupUserFrequency(corpId, status, startDate, endDate,
                lastPeriodStart, lastPeriodEnd, beforeLastPeriodStart, beforeLastPeriodEnd);
    }


    private ScrmStatisticsIndicatorDTO getMemberInfos(String corpId, Integer status,
                                                      Date startDate, Date endDate,
                                                      Date lastPeriodStart, Date lastPeriodEnd,
                                                      Date beforeLastPeriodStart, Date beforeLastPeriodEnd) {
        return memberInfoDOMapper.countGroupMemberCount(corpId, status,
                startDate, endDate,
                lastPeriodStart, lastPeriodEnd,
                beforeLastPeriodStart, beforeLastPeriodEnd);

    }


    public EntireStatisticsIndicatorDTO getEntireRepeatCount(String corpId, Integer memberStatus, Integer friendStatus,
                                                             Date startDate, Date endDate,
                                                             Date lastPeriodStart, Date lastPeriodEnd,
                                                             Date beforeLastPeriodStart, Date beforeLastPeriodEnd) {
        return memberInfoDOMapper.statisticsRepeatCount(corpId, memberStatus, friendStatus,
                startDate, endDate,
                lastPeriodStart, lastPeriodEnd,
                beforeLastPeriodStart, beforeLastPeriodEnd);
    }


    private WeChatFriendStatisticsDTO buildResultDTOForFriend(String indicatorType, String indicator, String weekLinkRation, String monthLinkRation) {
        WeChatFriendStatisticsDTO dto = new WeChatFriendStatisticsDTO();
        dto.setIndicatorType(indicatorType);
        dto.setIndicator(indicator);
        dto.setWeekLinkRatio(weekLinkRation);
        dto.setMothLinkRation(monthLinkRation);
        return dto;
    }

    private ScrmUserStatisticsDTO buildResultDTOForScrm(String indicatorType, String indicator, String weekLinkRation, String monthLinkRation) {
        ScrmUserStatisticsDTO dto = new ScrmUserStatisticsDTO();
        dto.setIndicatorType(indicatorType);
        dto.setIndicator(indicator);
        dto.setWeekLinkRatio(weekLinkRation);
        dto.setMothLinkRation(monthLinkRation);
        return dto;
    }

    public String calculateLinkRatio(long currentPeriod, long lastPeriod) {
        BigDecimal currentPeriodNumBigDecimal = new BigDecimal(currentPeriod);
        BigDecimal lastPeriodNumBigDecimal = new BigDecimal(lastPeriod);
        if (lastPeriodNumBigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        BigDecimal divisor = lastPeriodNumBigDecimal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : lastPeriodNumBigDecimal;
        BigDecimal ratio = currentPeriodNumBigDecimal.subtract(lastPeriodNumBigDecimal).divide(divisor, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        return ratio.stripTrailingZeros().toPlainString() + "%";
    }

    public String calculateLostRatio(String currentStr, String lastStr) {
        if (StringUtils.isBlank(currentStr) || StringUtils.isBlank(lastStr)) {
            return "";
        }
        BigDecimal currentPeriod = new BigDecimal(currentStr.replace("%", "")).divide(new BigDecimal(100));
        BigDecimal lastPeriod = new BigDecimal(lastStr.replace("%", "")).divide(new BigDecimal(100));
        if (lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        BigDecimal divisor = lastPeriod.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : lastPeriod;
        BigDecimal ratio = currentPeriod.subtract(lastPeriod).divide(divisor, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        return ratio.stripTrailingZeros().toPlainString() + "%";
    }


    private FriendStatisticsIndicatorDTO getContactUsers(String corpId, Integer status,
                                                         Date startDate, Date endDate,
                                                         Date lastPeriodStart, Date lastPeriodEnd,
                                                         Date beforeLastPeriodStart, Date beforeLastPeriodEnd) {
        return contactUserToDoMapper.countContactUser(corpId, status,
                startDate, endDate,
                lastPeriodStart, lastPeriodEnd,
                beforeLastPeriodStart, beforeLastPeriodEnd);
    }

    private FriendStatisticsIndicatorDTO getContactUserLogs(String corpId, Integer status,
                                                            Date startDate, Date endDate,
                                                            Date lastPeriodStart, Date lastPeriodEnd,
                                                            Date beforeLastPeriodStart, Date beforeLastPeriodEnd) {
        return contactUserLogDOMapper.countContactUserLog(corpId, status,
                startDate, endDate,
                lastPeriodStart, lastPeriodEnd,
                beforeLastPeriodStart, beforeLastPeriodEnd);
    }


    public String calculateRatio(long numerator, long denominator) {
        BigDecimal numeratorBD = new BigDecimal(numerator);
        BigDecimal denominatorBD = new BigDecimal(denominator);
        if (denominatorBD.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        BigDecimal divisor = denominatorBD.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE : denominatorBD;
        BigDecimal ratio = numeratorBD.divide(divisor, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        return ratio.stripTrailingZeros().toPlainString() + "%";
    }
}
