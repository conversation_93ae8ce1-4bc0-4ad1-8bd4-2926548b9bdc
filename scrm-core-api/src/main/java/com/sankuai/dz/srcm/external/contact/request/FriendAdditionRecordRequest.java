package com.sankuai.dz.srcm.external.contact.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述：查询用户好友添加记录请求
 *
 * <AUTHOR>
 * @version 1.0 2024-2024/10/22-19:56
 * @description: TODO
 * @since 1.0
 */
@Data
public class FriendAdditionRecordRequest implements Serializable {
    private String corpId;

    // 用户手机号
    private String mobileNo;

    // 用户昵称
    private String nickName;

    // 跟进人微信用户Id
    private String staffId;

    // 添加时间
    private Long addTime;
}
