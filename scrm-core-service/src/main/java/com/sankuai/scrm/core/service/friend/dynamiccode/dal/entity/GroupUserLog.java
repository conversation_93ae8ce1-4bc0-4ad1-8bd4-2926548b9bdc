package com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Baby_OperatorHelper_GroupUserLog
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GroupUserLog {
    /**
     *   字段: Id
     */
    private Long id;

    /**
     *   字段: UnionId
     *   说明: 用户unionId
     */
    private String unionId;

    /**
     *   字段: UserNickName
     */
    private String userNickName;

    /**
     *   字段: JoinType
     *   说明: 进群方式 1：直接邀请入群 2：通过邀请链接入群 3：通过扫描群二维码入群
     */
    private Integer joinType;

    /**
     *   字段: GroupMemberId
     */
    private String groupMemberId;

    /**
     *   字段: ActionType
     *   说明: 1：入群 2：离开群
     */
    private Integer actionType;

    /**
     *   字段: ActionTime
     *   说明: 入群/离开群时间
     */
    private Date actionTime;

    /**
     *   字段: GroupId
     *   说明: 群id
     */
    private String groupId;

    /**
     *   字段: CorpId
     *   说明: 企业id
     */
    private String corpId;

    /**
     *   字段: CreateTime
     */
    private Date createTime;

    /**
     *   字段: UpdateTime
     */
    private Date updateTime;

    /**
     *   字段: Deleted
     *   说明: 0：未删除 1：删除
     */
    private Boolean deleted;
}