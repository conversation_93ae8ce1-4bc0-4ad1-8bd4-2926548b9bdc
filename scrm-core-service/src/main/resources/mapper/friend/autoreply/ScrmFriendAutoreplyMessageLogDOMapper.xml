<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.mapper.ScrmFriendAutoreplyMessageLogDOMapper">
  <resultMap id="BaseResultMap"
             type="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
    <result column="assistant_account" jdbcType="VARCHAR" property="assistantAccount"/>
    <result column="assistant_id" jdbcType="BIGINT" property="assistantId"/>
    <result column="sender_type" jdbcType="INTEGER" property="senderType"/>
    <result column="friend_ww_user_id" jdbcType="VARCHAR" property="friendWwUserId"/>
    <result column="friend_nickname" jdbcType="VARCHAR" property="friendNickname"/>
    <result column="friend_union_id" jdbcType="VARCHAR" property="friendUnionId"/>
    <result column="friend_avatar" jdbcType="VARCHAR" property="friendAvatar"/>
    <result column="friend_user_type" jdbcType="INTEGER" property="friendUserType"/>
    <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
    <result column="content_type" jdbcType="INTEGER" property="contentType"/>
    <result column="send_time_millis" jdbcType="BIGINT" property="sendTimeMillis"/>
    <result column="generate_duration" jdbcType="BIGINT" property="generateDuration"/>
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="generate_type" jdbcType="INTEGER" property="generateType"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
             type="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    <result column="content" jdbcType="LONGVARCHAR" property="content"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_code, assistant_account, assistant_id, sender_type, friend_ww_user_id, 
    friend_nickname, friend_union_id, friend_avatar, friend_user_type, msg_id, content_type, 
    send_time_millis, generate_duration, add_time, update_time, generate_type
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.example.ScrmFriendAutoreplyMessageLogDOExample"
          resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from scrm_friend_autoreply_message_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.example.ScrmFriendAutoreplyMessageLogDOExample"
          resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    from scrm_friend_autoreply_message_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from scrm_friend_autoreply_message_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from scrm_friend_autoreply_message_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.example.ScrmFriendAutoreplyMessageLogDOExample">
    delete from scrm_friend_autoreply_message_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </delete>
  <insert id="insert"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_friend_autoreply_message_log (business_code, assistant_account, assistant_id,
    sender_type, friend_ww_user_id, friend_nickname,
    friend_union_id, friend_avatar, friend_user_type,
    msg_id, content_type, send_time_millis,
    generate_duration, add_time, update_time,
    generate_type, content)
    values (#{businessCode,jdbcType=VARCHAR}, #{assistantAccount,jdbcType=VARCHAR}, #{assistantId,jdbcType=BIGINT},
    #{senderType,jdbcType=INTEGER}, #{friendWwUserId,jdbcType=VARCHAR}, #{friendNickname,jdbcType=VARCHAR},
    #{friendUnionId,jdbcType=VARCHAR}, #{friendAvatar,jdbcType=VARCHAR}, #{friendUserType,jdbcType=INTEGER},
    #{msgId,jdbcType=VARCHAR}, #{contentType,jdbcType=INTEGER}, #{sendTimeMillis,jdbcType=BIGINT},
    #{generateDuration,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{generateType,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_friend_autoreply_message_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessCode != null">
        business_code,
      </if>
      <if test="assistantAccount != null">
        assistant_account,
      </if>
      <if test="assistantId != null">
        assistant_id,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="friendWwUserId != null">
        friend_ww_user_id,
      </if>
      <if test="friendNickname != null">
        friend_nickname,
      </if>
      <if test="friendUnionId != null">
        friend_union_id,
      </if>
      <if test="friendAvatar != null">
        friend_avatar,
      </if>
      <if test="friendUserType != null">
        friend_user_type,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="sendTimeMillis != null">
        send_time_millis,
      </if>
      <if test="generateDuration != null">
        generate_duration,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="generateType != null">
        generate_type,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="assistantAccount != null">
        #{assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="assistantId != null">
        #{assistantId,jdbcType=BIGINT},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=INTEGER},
      </if>
      <if test="friendWwUserId != null">
        #{friendWwUserId,jdbcType=VARCHAR},
      </if>
      <if test="friendNickname != null">
        #{friendNickname,jdbcType=VARCHAR},
      </if>
      <if test="friendUnionId != null">
        #{friendUnionId,jdbcType=VARCHAR},
      </if>
      <if test="friendAvatar != null">
        #{friendAvatar,jdbcType=VARCHAR},
      </if>
      <if test="friendUserType != null">
        #{friendUserType,jdbcType=INTEGER},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="sendTimeMillis != null">
        #{sendTimeMillis,jdbcType=BIGINT},
      </if>
      <if test="generateDuration != null">
        #{generateDuration,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="generateType != null">
        #{generateType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.example.ScrmFriendAutoreplyMessageLogDOExample"
          resultType="java.lang.Long">
    select count(*) from scrm_friend_autoreply_message_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_friend_autoreply_message_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessCode != null">
        business_code = #{record.businessCode,jdbcType=VARCHAR},
      </if>
      <if test="record.assistantAccount != null">
        assistant_account = #{record.assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.assistantId != null">
        assistant_id = #{record.assistantId,jdbcType=BIGINT},
      </if>
      <if test="record.senderType != null">
        sender_type = #{record.senderType,jdbcType=INTEGER},
      </if>
      <if test="record.friendWwUserId != null">
        friend_ww_user_id = #{record.friendWwUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.friendNickname != null">
        friend_nickname = #{record.friendNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.friendUnionId != null">
        friend_union_id = #{record.friendUnionId,jdbcType=VARCHAR},
      </if>
      <if test="record.friendAvatar != null">
        friend_avatar = #{record.friendAvatar,jdbcType=VARCHAR},
      </if>
      <if test="record.friendUserType != null">
        friend_user_type = #{record.friendUserType,jdbcType=INTEGER},
      </if>
      <if test="record.msgId != null">
        msg_id = #{record.msgId,jdbcType=VARCHAR},
      </if>
      <if test="record.contentType != null">
        content_type = #{record.contentType,jdbcType=INTEGER},
      </if>
      <if test="record.sendTimeMillis != null">
        send_time_millis = #{record.sendTimeMillis,jdbcType=BIGINT},
      </if>
      <if test="record.generateDuration != null">
        generate_duration = #{record.generateDuration,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.generateType != null">
        generate_type = #{record.generateType,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update scrm_friend_autoreply_message_log
    set id = #{record.id,jdbcType=BIGINT},
    business_code = #{record.businessCode,jdbcType=VARCHAR},
    assistant_account = #{record.assistantAccount,jdbcType=VARCHAR},
    assistant_id = #{record.assistantId,jdbcType=BIGINT},
    sender_type = #{record.senderType,jdbcType=INTEGER},
    friend_ww_user_id = #{record.friendWwUserId,jdbcType=VARCHAR},
    friend_nickname = #{record.friendNickname,jdbcType=VARCHAR},
    friend_union_id = #{record.friendUnionId,jdbcType=VARCHAR},
    friend_avatar = #{record.friendAvatar,jdbcType=VARCHAR},
    friend_user_type = #{record.friendUserType,jdbcType=INTEGER},
    msg_id = #{record.msgId,jdbcType=VARCHAR},
    content_type = #{record.contentType,jdbcType=INTEGER},
    send_time_millis = #{record.sendTimeMillis,jdbcType=BIGINT},
    generate_duration = #{record.generateDuration,jdbcType=BIGINT},
    add_time = #{record.addTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    generate_type = #{record.generateType,jdbcType=INTEGER},
    content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_friend_autoreply_message_log
    set id = #{record.id,jdbcType=BIGINT},
    business_code = #{record.businessCode,jdbcType=VARCHAR},
    assistant_account = #{record.assistantAccount,jdbcType=VARCHAR},
    assistant_id = #{record.assistantId,jdbcType=BIGINT},
    sender_type = #{record.senderType,jdbcType=INTEGER},
    friend_ww_user_id = #{record.friendWwUserId,jdbcType=VARCHAR},
    friend_nickname = #{record.friendNickname,jdbcType=VARCHAR},
    friend_union_id = #{record.friendUnionId,jdbcType=VARCHAR},
    friend_avatar = #{record.friendAvatar,jdbcType=VARCHAR},
    friend_user_type = #{record.friendUserType,jdbcType=INTEGER},
    msg_id = #{record.msgId,jdbcType=VARCHAR},
    content_type = #{record.contentType,jdbcType=INTEGER},
    send_time_millis = #{record.sendTimeMillis,jdbcType=BIGINT},
    generate_duration = #{record.generateDuration,jdbcType=BIGINT},
    add_time = #{record.addTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    generate_type = #{record.generateType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByPrimaryKeySelective"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    update scrm_friend_autoreply_message_log
    <set>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="assistantAccount != null">
        assistant_account = #{assistantAccount,jdbcType=VARCHAR},
      </if>
      <if test="assistantId != null">
        assistant_id = #{assistantId,jdbcType=BIGINT},
      </if>
      <if test="senderType != null">
        sender_type = #{senderType,jdbcType=INTEGER},
      </if>
      <if test="friendWwUserId != null">
        friend_ww_user_id = #{friendWwUserId,jdbcType=VARCHAR},
      </if>
      <if test="friendNickname != null">
        friend_nickname = #{friendNickname,jdbcType=VARCHAR},
      </if>
      <if test="friendUnionId != null">
        friend_union_id = #{friendUnionId,jdbcType=VARCHAR},
      </if>
      <if test="friendAvatar != null">
        friend_avatar = #{friendAvatar,jdbcType=VARCHAR},
      </if>
      <if test="friendUserType != null">
        friend_user_type = #{friendUserType,jdbcType=INTEGER},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="sendTimeMillis != null">
        send_time_millis = #{sendTimeMillis,jdbcType=BIGINT},
      </if>
      <if test="generateDuration != null">
        generate_duration = #{generateDuration,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="generateType != null">
        generate_type = #{generateType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    update scrm_friend_autoreply_message_log
    set business_code     = #{businessCode,jdbcType=VARCHAR},
        assistant_account = #{assistantAccount,jdbcType=VARCHAR},
        assistant_id      = #{assistantId,jdbcType=BIGINT},
        sender_type       = #{senderType,jdbcType=INTEGER},
        friend_ww_user_id = #{friendWwUserId,jdbcType=VARCHAR},
        friend_nickname   = #{friendNickname,jdbcType=VARCHAR},
        friend_union_id   = #{friendUnionId,jdbcType=VARCHAR},
        friend_avatar     = #{friendAvatar,jdbcType=VARCHAR},
        friend_user_type  = #{friendUserType,jdbcType=INTEGER},
        msg_id            = #{msgId,jdbcType=VARCHAR},
        content_type      = #{contentType,jdbcType=INTEGER},
        send_time_millis  = #{sendTimeMillis,jdbcType=BIGINT},
        generate_duration = #{generateDuration,jdbcType=BIGINT},
        add_time          = #{addTime,jdbcType=TIMESTAMP},
        update_time       = #{updateTime,jdbcType=TIMESTAMP},
        generate_type     = #{generateType,jdbcType=INTEGER},
        content           = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
          parameterType="com.sankuai.scrm.core.service.friend.autoreply.intelligence.dal.entity.ScrmFriendAutoreplyMessageLogDO">
    update scrm_friend_autoreply_message_log
    set business_code     = #{businessCode,jdbcType=VARCHAR},
        assistant_account = #{assistantAccount,jdbcType=VARCHAR},
        assistant_id      = #{assistantId,jdbcType=BIGINT},
        sender_type       = #{senderType,jdbcType=INTEGER},
        friend_ww_user_id = #{friendWwUserId,jdbcType=VARCHAR},
        friend_nickname   = #{friendNickname,jdbcType=VARCHAR},
        friend_union_id   = #{friendUnionId,jdbcType=VARCHAR},
        friend_avatar     = #{friendAvatar,jdbcType=VARCHAR},
        friend_user_type  = #{friendUserType,jdbcType=INTEGER},
        msg_id            = #{msgId,jdbcType=VARCHAR},
        content_type      = #{contentType,jdbcType=INTEGER},
        send_time_millis  = #{sendTimeMillis,jdbcType=BIGINT},
        generate_duration = #{generateDuration,jdbcType=BIGINT},
        add_time          = #{addTime,jdbcType=TIMESTAMP},
        update_time       = #{updateTime,jdbcType=TIMESTAMP},
        generate_type     = #{generateType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_friend_autoreply_message_log
    (business_code, assistant_account, assistant_id, sender_type, friend_ww_user_id,
    friend_nickname, friend_union_id, friend_avatar, friend_user_type, msg_id, content_type,
    send_time_millis, generate_duration, add_time, update_time, generate_type, content
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessCode,jdbcType=VARCHAR}, #{item.assistantAccount,jdbcType=VARCHAR},
      #{item.assistantId,jdbcType=BIGINT}, #{item.senderType,jdbcType=INTEGER}, #{item.friendWwUserId,jdbcType=VARCHAR},
      #{item.friendNickname,jdbcType=VARCHAR}, #{item.friendUnionId,jdbcType=VARCHAR},
      #{item.friendAvatar,jdbcType=VARCHAR}, #{item.friendUserType,jdbcType=INTEGER},
      #{item.msgId,jdbcType=VARCHAR}, #{item.contentType,jdbcType=INTEGER}, #{item.sendTimeMillis,jdbcType=BIGINT},
      #{item.generateDuration,jdbcType=BIGINT}, #{item.addTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.generateType,jdbcType=INTEGER}, #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>