package com.sankuai.scrm.core.service.pchat.dal.activity.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_PersonalWx_ActivityConfig
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmPersonalWxActivityConfig {
    /**
     *   字段: id
     *   说明: id
     */
    private Long id;

    /**
     *   字段: activity_id
     *   说明: 活动id
     */
    private Long activityId;

    /**
     *   字段: countersign
     *   说明: 口令
     */
    private String countersign;

    /**
     *   字段: countersign_answer
     *   说明: 播报内容
     */
    private String countersignAnswer;

    /**
     *   字段: countersign_mini_prog
     *   说明: 是否开启小程序
     */
    private String countersignMiniProg;

    /**
     *   字段: countersign_mini_config
     *   说明: 小程序配置
     */
    private String countersignMiniConfig;

    /**
     *   字段: promotion_tip_classification
     *   说明: 建议分享人群类别
     */
    private String promotionTipClassification;

    /**
     *   字段: promotion_tip_group_foo
     *   说明: 群名称配置
     */
    private String promotionTipGroupFoo;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: app_id
     *   说明: app_id，用于区分业务
     */
    private String appId;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}