package com.sankuai.scrm.core.service.abtest.strategy.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.abtest.strategy.config.dto.UserIntentionStrategyConfigDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

public class UserIntentionStrategyConfigTest {

    private static final String LION_KEY = "user_intention_strategy_config";

    private static final String APP_NAME = "test-app";

    private UserIntentionStrategyConfig userIntentionStrategyConfig;

    private ConfigRepository configRepository;

    private Method initMethod;

    private MockedStatic<Lion> lionMockedStatic;

    private MockedStatic<Environment> environmentMockedStatic;

    private MockedStatic<JsonUtils> jsonUtilsMockedStatic;

    @BeforeEach
    public void setUp() throws Exception {
        // Create real instance
        userIntentionStrategyConfig = new UserIntentionStrategyConfig();
        // Create mock for ConfigRepository
        configRepository = mock(ConfigRepository.class);
        // Setup static mocks
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
        jsonUtilsMockedStatic = mockStatic(JsonUtils.class);
        // Mock Environment.getAppName()
        environmentMockedStatic.when(Environment::getAppName).thenReturn(APP_NAME);
        // Mock Lion.getConfigRepository() to return our mock
        lionMockedStatic.when(Lion::getConfigRepository).thenReturn(configRepository);
        // Set the configRepository field in the class under test using reflection
        Field configRepositoryField = UserIntentionStrategyConfig.class.getDeclaredField("configRepository");
        configRepositoryField.setAccessible(true);
        configRepositoryField.set(userIntentionStrategyConfig, configRepository);
        // Get access to the private init method
        initMethod = UserIntentionStrategyConfig.class.getDeclaredMethod("init");
        initMethod.setAccessible(true);
    }

    @AfterEach
    public void tearDown() {
        lionMockedStatic.close();
        environmentMockedStatic.close();
        jsonUtilsMockedStatic.close();
    }

    /**
     * Test init method with valid configuration
     */
    @Test
    public void testInitWithValidConfiguration() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        configDTO.setIntentionScoreThreshold(new BigDecimal("0.8"));
        configDTO.setHesitationScoreThreshold(new BigDecimal("0.5"));
        Map<String, String> templates = new HashMap<>();
        templates.put("key1", "value1");
        configDTO.setMessageTemplates(templates);
        lionMockedStatic.when(() -> Lion.getBean(eq(APP_NAME), eq(LION_KEY), eq(UserIntentionStrategyConfigDTO.class))).thenReturn(configDTO);
        jsonUtilsMockedStatic.when(() -> JsonUtils.toStr(any())).thenReturn("{\"intentionScoreThreshold\":0.8,\"hesitationScoreThreshold\":0.5}");
        // act
        initMethod.invoke(userIntentionStrategyConfig);
        // assert
        verify(configRepository).addConfigListener(eq(LION_KEY), eq(userIntentionStrategyConfig));
        // Verify the DTO was set correctly using reflection
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        UserIntentionStrategyConfigDTO actualDTO = (UserIntentionStrategyConfigDTO) field.get(userIntentionStrategyConfig);
        assertNotNull(actualDTO);
        assertEquals(new BigDecimal("0.8"), actualDTO.getIntentionScoreThreshold());
        assertEquals(new BigDecimal("0.5"), actualDTO.getHesitationScoreThreshold());
        assertEquals(templates, actualDTO.getMessageTemplates());
    }

    /**
     * Test init method with null configuration
     */
    @Test
    public void testInitWithNullConfiguration() throws Throwable {
        // arrange
        lionMockedStatic.when(() -> Lion.getBean(eq(APP_NAME), eq(LION_KEY), eq(UserIntentionStrategyConfigDTO.class))).thenReturn(null);
        jsonUtilsMockedStatic.when(() -> JsonUtils.toStr(any())).thenReturn("null");
        // act
        initMethod.invoke(userIntentionStrategyConfig);
        // assert
        verify(configRepository).addConfigListener(eq(LION_KEY), eq(userIntentionStrategyConfig));
        // Verify the DTO was set to null using reflection
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        UserIntentionStrategyConfigDTO actualDTO = (UserIntentionStrategyConfigDTO) field.get(userIntentionStrategyConfig);
        assertNull(actualDTO);
    }

    /**
     * Test init method when Lion.getBean throws an exception
     */
    @Test
    public void testInitWhenLionGetBeanThrowsException() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("Lion service unavailable");
        lionMockedStatic.when(() -> Lion.getBean(eq(APP_NAME), eq(LION_KEY), eq(UserIntentionStrategyConfigDTO.class))).thenThrow(expectedException);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> initMethod.invoke(userIntentionStrategyConfig));
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Lion service unavailable", exception.getCause().getMessage());
        // Verify that addConfigListener was not called due to the exception
        verify(configRepository, never()).addConfigListener(eq(LION_KEY), eq(userIntentionStrategyConfig));
    }

    /**
     * Test init method when addConfigListener throws an exception
     */
    @Test
    public void testInitWhenAddConfigListenerThrowsException() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        configDTO.setIntentionScoreThreshold(new BigDecimal("0.8"));
        lionMockedStatic.when(() -> Lion.getBean(eq(APP_NAME), eq(LION_KEY), eq(UserIntentionStrategyConfigDTO.class))).thenReturn(configDTO);
        jsonUtilsMockedStatic.when(() -> JsonUtils.toStr(any())).thenReturn("{\"intentionScoreThreshold\":0.8}");
        RuntimeException expectedException = new RuntimeException("Failed to add listener");
        doThrow(expectedException).when(configRepository).addConfigListener(eq(LION_KEY), eq(userIntentionStrategyConfig));
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> initMethod.invoke(userIntentionStrategyConfig));
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Failed to add listener", exception.getCause().getMessage());
    }

    /**
     * Test the configChanged method
     */
    @Test
    public void testConfigChanged() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO oldConfigDTO = new UserIntentionStrategyConfigDTO();
        oldConfigDTO.setIntentionScoreThreshold(new BigDecimal("0.7"));
        // Set initial config
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        field.set(userIntentionStrategyConfig, oldConfigDTO);
        // New config to be loaded after change
        UserIntentionStrategyConfigDTO newConfigDTO = new UserIntentionStrategyConfigDTO();
        newConfigDTO.setIntentionScoreThreshold(new BigDecimal("0.9"));
        lionMockedStatic.when(() -> Lion.getBean(eq(APP_NAME), eq(LION_KEY), eq(UserIntentionStrategyConfigDTO.class))).thenReturn(newConfigDTO);
        jsonUtilsMockedStatic.when(() -> JsonUtils.toStr(any())).thenReturn("{\"intentionScoreThreshold\":0.9}");
        ConfigEvent configEvent = mock(ConfigEvent.class);
        when(configEvent.getKey()).thenReturn(LION_KEY);
        // act
        userIntentionStrategyConfig.configChanged(configEvent);
        // assert
        UserIntentionStrategyConfigDTO actualDTO = (UserIntentionStrategyConfigDTO) field.get(userIntentionStrategyConfig);
        assertNotNull(actualDTO);
        assertEquals(new BigDecimal("0.9"), actualDTO.getIntentionScoreThreshold());
    }

    /**
     * Test getIntentionScoreThreshold method
     */
    @Test
    public void testGetIntentionScoreThreshold() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        configDTO.setIntentionScoreThreshold(new BigDecimal("0.8"));
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        field.set(userIntentionStrategyConfig, configDTO);
        // act
        BigDecimal result = userIntentionStrategyConfig.getIntentionScoreThreshold();
        // assert
        assertEquals(new BigDecimal("0.8"), result);
    }

    /**
     * Test getHesitationScoreThreshold method
     */
    @Test
    public void testGetHesitationScoreThreshold() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        configDTO.setHesitationScoreThreshold(new BigDecimal("0.5"));
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        field.set(userIntentionStrategyConfig, configDTO);
        // act
        BigDecimal result = userIntentionStrategyConfig.getHesitationScoreThreshold();
        // assert
        assertEquals(new BigDecimal("0.5"), result);
    }

    /**
     * Test getMessageTemplates method
     */
    @Test
    public void testGetMessageTemplates() throws Throwable {
        // arrange
        UserIntentionStrategyConfigDTO configDTO = new UserIntentionStrategyConfigDTO();
        Map<String, String> templates = new HashMap<>();
        templates.put("key1", "value1");
        configDTO.setMessageTemplates(templates);
        Field field = UserIntentionStrategyConfig.class.getDeclaredField("userIntentionStrategyConfigDTO");
        field.setAccessible(true);
        field.set(userIntentionStrategyConfig, configDTO);
        // act
        Map<String, String> result = userIntentionStrategyConfig.getMessageTemplates();
        // assert
        assertEquals(templates, result);
    }
}
