package com.sankuai.scrm.core.service.user.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_UserGrowth_YimeiLiveUserTag
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmUserGrowthYimeiLiveUserTag {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: user_id
     *   说明: userid
     */
    private Long userId;

    /**
     *   字段: union_id
     *   说明: union_id
     */
    private String unionId;

    /**
     *   字段: project_id
     *   说明: 项目id
     */
    private String projectId;

    /**
     *   字段: consultant_task_id
     *   说明: 所属的咨询师任务id
     */
    private Long consultantTaskId;

    /**
     *   字段: tag_id
     *   说明: 标签id
     */
    private Integer tagId;

    /**
     *   字段: status
     *   说明: 是否可用，0为可用，1为不可用
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: tag_type_id
     *   说明: 标签类型id
     */
    private Integer tagTypeId;

    /**
     *   字段: wx_id
     *   说明: 在微信群内的wxid
     */
    private String wxId;

    /**
     *   字段: action_time
     *   说明: 行为更新时间
     */
    private Date actionTime;
}