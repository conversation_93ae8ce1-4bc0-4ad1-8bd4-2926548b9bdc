package com.sankuai.scrm.core.service.pchat.service.im;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.GroupManageProcessor;
import com.sankuai.scrm.core.service.util.SwitchUtil;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupInfoResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupNotice;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.MockedStatic;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ImCommonServiceSetGroupNoticeTest {

    @InjectMocks
    private ImCommonService imCommonService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private PrivateLiveAdapterRouter adapterRouter;

    private ScrmPersonalWxGroupInfoEntity groupInfoEntity;

    private MockedStatic<SwitchUtil> switchUtilMock;

    @Before
    public void setUp() {
        groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        switchUtilMock = mockStatic(SwitchUtil.class);
        // Default behavior for switch
        switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(any())).thenReturn(false);
    }

    @After
    public void tearDown() {
        if (switchUtilMock != null) {
            switchUtilMock.close();
        }
    }

    @Test
    public void testSetGroupNoticeNormal() throws Throwable {
        // Given
        GroupInfoResponse response = new GroupInfoResponse();
        groupInfoEntity.setGroupNotice(1L);
        groupInfoEntity.setProjectId("testProjectId");
        groupInfoEntity.setAppId("testAppId");
        ScrmPersonalWxGroupMsg groupMsg = new ScrmPersonalWxGroupMsg();
        when(groupManageDomainService.queryGroupMsgById(1L)).thenReturn(groupMsg);
        switchUtilMock.when(() -> SwitchUtil.checkQyGraySwitch(Arrays.asList("testProjectId"))).thenReturn(true);
        GroupManageProcessor processor = mock(GroupManageProcessor.class);
        when(adapterRouter.getProcessor(eq(GroupManageProcessor.class), eq("testAppId"))).thenReturn(processor);
        GroupNotice groupNotice = new GroupNotice();
        when(processor.getGroupNotice(eq(groupMsg))).thenReturn(groupNotice);
        // When
        imCommonService.setGroupNotice(groupInfoEntity, response);
        // Then
        assertEquals("Group notice should be set correctly", groupNotice, response.getGroupNotice());
        verify(processor).getGroupNotice(eq(groupMsg));
    }
}
