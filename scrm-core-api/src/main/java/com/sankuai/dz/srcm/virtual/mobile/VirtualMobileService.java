package com.sankuai.dz.srcm.virtual.mobile;


import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.virtual.mobile.request.VirtualMobileRequest;
import com.sankuai.dz.srcm.virtual.mobile.response.VirtualMobileResponse;

public interface VirtualMobileService {

    RemoteResponse<VirtualMobileResponse> buildVirtualMobile(VirtualMobileRequest virtualMobileRequest);

    RemoteResponse<Boolean> removeVirtualMobileBindRelate(String phone,String virtualMobile);

}
