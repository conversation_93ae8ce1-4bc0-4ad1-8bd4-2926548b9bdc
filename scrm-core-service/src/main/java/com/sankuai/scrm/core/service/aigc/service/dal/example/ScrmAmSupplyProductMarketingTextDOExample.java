package com.sankuai.scrm.core.service.aigc.service.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmAmSupplyProductMarketingTextDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmAmSupplyProductMarketingTextDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmAmSupplyProductMarketingTextDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmAmSupplyProductMarketingTextDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmAmSupplyProductMarketingTextDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria)this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria)this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria)this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria)this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria)this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria)this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria)this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria)this;
        }

        public Criteria andUuidIsNull() {
            addCriterion("uuid is null");
            return (Criteria)this;
        }

        public Criteria andUuidIsNotNull() {
            addCriterion("uuid is not null");
            return (Criteria)this;
        }

        public Criteria andUuidEqualTo(String value) {
            addCriterion("uuid =", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidNotEqualTo(String value) {
            addCriterion("uuid <>", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidGreaterThan(String value) {
            addCriterion("uuid >", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidGreaterThanOrEqualTo(String value) {
            addCriterion("uuid >=", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidLessThan(String value) {
            addCriterion("uuid <", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidLessThanOrEqualTo(String value) {
            addCriterion("uuid <=", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidLike(String value) {
            addCriterion("uuid like", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidNotLike(String value) {
            addCriterion("uuid not like", value, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidIn(List<String> values) {
            addCriterion("uuid in", values, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidNotIn(List<String> values) {
            addCriterion("uuid not in", values, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidBetween(String value1, String value2) {
            addCriterion("uuid between", value1, value2, "uuid");
            return (Criteria)this;
        }

        public Criteria andUuidNotBetween(String value1, String value2) {
            addCriterion("uuid not between", value1, value2, "uuid");
            return (Criteria)this;
        }

        public Criteria andFridayIdIsNull() {
            addCriterion("friday_id is null");
            return (Criteria)this;
        }

        public Criteria andFridayIdIsNotNull() {
            addCriterion("friday_id is not null");
            return (Criteria)this;
        }

        public Criteria andFridayIdEqualTo(String value) {
            addCriterion("friday_id =", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdNotEqualTo(String value) {
            addCriterion("friday_id <>", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdGreaterThan(String value) {
            addCriterion("friday_id >", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdGreaterThanOrEqualTo(String value) {
            addCriterion("friday_id >=", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdLessThan(String value) {
            addCriterion("friday_id <", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdLessThanOrEqualTo(String value) {
            addCriterion("friday_id <=", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdLike(String value) {
            addCriterion("friday_id like", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdNotLike(String value) {
            addCriterion("friday_id not like", value, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdIn(List<String> values) {
            addCriterion("friday_id in", values, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdNotIn(List<String> values) {
            addCriterion("friday_id not in", values, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdBetween(String value1, String value2) {
            addCriterion("friday_id between", value1, value2, "fridayId");
            return (Criteria)this;
        }

        public Criteria andFridayIdNotBetween(String value1, String value2) {
            addCriterion("friday_id not between", value1, value2, "fridayId");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionIsNull() {
            addCriterion("generation_version is null");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionIsNotNull() {
            addCriterion("generation_version is not null");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionEqualTo(Integer value) {
            addCriterion("generation_version =", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionNotEqualTo(Integer value) {
            addCriterion("generation_version <>", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionGreaterThan(Integer value) {
            addCriterion("generation_version >", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("generation_version >=", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionLessThan(Integer value) {
            addCriterion("generation_version <", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionLessThanOrEqualTo(Integer value) {
            addCriterion("generation_version <=", value, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionIn(List<Integer> values) {
            addCriterion("generation_version in", values, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionNotIn(List<Integer> values) {
            addCriterion("generation_version not in", values, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionBetween(Integer value1, Integer value2) {
            addCriterion("generation_version between", value1, value2, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("generation_version not between", value1, value2, "generationVersion");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeIsNull() {
            addCriterion("generation_time is null");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeIsNotNull() {
            addCriterion("generation_time is not null");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeEqualTo(Date value) {
            addCriterion("generation_time =", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeNotEqualTo(Date value) {
            addCriterion("generation_time <>", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeGreaterThan(Date value) {
            addCriterion("generation_time >", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("generation_time >=", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeLessThan(Date value) {
            addCriterion("generation_time <", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeLessThanOrEqualTo(Date value) {
            addCriterion("generation_time <=", value, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeIn(List<Date> values) {
            addCriterion("generation_time in", values, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeNotIn(List<Date> values) {
            addCriterion("generation_time not in", values, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeBetween(Date value1, Date value2) {
            addCriterion("generation_time between", value1, value2, "generationTime");
            return (Criteria)this;
        }

        public Criteria andGenerationTimeNotBetween(Date value1, Date value2) {
            addCriterion("generation_time not between", value1, value2, "generationTime");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeIsNull() {
            addCriterion("available_type is null");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeIsNotNull() {
            addCriterion("available_type is not null");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeEqualTo(Integer value) {
            addCriterion("available_type =", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeNotEqualTo(Integer value) {
            addCriterion("available_type <>", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeGreaterThan(Integer value) {
            addCriterion("available_type >", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("available_type >=", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeLessThan(Integer value) {
            addCriterion("available_type <", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeLessThanOrEqualTo(Integer value) {
            addCriterion("available_type <=", value, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeIn(List<Integer> values) {
            addCriterion("available_type in", values, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeNotIn(List<Integer> values) {
            addCriterion("available_type not in", values, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeBetween(Integer value1, Integer value2) {
            addCriterion("available_type between", value1, value2, "availableType");
            return (Criteria)this;
        }

        public Criteria andAvailableTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("available_type not between", value1, value2, "availableType");
            return (Criteria)this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria)this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria)this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria)this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria)this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria)this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}