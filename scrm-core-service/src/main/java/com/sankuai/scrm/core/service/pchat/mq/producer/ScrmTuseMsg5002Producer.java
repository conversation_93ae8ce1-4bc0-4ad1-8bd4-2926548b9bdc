package com.sankuai.scrm.core.service.pchat.mq.producer;

import com.sankuai.scrm.core.service.pchat.enums.TuseMsgNTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description 涂色消息异步存储5002
 * <AUTHOR>
 * @Create On 2024/8/26 16:26
 * @Version v1.0.0
 */
@Service
@Slf4j
public class ScrmTuseMsg5002Producer extends ScrmTuseMsgProducer {

    @Override
    public String getProducerName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet("daozong", "com.sankuai.medicalcosmetology.scrm.core", "scrm.pchat.tuse.callback.msg.5002");
    }

    public boolean canProcess(String ntype) {
        return TuseMsgNTypeEnum.MT_5002.getCode().equals(ntype);
    }
}
