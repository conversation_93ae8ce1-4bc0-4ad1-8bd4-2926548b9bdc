package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.function.InvokeDetailColumnGetter;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GroupSendStrategy implements SendStrategy {
    @Resource
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;
    @Resource
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;
    @Resource
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;
    @Resource
    private ExecuteWriteDomainService executeWriteDomainService;

    @Override
    public void processSuccess(ScrmProcessOrchestrationDTO processOrchestrationDTO,
                               List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS,
                               ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO,
                               List<MsgTaskDetailResultDTO> taskDetailResultDTOS,
                               String msgId) {

        List<String> failList = taskDetailResultDTOS.stream()
                .filter(o -> o.getStatus() == MsgPushDetailStatus.FAILED.getCode())
                .map(MsgTaskDetailResultDTO::getReceiverId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(failList)) {
            ScrmAmProcessOrchestrationWxInvokeDetailDOExample wxInvokeDetailDOExample2 = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
            wxInvokeDetailDOExample2.createCriteria()
                    .andProcessOrchestrationIdEqualTo(processOrchestrationDTO.getId())
                    .andProcessOrchestrationVersionEqualTo(processOrchestrationDTO.getValidVersion())
                    .andStatusEqualTo(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                            .getValue().byteValue())
                    .andGroupIdIn(failList);
            List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOList = wxInvokeDetailDOMapper
                    .selectByExample(wxInvokeDetailDOExample2);
            ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
            wxInvokeDetailDO.setStatus(
                    ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER
                            .getValue().byteValue());
            wxInvokeDetailDOMapper.updateByExampleSelective(wxInvokeDetailDO, wxInvokeDetailDOExample2);

            ScrmAmProcessOrchestrationExecuteLogDO updateLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
            updateLogDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE
                    .getValue().byteValue());
            ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
            executeLogDOExample.createCriteria()
                    .andIdIn(wxInvokeDetailDOList.stream()
                            .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getExecuteLogId)
                            .collect(Collectors.toList()));
            updateLogDO.setUpdateTime(new Date());
            executeLogDOMapper.updateByExampleSelective(updateLogDO, executeLogDOExample);
        }

        ScrmAmProcessOrchestrationWxInvokeDetailDOExample wxInvokeDetailDOExample1 = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
        wxInvokeDetailDOExample1.createCriteria()
                .andIdIn(wxInvokeDetailDOS.stream().map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getId)
                        .collect(Collectors.toList()))
                .andStatusEqualTo(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                        .getValue().byteValue());
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND
                .getValue().byteValue());
        wxInvokeDetailDO.setInvokeLogId(wxInvokeLogDO.getId());
        wxInvokeDetailDOMapper.updateByExampleSelective(wxInvokeDetailDO, wxInvokeDetailDOExample1);

        updateExecuteLogSender(wxInvokeDetailDOS, taskDetailResultDTOS);
    }

    private void updateExecuteLogSender(List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS,
                                        List<MsgTaskDetailResultDTO> taskDetailResultDTOS) {
        if (CollectionUtils.isEmpty(taskDetailResultDTOS) || CollectionUtils.isEmpty(wxInvokeDetailDOS)) {
            return;
        }

        Map<String, List<MsgTaskDetailResultDTO>> sender2result = taskDetailResultDTOS.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSender()))
                .collect(Collectors.groupingBy(MsgTaskDetailResultDTO::getSender));

        if (MapUtils.isEmpty(sender2result)) {
            return;
        }

        for (Map.Entry<String, List<MsgTaskDetailResultDTO>> entry : sender2result.entrySet()) {
            String sender = entry.getKey();
            List<MsgTaskDetailResultDTO> result = entry.getValue();

            if (CollectionUtils.isEmpty(result)) {
                continue;
            }

            Set<String> receivers = result.stream().map(MsgTaskDetailResultDTO::getReceiverId).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(receivers)) {
                continue;
            }

            List<Long> executeLogIds = wxInvokeDetailDOS.stream()
                    .filter(o -> receivers.contains(o.getGroupId()))
                    .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getExecuteLogId)
                    .collect(Collectors.toList());

            executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        }
    }

    @Override
    public <R> void processFailure(List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS,
                                   MsgPushRequest request,
                                   MsgPushResponse<R> response) {
        log.error("GroupSendStrategy dealSendFailed fail, request:{}, response:{}", JsonUtils.toStr(request), JsonUtils.toStr(response));
        List<Long> executeLogIds = totalInvokeDetailDOS.stream()
                .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getExecuteLogId)
                .collect(Collectors.toList());

        executeWriteDomainService.updateExecuteLogStatusByIds(
                ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE, executeLogIds
        );
    }

    @Override
    public InvokeDetailColumnGetter getColumnGetter() {
        return ScrmAmProcessOrchestrationWxInvokeDetailDO::getGroupId;
    }
}
