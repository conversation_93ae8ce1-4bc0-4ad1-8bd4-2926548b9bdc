package com.sankuai.scrm.core.service.pchat.callback;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.scrm.core.service.infrastructure.util.SpringUtil;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxChatLogWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dto.CallbackDTO;
import com.sankuai.scrm.core.service.pchat.dto.robotFunction.SendPrivateChatMessagesByUserDTO;
import com.sankuai.scrm.core.service.pchat.notify.ClairvoyanceService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RobotFunctionCbServiceImplDo5001Test {

    @InjectMocks
    private RobotFunctionCbServiceImpl robotFunctionCbService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Mock
    private ClairvoyanceService clairvoyanceService;

    private String validStrContext;

    private String invalidStrContext;

    private MockedStatic<SpringUtil> mockedSpringUtil;

    @BeforeEach
    public void setUp() {
        // Mock static SpringUtil.getBean
        mockedSpringUtil = mockStatic(SpringUtil.class);
        mockedSpringUtil.when(() -> SpringUtil.getBean(ClairvoyanceService.class)).thenReturn(clairvoyanceService);
        // Set up valid callback data
        CallbackDTO<SendPrivateChatMessagesByUserDTO> validCallbackDTO = new CallbackDTO<>();
        validCallbackDTO.setNType(5001);
        validCallbackDTO.setVcMerchantNo("merchant123");
        validCallbackDTO.setVcRobotSerialNo("robot123");
        validCallbackDTO.setVcSerialNo("serial123");
        // Success result
        validCallbackDTO.setNResult(0);
        validCallbackDTO.setNTimeStamp(System.currentTimeMillis());
        SendPrivateChatMessagesByUserDTO dto = new SendPrivateChatMessagesByUserDTO();
        dto.setVcMsgSerialNo("msg123");
        dto.setVcFromWxUserSerialNo("from123");
        dto.setVcToWxUserSerialNo("to123");
        dto.setVcContent("test content");
        dto.setNMsgType(1);
        dto.setDtMsgTime(String.valueOf(System.currentTimeMillis()));
        validCallbackDTO.setData(dto);
        validStrContext = JSONObject.toJSONString(validCallbackDTO);
        invalidStrContext = "invalid json string";
        // Set up default mock behaviors
        ScrmPersonalWxChatLogWithBLOBs chatLog = new ScrmPersonalWxChatLogWithBLOBs();
        chatLog.setId(1L);
    }

    @AfterEach
    public void tearDown() {
        if (mockedSpringUtil != null) {
            mockedSpringUtil.close();
        }
    }

    @Test
    public void testDo5001_ParseFailure() throws Throwable {
        String validStrContext = "{\"vcMerchantNo\":\"202309150012561\",\"vcRobotWxId\":\"wxid_xv3rnx1a797f22\",\"vcRobotSerialNo\":\"FA2A5CC8FA35F23FB301D3B7B0BE9863\",\"vcSerialNo\":\"20250819004001687293910492055\",\"nType\":5001,\"nResult\":1,\"vcResult\":\"SUCCESS\",\"nTimeStamp\":1755535201689,\"Data\":{\"vcMsgSerialNo\":\"20250819004001687293910492055\",\"vcMsgId\":\"5806902582763346387\",\"vcFromWxUserSerialNo\":\"C196266F837D14E0B693F961BEE37B66\",\"vcFromWxUserWxId\":\"weixin\",\"vcToWxUserSerialNo\":\"FA2A5CC8FA35F23FB301D3B7B0BE9863\",\"vcToWxUserWxId\":\"wxid_xv3rnx1a797f22\",\"nMsgType\":4001,\"vcContent\":\"<msg>\\n        <appmsg appid=\\\"\\\" sdkver=\\\"0\\\">\\n                <title><![CDATA[微信安全提醒]]></title>\\n                <des><![CDATA[该微信号在 2025-08-19 00:40 生成的群二维码已经失效。\\n\\n查看\\\"详情\\\"可了解和反馈更多细节。]]></des>\\n                <action></action>\\n                <type>5</type>\\n                <showtype>1</showtype>\\n                <content><![CDATA[]]></content>\\n                <url><![CDATA[http://mp.weixin.qq.com/s/_CX-GyypXy-9DF1edBpTvQ]]></url>\\n                <lowurl><![CDATA[]]></lowurl>\\n                <appattach><totallen>0</totallen><attachid></attachid><fileext></fileext></appattach>\\n                <extinfo></extinfo>\\n                <mmreader>\\n                        <category type=\\\"0\\\" count=\\\"1\\\">\\n                        <name><![CDATA[微信团队]]></name>\\n                        <topnew>\\n                                <cover><![CDATA[]]></cover><width>0</width><height>0</height>\\n                                <digest><![CDATA[该微信号在 2025-08-19 00:40 生成的群二维码已经失效。\\n\\n查看\\\"详情\\\"可了解和反馈更多细节。]]></digest>\\n                        </topnew>\\n                        <item>\\n                                <itemshowtype>4</itemshowtype>\\n                                <title><![CDATA[微信安全提醒]]></title>\\n                                <url><![CDATA[http://mp.weixin.qq.com/s/_CX-GyypXy-9DF1edBpTvQ]]></url><shorturl><![CDATA[]]></shorturl><longurl><![CDATA[]]></longurl>\\n                                <pub_time>1755535201</pub_time>\\n                                <cover><![CDATA[]]></cover><tweetid></tweetid>\\n                                <digest><![CDATA[该微信号在 2025-08-19 00:40 生成的群二维码已经失效。\\n\\n查看\\\"详情\\\"可了解和反馈更多细节。]]></digest>\\n                                <fileid>0</fileid>\\n                                <sources>\\n                                        <source>\\n                                                <name><![CDATA[微信团队]]></name>\\n                                        </source>\\n                                </sources>\\n                                <styles>\\n                                        <topColor><![CDATA[#000000]]></topColor>\\n                                <style>\\n                                        <range><![CDATA[{5,13}]]></range><font><![CDATA[s]]></font>\\n                                        <color><![CDATA[#000000]]></color>\\n                                </style>\\n                                <style>\\n                                        <range><![CDATA[{27,3}]]></range><font><![CDATA[s]]></font><color><![CDATA[#000000]]></color>\\n                                </style>\\n                                        <style>\\n                                                <range><![CDATA[{52,25}]]></range><font><![CDATA[s]]></font><color><![CDATA[#000000]]></color>\\n                                        </style>\\n                                </styles>\\n                                <native_url></native_url>\\n                                \\n                        </item>\\n                        </category>\\n                        <publisher>\\n                                <username><![CDATA[weixin]]></username>\\n                                <nickname><![CDATA[微信团队]]></nickname>\\n                        </publisher>\\n                </mmreader>\\n                <thumburl><![CDATA[]]></thumburl>\\n                <template_id><![CDATA[7iOD64puv-rLJy8P04XXvMBNVyiw6FK7pqKBlWPcwI]]></template_id>\\n        </appmsg>\\n        <fromusername><![CDATA[weixin]]></fromusername>\\n        <appinfo>\\n        <version>0</version><appname><![CDATA[微信团队]]></appname><isforceupdate>1</isforceupdate>\\n        </appinfo>\\n</msg>\",\"nVoiceTime\":0,\"vcShareTitle\":\"\",\"vcShareDesc\":\"\",\"vcShareUrl\":\"\",\"dtMsgTime\":\"2025-08-19 00:40:01.000\",\"nPlatformMsgType\":10,\"vcRelaSerialNo\":\"\",\"nMsgNum\":-1,\"vcDownFileSerialNo\":\"\",\"nFileSize\":0.0,\"vcObjectData\":{\"nObjectId\":\"\",\"vcObjectNonceId\":\"\"},\"vcQuoteMsgId\":\"\",\"vcCardId\":\"\",\"nSortId\":0,\"vcContentXml\":null,\"fileList\":[],\"nEmojiLen\":0,\"vcEmojiMd5\":\"\",\"vcMd5\":\"\",\"IsMasterImg\":true,\"collection_serial_no\":\"\",\"thumbnail_url\":\"\",\"isOfficialAccountMssageTotice\":false}}";
        // act
        when(personalWxCommonService.isMsgRepeat(anyString(),anyString(),anyString(),anyString())).thenReturn(false);
        robotFunctionCbService.do5001(validStrContext);
    }

}
