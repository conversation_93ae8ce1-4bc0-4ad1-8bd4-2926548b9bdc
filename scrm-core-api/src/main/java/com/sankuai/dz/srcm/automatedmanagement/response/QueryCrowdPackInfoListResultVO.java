package com.sankuai.dz.srcm.automatedmanagement.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询人群包信息列表返回值
 *
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/17
 */
@Data
public class QueryCrowdPackInfoListResultVO implements Serializable {

    /**
     *   字段: id
     *   说明: 自增 人群包主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 人群包名称
     */
    private String name;

    /**
     *   字段: remark
     *   说明: 人群包说明
     */
    private String remark;

    /**
     *   字段: type
     *   说明: 人群包类型(0:零时包 1:定时更新包 2:手动更新包)
     */
    private Integer type;

    /**
     *   字段: valid_pack_version
     *   说明: 目前生效版本
     */
    private String validPackVersion;

    /**
     *   字段: creator_id
     *   说明: 创建者id
     */
    private String creatorId;

    /**
     *   字段: last_updater_id
     *   说明: 最后更新者id
     */
    private String lastUpdaterId;

    /**
     *   字段: app_id
     *   说明: 业务id
     */
    private String appId;

    /**
     * 当前用户数
     */
    private int crowdCount = 0;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private String updateTime;

    /**
     * 执行状态
     */
    private Integer runStatus = 0;
}
