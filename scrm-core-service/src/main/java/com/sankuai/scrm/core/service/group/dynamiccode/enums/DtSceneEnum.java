package com.sankuai.scrm.core.service.group.dynamiccode.enums;

import lombok.Getter;

@Getter
public enum DtSceneEnum {

    liren(1, "丽人社群", "ww9549b0976e58e955"),
    <PERSON><PERSON><PERSON>(2, "休娱社群", "wwbfed8aef653653a3"),
    q<PERSON><PERSON>(3, "亲子社群", "ww69823411e366c641"),
    a<PERSON><PERSON><PERSON><PERSON>(4, "爱漂亮直播社群", "ww07ad5f7a1a72d5be"),
    medical(5, "医疗社群", "wwf8d82785fa3b9b87"),
    zong<PERSON>(6, "综发社群", "ww975965cae5f5138f"),
    pedicure_bathe_ktv(7, "休娱(足洗K)", "ww6aca478daf1acad8");

    private final int code;
    private final String desc;
    private final String corpId;

    DtSceneEnum(int code, String desc, String corpId) {
        this.code = code;
        this.desc = desc;
        this.corpId = corpId;
    }

    public static DtSceneEnum fromCode(String corpId) {
        for (DtSceneEnum enumValue : DtSceneEnum.values()) {
            if (enumValue.corpId.equals(corpId)) {
                return enumValue;
            }
        }
        return null;
    }

}
