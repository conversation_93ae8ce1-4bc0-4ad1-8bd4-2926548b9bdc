<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserCustomMapper">
    <select id="batchCountFriendNumByStaffId" resultType="com.sankuai.scrm.core.service.external.contact.bo.StaffFriendNumBO">
        SELECT StaffId as staffId, count(DISTINCT ExternalUserId) as friendNum FROM Baby_OperatorHelper_ContactUser
        WHERE CorpId=#{corpId}
        <if test="list!=null and list.size()>0">
            AND StaffId IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND Status=1
        GROUP BY StaffId;
    </select>
</mapper>