package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupCreateRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMemberListRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsgListRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMemberInfoResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMsgListResponse;
import com.sankuai.scrm.core.service.infrastructure.util.SpringUtil;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionDelegate;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import com.sankuai.scrm.core.service.pchat.adapter.router.PrivateLiveAdapterRouter;
import com.sankuai.scrm.core.service.pchat.adapter.service.MemberProcessor;
import com.sankuai.scrm.core.service.pchat.bo.WxTaskBo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxMsgTaskContent;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.domain.BizIdentificationService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupMsgDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxLiveGroupConfigDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmRestrictionIdentificationDomainService;
import com.sankuai.scrm.core.service.pchat.dto.ScrmPersonalWxGroupMemberInfoDTO;
import com.sankuai.scrm.core.service.pchat.service.ScrmBatchCreateGroupService;
import com.sankuai.scrm.core.service.pchat.service.ScrmLoginService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxSendMessageService;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import com.sankuai.scrm.core.service.util.SwitchUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxGroupManageServiceImplCreateGroupSetTest {

    @InjectMocks
    private ScrmPersonalWxGroupManageServiceImpl scrmPersonalWxGroupManageService;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private ScrmPersonalWxLiveGroupConfigDomainService personalWxLiveGroupConfigDomainService;

    @Mock
    private ScrmBatchCreateGroupService scrmBatchCreateGroupService;

    @Mock
    private ScrmPersonalWxSendMessageService personalWxSendMessageService;

    @Mock
    private ScrmLoginService scrmLoginService;

    /**
     * 测试群成员列表查询，当请求参数为空时
     */
    @Mock
    private ScrmPersonalWxGroupInfoEntityMapper personalWxGroupInfoEntityMapper;

    @Mock
    private ScrmRestrictionIdentificationDomainService scrmRestrictionIdentificationDomainService;

    @Mock
    private PrivateLiveAdapterRouter adapterRouter;

    @Mock
    private MemberProcessor memberProcessor;

    @Mock
    private CheckPermissionDelegate checkPermissionDelegate;

    /**
     * 测试群成员列表查询，当请求参数为空时
     */
    private GroupMemberListRequest request;

    private MockedStatic<SwitchUtil> switchUtilMockedStatic;

    private MockedStatic<SpringUtil> springUtilMockedStatic;

    private MockedStatic<CheckPermissionUtil> checkPermissionUtilMockedStatic;

    @Mock
    private ScrmPersonalWxGroupMsgDomainService personalWxGroupMsgDomainService;

    @Mock
    private WebcastService webcastService;

    @Mock
    private BizIdentificationService bizIdentificationService;

    @Before
    public void setUp() {
        request = spy(new GroupMemberListRequest());
        request.setGroupIds(Collections.singletonList(1L));
        request.setAppId("testAppId");
        switchUtilMockedStatic = mockStatic(SwitchUtil.class);
        springUtilMockedStatic = mockStatic(SpringUtil.class);
        checkPermissionUtilMockedStatic = mockStatic(CheckPermissionUtil.class);
        springUtilMockedStatic.when(() -> SpringUtil.getBean(CheckPermissionDelegate.class)).thenReturn(checkPermissionDelegate);
        // Mock PagerList for member query
        PagerList<ScrmPersonalWxGroupMemberInfoDTO> memberList = new PagerList<>();
        memberList.setData(new ArrayList<>());
        when(groupManageDomainService.queryGroupAndFissionMemberList(any(), any())).thenReturn(memberList);
        // Mock adapter router
        when(adapterRouter.getProcessor(eq(MemberProcessor.class), any())).thenReturn(memberProcessor);
        // Mock checkUserPermission to do nothing by default
        checkPermissionUtilMockedStatic.when(() -> CheckPermissionUtil.checkUserPermission(any(), any())).then(invocation -> null);
        // Mock empty maps for message and blacklist queries
        when(personalWxSendMessageService.queryLatestSentMsg(any(), any(), any())).thenReturn(Collections.emptyMap());
        when(scrmRestrictionIdentificationDomainService.getBlacklistUser(any(), any())).thenReturn(Collections.emptyList());
    }

    @After
    public void tearDown() {
        if (switchUtilMockedStatic != null) {
            switchUtilMockedStatic.close();
        }
        if (springUtilMockedStatic != null) {
            springUtilMockedStatic.close();
        }
        if (checkPermissionUtilMockedStatic != null) {
            checkPermissionUtilMockedStatic.close();
        }
    }

    private GroupCreateRequest createRequest() {
        GroupCreateRequest request = new GroupCreateRequest();
        request.setGroupName("testGroup");
        request.setGroupCount(1);
        request.setWebcastId("testWebcastId");
        return request;
    }

    private ScrmPersonalWxLiveGroupConfig createMockConfig() {
        ScrmPersonalWxLiveGroupConfig mockConfig = new ScrmPersonalWxLiveGroupConfig();
        mockConfig.setProjectId("testWebcastId");
        return mockConfig;
    }

    private WxTaskBo createMockWxTaskBo() {
        WxTaskBo bo = new WxTaskBo();
        bo.setMsgId(1L);
        bo.setMsgContentId(1L);
        bo.setProjectId("test_project");
        bo.setReceiveWxSerialNo("test_serial_no");
        return bo;
    }

    private ScrmPersonalWxMsgTaskContent createMockTaskContent() {
        ScrmPersonalWxMsgTaskContent content = new ScrmPersonalWxMsgTaskContent();
        content.setId(1L);
        content.setSendType("test_type");
        return content;
    }

    /**
     * 测试群成员列表查询，当请求参数为空时
     */
    private PagerList<WxTaskBo> createMockPagerList(List<WxTaskBo> data) {
        PagerList<WxTaskBo> pagerList = new PagerList<>();
        pagerList.setTotal(data.size());
        pagerList.setData(data);
        return pagerList;
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCreateGroupSetInvalidParam() throws Throwable {
        GroupCreateRequest request = createRequest();
        request.setGroupName("");
        scrmPersonalWxGroupManageService.createGroupSet(request);
    }

    /**
     * 测试参数校验失败的情况
     */
    @Test
    public void testGroupMemberList_InvalidParam() throws Throwable {
        // arrange
        doThrow(new IllegalArgumentException("Invalid param")).when(request).validParam();
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> scrmPersonalWxGroupManageService.groupMemberList(request));
    }

    /**
     * 测试查询群组信息失败的情况
     */
    @Test
    public void testGroupMemberList_NoGroupInfoFound() throws Throwable {
        // arrange
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.emptyList());
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> scrmPersonalWxGroupManageService.groupMemberList(request));
    }

    /**
     * 测试权限校验失败的情况
     */
    @Test
    public void testGroupMemberList_PermissionCheckFailed() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        checkPermissionUtilMockedStatic.when(() -> CheckPermissionUtil.checkUserPermission(any(), any())).thenThrow(new SecurityException("No permission"));
        // act & assert
        assertThrows(SecurityException.class, () -> scrmPersonalWxGroupManageService.groupMemberList(request));
    }

    /**
     * 测试启用企微逻辑开关的情况
     */
    @Test
    public void testGroupMemberList_QyGraySwitchEnabled() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        // Mock group sets
        HashSet<String> groupSets = new HashSet<>();
        groupSets.add("testGroup");
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试未启用企微逻辑开关的情况
     */
    @Test
    public void testGroupMemberList_QyGraySwitchDisabled() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(false);
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试包含裂变成员的情况
     */
    @Test
    public void testGroupMemberList_WithFissionMember() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        request.setIsFissionMember(true);
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试转移群主场景的情况
     */
    @Test
    public void testGroupMemberList_TransferGroupOwnerScene() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        request.setScene(1);
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试查询最新消息失败的情况
     */
    @Test
    public void testGroupMemberList_LatestSentMsgQueryFailed() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        when(personalWxSendMessageService.queryLatestSentMsg(any(), any(), any())).thenReturn(Collections.emptyMap());
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试查询风险用户失败的情况
     */
    @Test
    public void testGroupMemberList_RestrictedUserQueryFailed() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGroupMemberList_NormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setProjectId("projectId");
        when(groupManageDomainService.queryGroupByIds(any())).thenReturn(Collections.singletonList(groupInfo));
        switchUtilMockedStatic.when(() -> SwitchUtil.checkQyGraySwitch(anyList())).thenReturn(true);
        when(personalWxSendMessageService.queryLatestSentMsg(any(), any(), any())).thenReturn(Collections.emptyMap());
        // act
        PageRemoteResponse<GroupMemberInfoResponse> response = scrmPersonalWxGroupManageService.groupMemberList(request);
        // assert
        assertNotNull(response);
        assertEquals(0, response.getData().size());
    }

    /**
     * Test empty result handling
     */
    @Test
    public void testGroupMsgTaskListWithEmptyResult() throws Throwable {
        // arrange
        GroupMsgListRequest request = new GroupMsgListRequest();
        request.setWebcastId("test_webcast_id");
        request.setMsgState("1");
        PagerList<WxTaskBo> emptyPagerList = createMockPagerList(new ArrayList<>());
        when(groupManageDomainService.queryMsgTaskList(any(), any(), any(), any(), anyInt(), anyInt(), any())).thenReturn(emptyPagerList);
        // act
        PageRemoteResponse<GroupMsgListResponse> response = scrmPersonalWxGroupManageService.groupMsgTaskList(request);
        // assert
        assertNotNull(response.getData());
        assertEquals(0, response.getData().size());
    }

    /**
     * Test message state query path
     */
    @Test
    public void testGroupMsgTaskListWithMsgState() throws Throwable {
        // arrange
        GroupMsgListRequest request = new GroupMsgListRequest();
        request.setWebcastId("test_webcast_id");
        request.setMsgState("1");
        List<WxTaskBo> taskBos = Arrays.asList(createMockWxTaskBo());
        PagerList<WxTaskBo> pagerList = createMockPagerList(taskBos);
        when(groupManageDomainService.queryMsgTaskList(any(), any(), any(), eq("1"), anyInt(), anyInt(), any())).thenReturn(pagerList);
        List<ScrmPersonalWxMsgTaskContent> contents = Arrays.asList(createMockTaskContent());
        when(groupManageDomainService.queryMsgTaskContentByIds(any())).thenReturn(contents);
        when(webcastService.queryWebcastNameByIds(any())).thenReturn(new HashMap<>());
        // act
        PageRemoteResponse<GroupMsgListResponse> response = scrmPersonalWxGroupManageService.groupMsgTaskList(request);
        // assert
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        // Verify interactions
        verify(groupManageDomainService).queryMsgTaskList(any(), any(), any(), eq("1"), anyInt(), anyInt(), any());
        verify(groupManageDomainService).queryMsgTaskContentByIds(anyList());
        verify(webcastService).queryWebcastNameByIds(anyList());
    }
}
