package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/19
 * @Description:群主被转移者类型
 */
@Getter
public enum GroupTransferTypeEnum {

    MANUAL(1, "手动"),

    CONSULTANT(2, "转移至咨询师");

    private final Integer code;

    private final String desc;

    GroupTransferTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean contains(Integer code) {
        for (GroupTransferTypeEnum groupTransferTypeEnum : GroupTransferTypeEnum.values()) {
            if (groupTransferTypeEnum.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

}
