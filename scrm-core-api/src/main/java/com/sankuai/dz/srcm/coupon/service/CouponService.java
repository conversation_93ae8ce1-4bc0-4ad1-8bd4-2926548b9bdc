package com.sankuai.dz.srcm.coupon.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.coupon.dto.CouponEventDTO;
import com.sankuai.dz.srcm.coupon.dto.CouponInfoDTO;
import com.sankuai.dz.srcm.coupon.request.CouponUpdateRequest;

public interface CouponService {

    RemoteResponse<Boolean> updateCoupon(CouponUpdateRequest request);

    RemoteResponse<CouponInfoDTO> queryCoupon(Long id);

    PageRemoteResponse<CouponInfoDTO> queryCouponList(String appId, Integer page, Integer pageSize);

    RemoteResponse<Boolean> checkCouponQualification(String appId, Long mtUserId, Integer pageLocation);

    RemoteResponse<Boolean> checkCouponQualification(Long couponId, Long mtUserId, Integer pageLocation);

    RemoteResponse<Boolean> logCouponEvent(CouponEventDTO couponEventDTO);
}
