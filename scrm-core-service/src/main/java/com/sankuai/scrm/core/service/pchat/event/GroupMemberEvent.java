package com.sankuai.scrm.core.service.pchat.event;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;

import java.util.List;

/**
 * @Description 群成员新增事件
 * <AUTHOR>
 * @Create On 2024/1/3 19:43
 * @Version v1.0.0
 */
public class GroupMemberEvent extends ImEvent {

    private List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities;
    private String eventSource;//4501 4502

    public GroupMemberEvent(Object source) {
        super(source);
        this.groupMemberInfoEntities = (List<ScrmPersonalWxGroupMemberInfoEntity>) source;
    }

    public List<ScrmPersonalWxGroupMemberInfoEntity> getGroupMemberInfoEntities() {
        return groupMemberInfoEntities;
    }

    public void setGroupMemberInfoEntities(List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities) {
        this.groupMemberInfoEntities = groupMemberInfoEntities;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public String getEventSource() {
        return eventSource;
    }
}
