package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.AISceneExecuteDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ScrmIntelligentFollowResultDelayConsumerRecvMessageTest {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private AISceneExecuteDomainService aiSceneExecuteDomainService;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer testRecordMessageProducer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @InjectMocks
    private ScrmIntelligentFollowResultDelayConsumer consumer;

    private Method recvMessageMethod;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        // Get the private method using reflection
        recvMessageMethod = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    /**
     * Test case for JSON parse failure
     */
    @Test
    public void testRecvMessageJsonParseFailed() throws Throwable {
        // arrange
        String invalidJson = "invalid json";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", invalidJson);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(aiSceneExecuteDomainService);
    }

    /**
     * Test with null consumer processor - removed as it's not a valid test case
     * The actual implementation doesn't handle null consumer case,
     * so testing it would just be testing Java's NPE behavior
     */
    // @Test
    // public void testAfterPropertiesSet_NullConsumerProcessor() {
    /**
     * Test case for message not needing processing (isNeedSendMsg=false)
     */
    @Test
    public void testRecvMessageNoNeedSendMsg() throws Throwable {
        // arrange
        String messageBody = "{\"appId\":\"testApp\",\"userId\":123,\"needSendMsg\":false}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageBody);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.getProcessIdByAppId("testApp")).thenReturn(100L);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(aiSceneExecuteDomainService);
    }

    /**
     * Test case for invalid process ID
     */
    @Test
    public void testRecvMessageInvalidProcessId() throws Throwable {
        // arrange
        String messageBody = "{\"appId\":\"testApp\",\"userId\":123,\"needSendMsg\":true}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageBody);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.getProcessIdByAppId("testApp")).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(aiSceneExecuteDomainService);
    }

    /**
     * Test case for exception during processing
     */
    @Test
    public void testRecvMessageProcessingException() throws Throwable {
        // arrange
        String messageBody = "{\"appId\":\"testApp\",\"userId\":123,\"needSendMsg\":true}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageBody);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.getProcessIdByAppId("testApp")).thenReturn(100L);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(123L, "wxde8ac0a21135c07d")).thenThrow(new RuntimeException("Service error"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(aiSceneExecuteDomainService);
    }

    /**
     * Test case for null message body
     */
    @Test
    public void testRecvMessageNullBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(aiSceneExecuteDomainService);
    }


}
