<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmRestrictionGroupCustomMapper">
 <select id="countRestrictionUserByProject" resultType="java.lang.Long">
   SELECT count(1) restriction_user from Scrm_PersonalWx_GroupInfo g
   INNER JOIN Scrm_PersonalWx_GroupMemberInfo m on m.group_id=g.chat_room_wx_serial_no
   INNER JOIN scrm_restriction_user u on u.wx_id=m.wx_id
   where g.project_id=#{projectId} and g.is_visible='1' and g.deleted=false and g.status='2'
   and m.status='0' and u.is_delete='0' and u.identity_type='1'
 </select>
</mapper>