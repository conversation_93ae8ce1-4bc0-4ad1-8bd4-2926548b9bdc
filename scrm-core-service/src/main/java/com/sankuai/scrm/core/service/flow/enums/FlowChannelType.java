package com.sankuai.scrm.core.service.flow.enums;

import lombok.Getter;

@Getter
public enum FlowChannelType {

    YHM("yhm", "优惠码"),
    DTM("dtm", "地推码"),
    QB("qb", "全部渠道");

    private final String channel;
    private final String desc;

    FlowChannelType(String channel, String desc) {
        this.channel = channel;
        this.desc = desc;
    }

    public static FlowChannelType fromChannel(String channel) {
        for (FlowChannelType channelType : FlowChannelType.values()) {
            if (channelType.getChannel().equals(channel)) {
                return channelType;
            }
        }
        return QB;
    }
}
