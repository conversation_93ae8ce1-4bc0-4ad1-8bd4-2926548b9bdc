package com.sankuai.scrm.core.service.group.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: wechat_group_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GroupInfoEntity {
    /**
     *   字段: id
     *   说明: 自增id
     */
    private Integer id;

    /**
     *   字段: corp_id
     *   说明: 企业id
     */
    private String corpId;

    /**
     *   字段: org_id
     *   说明: 部门id
     */
    private Long orgId;

    /**
     *   字段: group_id
     *   说明: 群id
     */
    private String groupId;

    /**
     *   字段: group_name
     *   说明: 群名字
     */
    private String groupName;

    /**
     *   字段: group_notice
     *   说明: 群公告
     */
    private String groupNotice;

    /**
     *   字段: member_count
     */
    private Integer memberCount;

    /**
     *   字段: owner
     */
    private String owner;

    /**
     *   字段: add_time
     *   说明: 更新时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: deleted
     *   说明: 是否删除 0 未删除
     */
    private Boolean deleted;

    /**
     *   字段: admin_list
     */
    private String adminList;
}