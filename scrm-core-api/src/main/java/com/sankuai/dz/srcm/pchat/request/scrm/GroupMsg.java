package com.sankuai.dz.srcm.pchat.request.scrm;


import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * @Description 当发送消息是文本时msgContent为文本信息 必填
 * 当发送消息是图片时msgContent 为图片链接 必填 ；
 * 当发送消息是视频时msgContent为视频的图片 必填，href为视频链接地址 必填，voticeTime视频时长 必填；
 * 当发送消息是图文时msgContent为文章图片链接地址 必填，href为文章链接地址 必填 description 为文章描述 title为标题 必填 ；
 * 当发送消息是小程序时msgContent为小程序路径 必必填 href为小程序图片 必填，title为小程序标题 必填 ，description为小程序描述 必填
 * <AUTHOR>
 * @Create On 2023/11/14 16:27
 * @@Version v1.0.0
 */
public class GroupMsg implements Serializable {

    /**
     * 消息id
     */
    private String uuid;
    /**
     * @Description
     * <AUTHOR>
     * @Create On 2023/11/14 16:27
     * @@Version v1.0.0
     */

    /**
     * 消息类型 1 文本 2图片  3 小程序 4图文 5视频
     */
    private String msgType;

    /**
     * 小程序子类型
     *
     * @see @ImMiniProgPageType
     * 1 直播间 2 个人中心 3 裂变活动 4 商品 5 社群红包
     */
    private String subMsgType;

    /**
     * 涂色消息类型
     */
    private Integer tuseMsgType;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 1 提示所有人 0 否 2部分人
     */
    private Integer isHit;
    /**
     * @人在文本的所在位置 0 文本开始位置 1文本结束位置 2任意位置
     */
    private Integer atLocation;

    /**
     * 消息间隔 单位秒
     */
    private Integer msgInterval;

    /**
     * 描述
     */
    private String description;

    /**
     * 消息链接，参照下面备注
     */
    private String href;

    /**
     * 标题
     */
    private String title;

    /**
     * 视频音长，如果是视频的话必填
     */
    private Integer voiceTime;

    private List<String> hitUsers;
    /**
     * 直播id
     */
    private String webcastId;

    private Boolean isDiscretization = true;

    public String getUuid() {
        if (uuid == null) {
            this.uuid = UUID.randomUUID().toString().replaceAll("-", "");
        }
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getSubMsgType() {
        return subMsgType;
    }

    public void setSubMsgType(String subMsgType) {
        this.subMsgType = subMsgType;
    }

    public Integer getTuseMsgType() {
        return tuseMsgType;
    }

    public void setTuseMsgType(Integer tuseMsgType) {
        this.tuseMsgType = tuseMsgType;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public Integer getIsHit() {
        return isHit;
    }

    public void setIsHit(Integer isHit) {
        this.isHit = isHit;
    }

    public Integer getAtLocation() {
        return atLocation;
    }

    public void setAtLocation(Integer atLocation) {
        this.atLocation = atLocation;
    }

    public Integer getMsgInterval() {
        return msgInterval == null ? 0 : (msgInterval < 0 ? 0 : msgInterval);
    }

    public void setMsgInterval(Integer msgInterval) {
        this.msgInterval = msgInterval;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getVoiceTime() {
        return voiceTime == null ? 0 : voiceTime;
    }

    public void setVoiceTime(Integer voiceTime) {
        this.voiceTime = voiceTime;
    }

    public List<String> getHitUsers() {
        return hitUsers;
    }

    public void setHitUsers(List<String> hitUsers) {
        this.hitUsers = hitUsers;
    }

    public String getWebcastId() {
        return webcastId;
    }

    public void setWebcastId(String webcastId) {
        this.webcastId = webcastId;
    }

    public Boolean getIsDiscretization() {
        return isDiscretization;
    }

    public void setIsDiscretization(Boolean isDiscretization) {
        this.isDiscretization = isDiscretization;
    }
}