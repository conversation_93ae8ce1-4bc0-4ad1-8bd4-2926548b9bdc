package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.cat.Cat;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.abtest.service.ABTestService;
import com.sankuai.scrm.core.service.aigc.service.enums.AISceneABTestStatusEnum;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintLandProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Properties;
import com.sankuai.scrm.core.service.abtest.service.ABTestHandleRequest;

@Slf4j
@Component
public class GroupRetailAiEngineUserFootprintDiversionConsumer implements InitializingBean, DisposableBean {

    @Autowired
    private RealTimeTaskConsumerConfig consumerConfig;

    @Autowired
    private GroupRetailAiEngineUserFootprintLandProducer userFootprintLandProducer;

    @Autowired
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @Autowired
    private ScrmGrowthUserInfoDomainService domainService;

    private IConsumerProcessor consumer;

    @Autowired
    private ABTestService<Object> abTestService;

    private static final String CAT_TYPE = GroupRetailAiEngineUserFootprintDiversionConsumer.class.getSimpleName();

    private ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        try {
            GroupRetailAiEngineMqMessageDTO mqMessageDTO = JsonUtils.toObjectSafe(message.getBody(), GroupRetailAiEngineMqMessageDTO.class);
            if(null == mqMessageDTO || null == mqMessageDTO.getUserTrackDTO() || CollectionUtils.isEmpty(mqMessageDTO.getUserTrackDTO().getUserid())){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if("dp".equalsIgnoreCase(mqMessageDTO.getUserTrackDTO().getPlatformlist().get(0))){
                Long mtUserId = domainService.queryMtUserIdByDpUserId(mqMessageDTO.getUserTrackDTO().getUserid().get(0));
                if(null != mtUserId){
                    mqMessageDTO.getUserTrackDTO().setUserid(new ArrayList<>());
                    mqMessageDTO.getUserTrackDTO().getUserid().add(mtUserId);
                }
            }
            if (consumerConfig.isInWhitelist(mqMessageDTO.getUserTrackDTO().getUserid())) {
                long userId = mqMessageDTO.getUserTrackDTO().getUserid().get(0);
                String validAppId = consumerConfig.getValidAppId(userId, null);
                if(StringUtils.isBlank(validAppId)){
                    Cat.logEvent(CAT_TYPE, "validAppIdIsBlank");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                mqMessageDTO.setValidAppid(validAppId);
                userFootprintLandProducer.sendMessage(mqMessageDTO);
            } else {
                ABTestContext abTestContext = new ABTestContext();
                abTestContext.setUserId(mqMessageDTO.getUserTrackDTO().getUserid().get(0));
                abTestContext.setPlatForm(PlatformEnum.MT);
                abTestContext.setAppId("default");
                ABTestHandleRequest<Object> request = ABTestHandleRequest.builder()
                    .userId(null)
                    .splitType(null)
                    .appId("default")
                    .param(null)
                    .context(abTestContext)
                    .strategyName("LossGroupStrategy")
                    .abTestStatus(AISceneABTestStatusEnum.LOSS_GROUP.code)
                    .build();
                abTestService.handle(request);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("GroupRetailAiEngineDispatchConsumer error, message is {}", JsonUtils.toStr(message), e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

    }

    @Override
    public void destroy() throws Exception {
        if (this.consumer != null) {
            this.consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.group.retail.ai.engine.user.footprint.diversion.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildCommonConsumerFactory(properties, "scrm.group.retail.ai.engine.user.footprint.diversion");
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }

}
