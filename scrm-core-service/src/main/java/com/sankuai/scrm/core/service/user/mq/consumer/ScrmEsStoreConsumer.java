package com.sankuai.scrm.core.service.user.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.user.domain.tag.NonLiveActionUserDataDomainService;
import com.sankuai.scrm.core.service.user.domain.tag.UserTagEsCommonDomainService;
import com.sankuai.scrm.core.service.user.mq.dto.EsStoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/6/4 17:24
 * @Version v1.0.0
 */
@Slf4j
@Service
public class ScrmEsStoreConsumer implements InitializingBean, DisposableBean {
    /**
     * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建.
     */
    private static IConsumerProcessor consumer;
    @Resource
    private UserTagEsCommonDomainService userTagEsCommonDomainService;

    @Resource
    private NonLiveActionUserDataDomainService nonLiveActionUserDataDomainService;

    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.usertag.es.store.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "scrm.usertag.es.store");

        consumer.recvMessageWithParallel(String.class, this::recvMessage);

    }

    private ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        try {
            String body = message.getBody().toString();
            EsStoreDTO esStoreDTO = JSON.parseObject(body, EsStoreDTO.class);
            if (esStoreDTO == null || StringUtils.isBlank(esStoreDTO.getDocJson()) || StringUtils.isBlank(esStoreDTO.getIndex()) || StringUtils.isBlank(esStoreDTO.getId())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            userTagEsCommonDomainService.updateESDocByIdSelectiveWithRetry(esStoreDTO.getIndex(), esStoreDTO.getDocJson(), esStoreDTO.getId());
        } catch (Exception e) {
            log.error("ScrmEsStoreConsumer has error,msg is {}", message, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }
}
