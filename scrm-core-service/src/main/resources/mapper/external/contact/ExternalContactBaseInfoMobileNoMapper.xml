<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.external.contact.dal.mapper.ExternalContactBaseInfoMobileNoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="base_info_id" jdbcType="BIGINT" property="baseInfoId" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, base_info_id, mobile_no, add_time, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoMobileNoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from external_contact_base_info_mobile_no
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from external_contact_base_info_mobile_no
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from external_contact_base_info_mobile_no
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoMobileNoExample">
    delete from external_contact_base_info_mobile_no
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into external_contact_base_info_mobile_no (base_info_id, mobile_no, add_time, 
      update_time, status)
    values (#{baseInfoId,jdbcType=BIGINT}, #{mobileNo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into external_contact_base_info_mobile_no
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baseInfoId != null">
        base_info_id,
      </if>
      <if test="mobileNo != null">
        mobile_no,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baseInfoId != null">
        #{baseInfoId,jdbcType=BIGINT},
      </if>
      <if test="mobileNo != null">
        #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoMobileNoExample" resultType="java.lang.Long">
    select count(*) from external_contact_base_info_mobile_no
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update external_contact_base_info_mobile_no
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.baseInfoId != null">
        base_info_id = #{record.baseInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.mobileNo != null">
        mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update external_contact_base_info_mobile_no
    set id = #{record.id,jdbcType=BIGINT},
      base_info_id = #{record.baseInfoId,jdbcType=BIGINT},
      mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo">
    update external_contact_base_info_mobile_no
    <set>
      <if test="baseInfoId != null">
        base_info_id = #{baseInfoId,jdbcType=BIGINT},
      </if>
      <if test="mobileNo != null">
        mobile_no = #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo">
    update external_contact_base_info_mobile_no
    set base_info_id = #{baseInfoId,jdbcType=BIGINT},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into external_contact_base_info_mobile_no
    (base_info_id, mobile_no, add_time, update_time, status)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.baseInfoId,jdbcType=BIGINT}, #{item.mobileNo,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=TINYINT})
    </foreach>
  </insert>

  <update id="batchUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      update external_contact_base_info_mobile_no
      set
        mobile_no = #{item.mobileNo,jdbcType=VARCHAR},
        update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        status = #{item.status,jdbcType=TINYINT}
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>