package com.sankuai.dz.srcm.activity.fission.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.GroupFissionQRCodePageDTO;
import com.sankuai.dz.srcm.activity.fission.dto.QueryActivityStatusResponseDTO;
import com.sankuai.dz.srcm.activity.fission.dto.ReceiveUserAuthorizationResponseDTO;
import com.sankuai.dz.srcm.activity.fission.request.GroupQrCodePageRequest;
import com.sankuai.dz.srcm.activity.fission.request.QueryActivityStatusRequest;
import com.sankuai.dz.srcm.activity.fission.request.ReceiveUserAuthorizationRequest;
import com.sankuai.dz.srcm.activity.fission.request.UserEventLogRequest;

/**
 * @Description C端用户相关接口
 * <AUTHOR>
 * @Create On 2024/7/17 10:39
 * @Version v1.0.0
 */
public interface LightCrmGroupFissionToCService {

    /**
     * 查询用户参与活动的群二维码页面信息（非企微群）
     *
     * @param request 请求参数
     * @return 群二维码页面信息
     */
    RemoteResponse<GroupFissionQRCodePageDTO> queryGroupQrCodePage(GroupQrCodePageRequest request);

    /**
     * 接收并存储用户授权信息
     *
     * @param request 请求体
     * @return 操作结果 DTO
     */
    RemoteResponse<ReceiveUserAuthorizationResponseDTO> receiveUserAuthorization(ReceiveUserAuthorizationRequest request);

    /**
     * 查询活动状态及用户授权状态
     *
     * @param request 请求体
     * @return 活动状态及授权信息 DTO
     */
    RemoteResponse<QueryActivityStatusResponseDTO> queryActivityStatus(QueryActivityStatusRequest request);

    RemoteResponse<Void> logUserEvent(UserEventLogRequest request);
}
