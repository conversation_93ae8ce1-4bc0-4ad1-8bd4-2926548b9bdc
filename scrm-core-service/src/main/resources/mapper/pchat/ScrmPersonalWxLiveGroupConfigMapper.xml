<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxLiveGroupConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="estimate_group_count" jdbcType="INTEGER" property="estimateGroupCount" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="robot_type" jdbcType="INTEGER" property="robotType" />
    <result column="deleted" jdbcType="CHAR" property="deleted" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, estimate_group_count, add_time, app_id, creator, updater, update_time, 
    robot_type, deleted, project_name, remark
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxLiveGroupConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_LiveGroupConfig
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_LiveGroupConfig
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_LiveGroupConfig
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxLiveGroupConfigExample">
    delete from Scrm_PersonalWx_LiveGroupConfig
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_LiveGroupConfig (project_id, estimate_group_count, add_time, 
      app_id, creator, updater, 
      update_time, robot_type, deleted, 
      project_name, remark)
    values (#{projectId,jdbcType=VARCHAR}, #{estimateGroupCount,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{appId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{robotType,jdbcType=INTEGER}, #{deleted,jdbcType=CHAR}, 
      #{projectName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_LiveGroupConfig
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="estimateGroupCount != null">
        estimate_group_count,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="robotType != null">
        robot_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="estimateGroupCount != null">
        #{estimateGroupCount,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="robotType != null">
        #{robotType,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=CHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxLiveGroupConfigExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_LiveGroupConfig
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_LiveGroupConfig
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.estimateGroupCount != null">
        estimate_group_count = #{record.estimateGroupCount,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.updater != null">
        updater = #{record.updater,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.robotType != null">
        robot_type = #{record.robotType,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=CHAR},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_LiveGroupConfig
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      estimate_group_count = #{record.estimateGroupCount,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      app_id = #{record.appId,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      updater = #{record.updater,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      robot_type = #{record.robotType,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=CHAR},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig">
    update Scrm_PersonalWx_LiveGroupConfig
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="estimateGroupCount != null">
        estimate_group_count = #{estimateGroupCount,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="robotType != null">
        robot_type = #{robotType,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=CHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveGroupConfig">
    update Scrm_PersonalWx_LiveGroupConfig
    set project_id = #{projectId,jdbcType=VARCHAR},
      estimate_group_count = #{estimateGroupCount,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      app_id = #{appId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      robot_type = #{robotType,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=CHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_LiveGroupConfig
    (project_id, estimate_group_count, add_time, app_id, creator, updater, update_time, 
      robot_type, deleted, project_name, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.projectId,jdbcType=VARCHAR}, #{item.estimateGroupCount,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.appId,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.robotType,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=CHAR}, #{item.projectName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>