package com.sankuai.scrm.core.service.open.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import java.util.Properties;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrmOpenEntGroupMessageProducerAfterPropertiesSet1Test {

    /**
     * 测试正常初始化场景
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        // arrange
        ScrmOpenEntGroupMessageProducer producer = new ScrmOpenEntGroupMessageProducer();
        IProducerProcessor<?, String> mockProducer = Mockito.mock(IProducerProcessor.class);
        try (MockedStatic<MafkaClient> mockedMafkaClient = Mockito.mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
            // act
            producer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()));
            assertNotNull(getProducerField(producer));
        }
    }

    /**
     * 测试Properties设置正确的值
     */
    @Test
    public void testAfterPropertiesSetPropertiesAreSetCorrectly() throws Throwable {
        // arrange
        ScrmOpenEntGroupMessageProducer producer = new ScrmOpenEntGroupMessageProducer();
        IProducerProcessor<?, String> mockProducer = Mockito.mock(IProducerProcessor.class);
        try (MockedStatic<MafkaClient> mockedMafkaClient = Mockito.mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                return mockProducer;
            });
            // act
            producer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()));
            assertNotNull(getProducerField(producer));
        }
    }

    /**
     * 测试topic名称设置正确
     */
    @Test
    public void testAfterPropertiesSetTopicNameIsCorrect() throws Throwable {
        // arrange
        ScrmOpenEntGroupMessageProducer producer = new ScrmOpenEntGroupMessageProducer();
        IProducerProcessor<?, String> mockProducer = Mockito.mock(IProducerProcessor.class);
        try (MockedStatic<MafkaClient> mockedMafkaClient = Mockito.mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                String topic = invocation.getArgument(1);
                assertEquals("scrm_open_ent_group_message_notify", topic);
                return mockProducer;
            });
            // act
            producer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()));
            assertNotNull(getProducerField(producer));
        }
    }

    // 辅助方法，通过反射获取producer字段
    private IProducerProcessor<?, String> getProducerField(ScrmOpenEntGroupMessageProducer producer) throws NoSuchFieldException, IllegalAccessException {
        java.lang.reflect.Field field = ScrmOpenEntGroupMessageProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        return (IProducerProcessor<?, String>) field.get(producer);
    }
}
