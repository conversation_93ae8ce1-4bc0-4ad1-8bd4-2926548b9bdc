package com.sankuai.dz.srcm.automatedmanagement.enums;

import org.apache.curator.shaded.com.google.common.collect.Sets;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 0:零时包 1:定时更新包 2:手动更新包
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/22
 */
public enum ScrmCrowdPackTypeEnum {
    TEMP_PACK("tempPack", 1, "临时包"),
    TIME_PACK("timePack", 2, "定时更新包"),
    MANUAL_PACK("manualPack", 3, "手动更新包"),

    UNKNOWN("unknown", 0, "未知"),
    ALL("all", 4, "全部"),
    PERSONA_PACK("personaPack", 5, "persona人群包"),
    EXCEL_UPLOAD_PACK("excelPack", 6, "excel人群包"),


    DELETED_TEMP_PACK("deletedTempPack", 21, "已删除"),
    DELETED_TIME_PACK("deletedTimePack", 22, "已删除"),
    DELETED_MANUAL_PACK("deletedManualPack", 23, "已删除"),
    DELETED_PERSONA_PACK("deletedPersonaPack", 20 + 5, "已删除"),
    DELETED_EXCEL_UPLOAD_PACK("deletedExcelPack", 20 + 6, "已删除"),
    ;

    private String code;

    private Integer value;

    private String description;

    public static final Set<Integer> UNSUPPORTED_CREATE_TYPE = Sets.newHashSet(PERSONA_PACK.value, EXCEL_UPLOAD_PACK.value);

    ScrmCrowdPackTypeEnum(String code, int value, String description) {
        this.code = code;
        this.value = value;
        this.description = description;
    }

    public static ScrmCrowdPackTypeEnum getTypeByValue(int i) {
        for (ScrmCrowdPackTypeEnum productTypeEnum : ScrmCrowdPackTypeEnum.values()) {
            if (productTypeEnum.getValue() == i) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ScrmCrowdPackTypeEnum getTypeByCode(String code) {
        for (ScrmCrowdPackTypeEnum productTypeEnum : ScrmCrowdPackTypeEnum.values()) {
            if (productTypeEnum.getCode().equalsIgnoreCase(code)|| productTypeEnum.name().equalsIgnoreCase(code)) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static Set<Integer> getDeletedPackType() {
        Set<Integer> deleteStatus = new HashSet();
        deleteStatus.add(ScrmCrowdPackTypeEnum.DELETED_TEMP_PACK.getValue());
        deleteStatus.add(ScrmCrowdPackTypeEnum.DELETED_TIME_PACK.getValue());
        deleteStatus.add(ScrmCrowdPackTypeEnum.DELETED_MANUAL_PACK.getValue());
        deleteStatus.add(ScrmCrowdPackTypeEnum.DELETED_PERSONA_PACK.getValue());
        deleteStatus.add(ScrmCrowdPackTypeEnum.DELETED_EXCEL_UPLOAD_PACK.getValue());
        return deleteStatus;
    }

    public String getCode() {
        return code;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
