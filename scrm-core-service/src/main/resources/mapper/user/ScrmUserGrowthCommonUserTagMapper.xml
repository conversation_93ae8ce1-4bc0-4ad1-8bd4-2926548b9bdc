<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthCommonUserTagMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="tag_id" jdbcType="INTEGER" property="tagId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tag_type_id" jdbcType="INTEGER" property="tagTypeId" />
    <result column="action_time" jdbcType="TIMESTAMP" property="actionTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, union_id, tag_id, status, add_time, update_time, tag_type_id, action_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.user.dal.example.ScrmUserGrowthCommonUserTagExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_UserGrowth_CommonUserTag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_UserGrowth_CommonUserTag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_UserGrowth_CommonUserTag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.user.dal.example.ScrmUserGrowthCommonUserTagExample">
    delete from Scrm_UserGrowth_CommonUserTag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_UserGrowth_CommonUserTag (user_id, union_id, tag_id, 
      status, add_time, update_time, 
      tag_type_id, action_time)
    values (#{userId,jdbcType=BIGINT}, #{unionId,jdbcType=VARCHAR}, #{tagId,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tagTypeId,jdbcType=INTEGER}, #{actionTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_UserGrowth_CommonUserTag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tagTypeId != null">
        tag_type_id,
      </if>
      <if test="actionTime != null">
        action_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tagTypeId != null">
        #{tagTypeId,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.user.dal.example.ScrmUserGrowthCommonUserTagExample" resultType="java.lang.Long">
    select count(*) from Scrm_UserGrowth_CommonUserTag
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_UserGrowth_CommonUserTag
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tagTypeId != null">
        tag_type_id = #{record.tagTypeId,jdbcType=INTEGER},
      </if>
      <if test="record.actionTime != null">
        action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_UserGrowth_CommonUserTag
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      tag_id = #{record.tagId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tag_type_id = #{record.tagTypeId,jdbcType=INTEGER},
      action_time = #{record.actionTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag">
    update Scrm_UserGrowth_CommonUserTag
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tagTypeId != null">
        tag_type_id = #{tagTypeId,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        action_time = #{actionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag">
    update Scrm_UserGrowth_CommonUserTag
    set user_id = #{userId,jdbcType=BIGINT},
      union_id = #{unionId,jdbcType=VARCHAR},
      tag_id = #{tagId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tag_type_id = #{tagTypeId,jdbcType=INTEGER},
      action_time = #{actionTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_UserGrowth_CommonUserTag
    (user_id, union_id, tag_id, status, add_time, update_time, tag_type_id, action_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=BIGINT}, #{item.unionId,jdbcType=VARCHAR}, #{item.tagId,jdbcType=INTEGER}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.tagTypeId,jdbcType=INTEGER}, #{item.actionTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>