<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.session.dao.mapper.ScrmPublicSentimentCollectMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="receiver_id" jdbcType="VARCHAR" property="receiverId"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="matching_reason" jdbcType="INTEGER" property="matchingReason"/>
        <result column="collate_time" jdbcType="DATE" property="collateTime"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="content_type" jdbcType="INTEGER" property="contentType"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , user_id, user_name, receiver_id, receiver_name, send_time, end_time, matching_reason,
    collate_time, add_time, update_time, app_id, content_type
    </sql>
    <sql id="Blob_Column_List">
        content
    </sql>
    <select id="selectByExampleWithBLOBs"
            parameterType="com.sankuai.scrm.core.service.session.dao.example.ScrmPublicSentimentCollectExample"
            resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from scrm_public_sentiment_collect
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByExample"
            parameterType="com.sankuai.scrm.core.service.session.dao.example.ScrmPublicSentimentCollectExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from scrm_public_sentiment_collect
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from scrm_public_sentiment_collect
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from scrm_public_sentiment_collect
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.sankuai.scrm.core.service.session.dao.example.ScrmPublicSentimentCollectExample">
        delete from scrm_public_sentiment_collect
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into scrm_public_sentiment_collect (user_id, user_name, receiver_id,
        receiver_name, send_time, end_time,
        matching_reason, collate_time, add_time,
        update_time, app_id, content_type,
        content)
        values (#{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{receiverId,jdbcType=VARCHAR},
        #{receiverName,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
        #{matchingReason,jdbcType=INTEGER}, #{collateTime,jdbcType=DATE}, #{addTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{appId,jdbcType=VARCHAR}, #{contentType,jdbcType=INTEGER},
        #{content,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective"
            parameterType="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into scrm_public_sentiment_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="receiverId != null">
                receiver_id,
            </if>
            <if test="receiverName != null">
                receiver_name,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="matchingReason != null">
                matching_reason,
            </if>
            <if test="collateTime != null">
                collate_time,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="contentType != null">
                content_type,
            </if>
            <if test="content != null">
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="receiverId != null">
                #{receiverId,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="matchingReason != null">
                #{matchingReason,jdbcType=INTEGER},
            </if>
            <if test="collateTime != null">
                #{collateTime,jdbcType=DATE},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null">
                #{contentType,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.sankuai.scrm.core.service.session.dao.example.ScrmPublicSentimentCollectExample"
            resultType="java.lang.Long">
        select count(*) from scrm_public_sentiment_collect
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update scrm_public_sentiment_collect
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=VARCHAR},
            </if>
            <if test="record.userName != null">
                user_name = #{record.userName,jdbcType=VARCHAR},
            </if>
            <if test="record.receiverId != null">
                receiver_id = #{record.receiverId,jdbcType=VARCHAR},
            </if>
            <if test="record.receiverName != null">
                receiver_name = #{record.receiverName,jdbcType=VARCHAR},
            </if>
            <if test="record.sendTime != null">
                send_time = #{record.sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.endTime != null">
                end_time = #{record.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.matchingReason != null">
                matching_reason = #{record.matchingReason,jdbcType=INTEGER},
            </if>
            <if test="record.collateTime != null">
                collate_time = #{record.collateTime,jdbcType=DATE},
            </if>
            <if test="record.addTime != null">
                add_time = #{record.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.appId != null">
                app_id = #{record.appId,jdbcType=VARCHAR},
            </if>
            <if test="record.contentType != null">
                content_type = #{record.contentType,jdbcType=INTEGER},
            </if>
            <if test="record.content != null">
                content = #{record.content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update scrm_public_sentiment_collect
        set id = #{record.id,jdbcType=BIGINT},
        user_id = #{record.userId,jdbcType=VARCHAR},
        user_name = #{record.userName,jdbcType=VARCHAR},
        receiver_id = #{record.receiverId,jdbcType=VARCHAR},
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
        matching_reason = #{record.matchingReason,jdbcType=INTEGER},
        collate_time = #{record.collateTime,jdbcType=DATE},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        app_id = #{record.appId,jdbcType=VARCHAR},
        content_type = #{record.contentType,jdbcType=INTEGER},
        content = #{record.content,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update scrm_public_sentiment_collect
        set id = #{record.id,jdbcType=BIGINT},
        user_id = #{record.userId,jdbcType=VARCHAR},
        user_name = #{record.userName,jdbcType=VARCHAR},
        receiver_id = #{record.receiverId,jdbcType=VARCHAR},
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
        matching_reason = #{record.matchingReason,jdbcType=INTEGER},
        collate_time = #{record.collateTime,jdbcType=DATE},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        app_id = #{record.appId,jdbcType=VARCHAR},
        content_type = #{record.contentType,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        update scrm_public_sentiment_collect
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="receiverId != null">
                receiver_id = #{receiverId,jdbcType=VARCHAR},
            </if>
            <if test="receiverName != null">
                receiver_name = #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="matchingReason != null">
                matching_reason = #{matchingReason,jdbcType=INTEGER},
            </if>
            <if test="collateTime != null">
                collate_time = #{collateTime,jdbcType=DATE},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null">
                content_type = #{contentType,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        update scrm_public_sentiment_collect
        set user_id         = #{userId,jdbcType=VARCHAR},
            user_name       = #{userName,jdbcType=VARCHAR},
            receiver_id     = #{receiverId,jdbcType=VARCHAR},
            receiver_name   = #{receiverName,jdbcType=VARCHAR},
            send_time       = #{sendTime,jdbcType=TIMESTAMP},
            end_time        = #{endTime,jdbcType=TIMESTAMP},
            matching_reason = #{matchingReason,jdbcType=INTEGER},
            collate_time    = #{collateTime,jdbcType=DATE},
            add_time        = #{addTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            app_id          = #{appId,jdbcType=VARCHAR},
            content_type    = #{contentType,jdbcType=INTEGER},
            content         = #{content,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect">
        update scrm_public_sentiment_collect
        set user_id         = #{userId,jdbcType=VARCHAR},
            user_name       = #{userName,jdbcType=VARCHAR},
            receiver_id     = #{receiverId,jdbcType=VARCHAR},
            receiver_name   = #{receiverName,jdbcType=VARCHAR},
            send_time       = #{sendTime,jdbcType=TIMESTAMP},
            end_time        = #{endTime,jdbcType=TIMESTAMP},
            matching_reason = #{matchingReason,jdbcType=INTEGER},
            collate_time    = #{collateTime,jdbcType=DATE},
            add_time        = #{addTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            app_id          = #{appId,jdbcType=VARCHAR},
            content_type    = #{contentType,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into scrm_public_sentiment_collect
        (user_id, user_name, receiver_id, receiver_name, send_time, end_time, matching_reason,
        collate_time, add_time, update_time, app_id, content_type, content)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.receiverId,jdbcType=VARCHAR},
            #{item.receiverName,jdbcType=VARCHAR}, #{item.sendTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP},
            #{item.matchingReason,jdbcType=INTEGER}, #{item.collateTime,jdbcType=DATE},
            #{item.addTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.appId,jdbcType=VARCHAR}, #{item.contentType,jdbcType=INTEGER},
            #{item.content,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>
    <select id="selectByPage" resultType="com.sankuai.scrm.core.service.session.dao.entity.GroupSessionCollectPageList">
        select
        collate_time collectTime,
        count(id) as sessionCount,
        sum(case when content_type = 1 then 1 else 0 end) as complaintSuggestion,
        sum(case when content_type = 2 then 1 else 0 end) as excessiveDisturbance,
        sum(case when content_type = 3 then 1 else 0 end) as aestheticConsultation,
        sum(case when content_type = 4 then 1 else 0 end) as preSalesConsultation,
        sum(case when content_type = 5 then 1 else 0 end) as eventConsultation,
        sum(case when content_type = 6 then 1 else 0 end) as otherCategories
        from scrm_public_sentiment_collect
        where app_id = #{request.appId}
        <if test="request.startTime != null and request.endTime != null">
            and collate_time between #{request.startTime} and #{request.endTime}
        </if>
        <if test="request.startTime == null or request.endTime == null">
            group by collate_time
        </if>
        order by collate_time desc limit #{request.pageNum}, #{request.pageSize}
    </select>

    <select id="queryCollectTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        select
        collate_time collectTime,
        count(id) as sessionCount,
        sum(case when content_type = 1 then 1 else 0 end) as complaintSuggestion,
        sum(case when content_type = 2 then 1 else 0 end) as excessiveDisturbance,
        sum(case when content_type = 3 then 1 else 0 end) as aestheticConsultation,
        sum(case when content_type = 4 then 1 else 0 end) as preSalesConsultation,
        sum(case when content_type = 5 then 1 else 0 end) as eventConsultation,
        sum(case when content_type = 6 then 1 else 0 end) as otherCategories
        from scrm_public_sentiment_collect
        where app_id = #{request.appId}
        <if test="request.startTime != null and request.endTime != null">
            and collate_time between #{request.startTime} and #{request.endTime}
        </if>
        <if test="request.startTime == null or request.endTime == null">
            group by collate_time
        </if>
        order by collate_time desc
        ) AS t;
    </select>

</mapper>