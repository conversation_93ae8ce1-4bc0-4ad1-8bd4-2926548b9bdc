package com.sankuai.scrm.core.service.pchat.thirdparty.consultant;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantTaskDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantTaskSearchDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageSortRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantAccountService;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.ConsultantInfoRequest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupConsultantMapping;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/2 18:37
 * @Version v1.0.0
 */

@Slf4j
@Service
public class ConsultantServiceImpl implements ConsultantService {
    @Resource
    private PrivateLiveConsultantAccountService privateLiveConsultantAccountService;
    @Resource
    private PrivateLiveConsultantAccountService newPrivateLiveConsultantAccountService;
    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @MdpConfig("consultant.service.migration.switch:false")
    private Boolean useNewAppkey;

    @Override
    public PageRemoteResponse<ConsultantInfoDTO> queryConsultantInfo(ConsultantInfoRequest consultantInfoRequest) {
        log.info("查询咨询师列表：param:{}", JsonUtils.toStr(consultantInfoRequest));
        PrivateLiveConsultantTaskSearchDTO searchDTO = new PrivateLiveConsultantTaskSearchDTO();
        searchDTO.setKeyword(consultantInfoRequest.getKeyword());
        searchDTO.setLiveId(StrUtil.isEmpty(consultantInfoRequest.getWebcastId()) ? null : consultantInfoRequest.getWebcastId());
        searchDTO.setStatus(ConsultantTaskApproveStatusEnum.PASS.getCode());
        PageSortRequest request = PageSortRequest.builder().condition(searchDTO).pageNum(consultantInfoRequest.getStart1PageNo()).pageSize(consultantInfoRequest.getPageSize()).sortMap(sortField()).build();
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> response = loadConsultantTasksPage((request));
        PageDataDTO<PrivateLiveConsultantTaskDTO> pageDataDTO = response.getData();
        if (Objects.isNull(pageDataDTO) || CollectionUtil.isEmpty(pageDataDTO.getList())) {
            return PageRemoteResponse.success(new ArrayList<>(), 0, true);
        }
        List<ConsultantInfoDTO> consultantInfoDTOS = buildConsultantInfoDTO(pageDataDTO);
        checkIsInGroup(consultantInfoRequest, consultantInfoDTOS);
        return PageRemoteResponse.success(consultantInfoDTOS, pageDataDTO.getPageInfoDTO().getTotalCount(), pageDataDTO.getPageInfoDTO().getPageSize() != pageDataDTO.getList().size());
    }

    /**
     * 检查咨询师是否在群（个人溯源需要，社群溯源时此方法不一定准确【也不会需要次方法】）
     *
     * @param consultantInfoRequest
     * @param consultantInfoDTOS
     */
    private void checkIsInGroup(ConsultantInfoRequest consultantInfoRequest, List<ConsultantInfoDTO> consultantInfoDTOS) {
        if (CollectionUtil.isEmpty(consultantInfoDTOS)) {
            return;
        }
        if (Boolean.TRUE == consultantInfoRequest.getIsCheckInGroup()) {
            List<ScrmPersonalWxGroupConsultantMapping> groupConsultantMappings = personalWxGroupManageDomainService.queryGroupConsultantMappingByProjectId(consultantInfoRequest.getWebcastId(), null);
            Set<Long> existsConsultantId = groupConsultantMappings.stream().map(ScrmPersonalWxGroupConsultantMapping::getConsultantTaskId).collect(Collectors.toSet());
            consultantInfoDTOS.forEach(c -> c.setInGroup(existsConsultantId.contains(c.getConsultantTaskId())));
        }

    }

    private List<ConsultantInfoDTO> buildConsultantInfoDTO(PageDataDTO<PrivateLiveConsultantTaskDTO> pageDataDTO) {
        return pageDataDTO.getList().stream().map(p -> {
            ConsultantInfoDTO dto = new ConsultantInfoDTO();
            dto.setConsultantName(p.getNickname());
            dto.setConsultantTaskId(p.getId());
            dto.setWxAlias(p.getWechatNumber());
            dto.setLiveConsultantName(p.getShareName());
            dto.setStatus(p.getStatus());
            dto.setWebcastId(p.getLiveId());
            dto.setPhoneNumber(p.getPhoneNumber());
            dto.setUnionId(p.getUnionId());
            dto.setAvatar(p.getAvatarUrl());
            dto.setActualName(p.getActualName());
            dto.setTaskType(p.getTaskType());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ConsultantInfoDTO> queryConsultantInfoById(List<Long> consultantTaskIds) {
        PrivateLiveConsultantTaskSearchDTO condition = new PrivateLiveConsultantTaskSearchDTO();
        condition.setIds(consultantTaskIds);
        PageSortRequest request = (PageSortRequest.builder().condition(condition).pageNum(1).pageSize(consultantTaskIds.size()).sortMap(sortField()).build());
        request.setNeedPhoneNumberToken(true);
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> response = loadConsultantTasksPage(request);
        PageDataDTO<PrivateLiveConsultantTaskDTO> pageDataDTO = response.getData();
        if (Objects.isNull(pageDataDTO) || CollectionUtil.isEmpty(pageDataDTO.getList())) {
            return null;
        }
        return buildConsultantInfoDTO(pageDataDTO);
    }

    private static Map<String, Integer> sortField() {
        Map<String, Integer> sort = new HashMap<>();
        sort.put("nickname", 1);
        return sort;
    }

    @Override
    public ConsultantInfoDTO queryConsultantInfoById(Long consultantTaskId) {
        PrivateLiveConsultantTaskSearchDTO condition = new PrivateLiveConsultantTaskSearchDTO();
        condition.setIds(Collections.singletonList(consultantTaskId));
        PageSortRequest request = PageSortRequest.builder().pageNum(1).pageSize(1).condition(condition).build();
        request.setNeedPhoneNumberToken(true);
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> response = loadConsultantTasksPage(request);
        PageDataDTO<PrivateLiveConsultantTaskDTO> pageDataDTO = response.getData();
        if (Objects.isNull(pageDataDTO) || CollectionUtil.isEmpty(pageDataDTO.getList())) {
            return null;
        }
        List<ConsultantInfoDTO> consultantInfoDTOS = buildConsultantInfoDTO(pageDataDTO);
        if (CollectionUtil.isEmpty(consultantInfoDTOS)) {
            return null;
        }

        return consultantInfoDTOS.get(0);
    }

    private RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> loadConsultantTasksPage(PageSortRequest request) {
        return useNewAppkey ? newPrivateLiveConsultantAccountService.loadConsultantTasksPage(request) : privateLiveConsultantAccountService.loadConsultantTasksPage(request);
    }
}
