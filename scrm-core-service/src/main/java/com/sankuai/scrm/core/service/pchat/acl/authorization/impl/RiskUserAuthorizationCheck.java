package com.sankuai.scrm.core.service.pchat.acl.authorization.impl;

import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.dzrtc.privatelive.operation.api.common.DefaultErrorCode;
import com.sankuai.dzrtc.privatelive.operation.api.common.ResponseDTO;
import com.sankuai.dzrtc.privatelive.operation.api.enums.AuthCodeEnum;
import com.sankuai.scrm.core.service.pchat.acl.authorization.AuthorizationCheckSpi;
import com.sankuai.scrm.core.service.pchat.acl.authorization.dto.AuthorizationCheckRequest;
import com.sankuai.scrm.core.service.pchat.acl.authorization.dto.AuthorizationCheckResponse;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import org.springframework.stereotype.Component;

@Component
public class RiskUserAuthorizationCheck implements AuthorizationCheckSpi {

    @Override
    public AuthorizationCheckResponse checkUserAuthority(AuthorizationCheckRequest authorizationCheckRequest) {
        // 角色权限的校验（垂直越权）
        if (!getPermission(authorizationCheckRequest)) {
            return AuthorizationCheckResponse.fail("当前账号无操作权限");
        }

        return AuthorizationCheckResponse.success();
    }

    @Override
    public Integer getAuthorityCode() {
        return AuthorityCodeEnum.RISK_USER_OPERATION_PERMISSION.getCode();
    }

    private boolean getPermission(AuthorizationCheckRequest authorizationCheckRequest) {
        ResponseDTO<Boolean> dto = AnchorAuthUtils.checkAuth(AuthCodeEnum.COMMUNITY_RISK_CODE.getCode(), null);
        if (dto.getCode() == DefaultErrorCode.OK.getCode()) {
            return dto.getData();
        } else if (dto.getCode() == DefaultErrorCode.PARAM_ERROR.getCode()) {
            return true;
        } else if (dto.getCode() == DefaultErrorCode.NO_AUTH.getCode()) {
            return true;
        }
        return false;
    }


}
