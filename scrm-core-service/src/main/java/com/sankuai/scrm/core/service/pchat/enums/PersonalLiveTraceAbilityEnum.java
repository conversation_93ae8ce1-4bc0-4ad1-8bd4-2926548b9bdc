package com.sankuai.scrm.core.service.pchat.enums;

import lombok.Getter;

/**
 * @Description 溯源类型
 * <AUTHOR>
 * @Create On 2024/07/08 17:09
 * @Version v1.0.0
 */
@Getter
public enum PersonalLiveTraceAbilityEnum {
    GROUP("1", "社群溯源"),
    PERSONAL("2", "个人溯源"),
    ;

    private final String code;

    private final String desc;

    PersonalLiveTraceAbilityEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PersonalLiveTraceAbilityEnum fromCode(String code) {
        for (PersonalLiveTraceAbilityEnum enumValue : PersonalLiveTraceAbilityEnum.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }
}
