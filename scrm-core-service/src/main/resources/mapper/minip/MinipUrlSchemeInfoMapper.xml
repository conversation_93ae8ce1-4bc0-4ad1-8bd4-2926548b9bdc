<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.minip.dao.mapper.MinipUrlSchemeInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.minip.dao.bo.MinipUrlSchemeInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="minip_app_id" jdbcType="VARCHAR" property="minipAppId" />
    <result column="open_link" jdbcType="VARCHAR" property="openLink" />
    <result column="scene" jdbcType="INTEGER" property="scene" />
    <result column="url_code" jdbcType="VARCHAR" property="urlCode" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="env_version" jdbcType="VARCHAR" property="envVersion" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, minip_app_id, open_link, scene, url_code, expire_time, env_version, path, query, 
    app_id, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.minip.dao.example.MinipUrlSchemeInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from minip_url_scheme_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from minip_url_scheme_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from minip_url_scheme_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.minip.dao.example.MinipUrlSchemeInfoExample">
    delete from minip_url_scheme_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.minip.dao.bo.MinipUrlSchemeInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into minip_url_scheme_info (minip_app_id, open_link, scene, 
      url_code, expire_time, env_version, 
      path, query, app_id, 
      status, add_time, update_time
      )
    values (#{minipAppId,jdbcType=VARCHAR}, #{openLink,jdbcType=VARCHAR}, #{scene,jdbcType=INTEGER}, 
      #{urlCode,jdbcType=VARCHAR}, #{expireTime,jdbcType=TIMESTAMP}, #{envVersion,jdbcType=VARCHAR}, 
      #{path,jdbcType=VARCHAR}, #{query,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.minip.dao.bo.MinipUrlSchemeInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into minip_url_scheme_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="minipAppId != null">
        minip_app_id,
      </if>
      <if test="openLink != null">
        open_link,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="urlCode != null">
        url_code,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="envVersion != null">
        env_version,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="query != null">
        query,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="minipAppId != null">
        #{minipAppId,jdbcType=VARCHAR},
      </if>
      <if test="openLink != null">
        #{openLink,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=INTEGER},
      </if>
      <if test="urlCode != null">
        #{urlCode,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="envVersion != null">
        #{envVersion,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="query != null">
        #{query,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.minip.dao.example.MinipUrlSchemeInfoExample" resultType="java.lang.Long">
    select count(*) from minip_url_scheme_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update minip_url_scheme_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.minipAppId != null">
        minip_app_id = #{record.minipAppId,jdbcType=VARCHAR},
      </if>
      <if test="record.openLink != null">
        open_link = #{record.openLink,jdbcType=VARCHAR},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=INTEGER},
      </if>
      <if test="record.urlCode != null">
        url_code = #{record.urlCode,jdbcType=VARCHAR},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.envVersion != null">
        env_version = #{record.envVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.query != null">
        query = #{record.query,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update minip_url_scheme_info
    set id = #{record.id,jdbcType=BIGINT},
      minip_app_id = #{record.minipAppId,jdbcType=VARCHAR},
      open_link = #{record.openLink,jdbcType=VARCHAR},
      scene = #{record.scene,jdbcType=INTEGER},
      url_code = #{record.urlCode,jdbcType=VARCHAR},
      expire_time = #{record.expireTime,jdbcType=TIMESTAMP},
      env_version = #{record.envVersion,jdbcType=VARCHAR},
      path = #{record.path,jdbcType=VARCHAR},
      query = #{record.query,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.minip.dao.bo.MinipUrlSchemeInfo">
    update minip_url_scheme_info
    <set>
      <if test="minipAppId != null">
        minip_app_id = #{minipAppId,jdbcType=VARCHAR},
      </if>
      <if test="openLink != null">
        open_link = #{openLink,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=INTEGER},
      </if>
      <if test="urlCode != null">
        url_code = #{urlCode,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="envVersion != null">
        env_version = #{envVersion,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="query != null">
        query = #{query,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.minip.dao.bo.MinipUrlSchemeInfo">
    update minip_url_scheme_info
    set minip_app_id = #{minipAppId,jdbcType=VARCHAR},
      open_link = #{openLink,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=INTEGER},
      url_code = #{urlCode,jdbcType=VARCHAR},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      env_version = #{envVersion,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      query = #{query,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into minip_url_scheme_info
    (minip_app_id, open_link, scene, url_code, expire_time, env_version, path, query, 
      app_id, status, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.minipAppId,jdbcType=VARCHAR}, #{item.openLink,jdbcType=VARCHAR}, #{item.scene,jdbcType=INTEGER}, 
        #{item.urlCode,jdbcType=VARCHAR}, #{item.expireTime,jdbcType=TIMESTAMP}, #{item.envVersion,jdbcType=VARCHAR}, 
        #{item.path,jdbcType=VARCHAR}, #{item.query,jdbcType=VARCHAR}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>