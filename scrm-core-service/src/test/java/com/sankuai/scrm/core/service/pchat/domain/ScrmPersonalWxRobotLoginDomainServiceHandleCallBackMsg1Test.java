package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.dto.CallbackDTO;
import com.sankuai.scrm.core.service.pchat.dto.loginout.LoginSuccessDTO;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotLoginLog;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotInfoMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotLoginLogMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserInfoMapper;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import org.junit.*;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotClusterMapper;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxRobotLoginDomainServiceHandleCallBackMsg1Test {

    @InjectMocks
    private ScrmPersonalWxRobotLoginDomainService scrmPersonalWxRobotLoginDomainService;

    @Mock
    private ScrmPersonalWxRobotLoginLogMapper wxRobotLoginLogMapper;

    @Mock
    private ScrmPersonalWxRobotInfoMapper robotInfoMapper;

    @Mock
    private ScrmPersonalWxUserInfoMapper personalWxUserInfoMapper;

    @Mock
    private RedisStoreClient redisClient;

    private CallbackDTO callbackDTO;

    private LoginSuccessDTO loginSuccessDTO;

    @Before
    public void setUp() {
        callbackDTO = new CallbackDTO();
        callbackDTO.setNType(1003);
        callbackDTO.setVcSerialNo("123456");
        callbackDTO.setVcRobotSerialNo("7890");
        callbackDTO.setVcRobotWxId("wxid_123456");
        loginSuccessDTO = new LoginSuccessDTO();
        loginSuccessDTO.setVcNickName("TestNickName");
        loginSuccessDTO.setVcHeadImgUrl("http://test.url");
        loginSuccessDTO.setVcPersonQRCode("testQRCode");
        loginSuccessDTO.setVcWxAlias("testWxAlias");
        loginSuccessDTO.setNSex(1);
        callbackDTO.setData(loginSuccessDTO);
        // Mock Redis client to always return true for setnx
        when(redisClient.setnx(any(StoreKey.class), any())).thenReturn(true);
    }

    @Test
    public void testHandleCallBackMsg_NotMt1003() throws Throwable {
        callbackDTO.setNType(1001);
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(wxRobotLoginLogMapper, never()).selectByExample(any());
        verify(robotInfoMapper, never()).selectByExample(any());
        verify(personalWxUserInfoMapper, never()).selectByExample(any());
    }

    @Test
    public void testHandleCallBackMsg_HandleLogIsNull() throws Throwable {
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(robotInfoMapper, never()).selectByExample(any());
        verify(personalWxUserInfoMapper, never()).selectByExample(any());
    }

    @Test
    public void testHandleCallBackMsg_RobotInfoExists() throws Throwable {
        ScrmPersonalWxRobotLoginLog loginLog = new ScrmPersonalWxRobotLoginLog();
        loginLog.setClusterId("testClusterId");
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(Collections.singletonList(loginLog));
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmPersonalWxRobotInfo()));
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(robotInfoMapper, times(1)).updateByPrimaryKey(any());
        verify(personalWxUserInfoMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testHandleCallBackMsg_RobotInfoNotExists() throws Throwable {
        ScrmPersonalWxRobotLoginLog loginLog = new ScrmPersonalWxRobotLoginLog();
        loginLog.setClusterId("testClusterId");
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(Collections.singletonList(loginLog));
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(robotInfoMapper, times(1)).insertSelective(any());
        verify(personalWxUserInfoMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testHandleCallBackMsg_UserInfoExists() throws Throwable {
        ScrmPersonalWxRobotLoginLog loginLog = new ScrmPersonalWxRobotLoginLog();
        loginLog.setClusterId("testClusterId");
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(Collections.singletonList(loginLog));
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmPersonalWxRobotInfo()));
        when(personalWxUserInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmPersonalWxUserInfo()));
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(personalWxUserInfoMapper, times(1)).updateByPrimaryKey(any());
        verify(personalWxUserInfoMapper, never()).insert(any());
    }

    @Test
    public void testHandleCallBackMsg_UserInfoNotExists() throws Throwable {
        ScrmPersonalWxRobotLoginLog loginLog = new ScrmPersonalWxRobotLoginLog();
        loginLog.setClusterId("testClusterId");
        when(wxRobotLoginLogMapper.selectByExample(any())).thenReturn(Collections.singletonList(loginLog));
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmPersonalWxRobotInfo()));
        when(personalWxUserInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        verify(personalWxUserInfoMapper, never()).updateByPrimaryKey(any());
        verify(personalWxUserInfoMapper, times(1)).insert(any());
    }

    @Test
    public void testHandleCallBackMsg_ExceptionOccurs() throws Throwable {
        when(wxRobotLoginLogMapper.selectByExample(any())).thenThrow(new RuntimeException("Test exception"));
        try {
            scrmPersonalWxRobotLoginDomainService.handleCallBackMsg(callbackDTO);
        } catch (Exception e) {
            // Exception is expected
        }
        verify(robotInfoMapper, never()).selectByExample(any());
        verify(personalWxUserInfoMapper, never()).selectByExample(any());
    }
}
