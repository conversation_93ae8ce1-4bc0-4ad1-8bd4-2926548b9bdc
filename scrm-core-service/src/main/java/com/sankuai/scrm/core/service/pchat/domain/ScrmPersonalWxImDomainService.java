package com.sankuai.scrm.core.service.pchat.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxChatLog;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxIMConnLog;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxIMOnlineConn;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxChatLogExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxIMOnlineConnExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxChatLogMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxIMConnLogMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxIMMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxIMOnlineConnMapper;
import com.sankuai.scrm.core.service.pchat.enums.PersonalMsgTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxMsgEnum;
import com.sankuai.scrm.core.service.pchat.enums.im.ImConnectType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/1/8 16:51
 * @Version v1.0.0
 */
@Slf4j
@Component
public class ScrmPersonalWxImDomainService {
    @Resource
    private ScrmPersonalWxChatLogMapper personalWxChatLogMapper;
    @Resource
    private ScrmPersonalWxIMConnLogMapper personalWxIMConnLogMapper;
    @Resource
    private ScrmPersonalWxIMOnlineConnMapper personalWxIMOnlineConnMapper;
    @Resource
    private ScrmPersonalWxIMMapper personalWxIMMapper;

    public void saveOrUpdateConn(ScrmPersonalWxIMOnlineConn onlineConn) {
        List<ScrmPersonalWxIMOnlineConn> scrmPersonalWxIMOnlineConns = queryOnlineConn(onlineConn);
        if (CollectionUtil.isEmpty(scrmPersonalWxIMOnlineConns)) {
            personalWxIMOnlineConnMapper.insert(onlineConn);
        } else {
            scrmPersonalWxIMOnlineConns.forEach(o -> {
                o.setAddTime(new Date());
                o.setProjectId(onlineConn.getProjectId());
                o.setMainAnchorId(onlineConn.getMainAnchorId());
                o.setAppId(onlineConn.getAppId());
                personalWxIMOnlineConnMapper.updateByPrimaryKeySelective(o);
            });
            saveConnectLog(onlineConn, ImConnectType.DIS_CONNECT);
        }
        saveConnectLog(onlineConn, ImConnectType.CONNECT);
    }

    public List<ScrmPersonalWxIMOnlineConn> queryOnlineConn(ScrmPersonalWxIMOnlineConn onlineConn) {
        ScrmPersonalWxIMOnlineConnExample example = new ScrmPersonalWxIMOnlineConnExample();
        ScrmPersonalWxIMOnlineConnExample.Criteria criteria = example.createCriteria();
        criteria.andAliasEqualTo(onlineConn.getAlias());

        return personalWxIMOnlineConnMapper.selectByExample(example);

    }

    public void removeOnlineConn(String alias) {
        ScrmPersonalWxIMOnlineConnExample example = new ScrmPersonalWxIMOnlineConnExample();
        ScrmPersonalWxIMOnlineConnExample.Criteria criteria = example.createCriteria();
        criteria.andAliasEqualTo(alias);

        personalWxIMOnlineConnMapper.deleteByExample(example);
    }


    public void saveConnectLog(ScrmPersonalWxIMOnlineConn onlineConn, ImConnectType connectType) {
        ScrmPersonalWxIMConnLog connLog = new ScrmPersonalWxIMConnLog();
        connLog.setAlias(onlineConn.getAlias());
        connLog.setAppId(onlineConn.getAppId());
        connLog.setAddTime(new Date());
        connLog.setMainAnchorId(onlineConn.getMainAnchorId());
        connLog.setProjectId(onlineConn.getProjectId());
        connLog.setConnectType(connectType.getCode());
        personalWxIMConnLogMapper.insert(connLog);
    }

    public void saveConnectLog(ScrmPersonalWxIMConnLog connLog) {
        personalWxIMConnLogMapper.insert(connLog);
    }

    public List<ScrmPersonalWxChatLog> queryWxGroupChatLogTopN(String senderWxSerialNo, List<String> chatRoomSerialNos, Integer topN) {
        if (StrUtil.isBlank(senderWxSerialNo)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(chatRoomSerialNos)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxChatLogExample example = new ScrmPersonalWxChatLogExample();
        ScrmPersonalWxChatLogExample.Criteria criteria = example.createCriteria();
        criteria.andMsgTypeEqualTo(PersonalWxMsgEnum.GROUP_RECEIVE.getCode());
        criteria.andSendMsgTypeEqualTo(PersonalMsgTypeEnum.TEXT.getTuseCode().toString());
        if (CollectionUtil.isNotEmpty(chatRoomSerialNos)) {
            criteria.andChatRoomSerialNoIn(chatRoomSerialNos);
        }
        criteria.andSenderWxSerialNoEqualTo(senderWxSerialNo);

        example.setOrderByClause("add_time desc");
        example.limit(topN);

        return personalWxChatLogMapper.selectByExampleWithBLOBs(example);
    }

    public List<ScrmPersonalWxIMOnlineConn> queryOnlineConnByRobotSerialNo(String robotSerialNo) {
        return personalWxIMMapper.queryOnlineConnByRobotSerialNo(robotSerialNo);
    }

    public List<ScrmPersonalWxIMOnlineConn> queryOnlineConnByLiveId(String appId, String liveId) {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(liveId)) {
            return Lists.newArrayList();
        }
        ScrmPersonalWxIMOnlineConnExample example = new ScrmPersonalWxIMOnlineConnExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andProjectIdEqualTo(liveId);
        return personalWxIMOnlineConnMapper.selectByExample(example);
    }
}
