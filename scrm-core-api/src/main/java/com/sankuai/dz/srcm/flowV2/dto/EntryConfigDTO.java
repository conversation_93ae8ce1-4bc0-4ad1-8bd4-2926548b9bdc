package com.sankuai.dz.srcm.flowV2.dto;

import com.sankuai.dz.srcm.group.dynamiccode.dto.CityVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EntryConfigDTO {

    private Long configId;

    private String configName;

    private Integer entryType;

    private List<CityVO> cityInfoList;

    private Integer status;

    private Date createTime;

    private String creator;

    private String operator;

}
