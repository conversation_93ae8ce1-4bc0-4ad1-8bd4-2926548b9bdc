package com.sankuai.scrm.core.service.reply.facade;

import com.dianping.cat.Cat;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.reply.dto.MsgContentDTO;
import com.sankuai.dz.srcm.reply.dto.ScrmAssistantQuestionReplyDTO;
import com.sankuai.dz.srcm.reply.dto.ScrmQuestionReplyDTO;
import com.sankuai.dz.srcm.reply.dto.ScrmQuestionReplyPageDTO;
import com.sankuai.dz.srcm.reply.enums.ReplyStatus;
import com.sankuai.dz.srcm.reply.enums.SenderType;
import com.sankuai.dz.srcm.reply.facade.ScrmQuestionReplyService;
import com.sankuai.dz.srcm.reply.request.ScrmQuestionReplyDetailResquest;
import com.sankuai.dz.srcm.reply.request.ScrmQuestionReplyInfoRequest;
import com.sankuai.dz.srcm.reply.request.ScrmRelayStatusMarkRequest;
import com.sankuai.dz.srcm.session.dto.ChatRecordDTO;
import com.sankuai.scrm.core.service.reply.domain.MsgContentDomainService;
import com.sankuai.scrm.core.service.reply.domain.ScrmQuestionReplyInfoDomain;
import com.sankuai.scrm.core.service.reply.domain.ScrmQuestionReplyLogDomain;
import com.sankuai.scrm.core.service.reply.domain.ScrmQuestionReplyStatusDomain;
import com.sankuai.scrm.core.service.reply.domain.entity.ScrmQuestionReplyEntity;
import com.sankuai.scrm.core.service.reply.domain.entity.ScrmQuestionReplyInfo;
import com.sankuai.scrm.core.service.reply.domain.entity.ScrmQuestionReplyLogInfo;
import com.sankuai.scrm.core.service.session.dao.entity.ScrmSessionMsgRecordEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@MdpPigeonServer
public class ScrmQuestionReplyServiceImpl implements ScrmQuestionReplyService {

    @Resource
    private ScrmQuestionReplyInfoDomain scrmQuestionReplyInfoDomain;

    @Resource
    private ScrmQuestionReplyLogDomain scrmQuestionReplyLogDomain;

    @Resource
    private ScrmQuestionReplyStatusDomain scrmQuestionReplyStatusDomain;

    @Autowired
    private MsgContentDomainService msgContentDomainService;

    @Override
    public RemoteResponse<Boolean> markQuestionReplyStatus(ScrmRelayStatusMarkRequest statusMarkRequest) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.reply.facade.ScrmQuestionReplyServiceImpl.markQuestionReplyStatus(com.sankuai.dz.srcm.reply.request.ScrmRelayStatusMarkRequest)");
        try {
            if (statusMarkRequest == null) {
                return RemoteResponse.fail("参数为空");
            }

            long replyId = statusMarkRequest.getReplyId();
            int status = statusMarkRequest.getStatus();

            if (status != ReplyStatus.HANG.code) {
                return RemoteResponse.fail("status值不合法");
            }

            if (replyId <= 0) {
                return RemoteResponse.fail("reply值不合法");
            }

            ScrmQuestionReplyInfo replyInfo = scrmQuestionReplyInfoDomain.getReplyInfo(replyId);
            if (replyInfo == null) {
                return RemoteResponse.fail("该问答不存在");
            }

            if (replyInfo.getStatus() != ReplyStatus.NO_HANDLE.code) {
                return RemoteResponse.fail("该问答的状态不允许修改");
            }

            boolean result = scrmQuestionReplyStatusDomain.updateQuestionReplyStatus(replyId, status);
            return RemoteResponse.success(result);
        } catch (Exception e) {
            log.error("ScrmQuestionReplyServiceImpl.markQuestionReplyStatus has exception", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<ScrmQuestionReplyPageDTO> queryQuestionReplyInfoList(ScrmQuestionReplyInfoRequest replyInfoRequest) {
        try {
            if (replyInfoRequest == null) {
                return RemoteResponse.fail("参数为空");
            }

            String corpId = replyInfoRequest.getCorpId();
            String userId = replyInfoRequest.getChatUserId();
            int status = replyInfoRequest.getStatus();
            int num = replyInfoRequest.getNum();
            int size = replyInfoRequest.getSize();
            if (StringUtils.isBlank(corpId) || StringUtils.isBlank(userId)) {
                return RemoteResponse.fail("参数异常");
            }

            ScrmQuestionReplyPageDTO questionReplyPageDTO = new ScrmQuestionReplyPageDTO();
            List<Integer> statusSizeList = scrmQuestionReplyInfoDomain.statusStatisticsByCandidateHandler(corpId, userId);
            questionReplyPageDTO.setUnHandleSizeList(statusSizeList);


            List<ScrmQuestionReplyEntity> questionReplyEntityList = scrmQuestionReplyInfoDomain.getQuestionReplyListByHandler(corpId, userId, status, size, num);
            if (CollectionUtils.isEmpty(questionReplyEntityList)) {
                return RemoteResponse.success(questionReplyPageDTO);
            }
            int totalCount = scrmQuestionReplyInfoDomain.countByCandidateHandler(corpId, userId, status);
            questionReplyPageDTO.setTotalCount(totalCount);
            if (totalCount == 0) {
                return RemoteResponse.success(questionReplyPageDTO);
            }

            List<Long> replyIdList = questionReplyEntityList.stream().filter(Objects::nonNull).map(qr -> qr.getId()).collect(Collectors.toList());
            Map<Long, List<ScrmQuestionReplyLogInfo>> questionReplyLogMap = scrmQuestionReplyLogDomain.getQuestionReplyLog(replyIdList);

            List<ScrmQuestionReplyDTO> questionList = new ArrayList<>();
            if (questionReplyLogMap != null) {
                questionList = buildScrmQuestionReplyDTOList(questionReplyEntityList, questionReplyLogMap);
            }
            questionReplyPageDTO.setQuestionList(questionList);
            return RemoteResponse.success(questionReplyPageDTO);
        } catch (Exception e) {
            log.error("ScrmQuestionReplyServiceImpl.queryQuestionReplyInfoList has exception", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<ScrmAssistantQuestionReplyDTO> queryQuestionReplyDetail(ScrmQuestionReplyDetailResquest request) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.reply.facade.ScrmQuestionReplyServiceImpl.queryQuestionReplyDetail(com.sankuai.dz.srcm.reply.request.ScrmQuestionReplyDetailResquest)");
        try {
            if (ObjectUtils.isEmpty(request)) {
                return RemoteResponse.fail("param is empty");
            }
            if (StringUtils.isEmpty(request.getAppId()) || request.getReplyId() == null || ObjectUtils.isEmpty(request.getReplyTime()) || request.getScene() == null || request.getScene() <= 0) {
                return RemoteResponse.fail("param is wrong");
            }
            ScrmQuestionReplyInfo replyInfo = scrmQuestionReplyInfoDomain.getReplyInfo(request.getReplyId());
            if (replyInfo == null) {
                log.info("ScrmQuestionReplyServiceImpl queryQuestionReplyDetail: 消息回复记录为空, replyId={}",request.getReplyId());
                return RemoteResponse.success(null);
            }

            List<ScrmSessionMsgRecordEntity> recordEntities = scrmQuestionReplyLogDomain.querySessionMsgRecordList(replyInfo, request.getReplyTime(), request.getScene());
            if (ObjectUtils.isEmpty(recordEntities)) {
                log.info("ScrmQuestionReplyServiceImpl queryQuestionReplyDetail: 会话记录为空, replyId={}",request.getReplyId());
                return RemoteResponse.success(null);
            }

            ScrmQuestionReplyEntity replyEntity = new ScrmQuestionReplyEntity();
            BeanUtils.copyProperties(replyInfo, replyEntity);
            ScrmAssistantQuestionReplyDTO scrmQuestionReplyDTO = buildAssistantReplyDTO(recordEntities, replyEntity);
            return RemoteResponse.success(scrmQuestionReplyDTO);
        } catch (Exception e) {
            log.error("ScrmQuestionReplyServiceImpl.queryQuestionReplyDetail has exception", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    private List<ScrmQuestionReplyDTO> buildScrmQuestionReplyDTOList(List<ScrmQuestionReplyEntity> questionReplyEntityList, Map<Long, List<ScrmQuestionReplyLogInfo>> questionReplyLogMap) {
        List<ScrmQuestionReplyDTO> questionList = new ArrayList<>();
        for (ScrmQuestionReplyEntity replyEntity : questionReplyEntityList) {
            List<ScrmQuestionReplyLogInfo> questionReplyLogInfoList = questionReplyLogMap.get(replyEntity.getId());
            if(CollectionUtils.isEmpty(questionReplyLogInfoList)){
                continue;
            }
            ScrmQuestionReplyDTO scrmQuestionReplyDTO = buildScrmQuestionReplyDTO(questionReplyLogInfoList, replyEntity);
            questionList.add(scrmQuestionReplyDTO);
        }
        return questionList;
    }

    private ScrmQuestionReplyDTO buildScrmQuestionReplyDTO(List<ScrmQuestionReplyLogInfo> questionReplyLogInfoList, ScrmQuestionReplyEntity replyEntity) {
        ScrmQuestionReplyDTO scrmQuestionReplyDTO = new ScrmQuestionReplyDTO();
        scrmQuestionReplyDTO.setQuestionId(replyEntity.getId());
        scrmQuestionReplyDTO.setExternalUserAvatar(replyEntity.getAskerAvatar());
        scrmQuestionReplyDTO.setExternalUserId(replyEntity.getAskerUserId());
        scrmQuestionReplyDTO.setExternalUserName(replyEntity.getAskerName());
        scrmQuestionReplyDTO.setRealHandlerAvatar(replyEntity.getRealHandlerAvatar());
        scrmQuestionReplyDTO.setRealHandlerName(replyEntity.getRealHandlerName());
        scrmQuestionReplyDTO.setRealHandlerUserId(replyEntity.getRealHandlerUserId());
        scrmQuestionReplyDTO.setAskTime(replyEntity.getAskTime());
        scrmQuestionReplyDTO.setGroupId(replyEntity.getGroupId());
        scrmQuestionReplyDTO.setGroupName(replyEntity.getGroupName());
        scrmQuestionReplyDTO.setScene(replyEntity.getScene());
        scrmQuestionReplyDTO.setStatus(replyEntity.getStatus());
        scrmQuestionReplyDTO.setIsOvertime(replyEntity.getIsOvertime());
        List<MsgContentDTO> askList = new ArrayList<>();
        MsgContentDTO replyContent = null;
        for (ScrmQuestionReplyLogInfo questionReplyLogInfo : questionReplyLogInfoList) {
            if (questionReplyLogInfo.getSenderType() == SenderType.INNER_USER.code) {
                replyContent = msgContentDomainService.buildMsgContent(questionReplyLogInfo);
            } else {
                MsgContentDTO askContent = msgContentDomainService.buildMsgContent(questionReplyLogInfo);
                askList.add(askContent);
            }
        }

        scrmQuestionReplyDTO.setReplyContent(replyContent);
        scrmQuestionReplyDTO.setAskList(askList);
        return scrmQuestionReplyDTO;
    }
    private ScrmAssistantQuestionReplyDTO buildAssistantReplyDTO(  List<ScrmSessionMsgRecordEntity> recordEntities , ScrmQuestionReplyEntity replyEntity) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.reply.facade.ScrmQuestionReplyServiceImpl.buildAssistantReplyDTO(java.util.List,com.sankuai.scrm.core.service.reply.domain.entity.ScrmQuestionReplyEntity)");
        ScrmAssistantQuestionReplyDTO rReplyDTO = new ScrmAssistantQuestionReplyDTO();
        rReplyDTO.setQuestionId(replyEntity.getId());
        rReplyDTO.setExternalUserAvatar(replyEntity.getAskerAvatar());
        rReplyDTO.setExternalUserId(replyEntity.getAskerUserId());
        rReplyDTO.setExternalUserName(replyEntity.getAskerName());
        rReplyDTO.setRealHandlerAvatar(replyEntity.getRealHandlerAvatar());
        rReplyDTO.setRealHandlerName(replyEntity.getRealHandlerName());
        rReplyDTO.setRealHandlerUserId(replyEntity.getRealHandlerUserId());
        rReplyDTO.setAskTime(replyEntity.getAskTime());
        rReplyDTO.setGroupId(replyEntity.getGroupId());
        rReplyDTO.setGroupName(replyEntity.getGroupName());
        rReplyDTO.setScene(replyEntity.getScene());
        rReplyDTO.setStatus(replyEntity.getStatus());
        rReplyDTO.setIsOvertime(replyEntity.getIsOvertime());
        List<MsgContentDTO> askList = new ArrayList<>();
        List<ChatRecordDTO> sessionContentList = new ArrayList<>();
        MsgContentDTO replyContent = null;
        for (ScrmSessionMsgRecordEntity record : recordEntities) {
            ChatRecordDTO chatRecordDTO = new ChatRecordDTO();
            if (record.getSenderType() == SenderType.INNER_USER.code) {
                replyContent = msgContentDomainService.buildMsgContent(record);
                chatRecordDTO.setContent(replyContent);
                chatRecordDTO.setSource(SenderType.INNER_USER.code);
            } else if(record.getSenderType() == SenderType.OUTER_USER.code){
                replyContent = msgContentDomainService.buildMsgContent(record);
                chatRecordDTO.setContent(replyContent);
                chatRecordDTO.setSource(SenderType.OUTER_USER.code);
            }
            sessionContentList.add(chatRecordDTO);
        }
        rReplyDTO.setAskList(askList);
        rReplyDTO.setSessionContentList(sessionContentList);
        return rReplyDTO;
    }

}
