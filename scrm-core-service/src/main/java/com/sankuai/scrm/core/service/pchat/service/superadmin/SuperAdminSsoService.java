package com.sankuai.scrm.core.service.pchat.service.superadmin;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzrtc.privatelive.auth.sdk.RoleAuthUtils;
import com.sankuai.dzrtc.privatelive.auth.sdk.enums.OperationSysRoleEnum;
import com.sankuai.dzrtc.privatelive.operation.api.common.ResponseDTO;
import com.sankuai.scrm.core.service.pchat.exception.SuperAdminAuthException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/25 18:41
 * @Version v1.0.0
 */
@Slf4j
public class SuperAdminSsoService {
    public static SSOUseInfo getLoginUser() {
        try {
            String ssoUserJson = Tracer.getAllContext().getOrDefault("sso.user", "{}");
            if (StringUtils.isBlank(ssoUserJson)) {
                //抛异常
                log.info("ssoUserJson is null");
                return new SSOUseInfo();
            }
            SSOUseInfo ssoUseInfo = JSON.parseObject(ssoUserJson, SSOUseInfo.class);
            if (ssoUseInfo == null || ssoUseInfo.getId() == null) {
                //抛异常
                log.info("SSOUseInfo is null");
                return new SSOUseInfo();
            }
            return ssoUseInfo;
        } catch (Exception e) {
            log.error("getLoginUser has exception", e);
            return new SSOUseInfo();
        }
    }

    public static String checkLoginUser() {
        SSOUseInfo loginUser = getLoginUser();
        if (StringUtils.isBlank(loginUser.getLogin())) {
            throw new SuperAdminAuthException("未登录");
        }
        return loginUser.getLogin();
    }
    public static boolean hasAuth(OperationSysRoleEnum roleEnum) {
        ResponseDTO<Boolean> booleanResponseDTO = RoleAuthUtils.authRole(roleEnum);
        return booleanResponseDTO != null && booleanResponseDTO.isSuccess() && Boolean.TRUE == booleanResponseDTO.getData();
    }

}
