package com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/16
 */
@Data
@EqualsAndHashCode
public class ScrmProcessOrchestrationActionContentDTO implements Serializable {

    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: action_id
     *   说明: 动作id
     */
    private Integer actionId;

    /**
     *   字段: content_id
     *   说明: 内容id
     */
    private Integer contentId;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: process_orchestration_node_id
     *   说明: 对应节点id
     */
    private Long processOrchestrationNodeId;

    /**
     *   字段: content
     *   说明: 动作内容
     */
    private String content;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: content_type
     *   说明: 内容类型：0非供给类 1供给类
     */
    private Integer contentType;

    /**************************自定义字段************************************************/
    /**
     * 附件
     */
    private List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList;
}
