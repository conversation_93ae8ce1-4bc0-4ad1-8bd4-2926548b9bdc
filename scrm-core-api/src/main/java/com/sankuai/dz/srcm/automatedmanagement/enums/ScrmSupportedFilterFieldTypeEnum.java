package com.sankuai.dz.srcm.automatedmanagement.enums;

import java.util.Date;

/**
 * 流程编排支持的字段类型
 *
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/23
 */
public enum ScrmSupportedFilterFieldTypeEnum {

    STRING("string", 1, "字符串",String.class.getName()),
    INTEGER("int", 2, "整型",Integer.class.getName()),
    BOOLEAN("boolean", 3, "布尔",Boolean.class.getName()),
    LONG("long", 4, "长整型", Long.class.getName()),
    DATE("date", 5, "日期", Date.class.getName()),
    BYTE("byte", 6, "字节",Byte.class.getName()),

    STAFF("staff", 127, "员工", "staff"),
    UNKNOWN("unknown", 0, "未知", "unknown"),
    ;

    private String code;

    private int value;

    private String description;

    private String classFullName;

    ScrmSupportedFilterFieldTypeEnum(String code, int value, String description, String classFullName) {
        this.code = code;
        this.value = value;
        this.description = description;
        this.classFullName = classFullName;
    }

    public static ScrmSupportedFilterFieldTypeEnum getTypeByValue(int i) {
        for (ScrmSupportedFilterFieldTypeEnum productTypeEnum : ScrmSupportedFilterFieldTypeEnum.values()) {
            if (productTypeEnum.getValue() == i) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ScrmSupportedFilterFieldTypeEnum getTypeByCode(String code) {
        for (ScrmSupportedFilterFieldTypeEnum productTypeEnum : ScrmSupportedFilterFieldTypeEnum.values()) {
            if (productTypeEnum.getCode().equalsIgnoreCase(code)|| productTypeEnum.name().equalsIgnoreCase(code)) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getClassFullName() {
        return classFullName;
    }
}
