package com.sankuai.scrm.core.service.pchat.dal.activity.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmPersonalWxActivityRuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmPersonalWxActivityRuleExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmPersonalWxActivityRuleExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxActivityRuleExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxActivityRuleExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(Long value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(Long value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(Long value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(Long value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<Long> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<Long> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(Long value1, Long value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkIsNull() {
            addCriterion("reward_remark is null");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkIsNotNull() {
            addCriterion("reward_remark is not null");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkEqualTo(String value) {
            addCriterion("reward_remark =", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkNotEqualTo(String value) {
            addCriterion("reward_remark <>", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkGreaterThan(String value) {
            addCriterion("reward_remark >", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("reward_remark >=", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkLessThan(String value) {
            addCriterion("reward_remark <", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkLessThanOrEqualTo(String value) {
            addCriterion("reward_remark <=", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkLike(String value) {
            addCriterion("reward_remark like", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkNotLike(String value) {
            addCriterion("reward_remark not like", value, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkIn(List<String> values) {
            addCriterion("reward_remark in", values, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkNotIn(List<String> values) {
            addCriterion("reward_remark not in", values, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkBetween(String value1, String value2) {
            addCriterion("reward_remark between", value1, value2, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardRemarkNotBetween(String value1, String value2) {
            addCriterion("reward_remark not between", value1, value2, "rewardRemark");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoIsNull() {
            addCriterion("reward_take_away_info is null");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoIsNotNull() {
            addCriterion("reward_take_away_info is not null");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoEqualTo(String value) {
            addCriterion("reward_take_away_info =", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoNotEqualTo(String value) {
            addCriterion("reward_take_away_info <>", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoGreaterThan(String value) {
            addCriterion("reward_take_away_info >", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoGreaterThanOrEqualTo(String value) {
            addCriterion("reward_take_away_info >=", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoLessThan(String value) {
            addCriterion("reward_take_away_info <", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoLessThanOrEqualTo(String value) {
            addCriterion("reward_take_away_info <=", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoLike(String value) {
            addCriterion("reward_take_away_info like", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoNotLike(String value) {
            addCriterion("reward_take_away_info not like", value, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoIn(List<String> values) {
            addCriterion("reward_take_away_info in", values, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoNotIn(List<String> values) {
            addCriterion("reward_take_away_info not in", values, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoBetween(String value1, String value2) {
            addCriterion("reward_take_away_info between", value1, value2, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andRewardTakeAwayInfoNotBetween(String value1, String value2) {
            addCriterion("reward_take_away_info not between", value1, value2, "rewardTakeAwayInfo");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeIsNull() {
            addCriterion("verification_time is null");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeIsNotNull() {
            addCriterion("verification_time is not null");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeEqualTo(Date value) {
            addCriterion("verification_time =", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeNotEqualTo(Date value) {
            addCriterion("verification_time <>", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeGreaterThan(Date value) {
            addCriterion("verification_time >", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("verification_time >=", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeLessThan(Date value) {
            addCriterion("verification_time <", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeLessThanOrEqualTo(Date value) {
            addCriterion("verification_time <=", value, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeIn(List<Date> values) {
            addCriterion("verification_time in", values, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeNotIn(List<Date> values) {
            addCriterion("verification_time not in", values, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeBetween(Date value1, Date value2) {
            addCriterion("verification_time between", value1, value2, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andVerificationTimeNotBetween(Date value1, Date value2) {
            addCriterion("verification_time not between", value1, value2, "verificationTime");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchIsNull() {
            addCriterion("check_re_join_group_switch is null");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchIsNotNull() {
            addCriterion("check_re_join_group_switch is not null");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchEqualTo(String value) {
            addCriterion("check_re_join_group_switch =", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchNotEqualTo(String value) {
            addCriterion("check_re_join_group_switch <>", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchGreaterThan(String value) {
            addCriterion("check_re_join_group_switch >", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchGreaterThanOrEqualTo(String value) {
            addCriterion("check_re_join_group_switch >=", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchLessThan(String value) {
            addCriterion("check_re_join_group_switch <", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchLessThanOrEqualTo(String value) {
            addCriterion("check_re_join_group_switch <=", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchLike(String value) {
            addCriterion("check_re_join_group_switch like", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchNotLike(String value) {
            addCriterion("check_re_join_group_switch not like", value, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchIn(List<String> values) {
            addCriterion("check_re_join_group_switch in", values, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchNotIn(List<String> values) {
            addCriterion("check_re_join_group_switch not in", values, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchBetween(String value1, String value2) {
            addCriterion("check_re_join_group_switch between", value1, value2, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckReJoinGroupSwitchNotBetween(String value1, String value2) {
            addCriterion("check_re_join_group_switch not between", value1, value2, "checkReJoinGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchIsNull() {
            addCriterion("check_leave_group_switch is null");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchIsNotNull() {
            addCriterion("check_leave_group_switch is not null");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchEqualTo(String value) {
            addCriterion("check_leave_group_switch =", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchNotEqualTo(String value) {
            addCriterion("check_leave_group_switch <>", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchGreaterThan(String value) {
            addCriterion("check_leave_group_switch >", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchGreaterThanOrEqualTo(String value) {
            addCriterion("check_leave_group_switch >=", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchLessThan(String value) {
            addCriterion("check_leave_group_switch <", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchLessThanOrEqualTo(String value) {
            addCriterion("check_leave_group_switch <=", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchLike(String value) {
            addCriterion("check_leave_group_switch like", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchNotLike(String value) {
            addCriterion("check_leave_group_switch not like", value, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchIn(List<String> values) {
            addCriterion("check_leave_group_switch in", values, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchNotIn(List<String> values) {
            addCriterion("check_leave_group_switch not in", values, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchBetween(String value1, String value2) {
            addCriterion("check_leave_group_switch between", value1, value2, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andCheckLeaveGroupSwitchNotBetween(String value1, String value2) {
            addCriterion("check_leave_group_switch not between", value1, value2, "checkLeaveGroupSwitch");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupIsNull() {
            addCriterion("match_all_group is null");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupIsNotNull() {
            addCriterion("match_all_group is not null");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupEqualTo(String value) {
            addCriterion("match_all_group =", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupNotEqualTo(String value) {
            addCriterion("match_all_group <>", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupGreaterThan(String value) {
            addCriterion("match_all_group >", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupGreaterThanOrEqualTo(String value) {
            addCriterion("match_all_group >=", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupLessThan(String value) {
            addCriterion("match_all_group <", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupLessThanOrEqualTo(String value) {
            addCriterion("match_all_group <=", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupLike(String value) {
            addCriterion("match_all_group like", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupNotLike(String value) {
            addCriterion("match_all_group not like", value, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupIn(List<String> values) {
            addCriterion("match_all_group in", values, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupNotIn(List<String> values) {
            addCriterion("match_all_group not in", values, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupBetween(String value1, String value2) {
            addCriterion("match_all_group between", value1, value2, "matchAllGroup");
            return (Criteria) this;
        }

        public Criteria andMatchAllGroupNotBetween(String value1, String value2) {
            addCriterion("match_all_group not between", value1, value2, "matchAllGroup");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}