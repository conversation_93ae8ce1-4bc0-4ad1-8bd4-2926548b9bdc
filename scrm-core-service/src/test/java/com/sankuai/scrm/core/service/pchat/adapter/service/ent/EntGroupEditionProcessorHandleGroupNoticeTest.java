package com.sankuai.scrm.core.service.pchat.adapter.service.ent;

import com.sankuai.dz.srcm.pchat.request.scrm.GroupEditRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.pchat.adapter.bo.GroupNoticeEditionBO;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import com.sankuai.scrm.core.service.pchat.mq.dto.GroupNoticeTaskDTO;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import org.apache.commons.lang3.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import java.lang.reflect.Method;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupNotice;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EntGroupEditionProcessorHandleGroupNoticeTest {

    @InjectMocks
    private EntGroupEditionProcessor entGroupEditionProcessor;

    @Mock
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;

    @Mock
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    private GroupEditRequest request;

    private ScrmPersonalWxGroupInfoEntity groupInfoEntity;

    @Before
    public void setUp() {
        request = new GroupEditRequest();
        groupInfoEntity = mock(ScrmPersonalWxGroupInfoEntity.class);
    }

    private void invokeHandleGroupNotice(GroupEditRequest request, ScrmPersonalWxGroupInfoEntity groupInfoEntity) throws Exception {
        Method handleGroupNoticeMethod = EntGroupEditionProcessor.class.getDeclaredMethod("handleGroupNotice", GroupEditRequest.class, ScrmPersonalWxGroupInfoEntity.class);
        handleGroupNoticeMethod.setAccessible(true);
        handleGroupNoticeMethod.invoke(entGroupEditionProcessor, request, groupInfoEntity);
    }

    /**
     * 测试场景：群组状态不是 SUCCESS
     */
    @Test
    public void testHandleGroupNotice_GroupStatusNotSuccess() throws Throwable {
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.INIT.getCode());
        invokeHandleGroupNotice(request, groupInfoEntity);
        verify(groupManageDomainService, never()).queryGroupMsgById(anyLong());
        verify(dsCorpGroupAclService, never()).setGroupNotifyMsg(anyString(), anyString());
    }

    /**
     * 测试场景：群组状态是 SUCCESS，旧的群公告消息为空，新的群公告内容为空
     */
    @Test
    public void testHandleGroupNotice_GroupStatusSuccess_OldNoticeNull_NewNoticeNull() throws Throwable {
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.SUCCESS.getCode());
        when(groupInfoEntity.getGroupNotice()).thenReturn(null);
        request.setGroupNotice(null);
        invokeHandleGroupNotice(request, groupInfoEntity);
        verify(groupManageDomainService, never()).queryGroupMsgById(anyLong());
        verify(dsCorpGroupAclService, never()).setGroupNotifyMsg(anyString(), anyString());
    }

    /**
     * 测试场景：群组状态是 SUCCESS，旧的群公告消息为空，新的群公告内容不为空
     */
    @Test
    public void testHandleGroupNotice_GroupStatusSuccess_OldNoticeNull_NewNoticeNotNull() throws Throwable {
        // Arrange
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.SUCCESS.getCode());
        when(groupInfoEntity.getGroupNotice()).thenReturn(null);
        when(groupInfoEntity.getAppId()).thenReturn("appId");
        when(groupInfoEntity.getGroupId()).thenReturn("testGroupId");
        when(personalWxCommonService.getCreator()).thenReturn("creator");
        when(groupManageDomainService.saveGroupMsg(any(ScrmPersonalWxGroupMsg.class))).thenReturn(1L);
        GroupNotice groupNotice = new GroupNotice();
        groupNotice.setContent("New Notice");
        request.setGroupNotice(groupNotice);
        // Act
        invokeHandleGroupNotice(request, groupInfoEntity);
        // Assert
        ArgumentCaptor<ScrmPersonalWxGroupMsg> groupMsgCaptor = ArgumentCaptor.forClass(ScrmPersonalWxGroupMsg.class);
        verify(groupManageDomainService).saveGroupMsg(groupMsgCaptor.capture());
        verify(dsCorpGroupAclService).setGroupNotifyMsg("testGroupId", "New Notice");
        ScrmPersonalWxGroupMsg capturedGroupMsg = groupMsgCaptor.getValue();
        assertEquals("creator", capturedGroupMsg.getCreator());
        assertEquals("appId", capturedGroupMsg.getAppId());
        assertEquals("New Notice", capturedGroupMsg.getContent());
        assertNull(capturedGroupMsg.getId());
        assertNull(capturedGroupMsg.getMsgTypes());
        assertNull(capturedGroupMsg.getSendType());
        assertNull(capturedGroupMsg.getSendTime());
        assertNull(capturedGroupMsg.getSendFrequency());
        assertNull(capturedGroupMsg.getSendDuration());
    }

    /**
     * 测试场景：群组状态是 SUCCESS，旧的群公告消息不为空，新的群公告内容为空
     */
    @Test
    public void testHandleGroupNotice_GroupStatusSuccess_OldNoticeNotNull_NewNoticeNull() throws Throwable {
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.SUCCESS.getCode());
        when(groupInfoEntity.getGroupNotice()).thenReturn(1L);
        when(groupInfoEntity.getGroupId()).thenReturn("testGroupId");
        request.setGroupNotice(null);
        ScrmPersonalWxGroupMsg oldGroupMsg = ScrmPersonalWxGroupMsg.builder().id(1L).content("Old Notice").build();
        when(groupManageDomainService.queryGroupMsgById(1L)).thenReturn(oldGroupMsg);
        invokeHandleGroupNotice(request, groupInfoEntity);
        verify(dsCorpGroupAclService).setGroupNotifyMsg("testGroupId", "");
        verify(groupManageDomainService).queryGroupMsgById(1L);
        verify(groupManageDomainService, never()).saveGroupMsg(any(ScrmPersonalWxGroupMsg.class));
    }

    /**
     * 测试场景：群组状态是 SUCCESS，旧的群公告消息不为空，新的群公告内容不为空，新旧内容相同
     */
    @Test
    public void testHandleGroupNotice_GroupStatusSuccess_OldNoticeNotNull_NewNoticeNotNull_ContentSame() throws Throwable {
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.SUCCESS.getCode());
        when(groupInfoEntity.getGroupNotice()).thenReturn(1L);
        GroupNotice groupNotice = new GroupNotice();
        groupNotice.setContent("Old Notice");
        request.setGroupNotice(groupNotice);
        ScrmPersonalWxGroupMsg oldGroupMsg = ScrmPersonalWxGroupMsg.builder().id(1L).content("Old Notice").build();
        when(groupManageDomainService.queryGroupMsgById(1L)).thenReturn(oldGroupMsg);
        invokeHandleGroupNotice(request, groupInfoEntity);
        verify(dsCorpGroupAclService, never()).setGroupNotifyMsg(anyString(), anyString());
        verify(groupManageDomainService).queryGroupMsgById(1L);
        verify(groupManageDomainService, never()).saveGroupMsg(any(ScrmPersonalWxGroupMsg.class));
    }

    /**
     * 测试场景：群组状态是 SUCCESS，旧的群公告消息不为空，新的群公告内容不为空，新旧内容不相同
     */
    @Test
    public void testHandleGroupNotice_GroupStatusSuccess_OldNoticeNotNull_NewNoticeNotNull_ContentDifferent() throws Throwable {
        when(groupInfoEntity.getStatus()).thenReturn(PersonalGroupStatusEnum.SUCCESS.getCode());
        when(groupInfoEntity.getGroupNotice()).thenReturn(1L);
        when(groupInfoEntity.getGroupId()).thenReturn("testGroupId");
        GroupNotice groupNotice = new GroupNotice();
        groupNotice.setContent("New Notice");
        request.setGroupNotice(groupNotice);
        ScrmPersonalWxGroupMsg oldGroupMsg = ScrmPersonalWxGroupMsg.builder().id(1L).content("Old Notice").build();
        when(groupManageDomainService.queryGroupMsgById(1L)).thenReturn(oldGroupMsg);
        invokeHandleGroupNotice(request, groupInfoEntity);
        verify(dsCorpGroupAclService).setGroupNotifyMsg("testGroupId", "New Notice");
        verify(groupManageDomainService).queryGroupMsgById(1L);
        verify(groupManageDomainService).updateGroupMsgById(oldGroupMsg);
    }
}
