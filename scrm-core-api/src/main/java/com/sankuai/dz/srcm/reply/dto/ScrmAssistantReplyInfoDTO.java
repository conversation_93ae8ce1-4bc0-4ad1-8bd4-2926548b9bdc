package com.sankuai.dz.srcm.reply.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ScrmAssistantReplyInfoDTO implements Serializable {

    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date askTime;

    private String askerName;

    private String askerId;

    private int scene;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyTime;

    private int status;

    private int isOvertime;

    private String handleTime;

}
