package com.sankuai.scrm.core.service.pchat.adapter.service.tuse;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.enums.PersonalMsgSendTypeEnum;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupEditRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupMsg;
import com.sankuai.dz.srcm.pchat.request.scrm.GroupWelcomeMsg;
import com.sankuai.dz.srcm.pchat.tanjing.GroupManageService;
import com.sankuai.scrm.core.service.pchat.adapter.annotation.PrivateLiveProcessor;
import com.sankuai.scrm.core.service.pchat.adapter.bo.GroupEditionBO;
import com.sankuai.scrm.core.service.pchat.adapter.bo.GroupNoticeEditionBO;
import com.sankuai.scrm.core.service.pchat.adapter.service.GroupEditionProcessor;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import com.sankuai.scrm.core.service.pchat.constant.ExceptionMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMsg;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.dto.group.SensitiveContextDTO;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxLogBusinessRelateTableEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxLogBusinessTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.WeChatType;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.pchat.mq.dto.GroupNoticeTaskDTO;
import com.sankuai.scrm.core.service.pchat.mq.producer.DelayPayload;
import com.sankuai.scrm.core.service.pchat.mq.producer.DelayTaskCodeEnum;
import com.sankuai.scrm.core.service.pchat.mq.producer.ScrmPersonalWxMsgTaskProducer;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxCommonService;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxSendMessageService;
import com.sankuai.scrm.core.service.pchat.thirdparty.consultant.ConsultantService;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TuseGroupEditionProcessorHandleGroupNoticeTaskTest {

    @InjectMocks
    private TuseGroupEditionProcessor tuseGroupEditionProcessor;

    @Mock
    private GroupManageService groupManageService;

    @Mock
    private ScrmPersonalWxCommonService personalWxCommonService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常场景：群公告发送成功，日志记录成功
     */
    @Test
    public void testHandleGroupNotice_Success() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setCreateOwner("owner");
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        groupInfoEntity.setId(1L);
        GroupNoticeEditionBO noticeEditionBO = new GroupNoticeEditionBO();
        noticeEditionBO.setGroupInfoEntity(groupInfoEntity);
        noticeEditionBO.setContent("content");
        AsyncInvokeResultDTO asyncInvokeResultDTO = new AsyncInvokeResultDTO();
        asyncInvokeResultDTO.setVcSerialNo("serialNo");
        when(groupManageService.chatRoomAnnouncements(anyString(), anyString(), anyString(), anyString())).thenReturn(asyncInvokeResultDTO);
        // act
        tuseGroupEditionProcessor.handleGroupNotice(noticeEditionBO);
        // assert
        verify(groupManageService).chatRoomAnnouncements(ApiConstants.merchantNo, "owner", "chatRoomSerialNo", "content");
        verify(personalWxCommonService).createSerialNoLog("serialNo", PersonalWxLogBusinessTypeEnum.GROUP_NOTICE, PersonalWxLogBusinessRelateTableEnum.SCRM_PERSONALWX_GROUPINFO, 1L);
    }

    /**
     * 测试异常场景：groupInfoEntity 为空
     */
    @Test(expected = NullPointerException.class)
    public void testHandleGroupNotice_GroupInfoEntityIsNull() throws Throwable {
        // arrange
        GroupNoticeEditionBO noticeEditionBO = new GroupNoticeEditionBO();
        noticeEditionBO.setGroupInfoEntity(null);
        noticeEditionBO.setContent("content");
        // act
        tuseGroupEditionProcessor.handleGroupNotice(noticeEditionBO);
        // assert
        // Expecting NullPointerException
    }

    /**
     * 测试异常场景：groupManageService.chatRoomAnnouncements 返回 null
     */
    @Test(expected = NullPointerException.class)
    public void testHandleGroupNotice_ChatRoomAnnouncementsReturnsNull() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setCreateOwner("owner");
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        groupInfoEntity.setId(1L);
        GroupNoticeEditionBO noticeEditionBO = new GroupNoticeEditionBO();
        noticeEditionBO.setGroupInfoEntity(groupInfoEntity);
        noticeEditionBO.setContent("content");
        when(groupManageService.chatRoomAnnouncements(anyString(), anyString(), anyString(), anyString())).thenReturn(null);
        // act
        tuseGroupEditionProcessor.handleGroupNotice(noticeEditionBO);
        // assert
        // Expecting NullPointerException
    }

    /**
     * 测试异常场景：personalWxCommonService.createSerialNoLog 抛出异常
     */
    @Test(expected = Exception.class)
    public void testHandleGroupNotice_CreateSerialNoLogThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = new ScrmPersonalWxGroupInfoEntity();
        groupInfoEntity.setCreateOwner("owner");
        groupInfoEntity.setChatRoomWxSerialNo("chatRoomSerialNo");
        groupInfoEntity.setId(1L);
        GroupNoticeEditionBO noticeEditionBO = new GroupNoticeEditionBO();
        noticeEditionBO.setGroupInfoEntity(groupInfoEntity);
        noticeEditionBO.setContent("content");
        AsyncInvokeResultDTO asyncInvokeResultDTO = new AsyncInvokeResultDTO();
        asyncInvokeResultDTO.setVcSerialNo("serialNo");
        doThrow(new Exception()).when(personalWxCommonService).createSerialNoLog(anyString(), any(PersonalWxLogBusinessTypeEnum.class), any(PersonalWxLogBusinessRelateTableEnum.class), anyLong());
        // act
        tuseGroupEditionProcessor.handleGroupNotice(noticeEditionBO);
        // assert
        // Expecting Exception
    }
}
