package com.sankuai.scrm.core.service.user.crane;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthCommonUserTag;
import com.sankuai.scrm.core.service.user.domain.tag.NonLiveActionUserDataDomainService;
import com.sankuai.scrm.core.service.user.domain.tag.NonLiveActionUserDataEsDocService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmUserTagScoreCalcTaskInitEsCommonUserTagTest {

    @Mock
    private NonLiveActionUserDataDomainService nonLiveActionUserDataDomainService;

    @Mock
    private NonLiveActionUserDataEsDocService nonLiveActionUserDataEsDocService;

    private ScrmUserTagScoreInitTask scrmUserTagScoreInitTask;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<Environment> environmentMock;

    @BeforeEach
    public void setUp() {
        // Create new instance and inject mocks
        scrmUserTagScoreInitTask = new ScrmUserTagScoreInitTask();
        TestUtils.setField(scrmUserTagScoreInitTask, "nonLiveActionUserDataDomainService", nonLiveActionUserDataDomainService);
        TestUtils.setField(scrmUserTagScoreInitTask, "nonLiveActionUserDataEsDocService", nonLiveActionUserDataEsDocService);
        // Mock static methods
        lionMock = mockStatic(Lion.class);
        environmentMock = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
        when(Lion.getInt(eq("test-app"), eq("scrm_init_es_user_tag_page_size"), eq(300))).thenReturn(300);
    }

    @AfterEach
    public void tearDown() {
        if (lionMock != null) {
            lionMock.close();
        }
        if (environmentMock != null) {
            environmentMock.close();
        }
    }

    private List<ScrmUserGrowthCommonUserTag> createUserTags(int count, long startId) {
        List<ScrmUserGrowthCommonUserTag> tags = new ArrayList<>();
        for (long i = 0; i < count; i++) {
            ScrmUserGrowthCommonUserTag tag = new ScrmUserGrowthCommonUserTag();
            tag.setId(startId + i);
            tags.add(tag);
        }
        return tags;
    }

    /**
     * Tests single page of results
     */
    @Test
    public void testInitEsCommonUserTag_SinglePage() throws Throwable {
        // arrange
        List<ScrmUserGrowthCommonUserTag> singlePage = createUserTags(150, 1L);
        when(nonLiveActionUserDataDomainService.queryUserTag(0L, 300)).thenReturn(singlePage);
        // act
        scrmUserTagScoreInitTask.initEsCommonUserTag();
        // assert
        verify(nonLiveActionUserDataEsDocService, times(1)).writeTag(anyList());
        verify(nonLiveActionUserDataDomainService).queryUserTag(0L, 300);
    }

    /**
     * Tests empty result set
     */
    @Test
    public void testInitEsCommonUserTag_EmptyResult() throws Throwable {
        // arrange
        when(nonLiveActionUserDataDomainService.queryUserTag(anyLong(), anyInt())).thenReturn(new ArrayList<>());
        // act
        scrmUserTagScoreInitTask.initEsCommonUserTag();
        // assert
        verify(nonLiveActionUserDataEsDocService, never()).writeTag(anyList());
        verify(nonLiveActionUserDataDomainService).queryUserTag(0L, 300);
    }


    /**
     * Tests different page size from config
     */
    @Test
    public void testInitEsCommonUserTag_CustomPageSize() throws Throwable {
        // arrange
        when(Lion.getInt(eq("test-app"), eq("scrm_init_es_user_tag_page_size"), eq(300))).thenReturn(500);
        List<ScrmUserGrowthCommonUserTag> tags = createUserTags(500, 1L);
        when(nonLiveActionUserDataDomainService.queryUserTag(0L, 500)).thenReturn(tags);
        // act
        scrmUserTagScoreInitTask.initEsCommonUserTag();
        // assert
        verify(nonLiveActionUserDataDomainService).queryUserTag(0L, 500);
        verify(nonLiveActionUserDataEsDocService, times(1)).writeTag(anyList());
    }

    /**
     * Utility class for setting private fields
     */
    private static class TestUtils {

        public static void setField(Object target, String fieldName, Object value) {
            try {
                java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                field.set(target, value);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
