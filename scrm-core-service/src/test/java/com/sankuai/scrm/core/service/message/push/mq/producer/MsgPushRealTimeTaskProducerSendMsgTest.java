package com.sankuai.scrm.core.service.message.push.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class MsgPushRealTimeTaskProducerSendMsgTest {

    @Mock
    private IProducerProcessor<?, String> producer;

    @InjectMocks
    private MsgPushRealTimeTaskProducer msgPushRealTimeTaskProducer;

    /**
     * 测试发送空消息场景
     * 预期：直接返回不处理
     */
    @Test
    public void testSendMsgWithEmptyMessage() throws Throwable {
        // arrange
        String emptyMsg = "";
        // act
        msgPushRealTimeTaskProducer.sendMsg(emptyMsg);
        // assert
        verify(producer, never()).sendMessage(any());
    }

    /**
     * 测试发送成功场景
     * 预期：正常发送不抛出异常
     */
    @Test
    public void testSendMsgSuccessfully() throws Throwable {
        // arrange
        String validMsg = "valid message";
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(validMsg)).thenReturn(successResult);
        // act
        msgPushRealTimeTaskProducer.sendMsg(validMsg);
        // assert
        verify(producer).sendMessage(validMsg);
        // 不验证日志
    }

    /**
     * 测试发送失败场景
     * 预期：正常处理不抛出异常
     */
    @Test
    public void testSendMsgFailed() throws Throwable {
        // arrange
        String validMsg = "valid message";
        ProducerResult failedResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(validMsg)).thenReturn(failedResult);
        // act
        msgPushRealTimeTaskProducer.sendMsg(validMsg);
        // assert
        verify(producer).sendMessage(validMsg);
        // 不验证日志
    }

    /**
     * 测试发送异常场景
     * 预期：捕获异常不抛出
     */
    @Test
    public void testSendMsgWithException() throws Throwable {
        // arrange
        String validMsg = "valid message";
        when(producer.sendMessage(validMsg)).thenThrow(new RuntimeException("test exception"));
        // act
        msgPushRealTimeTaskProducer.sendMsg(validMsg);
        // assert
        verify(producer).sendMessage(validMsg);
        // 不验证日志
    }

    /**
     * 测试发送返回null结果场景
     * 预期：正常处理不抛出异常
     */
    @Test
    public void testSendMsgWithNullResult() throws Throwable {
        // arrange
        String validMsg = "valid message";
        when(producer.sendMessage(validMsg)).thenReturn(null);
        // act
        msgPushRealTimeTaskProducer.sendMsg(validMsg);
        // assert
        verify(producer).sendMessage(validMsg);
        // 不验证日志
    }
}
