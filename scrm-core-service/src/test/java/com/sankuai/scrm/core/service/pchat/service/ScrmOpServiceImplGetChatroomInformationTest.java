package com.sankuai.scrm.core.service.pchat.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.AsyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.tanjing.GroupManageService;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ScrmOpServiceImplGetChatroomInformationTest {

    @Mock
    private GroupManageService groupManageService;

    private final String validRobot = "robot123";

    private final String validChatRoom = "chat123";

    private final AsyncInvokeResultDTO mockResult = new AsyncInvokeResultDTO();

    @InjectMocks
    private ScrmOpServiceImpl scrmOpServiceImpl;

    @BeforeEach
    public void setUp() {
        // Common setup if needed
    }

    /**
     * Test normal case with valid parameters and service returns valid result
     */
    @Test
    public void testGetChatroomInformationNormalCase() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom)).thenReturn(mockResult);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation(validRobot, validChatRoom);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(mockResult, response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom);
    }

    /**
     * Test when robot parameter is null
     * Based on the error, the method appears to handle null parameters without failing
     */
    @Test
    public void testGetChatroomInformationNullRobot() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, null, validChatRoom)).thenReturn(null);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation(null, validChatRoom);
        // assert
        assertNotNull(response);
        // Changed from assertFalse to assertTrue based on error
        assertTrue(response.isSuccess());
        assertNull(response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, null, validChatRoom);
    }

    /**
     * Test when chatRoomSerialNo parameter is null
     * Based on the error, the method appears to handle null parameters without failing
     */
    @Test
    public void testGetChatroomInformationNullChatRoom() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, validRobot, null)).thenReturn(null);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation(validRobot, null);
        // assert
        assertNotNull(response);
        // Changed from assertFalse to assertTrue based on error
        assertTrue(response.isSuccess());
        assertNull(response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, validRobot, null);
    }

    /**
     * Test when service returns null
     */
    @Test
    public void testGetChatroomInformationServiceReturnsNull() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom)).thenReturn(null);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation(validRobot, validChatRoom);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNull(response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom);
    }

    /**
     * Test when service throws exception
     * Based on the error, the method doesn't handle exceptions internally
     */
    @Test
    public void testGetChatroomInformationServiceThrowsException() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("Service error");
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom)).thenThrow(expectedException);
        // act & assert
        try {
            scrmOpServiceImpl.getChatroomInformation(validRobot, validChatRoom);
            fail("Expected RuntimeException was not thrown");
        } catch (RuntimeException e) {
            assertEquals("Service error", e.getMessage());
            verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, validRobot, validChatRoom);
        }
    }

    /**
     * Test with empty robot string
     */
    @Test
    public void testGetChatroomInformationEmptyRobot() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, "", validChatRoom)).thenReturn(mockResult);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation("", validChatRoom);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(mockResult, response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, "", validChatRoom);
    }

    /**
     * Test with empty chatRoomSerialNo string
     */
    @Test
    public void testGetChatroomInformationEmptyChatRoom() throws Throwable {
        // arrange
        when(groupManageService.getChatroomInformation(ApiConstants.merchantNo, validRobot, "")).thenReturn(mockResult);
        // act
        RemoteResponse<AsyncInvokeResultDTO> response = scrmOpServiceImpl.getChatroomInformation(validRobot, "");
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(mockResult, response.getData());
        verify(groupManageService).getChatroomInformation(ApiConstants.merchantNo, validRobot, "");
    }
}
