package com.sankuai.dz.srcm.pchat.tanjing;

import com.sankuai.dz.srcm.pchat.anno.ApiMapping;
import com.sankuai.dz.srcm.pchat.dto.*;
import com.sankuai.dz.srcm.pchat.request.MerchantRobotPageRequest;

/**
 * 机器人信息接口
 */
@ApiMapping(name = "机器人信息接口")
public interface RobotInfoService {


    /**
     * 【异步调用】异步获取机器人好友列表接口（仅PC可用）
     * 同步获取好友列表数据有差异时，商家通过调用“异步获取机器人好友列表”这个接口可以获取到更准确的机器人的好友列表，通过3022好友信息列表回调；
     *
     * @param vcMerchantNo    商家编号
     * @param vcRobotSerialNo
     * @return
     */
    @ApiMapping(path = "/scrm/Friend/GetFriendListAsync", name = "【异步调用】异步获取机器人好友列表接口（仅PC可用）")
    AsyncInvokeResultDTO getFriendListAsync(String vcMerchantNo, String vcRobotSerialNo);

    /**
     * 【同步调用】获取机器人好友列表接口（兼容PC）
     *
     * 商家通过调用“获取机器人好友列表”这个接口可以获取到机器人的好友列表。
     *
     * @param vcMerchantNo    商家编号
     * @param vcRobotSerialNo 机器人编号
     * @return
     */
    @ApiMapping(path = "/sync/Friend/GetFriendList", name = "【同步调用】获取机器人好友列表接口（兼容PC）", isSync = true)
    SyncInvokeResultDTO<FriendListDTO> getFriendList(String vcMerchantNo, String vcRobotSerialNo);

    /**
     * 获取群列表接口（兼容PC）
     *
     * @param vcMerchantNo       商家编号
     * @param vcRobotSerialNo    机器人编号
     * @param vcChatRoomSerialNo 群编号，值不传的话，查询全部
     * @param isOpenMessage      是否已关注（10 是 11 否）, 0 查询全部
     * @return
     */
    @ApiMapping(path = "/sync/ChatRoom/GetChatRoomList", name = "获取群列表接口（兼容PC）", isSync = true)
    SyncInvokeResultDTO<GroupListDTO> getChatRoomList(String vcMerchantNo, String vcRobotSerialNo, String vcChatRoomSerialNo, Integer isOpenMessage);

    /**
     * 修改机器人性别
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @param sex             性别：0 未知 1 男 2 女
     * @return
     */
    @ApiMapping(path = "/scrm/robot/modify-robot-gender", name = "修改机器人性别", isSync = true)
    InvokeResultDTO modifyRobotGender(String robot_serial_no, String merchant_no, Integer sex);

    /**
     * 修改机器人头像
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @param head_img_url    新头像链接
     * @return
     */
    @ApiMapping(path = "/scrm/robot/modify-robot-headimg", name = "修改机器人头像", isSync = true)
    InvokeResultDTO modifyRobotHeadimg(String robot_serial_no, String merchant_no, String head_img_url);

    /**
     * 修改机器人昵称
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @param nick_name       昵称
     * @return
     */
    @ApiMapping(path = "/scrm/robot/modify-robot-nickname", name = "修改机器人昵称", isSync = true)
    InvokeResultDTO modifyRobotNickname(String robot_serial_no, String merchant_no, String nick_name);

    /**
     * 修改机器人个性签名
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @param whats_up        个性签名
     * @return
     */
    @ApiMapping(path = "/scrm/robot/modify-robot-whats-up", name = "修改机器人个性签名")
    AsyncInvokeResultDTO modifyRobotWhatsUp(String robot_serial_no, String merchant_no, String whats_up);


    /**
     * 分页获取商家机器人详情列表
     * 1090
     *
     * @param request
     * @return
     */
    @ApiMapping(path = "/scrm/Robot/get-merchant-robot-page-list", name = "分页获取商家机器人详情列表")
    AsyncInvokeResultDTO getMerchantRobotPageList(MerchantRobotPageRequest request);

    /**
     * 查询账号是否存在黑名单接口
     *
     * @param vcMerchantNo    商家编号
     * @param vcUserSerialNos 用户编号
     * @return
     */
    @ApiMapping(path = "/sync/Account/IsInBlackList", name = "查询账号是否存在黑名单接口", isSync = true)
    SyncInvokeResultDTO<String> isInBlackList(String vcMerchantNo, String[] vcUserSerialNos);

    /**
     * 获取机器人个人二维码3.0
     * 4517
     *
     * @param merchant_no     商家编号
     * @param robot_serial_no 机器人编号
     * @param type            1 获取 2 重置
     * @return
     */
    @ApiMapping(path = "/scrm/robot/get-or-update-personal-card", name = "获取机器人个人二维码3.0")
    AsyncInvokeResultDTO getOrUpdatePersonalCard(String merchant_no, String robot_serial_no, Integer type);

    /**
     * 修改地区
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @param region_code     地区code
     * @return
     */
    @ApiMapping(path = "/scrm/robot/modify-robot-region", name = "修改地区", isSync = true)
    InvokeResultDTO modifyRobotRegion(String robot_serial_no, String merchant_no, String region_code);

    /**
     * 【异步调用】异步分页获取机器人好友列表接口（仅PC可用）
     * 此接口用于分页获取机器人好友列表数据，当机器人好友数量较多时建议使用分页接口获取。仅支持PC号
     *
     * @param robot_serial_no 机器人编号
     * @param merchant_no     商家编号
     * @return
     */
    @ApiMapping(path = "/scrm/Friend/get-batch-friend-list", name = "【异步调用】异步分页获取机器人好友列表接口（仅PC可用）")
    AsyncInvokeResultDTO getBatchFriendList(String robot_serial_no, String merchant_no);

}
