package com.sankuai.dz.srcm.group.dynamiccode.dto;

import com.sankuai.dz.srcm.group.dynamiccode.enums.CommonOperationResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 企微群活码，保存渠道来源的操作结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveGroupDynamicCodeChannelResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作结果
     *
     * @see CommonOperationResultEnum#getCode()
     */
    private int resultCode;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 保存成功后返回保存后的对象。若保存失败则为空。
     */
    private GroupDynamicCodeChannelDTO groupDynamicCodeChannelDTO;

    public SaveGroupDynamicCodeChannelResult(int resultCode, String msg) {
        this.resultCode = resultCode;
        this.msg = msg;
    }
}
