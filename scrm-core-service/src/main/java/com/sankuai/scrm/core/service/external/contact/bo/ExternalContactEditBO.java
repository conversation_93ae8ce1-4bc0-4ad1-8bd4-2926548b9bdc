package com.sankuai.scrm.core.service.external.contact.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.CorpWxEventBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExternalContactEditBO extends CorpWxEventBO {

    private String userId;

    private String externalUserId;

}
