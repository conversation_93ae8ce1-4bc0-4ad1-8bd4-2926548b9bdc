package com.sankuai.scrm.core.service.pchat.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.Calendar;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class DateUtilDateSubTest {

    /**
     * 测试正常情况 - 减去正整数天数
     */
    @Test
    void testDateSubPositiveDays() {
        // arrange
        Date inputDate = new Date();
        int daysToSubtract = 5;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.add(Calendar.DAY_OF_MONTH, -daysToSubtract);
        Date expectedDate = calendar.getTime();
        // act
        Date result = DateUtil.dateSub(inputDate, daysToSubtract);
        // assert
        assertEquals(expectedDate, result);
    }

    /**
     * 测试边界情况 - 减去0天
     */
    @Test
    void testDateSubZeroDays() {
        // arrange
        Date inputDate = new Date();
        int daysToSubtract = 0;
        // act
        Date result = DateUtil.dateSub(inputDate, daysToSubtract);
        // assert
        assertEquals(inputDate, result);
    }

    /**
     * 测试边界情况 - 减去负天数（实际应增加天数）
     */
    @Test
    void testDateSubNegativeDays() {
        // arrange
        Date inputDate = new Date();
        int daysToSubtract = -3;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.add(Calendar.DAY_OF_MONTH, -daysToSubtract);
        Date expectedDate = calendar.getTime();
        // act
        Date result = DateUtil.dateSub(inputDate, daysToSubtract);
        // assert
        assertEquals(expectedDate, result);
    }

    /**
     * 测试边界情况 - 减去最大整数天数
     */
    @Test
    void testDateSubMaxIntegerDays() {
        // arrange
        Date inputDate = new Date();
        int daysToSubtract = Integer.MAX_VALUE;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.add(Calendar.DAY_OF_MONTH, -daysToSubtract);
        Date expectedDate = calendar.getTime();
        // act
        Date result = DateUtil.dateSub(inputDate, daysToSubtract);
        // assert
        assertEquals(expectedDate, result);
    }

    /**
     * 测试边界情况 - 减去最小整数天数
     */
    @Test
    void testDateSubMinIntegerDays() {
        // arrange
        Date inputDate = new Date();
        int daysToSubtract = Integer.MIN_VALUE;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        calendar.add(Calendar.DAY_OF_MONTH, -daysToSubtract);
        Date expectedDate = calendar.getTime();
        // act
        Date result = DateUtil.dateSub(inputDate, daysToSubtract);
        // assert
        assertEquals(expectedDate, result);
    }

    /**
     * 测试异常情况 - 输入null日期
     */
    @Test
    void testDateSubNullDate() {
        // arrange
        Date inputDate = null;
        int daysToSubtract = 5;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            DateUtil.dateSub(inputDate, daysToSubtract);
        });
    }

    /**
     * 测试Calendar操作是否正确
     */
    @Test
    void testDateSubCalendarOperations() {
        // arrange
        Calendar mockCalendar = mock(Calendar.class);
        Date inputDate = new Date();
        Date expectedDate = new Date();
        int daysToSubtract = 7;
        try (MockedStatic<Calendar> calendarMock = Mockito.mockStatic(Calendar.class)) {
            calendarMock.when(Calendar::getInstance).thenReturn(mockCalendar);
            when(mockCalendar.getTime()).thenReturn(expectedDate);
            // act
            Date result = DateUtil.dateSub(inputDate, daysToSubtract);
            // assert
            verify(mockCalendar).setTime(inputDate);
            verify(mockCalendar).add(Calendar.DAY_OF_MONTH, -daysToSubtract);
            verify(mockCalendar).getTime();
            assertEquals(expectedDate, result);
        }
    }
}
