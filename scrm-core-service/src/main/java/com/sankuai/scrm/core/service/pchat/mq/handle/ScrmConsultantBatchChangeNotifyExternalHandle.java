package com.sankuai.scrm.core.service.pchat.mq.handle;

import cn.hutool.core.collection.CollectionUtil;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.dz.srcm.user.dto.ConsultantChangeRequest;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxTraceService;
import com.sankuai.scrm.core.service.user.domain.PrivateUserDataHandleDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@Slf4j
@Service
public class ScrmConsultantBatchChangeNotifyExternalHandle {

    @Resource
    private PrivateUserDataHandleDomainService userDataHandleDomainService;
    @Resource
    private ScrmPersonalWxTraceService personalWxTraceService;

    public ConsumeStatus messageHandle(MafkaMessage message) {
        try {
            String body = message.getBody().toString();
            log.info("咨询师批量修改收到消息：topic:{},messageId:{},body:{}", message.getTopic(), message.getMessageID(), body);
            try {
                List<ConsultantChangeRequest> dtos = JsonUtils.toList(body, ConsultantChangeRequest.class);
                if (CollectionUtil.isEmpty(dtos)) {
                    log.error("咨询师批量修改不符合的消息不做处理:" + body);
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                dtos.forEach(request -> {
                    userDataHandleDomainService.handleUserTagConsultantChange(request);
                });

                personalWxTraceService.refreshUserTag(dtos);

            } catch (Exception e) {
                log.error("咨询师批量修改不符合的消息不做处理:" + body, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            log.error("ScrmConsultantBatchChangeNotifyExternalConsumer has error,msg is {}", message, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}