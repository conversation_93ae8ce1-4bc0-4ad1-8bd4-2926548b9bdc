package com.sankuai.scrm.core.service.external.user.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.external.user.dal.entity.ScrmMemberBaseInfo;
import com.sankuai.scrm.core.service.external.user.domain.ScrmMemberBaseInfoDomainService;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupDomainService;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.WeixinInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExternalUserInfoUpdateJob {

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private GroupDomainService groupDomainService;

    @Autowired
    private GroupMemberDomainService groupMemberDomainService;

    @Autowired
    private ScrmMemberBaseInfoDomainService scrmMemberBaseInfoDomainService;

    @Autowired
    private MtUserCenterAclService mtUserCenterAclService;

    @Crane("com.sankuai.medicalcosmetology.scrm.core.external.user.base.info.update")
    public void updateBaseInfo(List<String> appIds) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.external.user.crane.ExternalUserInfoUpdateJob.updateBaseInfo(java.util.List)");
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        appIds.forEach(this::updateBaseInfoForGivenAppId);
    }

    private void updateBaseInfoForGivenAppId(String appId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.external.user.crane.ExternalUserInfoUpdateJob.updateBaseInfoForGivenAppId(java.lang.String)");
        if (StringUtils.isEmpty(appId)) {
            return;
        }
        String corpId = appConfigRepository.getCorpIdByAppId(appId);
        if (StringUtils.isEmpty(corpId)) {
            return;
        }
        int offset = 0;
        int rows = 100;
        while (true) {
            List<GroupInfoEntity> groupInfoEntities = groupDomainService.queryGroupInfo(corpId, offset, rows);
            if (CollectionUtils.isEmpty(groupInfoEntities)) {
                break;
            }
            for (GroupInfoEntity groupInfoEntity : groupInfoEntities) {
                if (groupInfoEntity == null) {
                    continue;
                }
                List<MemberInfoEntity> memberInfoEntityList = groupMemberDomainService.queryMembersByGroupId(corpId, groupInfoEntity.getGroupId());
                Map<String, MemberInfoEntity> memberInfoEntityMap = memberInfoEntityList.stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(MemberInfoEntity::getGroupMemberId, Function.identity(), (k1, k2) -> k2));
                Set<String> groupMemberIdSet = memberInfoEntityMap.keySet();
                List<ScrmMemberBaseInfo> scrmMemberBaseInfoList = scrmMemberBaseInfoDomainService.queryScrmMemberBaseInfoList(appId,
                        Lists.newArrayList(groupMemberIdSet));
                Map<String, ScrmMemberBaseInfo> scrmMemberBaseInfoMap = scrmMemberBaseInfoList.stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(ScrmMemberBaseInfo::getExternalUserId, Function.identity(), (k1, k2) -> k2));
                List<ScrmMemberBaseInfo> records = Lists.newArrayList();
                for (String groupMemberId : groupMemberIdSet) {
                    if (StringUtils.isEmpty(groupMemberId) || memberInfoEntityMap.get(groupMemberId) == null) {
                        continue;
                    }
                    MemberInfoEntity memberInfo = memberInfoEntityMap.get(groupMemberId);
                    ScrmMemberBaseInfo scrmMemberBaseInfo = scrmMemberBaseInfoMap.get(groupMemberId);
                    List<WeixinInfo> weixinInfos = mtUserCenterAclService.getWeixinInfos(memberInfo.getUnionId());
                    ScrmMemberBaseInfo record = scrmMemberBaseInfoDomainService.buildScrmMemberBaseInfo(appId, memberInfo, weixinInfos);
                    if (record == null) {
                        continue;
                    }
                    if (scrmMemberBaseInfo == null) {
                        records.add(record);
                    } else {
                        if (scrmMemberBaseInfoDomainService.isDifferent(scrmMemberBaseInfo, record)) {
                            scrmMemberBaseInfoDomainService.updateScrmMemberBaseInfo(appId, groupMemberId, record.getName(),
                                    record.getAvatar(), null, null);
                        }
                    }
                    if (!StringUtils.equals(memberInfo.getAvatar(), record.getAvatar())) {
                        groupMemberDomainService.updateMemberInfo(memberInfo.getId(), record.getAvatar());
                    }
                }
                scrmMemberBaseInfoDomainService.batchInsertScrmMemberBaseInfo(records);
            }
            offset += rows;
        }
    }

}
