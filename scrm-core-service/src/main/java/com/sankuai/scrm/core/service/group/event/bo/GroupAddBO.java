package com.sankuai.scrm.core.service.group.event.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.CorpWxEventBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupAddBO extends CorpWxEventBO {

    @JsonProperty("ChatId")
    private String groupId;
}
