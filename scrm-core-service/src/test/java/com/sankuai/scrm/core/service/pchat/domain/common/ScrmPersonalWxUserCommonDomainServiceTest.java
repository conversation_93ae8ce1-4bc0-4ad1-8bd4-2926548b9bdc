package com.sankuai.scrm.core.service.pchat.domain.common;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotInfoCommonMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserInfoCommonMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmPersonalWxUserCommonDomainServiceTest {

    @Mock
    private ScrmPersonalWxRobotInfoCommonMapper robotInfoCommonMapper;

    @InjectMocks
    private ScrmPersonalWxUserCommonDomainService domainService;

    @Mock
    private ScrmPersonalWxUserInfoCommonMapper wxUserInfoCommonMapper;

    /**
     * 测试正常保存机器人信息
     */
    @Test
    public void testSaveRobotInfoNormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = new ScrmPersonalWxRobotInfoCommon();
        robotInfo.setRobotSerialNo("test123");
        robotInfo.setBizId("testBiz");
        // act
        domainService.saveRobotInfo(robotInfo);
        // assert
        verify(robotInfoCommonMapper, times(1)).insertSelective(robotInfo);
        assert robotInfo.getAddTime() != null;
        assert robotInfo.getUpdateTime() != null;
    }

    /**
     * 测试保存null值的机器人信息
     */
    @Test
    public void testSaveRobotInfoWithNullInput() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = null;
        try {
            // act
            domainService.saveRobotInfo(robotInfo);
        } catch (Exception e) {
            // assert
            assert e instanceof NullPointerException;
            return;
        }
        assert false : "Expected NullPointerException but none was thrown";
    }

    /**
     * 测试保存部分字段为null的机器人信息
     */
    @Test
    public void testSaveRobotInfoWithPartialFields() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = new ScrmPersonalWxRobotInfoCommon();
        robotInfo.setRobotSerialNo("test123");
        // bizId is null
        // act
        domainService.saveRobotInfo(robotInfo);
        // assert
        verify(robotInfoCommonMapper, times(1)).insertSelective(robotInfo);
        assert robotInfo.getAddTime() != null;
        assert robotInfo.getUpdateTime() != null;
    }

    /**
     * 测试保存机器人信息时mapper抛出异常
     */
    @Test
    public void testSaveRobotInfoWhenMapperThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = new ScrmPersonalWxRobotInfoCommon();
        robotInfo.setRobotSerialNo("test123");
        robotInfo.setBizId("testBiz");
        doThrow(new RuntimeException("DB error")).when(robotInfoCommonMapper).insertSelective(any());
        try {
            // act
            domainService.saveRobotInfo(robotInfo);
        } catch (Exception e) {
            // assert
            assert e instanceof RuntimeException;
            assert "DB error".equals(e.getMessage());
            return;
        }
        assert false : "Expected RuntimeException but none was thrown";
    }

    /**
     * 测试保存机器人信息时时间设置是否正确
     */
    @Test
    public void testSaveRobotInfoTimeSetting() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = new ScrmPersonalWxRobotInfoCommon();
        robotInfo.setRobotSerialNo("test123");
        // act
        long beforeTime = System.currentTimeMillis();
        domainService.saveRobotInfo(robotInfo);
        long afterTime = System.currentTimeMillis();
        // assert
        assert robotInfo.getAddTime().getTime() >= beforeTime && robotInfo.getAddTime().getTime() <= afterTime;
        assert robotInfo.getUpdateTime().getTime() >= beforeTime && robotInfo.getUpdateTime().getTime() <= afterTime;
    }

    @Test
    public void testUpdateUserInfo_ShouldUpdateTimeAndCallMapper() {
        // arrange
        ScrmPersonalWxUserInfoCommon wxUserInfo = new ScrmPersonalWxUserInfoCommon();
        wxUserInfo.setId(1L);
        wxUserInfo.setSerialNo("SN123456");
        wxUserInfo.setWxId("wx123");
        wxUserInfo.setNickname("TestUser");
        Date beforeUpdate = new Date();
        // act
        domainService.updateUserInfo(wxUserInfo);
        // assert
        // Capture the argument passed to updateByPrimaryKey
        ArgumentCaptor<ScrmPersonalWxUserInfoCommon> userInfoCaptor = ArgumentCaptor.forClass(ScrmPersonalWxUserInfoCommon.class);
        verify(wxUserInfoCommonMapper, times(1)).updateByPrimaryKey(userInfoCaptor.capture());
        ScrmPersonalWxUserInfoCommon capturedUserInfo = userInfoCaptor.getValue();
        assertNotNull("Update time should not be null", capturedUserInfo.getUpdateTime());
        assertNotNull("The same user info object should be used", capturedUserInfo);
        assertEquals("User ID should remain unchanged", 1L, capturedUserInfo.getId().longValue());
        assertEquals("Serial number should remain unchanged", "SN123456", capturedUserInfo.getSerialNo());
        assertEquals("WxId should remain unchanged", "wx123", capturedUserInfo.getWxId());
        assertEquals("Nickname should remain unchanged", "TestUser", capturedUserInfo.getNickname());
        // Verify that update time is set to current time (with some tolerance)
        Date afterUpdate = new Date();
        Date updateTime = capturedUserInfo.getUpdateTime();
        // The update time should be between beforeUpdate and afterUpdate
        // This is a loose check since exact time comparison can be flaky
        assertTrue("Update time should be set to current time", !updateTime.before(beforeUpdate) && !updateTime.after(afterUpdate));
    }

    @Test
    public void testUpdateUserInfo_WithExistingUpdateTime_ShouldOverwrite() {
        // arrange
        ScrmPersonalWxUserInfoCommon wxUserInfo = new ScrmPersonalWxUserInfoCommon();
        wxUserInfo.setId(1L);
        // 1 day ago
        Date oldDate = new Date(System.currentTimeMillis() - 86400000);
        wxUserInfo.setUpdateTime(oldDate);
        // act
        domainService.updateUserInfo(wxUserInfo);
        // assert
        ArgumentCaptor<ScrmPersonalWxUserInfoCommon> userInfoCaptor = ArgumentCaptor.forClass(ScrmPersonalWxUserInfoCommon.class);
        verify(wxUserInfoCommonMapper, times(1)).updateByPrimaryKey(userInfoCaptor.capture());
        ScrmPersonalWxUserInfoCommon capturedUserInfo = userInfoCaptor.getValue();
        assertNotNull("Update time should not be null", capturedUserInfo.getUpdateTime());
        assertNotNull("The same user info object should be used", capturedUserInfo);
        // Verify that the old update time was overwritten
        assertNotEquals("Update time should be changed", oldDate, capturedUserInfo.getUpdateTime());
        assertTrue("Update time should be more recent than old date", capturedUserInfo.getUpdateTime().after(oldDate));
    }

    @Test
    public void testUpdateUserInfo_MinimalUserInfo_ShouldStillUpdate() {
        // arrange
        ScrmPersonalWxUserInfoCommon wxUserInfo = new ScrmPersonalWxUserInfoCommon();
        // Only set ID, which is needed for primary key update
        wxUserInfo.setId(1L);
        // act
        domainService.updateUserInfo(wxUserInfo);
        // assert
        ArgumentCaptor<ScrmPersonalWxUserInfoCommon> userInfoCaptor = ArgumentCaptor.forClass(ScrmPersonalWxUserInfoCommon.class);
        verify(wxUserInfoCommonMapper, times(1)).updateByPrimaryKey(userInfoCaptor.capture());
        ScrmPersonalWxUserInfoCommon capturedUserInfo = userInfoCaptor.getValue();
        assertNotNull("Update time should not be null", capturedUserInfo.getUpdateTime());
        assertEquals("User ID should remain unchanged", 1L, capturedUserInfo.getId().longValue());
    }
}
