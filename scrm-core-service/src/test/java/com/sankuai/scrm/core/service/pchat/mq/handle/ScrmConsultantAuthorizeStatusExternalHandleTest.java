package com.sankuai.scrm.core.service.pchat.mq.handle;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxConsultantInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxConsultantDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.mq.dto.ExternalConsultantInfoDTO;
import com.sankuai.scrm.core.service.pchat.service.ScrmPersonalWxTraceService;
import com.sankuai.scrm.core.service.pchat.service.WebcastService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
public class ScrmConsultantAuthorizeStatusExternalHandleTest extends BaseMockTest {

    @Mock
    private ScrmPersonalWxConsultantDomainService personalWxConsultantDomainService;
    @Mock
    private WebcastService webcastService;
    @Mock
    private ScrmPersonalWxTraceService personalWxTraceService;
    @Mock
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;

    @InjectMocks
    private ScrmConsultantAuthorizeStatusExternalHandle scrmConsultantAuthorizeStatusExternalHandle;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试messageHandle方法，当消息体为空时
     */
    @Test
    public void testMessageHandleWithEmptyBody() {
        // arrange
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, "");

        // act
        ConsumeStatus result = scrmConsultantAuthorizeStatusExternalHandle.messageHandle(message);

        // assert
        verify(personalWxConsultantDomainService, never()).saveOrUpdateConsultantInfo(any(ScrmPersonalWxConsultantInfo.class));
        assertSame(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试messageHandle方法，当消息体无法解析为DTO时
     */
    @Test
    public void testMessageHandleWithInvalidBody() {
        // arrange
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, "invalid json");

        // act
        ConsumeStatus result = scrmConsultantAuthorizeStatusExternalHandle.messageHandle(message);

        // assert
        verify(personalWxConsultantDomainService, never()).saveOrUpdateConsultantInfo(any(ScrmPersonalWxConsultantInfo.class));
        assertSame(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试messageHandle方法，当消息体可以解析为DTO且处理成功时
     */
    @Test
    public void testMessageHandleWithValidBodyAndSuccess() {
        // arrange
        ExternalConsultantInfoDTO dto = new ExternalConsultantInfoDTO();
        dto.setActionType(0); // 设置一个不会触发cancelAuthorize逻辑的actionType
        String body = JSONObject.toJSONString(dto);
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, body);

        // act
        ConsumeStatus result = scrmConsultantAuthorizeStatusExternalHandle.messageHandle(message);

        // assert
        verify(personalWxConsultantDomainService, times(1)).saveOrUpdateConsultantInfo(any(ScrmPersonalWxConsultantInfo.class));
        assertSame(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}