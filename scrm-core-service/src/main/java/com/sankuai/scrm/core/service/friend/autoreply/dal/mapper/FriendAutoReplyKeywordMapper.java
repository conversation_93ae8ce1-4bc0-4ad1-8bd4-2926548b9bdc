package com.sankuai.scrm.core.service.friend.autoreply.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.friend.autoreply.dal.entity.FriendAutoReplyKeyword;
import com.sankuai.scrm.core.service.friend.autoreply.dal.example.FriendAutoReplyKeywordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FriendAutoReplyKeywordMapper extends MybatisBLOBsMapper<FriendAutoReplyKeyword, FriendAutoReplyKeywordExample, Long> {

    int batchInsert(@Param("list") List<FriendAutoReplyKeyword> list);

    List<FriendAutoReplyKeyword> getMatchedKeyWord(@Param("appId") String appId, @Param("msg") String msg);
}