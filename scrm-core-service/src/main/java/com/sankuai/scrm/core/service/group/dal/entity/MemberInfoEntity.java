package com.sankuai.scrm.core.service.group.dal.entity;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *   表名: wechat_group_member_info
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class MemberInfoEntity implements Serializable {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Integer id;

    /**
     *   字段: union_id
     *   说明: 用户unionid
     */
    private String unionId;

    /**
     *   字段: join_type
     *   说明: 入群方式
     */
    private Integer joinType;

    /**
     *   字段: group_member_id
     *   说明: 群成员id
     */
    private String groupMemberId;

    /**
     *   字段: member_name
     *   说明: 成员名
     */
    private String memberName;

    /**
     *   字段: member_nick_name
     *   说明: 成员昵称
     */
    private String memberNickName;

    /**
     *   字段: avatar
     *   说明: 头像
     */
    private String avatar;

    /**
     *   字段: member_type
     *   说明: 成员类型，1-企业成员，2-普通成员
     */
    private Byte memberType;

    /**
     *   字段: status
     *   说明: 群成员状态 0:在群里 1离开群
     */
    private Byte status;

    /**
     *   字段: state
     *   说明: 企业自定义的state参数，用于区分不同的入群渠道
     */
    private String state;

    /**
     *   字段: enter_time
     *   说明: 进群时间
     */
    private Date enterTime;

    /**
     *   字段: group_id
     *   说明: 群id
     */
    private String groupId;

    /**
     *   字段: corp_id
     *   说明: 企业id
     */
    private String corpId;

    /**
     *   字段: org_id
     *   说明: 部门id
     */
    private Long orgId;

    /**
     *   字段: corp_name
     *   说明: 企业简称
     */
    private String corpName;

    /**
     *   字段: deleted
     *   说明: 是否删除 0：未删除  1：删除
     */
    private Boolean deleted;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: add_time
     *   说明: 删除时间
     */
    private Date addTime;
}