package com.sankuai.scrm.core.service.abtest.strategy;

import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.scrm.core.service.abtest.context.ABTestContext;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LossGroupStrategy extends AbstractScrmFridayIntelligentFollowStrategy  {


    @Override
    public String getName() {
        return "LossGroupStrategy";
    }


    @Override
    public ABTestContext execute(IntelligentFollowActionTrackDTO trackDTO, ABTestContext context) {
        buildABTestRecordMessageDTOAndSendMessage(context);
        return context;
    }

    @Override
    public void fillABTestRecordMessageDTO(ABTestContext context, GroupRetailAiEngineABTestRecordMessageDTO testRecordMessageDTO) {
        testRecordMessageDTO.setOriginValue(0);
        testRecordMessageDTO.setTestValue(0);
        testRecordMessageDTO.setTestVersion(abTestConfig.getTestVersion("default"));
    }
}