package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmAmProcessOrchestrationRelatedOrderInfoDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmAmProcessOrchestrationRelatedOrderInfoDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmAmProcessOrchestrationRelatedOrderInfoDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmAmProcessOrchestrationRelatedOrderInfoDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmAmProcessOrchestrationRelatedOrderInfoDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNull() {
            addCriterion("process_orchestration_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIsNotNull() {
            addCriterion("process_orchestration_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdEqualTo(Long value) {
            addCriterion("process_orchestration_id =", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotEqualTo(Long value) {
            addCriterion("process_orchestration_id <>", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThan(Long value) {
            addCriterion("process_orchestration_id >", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id >=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThan(Long value) {
            addCriterion("process_orchestration_id <", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdLessThanOrEqualTo(Long value) {
            addCriterion("process_orchestration_id <=", value, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdIn(List<Long> values) {
            addCriterion("process_orchestration_id in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotIn(List<Long> values) {
            addCriterion("process_orchestration_id not in", values, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationIdNotBetween(Long value1, Long value2) {
            addCriterion("process_orchestration_id not between", value1, value2, "processOrchestrationId");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNull() {
            addCriterion("process_orchestration_version is null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIsNotNull() {
            addCriterion("process_orchestration_version is not null");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionEqualTo(String value) {
            addCriterion("process_orchestration_version =", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotEqualTo(String value) {
            addCriterion("process_orchestration_version <>", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThan(String value) {
            addCriterion("process_orchestration_version >", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionGreaterThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version >=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThan(String value) {
            addCriterion("process_orchestration_version <", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLessThanOrEqualTo(String value) {
            addCriterion("process_orchestration_version <=", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionLike(String value) {
            addCriterion("process_orchestration_version like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotLike(String value) {
            addCriterion("process_orchestration_version not like", value, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionIn(List<String> values) {
            addCriterion("process_orchestration_version in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotIn(List<String> values) {
            addCriterion("process_orchestration_version not in", values, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionBetween(String value1, String value2) {
            addCriterion("process_orchestration_version between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andProcessOrchestrationVersionNotBetween(String value1, String value2) {
            addCriterion("process_orchestration_version not between", value1, value2, "processOrchestrationVersion");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIsNull() {
            addCriterion("execute_log_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIsNotNull() {
            addCriterion("execute_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdEqualTo(Long value) {
            addCriterion("execute_log_id =", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotEqualTo(Long value) {
            addCriterion("execute_log_id <>", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdGreaterThan(Long value) {
            addCriterion("execute_log_id >", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdGreaterThanOrEqualTo(Long value) {
            addCriterion("execute_log_id >=", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdLessThan(Long value) {
            addCriterion("execute_log_id <", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdLessThanOrEqualTo(Long value) {
            addCriterion("execute_log_id <=", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIn(List<Long> values) {
            addCriterion("execute_log_id in", values, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotIn(List<Long> values) {
            addCriterion("execute_log_id not in", values, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdBetween(Long value1, Long value2) {
            addCriterion("execute_log_id between", value1, value2, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotBetween(Long value1, Long value2) {
            addCriterion("execute_log_id not between", value1, value2, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdIsNull() {
            addCriterion("target_mt_user_id is null");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdIsNotNull() {
            addCriterion("target_mt_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdEqualTo(Long value) {
            addCriterion("target_mt_user_id =", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdNotEqualTo(Long value) {
            addCriterion("target_mt_user_id <>", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdGreaterThan(Long value) {
            addCriterion("target_mt_user_id >", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("target_mt_user_id >=", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdLessThan(Long value) {
            addCriterion("target_mt_user_id <", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdLessThanOrEqualTo(Long value) {
            addCriterion("target_mt_user_id <=", value, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdIn(List<Long> values) {
            addCriterion("target_mt_user_id in", values, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdNotIn(List<Long> values) {
            addCriterion("target_mt_user_id not in", values, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdBetween(Long value1, Long value2) {
            addCriterion("target_mt_user_id between", value1, value2, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andTargetMtUserIdNotBetween(Long value1, Long value2) {
            addCriterion("target_mt_user_id not between", value1, value2, "targetMtUserId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdIsNull() {
            addCriterion("uni_order_id is null");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdIsNotNull() {
            addCriterion("uni_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdEqualTo(String value) {
            addCriterion("uni_order_id =", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdNotEqualTo(String value) {
            addCriterion("uni_order_id <>", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdGreaterThan(String value) {
            addCriterion("uni_order_id >", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("uni_order_id >=", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdLessThan(String value) {
            addCriterion("uni_order_id <", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdLessThanOrEqualTo(String value) {
            addCriterion("uni_order_id <=", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdLike(String value) {
            addCriterion("uni_order_id like", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdNotLike(String value) {
            addCriterion("uni_order_id not like", value, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdIn(List<String> values) {
            addCriterion("uni_order_id in", values, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdNotIn(List<String> values) {
            addCriterion("uni_order_id not in", values, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdBetween(String value1, String value2) {
            addCriterion("uni_order_id between", value1, value2, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andUniOrderIdNotBetween(String value1, String value2) {
            addCriterion("uni_order_id not between", value1, value2, "uniOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountIsNull() {
            addCriterion("order_total_amount is null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountIsNotNull() {
            addCriterion("order_total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountEqualTo(BigDecimal value) {
            addCriterion("order_total_amount =", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("order_total_amount <>", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("order_total_amount >", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("order_total_amount >=", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountLessThan(BigDecimal value) {
            addCriterion("order_total_amount <", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("order_total_amount <=", value, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountIn(List<BigDecimal> values) {
            addCriterion("order_total_amount in", values, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("order_total_amount not in", values, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_total_amount between", value1, value2, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andOrderTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("order_total_amount not between", value1, value2, "orderTotalAmount");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andRelateTypeIsNull() {
            addCriterion("relate_type is null");
            return (Criteria) this;
        }

        public Criteria andRelateTypeIsNotNull() {
            addCriterion("relate_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelateTypeEqualTo(Byte value) {
            addCriterion("relate_type =", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeNotEqualTo(Byte value) {
            addCriterion("relate_type <>", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeGreaterThan(Byte value) {
            addCriterion("relate_type >", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("relate_type >=", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeLessThan(Byte value) {
            addCriterion("relate_type <", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeLessThanOrEqualTo(Byte value) {
            addCriterion("relate_type <=", value, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeIn(List<Byte> values) {
            addCriterion("relate_type in", values, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeNotIn(List<Byte> values) {
            addCriterion("relate_type not in", values, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeBetween(Byte value1, Byte value2) {
            addCriterion("relate_type between", value1, value2, "relateType");
            return (Criteria) this;
        }

        public Criteria andRelateTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("relate_type not between", value1, value2, "relateType");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdIsNull() {
            addCriterion("coupon_group_id is null");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdIsNotNull() {
            addCriterion("coupon_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdEqualTo(String value) {
            addCriterion("coupon_group_id =", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdNotEqualTo(String value) {
            addCriterion("coupon_group_id <>", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdGreaterThan(String value) {
            addCriterion("coupon_group_id >", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_group_id >=", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdLessThan(String value) {
            addCriterion("coupon_group_id <", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdLessThanOrEqualTo(String value) {
            addCriterion("coupon_group_id <=", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdLike(String value) {
            addCriterion("coupon_group_id like", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdNotLike(String value) {
            addCriterion("coupon_group_id not like", value, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdIn(List<String> values) {
            addCriterion("coupon_group_id in", values, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdNotIn(List<String> values) {
            addCriterion("coupon_group_id not in", values, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdBetween(String value1, String value2) {
            addCriterion("coupon_group_id between", value1, value2, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andCouponGroupIdNotBetween(String value1, String value2) {
            addCriterion("coupon_group_id not between", value1, value2, "couponGroupId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdIsNull() {
            addCriterion("unified_coupon_id is null");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdIsNotNull() {
            addCriterion("unified_coupon_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdEqualTo(String value) {
            addCriterion("unified_coupon_id =", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdNotEqualTo(String value) {
            addCriterion("unified_coupon_id <>", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdGreaterThan(String value) {
            addCriterion("unified_coupon_id >", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdGreaterThanOrEqualTo(String value) {
            addCriterion("unified_coupon_id >=", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdLessThan(String value) {
            addCriterion("unified_coupon_id <", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdLessThanOrEqualTo(String value) {
            addCriterion("unified_coupon_id <=", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdLike(String value) {
            addCriterion("unified_coupon_id like", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdNotLike(String value) {
            addCriterion("unified_coupon_id not like", value, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdIn(List<String> values) {
            addCriterion("unified_coupon_id in", values, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdNotIn(List<String> values) {
            addCriterion("unified_coupon_id not in", values, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdBetween(String value1, String value2) {
            addCriterion("unified_coupon_id between", value1, value2, "unifiedCouponId");
            return (Criteria) this;
        }

        public Criteria andUnifiedCouponIdNotBetween(String value1, String value2) {
            addCriterion("unified_coupon_id not between", value1, value2, "unifiedCouponId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}