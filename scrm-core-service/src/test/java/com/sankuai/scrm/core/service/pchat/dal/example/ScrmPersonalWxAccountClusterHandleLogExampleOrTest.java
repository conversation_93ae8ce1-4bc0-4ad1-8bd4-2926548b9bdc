package com.sankuai.scrm.core.service.pchat.dal.example;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxAccountClusterHandleLogExampleOrTest {

    private ScrmPersonalWxAccountClusterHandleLogExample scrmPersonalWxAccountClusterHandleLogExample;

    @Before
    public void setUp() {
        scrmPersonalWxAccountClusterHandleLogExample = new ScrmPersonalWxAccountClusterHandleLogExample();
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmPersonalWxAccountClusterHandleLogExample.getOredCriteria().size();
        // act
        ScrmPersonalWxAccountClusterHandleLogExample.Criteria criteria = scrmPersonalWxAccountClusterHandleLogExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmPersonalWxAccountClusterHandleLogExample.getOredCriteria().size());
        assertTrue(scrmPersonalWxAccountClusterHandleLogExample.getOredCriteria().contains(criteria));
    }
}
