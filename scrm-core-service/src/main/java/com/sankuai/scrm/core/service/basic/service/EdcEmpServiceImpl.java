package com.sankuai.scrm.core.service.basic.service;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.basic.dto.emp.EmpInfoDTO;
import com.sankuai.dz.srcm.basic.service.EdcEmpService;
import com.sankuai.edc.open.common.dto.Paging;
import com.sankuai.edc.open.common.exception.EDCThriftException;
import com.sankuai.edc.open.sdk.model.domain.EdcEmpBaseItems;
import com.sankuai.edc.open.sdk.model.domain.EdcSearchEmpKeyword;
import com.sankuai.edc.open.sdk.model.domain.EdcSearchEmpOption;
import com.sankuai.edc.open.sdk.model.response.EdcOpResponse;
import com.sankuai.edc.open.sdk.service.IEdcEmpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

@Slf4j
@MdpPigeonServer
public class EdcEmpServiceImpl implements EdcEmpService {

    @Autowired
    private IEdcEmpService iEdcEmpService;

    @Override
    public PageRemoteResponse<EmpInfoDTO> queryEmpInfoByKeyWord(String searchWord, Integer page, Integer pageSize) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.basic.service.EdcEmpServiceImpl.queryEmpInfoByKeyWord(java.lang.String,java.lang.Integer,java.lang.Integer)");
        if (StringUtils.isEmpty(searchWord)) {
            return PageRemoteResponse.illegalArgument("搜索关键词不能为空");
        }
        if (page == null || page <= 0 || pageSize == null || pageSize <= 0 || pageSize > 50) {
            return PageRemoteResponse.illegalArgument("Page或pageSize错误");
        }
        try {
            EdcSearchEmpKeyword empKeyword = new EdcSearchEmpKeyword();
            empKeyword.setMis(searchWord);
            empKeyword.setName(searchWord);
            EdcSearchEmpOption option = new EdcSearchEmpOption();
            option.setStates(Lists.newArrayList(1));
            Paging paging = new Paging();
            int offset = (page - 1) * pageSize;
            int size = pageSize;
            paging.setOffset(offset);
            paging.setSize(size);
            EdcOpResponse<EdcEmpBaseItems> response = iEdcEmpService.searchEmployees(empKeyword, option, paging);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                return PageRemoteResponse.success(Lists.newArrayList(), 0, true);
            }
            EdcEmpBaseItems empBaseItems = response.getData();
            List<EmpInfoDTO> empInfoDTOList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(empBaseItems.getItems())) {
                empBaseItems.getItems().stream().filter(Objects::nonNull)
                        .forEach(empBase -> {
                            EmpInfoDTO empInfoDTO = new EmpInfoDTO();
                            empInfoDTO.setMisId(empBase.getMis());
                            empInfoDTO.setEmpName(empBase.getName());
                            empInfoDTOList.add(empInfoDTO);
                        });
            }
            return PageRemoteResponse.success(empInfoDTOList, empBaseItems.getCount(),
                    offset + size >= empBaseItems.getCount());
        } catch (EDCThriftException e) {
            log.error("EdcEmpService.queryEmpInfoByKeyWord fail, searchWord:{}", searchWord, e);
        }
        return PageRemoteResponse.success(Lists.newArrayList(), 0, true);
    }
}
