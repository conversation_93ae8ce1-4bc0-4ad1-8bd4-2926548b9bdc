package com.sankuai.dz.srcm.member.api;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.member.dto.*;

public interface DepartmentMemberAdminService {

    RemoteResponse<WxDepartmentRootNodeDTO> getWxDepartmentList(String appId);

    RemoteResponse<DepartmentUserListResponse> getDepartmentUserList(DepartmentUserListRequest request);

    RemoteResponse<DepartmentUserListResponse> getDepartmentUserListByFuzzyName(String appId, String userName, String userId,String mis);

    RemoteResponse<DepartmentUserListResponse> batchGetDepartmentUserListByUserId(String appId, String userIds);

    RemoteResponse<Boolean> setDepartmentMemberRole(SetDepartmentRoleRequest request);

    RemoteResponse<RoleListResponse> getDepartmentMemberRoleList(String appId);

    RemoteResponse<DepartmentUserListResponse> getDepartmentUserListByMisId(String appId,String misId);

}
