package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.coupon.dto.IssueNextCouponResultDTO;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.coupon.request.IssueNextCouponRequest;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.scrm.core.service.activity.fission.service.ActivityFissionServiceImpl;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActivSceneCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class NextCouponServiceImplIssueCouponStrictModeTest {

    @InjectMocks
    private NextCouponServiceImpl nextCouponService;

    @Mock
    private ActivityFissionServiceImpl activityFissionService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper scrmAmProcessOrchestrationDistributorCodeDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActivSceneCodeDOMapper scrmAmProcessOrchestrationActivSceneCodeDOMapper;

    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private SceneDomainService sceneDomainService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for coupon information query failure
     */
    @Test
    public void testIssueCouponStrictMode_CouponInfoQueryFails() throws Throwable {
        // arrange
        IssueNextCouponRequest request = new IssueNextCouponRequest();
        request.setCouponGroupId("testCouponGroupId");
        when(activityFissionService.queryMktCouponInfo(anyString())).thenReturn(RemoteResponse.fail("Query failed"));
        // act
        IssueNextCouponResultDTO result = nextCouponService.issueCouponStrictMode(request);
        // assert
        assertNull(result);
        verify(activityFissionService).queryMktCouponInfo("testCouponGroupId");
    }
}
