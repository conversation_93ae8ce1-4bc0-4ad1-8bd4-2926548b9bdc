package com.sankuai.scrm.core.service.message.push.domain;

import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushDetail;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushLog;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushTask;
import com.sankuai.scrm.core.service.message.push.dal.example.MsgPushLogExample;
import com.sankuai.scrm.core.service.message.push.dal.mapper.MsgPushLogMapper;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChannelType;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChatType;
import com.sankuai.scrm.core.service.message.push.response.MsgBatchSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MsgPushLogDomainService {

    @Autowired
    private MsgPushLogMapper msgPushLogMapper;

    public List<MsgPushLog> queryMsgPushLogList(String appId, MsgPushChatType chatType, MsgPushChannelType pushType,
                                                List<String> receiverIdList, Date startTime, Date endTime) {
        if (StringUtils.isEmpty(appId) || chatType == null || pushType == null || CollectionUtils.isEmpty(receiverIdList)
                || startTime == null || endTime == null) {
            return Lists.newArrayList();
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andChatTypeEqualTo(chatType.getCode())
                .andPushTypeEqualTo(pushType.getCode())
                .andReceiverIn(receiverIdList)
                .andAddTimeBetween(startTime, endTime);
        return msgPushLogMapper.selectByExample(example);
    }

    public List<MsgPushLog> queryMsgPushLogList(List<Long> detailIdList) {
        if (CollectionUtils.isEmpty(detailIdList)) {
            return Lists.newArrayList();
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria().andTaskDetailIdIn(detailIdList);
        return msgPushLogMapper.selectByExample(example);
    }

    public  List<MsgPushLog> queryMsgPushLogListByMsgMarkAndSender(String msgMark, String sender) {
        if (StringUtils.isEmpty(msgMark) || StringUtils.isEmpty(sender)) {
            return null;
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria().andMsgMarkEqualTo(msgMark).andSenderEqualTo(sender);
        return msgPushLogMapper.selectByExample(example);
    }

    public List<MsgPushLog> queryMsgPushLog(Long taskDetailId) {
        if (taskDetailId == null) {
            return Lists.newArrayList();
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria().andTaskDetailIdEqualTo(taskDetailId);
        return msgPushLogMapper.selectByExample(example);
    }

    public MsgPushLog queryMsgPushLog(String msgMark) {
        if (StringUtils.isEmpty(msgMark)) {
            return null;
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria().andMsgMarkEqualTo(msgMark);
        List<MsgPushLog> msgPushLogList = msgPushLogMapper.selectByExample(example);
        return msgPushLogList.stream().findFirst().orElse(null);
    }


    public MsgPushLog queryMsgPushLog(String msgMark, String receiver) {
        if (StringUtils.isEmpty(msgMark) || StringUtils.isEmpty(receiver)) {
            return null;
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria().andMsgMarkEqualTo(msgMark).andReceiverEqualTo(receiver);
        List<MsgPushLog> msgPushLogList = msgPushLogMapper.selectByExample(example);
        return msgPushLogList.stream().findFirst().orElse(null);
    }

    public MsgPushLog queryMsgPushLog(String appId, Integer chatType, Integer pushType, String sender, String receiver, String msgMark) {
        if (StringUtils.isEmpty(appId) || chatType == null || pushType == null || StringUtils.isEmpty(sender) ||
                StringUtils.isEmpty(receiver) || StringUtils.isEmpty(msgMark)) {
            return null;
        }
        MsgPushLogExample example = new MsgPushLogExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andChatTypeEqualTo(chatType)
                .andPushTypeEqualTo(pushType)
                .andSenderEqualTo(sender)
                .andReceiverEqualTo(receiver)
                .andMsgMarkEqualTo(msgMark);
        List<MsgPushLog> msgPushLogList = msgPushLogMapper.selectByExample(example);
        return msgPushLogList.stream().findFirst().orElse(null);
    }

    public boolean updateMsgPushLog(MsgPushLog msgPushLog) {
        if (msgPushLog == null) {
            return false;
        }
        int row = msgPushLogMapper.updateByPrimaryKeySelective(msgPushLog);
        return row > 0;
    }

    public void batchInsertMsgPushLog(MsgPushTask task, MsgBatchSendResponse response, List<MsgPushDetail> detailList) {
        if (task == null || response == null || CollectionUtils.isEmpty(detailList)) {
            return;
        }
        Set<String> failIdSet = Sets.newHashSet(response.getFailIdList());
        List<MsgPushLog> recordList = detailList.stream().flatMap(detail -> response.getMsgIdList().stream().map(msgId -> {
            MsgPushLog record = MsgPushLog.builder()
                    .appId(task.getAppId())
                    .taskDetailId(detail.getId())
                    .chatType(task.getTaskType())
                    .pushType(detail.getPushType())
                    .sender(detail.getSender())
                    .receiver(detail.getReceiver())
                    .msgMark(msgId)
                    .build();
            if (failIdSet.contains(detail.getReceiver())) {
                record.setErrMsg(response.getErrMsg());
            }
            return record;
        })).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(recordList)) {
            msgPushLogMapper.batchInsert(recordList);
        }
    }
}
