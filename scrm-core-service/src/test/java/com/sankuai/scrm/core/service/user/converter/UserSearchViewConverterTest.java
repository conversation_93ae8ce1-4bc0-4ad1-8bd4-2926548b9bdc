package com.sankuai.scrm.core.service.user.converter;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.user.dto.PeriodSearchStat;
import com.sankuai.dz.srcm.user.dto.UserSearchViewResult;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import java.lang.reflect.Method;
import org.junit.jupiter.api.DisplayName;
import java.lang.reflect.Modifier;

@ExtendWith(MockitoExtension.class)
public class UserSearchViewConverterTest {

    /**
     * Test when input UserSearchViewResult is null
     */
    @Test
    public void testConvertToUserSearchViewNullInput() throws Throwable {
        UserSearchView view = UserSearchViewConverter.convertToUserSearchView(null);
        org.junit.jupiter.api.Assertions.assertNull(view);
    }

    /**
     * Test when all properties in UserSearchViewResult are null
     */
    @Test
    public void testConvertToUserSearchViewAllPropertiesNull() throws Throwable {
        UserSearchViewResult result = new UserSearchViewResult();
        UserSearchView view = UserSearchViewConverter.convertToUserSearchView(result);
        org.junit.jupiter.api.Assertions.assertNotNull(view);
        org.junit.jupiter.api.Assertions.assertNull(view.getUserId());
        org.junit.jupiter.api.Assertions.assertNull(view.getCChain());
        org.junit.jupiter.api.Assertions.assertNull(view.getAppId());
        org.junit.jupiter.api.Assertions.assertNull(view.getPartitionDate());
        org.junit.jupiter.api.Assertions.assertNull(view.getCalDim());
        org.junit.jupiter.api.Assertions.assertNull(view.getBizlnBuCode());
        org.junit.jupiter.api.Assertions.assertNull(view.getSourceInfo());
        org.junit.jupiter.api.Assertions.assertNull(view.getIsDirect());
        org.junit.jupiter.api.Assertions.assertNull(view.getItemType());
        org.junit.jupiter.api.Assertions.assertNull(view.getLastSearchDate());
        org.junit.jupiter.api.Assertions.assertEquals(0, view.getLastSearchToNowDays());
        org.junit.jupiter.api.Assertions.assertEquals(0L, view.getLastdaySearchKeywordCnt());
        org.junit.jupiter.api.Assertions.assertEquals(0L, view.getLastdayClickQv());
        org.junit.jupiter.api.Assertions.assertEquals(0L, view.getLastdayQv());
        org.junit.jupiter.api.Assertions.assertEquals(0L, view.getLastdayClickItemCnt());
        org.junit.jupiter.api.Assertions.assertEquals(0L, view.getLastdayViewItemCnt());
        // Verify all period stats fields are null
        org.junit.jupiter.api.Assertions.assertNull(view.getSearchKeywordCnt1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getClickQv1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getQv1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getClickItemCnt1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getViewItemCnt1d());
    }

    /**
     * Test when some properties in UserSearchViewResult are null
     */
    @Test
    public void testConvertToUserSearchViewSomePropertiesNull() throws Throwable {
        UserSearchViewResult result = new UserSearchViewResult();
        result.setUserId(1L);
        result.setCChain(1);
        result.setAppId("appId");
        result.setPartitionDate("partitionDate");
        result.setCalDim("calDim");
        result.setBizlnBuCode(1);
        result.setSourceInfo("sourceInfo");
        result.setIsDirect(1);
        result.setItemType("itemType");
        result.setLastSearchDate("lastSearchDate");
        result.setLastSearchToNowDays(1);
        result.setLastdaySearchKeywordCnt(1L);
        result.setLastdayClickQv(1L);
        result.setLastdayQv(1L);
        result.setLastdayClickItemCnt(1L);
        result.setLastdayViewItemCnt(1L);
        UserSearchView view = UserSearchViewConverter.convertToUserSearchView(result);
        org.junit.jupiter.api.Assertions.assertNotNull(view);
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getUserId());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getCChain());
        org.junit.jupiter.api.Assertions.assertEquals("appId", view.getAppId());
        org.junit.jupiter.api.Assertions.assertEquals("partitionDate", view.getPartitionDate());
        org.junit.jupiter.api.Assertions.assertEquals("calDim", view.getCalDim());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getBizlnBuCode());
        org.junit.jupiter.api.Assertions.assertEquals("sourceInfo", view.getSourceInfo());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getIsDirect());
        org.junit.jupiter.api.Assertions.assertEquals("itemType", view.getItemType());
        org.junit.jupiter.api.Assertions.assertEquals("lastSearchDate", view.getLastSearchDate());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getLastSearchToNowDays());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdaySearchKeywordCnt());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayClickQv());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayQv());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayClickItemCnt());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayViewItemCnt());
        // Verify period stats fields are null when periodStats is null
        org.junit.jupiter.api.Assertions.assertNull(view.getSearchKeywordCnt1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getClickQv1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getQv1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getClickItemCnt1d());
        org.junit.jupiter.api.Assertions.assertNull(view.getViewItemCnt1d());
    }

    /**
     * Test when all properties in UserSearchViewResult are set
     */
    @Test
    public void testConvertToUserSearchViewAllPropertiesSet() throws Throwable {
        UserSearchViewResult result = new UserSearchViewResult();
        result.setUserId(1L);
        result.setCChain(1);
        result.setAppId("appId");
        result.setPartitionDate("partitionDate");
        result.setCalDim("calDim");
        result.setBizlnBuCode(1);
        result.setSourceInfo("sourceInfo");
        result.setIsDirect(1);
        result.setItemType("itemType");
        // Set period stats
        PeriodSearchStat stat1d = new PeriodSearchStat();
        stat1d.setPeriod("1d");
        stat1d.setSearchKeywordCnt(10L);
        stat1d.setClickQv(20L);
        stat1d.setQv(30L);
        stat1d.setClickItemCnt(40L);
        stat1d.setViewItemCnt(50L);
        result.setPeriodStats(Collections.singletonList(stat1d));
        result.setLastSearchDate("lastSearchDate");
        result.setLastSearchToNowDays(1);
        result.setLastdaySearchKeywordCnt(1L);
        result.setLastdayClickQv(1L);
        result.setLastdayQv(1L);
        result.setLastdayClickItemCnt(1L);
        result.setLastdayViewItemCnt(1L);
        UserSearchView view = UserSearchViewConverter.convertToUserSearchView(result);
        org.junit.jupiter.api.Assertions.assertNotNull(view);
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getUserId());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getCChain());
        org.junit.jupiter.api.Assertions.assertEquals("appId", view.getAppId());
        org.junit.jupiter.api.Assertions.assertEquals("partitionDate", view.getPartitionDate());
        org.junit.jupiter.api.Assertions.assertEquals("calDim", view.getCalDim());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getBizlnBuCode());
        org.junit.jupiter.api.Assertions.assertEquals("sourceInfo", view.getSourceInfo());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getIsDirect());
        org.junit.jupiter.api.Assertions.assertEquals("itemType", view.getItemType());
        org.junit.jupiter.api.Assertions.assertEquals("lastSearchDate", view.getLastSearchDate());
        org.junit.jupiter.api.Assertions.assertEquals(1, view.getLastSearchToNowDays());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdaySearchKeywordCnt());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayClickQv());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayQv());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayClickItemCnt());
        org.junit.jupiter.api.Assertions.assertEquals(1L, view.getLastdayViewItemCnt());
        // Verify period stats fields are set correctly
        org.junit.jupiter.api.Assertions.assertEquals(10L, view.getSearchKeywordCnt1d());
        org.junit.jupiter.api.Assertions.assertEquals(20L, view.getClickQv1d());
        org.junit.jupiter.api.Assertions.assertEquals(30L, view.getQv1d());
        org.junit.jupiter.api.Assertions.assertEquals(40L, view.getClickItemCnt1d());
        org.junit.jupiter.api.Assertions.assertEquals(50L, view.getViewItemCnt1d());
    }

    @Test
    public void testToLong_WhenInputIsNull_ShouldReturnZero() throws Throwable {
        // arrange
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toLong", Long.class);
        method.setAccessible(true);
        // act
        Long result = (Long) method.invoke(null, (Long) null);
        // assert
        assertEquals(0L, result);
    }

    @Test
    public void testToLong_WhenInputIsValidLong_ShouldReturnSameValue() throws Throwable {
        // arrange
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toLong", Long.class);
        method.setAccessible(true);
        Long input = 12345L;
        // act
        Long result = (Long) method.invoke(null, input);
        // assert
        assertEquals(input, result);
    }

    @Test
    public void testToLong_WhenInputIsMinValue_ShouldReturnSameValue() throws Throwable {
        // arrange
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toLong", Long.class);
        method.setAccessible(true);
        Long input = Long.MIN_VALUE;
        // act
        Long result = (Long) method.invoke(null, input);
        // assert
        assertEquals(Long.MIN_VALUE, result);
    }

    @Test
    public void testToLong_WhenInputIsMaxValue_ShouldReturnSameValue() throws Throwable {
        // arrange
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toLong", Long.class);
        method.setAccessible(true);
        Long input = Long.MAX_VALUE;
        // act
        Long result = (Long) method.invoke(null, input);
        // assert
        assertEquals(Long.MAX_VALUE, result);
    }

    @Test
    public void testToLong_WhenInputIsZero_ShouldReturnZero() throws Throwable {
        // arrange
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toLong", Long.class);
        method.setAccessible(true);
        Long input = 0L;
        // act
        Long result = (Long) method.invoke(null, input);
        // assert
        assertEquals(0L, result);
    }

    @Test
    @DisplayName("toInteger should return 0 when input is null")
    public void testToIntegerWithNullValue() throws Throwable {
        // arrange
        Integer input = null;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(0, result, "Should return 0 when input is null");
    }

    @Test
    @DisplayName("toInteger should return the same value when input is positive")
    public void testToIntegerWithPositiveValue() throws Throwable {
        // arrange
        Integer input = 42;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(input, result, "Should return the same value when input is not null");
    }

    @Test
    @DisplayName("toInteger should return the same value when input is negative")
    public void testToIntegerWithNegativeValue() throws Throwable {
        // arrange
        Integer input = -10;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(input, result, "Should return the same value when input is not null");
    }

    @Test
    @DisplayName("toInteger should return 0 when input is 0")
    public void testToIntegerWithZeroValue() throws Throwable {
        // arrange
        Integer input = 0;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(0, result, "Should return 0 when input is 0");
    }

    @Test
    @DisplayName("toInteger should return Integer.MAX_VALUE when input is Integer.MAX_VALUE")
    public void testToIntegerWithMaxValue() throws Throwable {
        // arrange
        Integer input = Integer.MAX_VALUE;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(Integer.MAX_VALUE, result, "Should return Integer.MAX_VALUE when input is Integer.MAX_VALUE");
    }

    @Test
    @DisplayName("toInteger should return Integer.MIN_VALUE when input is Integer.MIN_VALUE")
    public void testToIntegerWithMinValue() throws Throwable {
        // arrange
        Integer input = Integer.MIN_VALUE;
        // act
        Method toIntegerMethod = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        toIntegerMethod.setAccessible(true);
        Integer result = (Integer) toIntegerMethod.invoke(null, input);
        // assert
        assertEquals(Integer.MIN_VALUE, result, "Should return Integer.MIN_VALUE when input is Integer.MIN_VALUE");
    }

    @Test
    public void testToInteger_WhenInputIsNull_ShouldReturnZero() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        // act
        Integer result = (Integer) method.invoke(null, new Object[] { null });
        // assert
        assertEquals(0, result, "当输入为null时应该返回0");
    }

    @Test
    public void testToInteger_WhenInputIsPositiveNumber_ShouldReturnSameValue() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        Integer input = 42;
        // act
        Integer result = (Integer) method.invoke(null, input);
        // assert
        assertEquals(input, result, "当输入为正数时应该返回相同的值");
    }

    @Test
    public void testToInteger_WhenInputIsNegativeNumber_ShouldReturnSameValue() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        Integer input = -42;
        // act
        Integer result = (Integer) method.invoke(null, input);
        // assert
        assertEquals(input, result, "当输入为负数时应该返回相同的值");
    }

    @Test
    public void testToInteger_WhenInputIsMaxValue_ShouldReturnSameValue() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        Integer input = Integer.MAX_VALUE;
        // act
        Integer result = (Integer) method.invoke(null, input);
        // assert
        assertEquals(input, result, "当输入为Integer.MAX_VALUE时应该返回相同的值");
    }

    @Test
    public void testToInteger_WhenInputIsMinValue_ShouldReturnSameValue() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        Integer input = Integer.MIN_VALUE;
        // act
        Integer result = (Integer) method.invoke(null, input);
        // assert
        assertEquals(input, result, "当输入为Integer.MIN_VALUE时应该返回相同的值");
    }

    @Test
    public void testToInteger_WhenInputIsZero_ShouldReturnZero() throws Exception {
        // arrange
        Method method = getPrivateMethod();
        Integer input = 0;
        // act
        Integer result = (Integer) method.invoke(null, input);
        // assert
        assertEquals(input, result, "当输入为0时应该返回0");
    }

    private Method getPrivateMethod() throws NoSuchMethodException {
        Method method = UserSearchViewConverter.class.getDeclaredMethod("toInteger", Integer.class);
        assertTrue(Modifier.isPrivate(method.getModifiers()), "方法应该是private的");
        assertTrue(Modifier.isStatic(method.getModifiers()), "方法应该是static的");
        method.setAccessible(true);
        return method;
    }
}
