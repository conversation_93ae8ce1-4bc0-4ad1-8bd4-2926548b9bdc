package com.sankuai.scrm.core.service.open.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.baby.customer.operator.operatorhelper.api.msg.task.MsgTaskService;
import com.dianping.baby.customer.operator.operatorhelper.dto.msg.task.AtUserMessageContentDTO;
import com.dianping.baby.customer.operator.operatorhelper.dto.msg.task.MessageContentDTO;
import com.dianping.baby.customer.operator.operatorhelper.dto.msg.task.MsgTaskDTO;
import com.dianping.baby.customer.operator.operatorhelper.enums.MessageContentTypeEnum;
import com.dianping.baby.customer.operator.operatorhelper.enums.MessageTaskStrategyTypeEnum;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.open.ScrmOpenChatService;
import com.sankuai.dz.srcm.open.msgbody.ScrmOpenGroupMsgBody;
import com.sankuai.dz.srcm.open.msgbody.ScrmOpenPrivateMsgBody;
import com.sankuai.dz.srcm.open.request.ScrmChatRecordRequest;
import com.sankuai.dz.srcm.open.request.ScrmGroupMessageRequest;
import com.sankuai.dz.srcm.open.request.ScrmPrivateMessageRequest;
import com.sankuai.dz.srcm.open.response.ScrmOpenChatRecordDTO;
import com.sankuai.scrm.core.service.chat.dal.entity.OperatorHelperGroupChatLog;
import com.sankuai.scrm.core.service.chat.domain.GroupChatLogDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.chat.mq.msg.GroupChatMsg;
import com.sankuai.scrm.core.service.chat.mq.msg.PrivateChatMsg;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsFriendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.open.convert.*;
import com.sankuai.scrm.core.service.open.producer.ScrmOpenEntGroupMessageProducer;
import com.sankuai.scrm.core.service.open.producer.ScrmOpenEntPrivateMessageProducer;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxChatLog;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxChatLogWithBLOBs;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxUserMemberTypeEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.FriendAssistantDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.MsgContentDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.SendFriendMsgTRequest;
import com.sankuai.service.fe.corp.ds.enums.msg.ChannelStrategyTEnum;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@MdpPigeonServer(url = "com.sankuai.dz.srcm.open.ScrmOpenChatService")
@Slf4j
public class ScrmOpenChatServiceImpl implements ScrmOpenChatService {
    


    @Autowired
    private MsgUnifiedPushService  msgUnifiedPushService;



    @Autowired
    private CorpAppConfigRepository appConfigRepository;


    @Autowired
    private GroupChatLogDomainService groupChatLogDomainService;

    @Autowired
    private PrivateChatDomainService privateChatDomainService;

    @Autowired
    private DsFriendAcl dsFriendAcl;

    @Resource
    private MsgTaskService msgTaskService;


    
    @Override
    public RemoteResponse<Long> sendGroupMsg(ScrmGroupMessageRequest scrmGroupMessageRequest) {
        log.info("ScrmOpenChatService.sendGroupMsg.request:{}", JSONObject.toJSONString(scrmGroupMessageRequest));
        CorpAppConfig configByAppId = appConfigRepository.getConfigByAppId(scrmGroupMessageRequest.getAppId());
        if (configByAppId == null) {
            return RemoteResponse.fail("appId不存在");
        }
        /**
        * 不指定机器人会随机分配一个
         * * */
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(ScrmGroupMessageRequestConvert.convert(scrmGroupMessageRequest));
        log.info("ScrmOpenChatService.sendGroupMsg.request:{},response:{}", JSONObject.toJSONString(scrmGroupMessageRequest),JSONObject.toJSONString(response));
        return RemoteResponse.success(response.getData());
    }

    @Override
    public RemoteResponse<Long> sendPrivateMsg(ScrmPrivateMessageRequest scrmPrivateMessageRequest) {
        log.info("ScrmOpenChatService.sendPrivateMsg.request:{}", JSONObject.toJSONString(scrmPrivateMessageRequest));
        CorpAppConfig configByAppId = appConfigRepository.getConfigByAppId(scrmPrivateMessageRequest.getAppId());
        if (configByAppId == null) {
            return RemoteResponse.fail("appId不存在");
        }
        MsgPushResponse<Long> response = msgUnifiedPushService.saveMsgPushTask(ScrmPrivateMessageRequestConvert.convert(scrmPrivateMessageRequest));
        log.info("ScrmOpenChatService.sendPrivateMsg.request:{},response:{}", JSONObject.toJSONString(scrmPrivateMessageRequest),JSONObject.toJSONString(response));
        return RemoteResponse.success(response.getData());
    }

    @Override
    public PageRemoteResponse<ScrmOpenChatRecordDTO> queryChatRecordByPage(ScrmChatRecordRequest scrmChatRecordRequest) {
        log.info("queryChatRecordByPage.request:{}", JSONObject.toJSONString(scrmChatRecordRequest));
        PageRemoteResponse<ScrmOpenChatRecordDTO>  checkParamResponse = checkParam(scrmChatRecordRequest);
        if (checkParamResponse != null){
            return checkParamResponse;
        }
        if (scrmChatRecordRequest.getQueryType() == 1) {
            List<OperatorHelperPrivateChat> operatorHelperPrivateChats = privateChatDomainService.queryPrivateChatLogByPage(scrmChatRecordRequest);
            List<ScrmOpenChatRecordDTO> collect = operatorHelperPrivateChats.stream().map(ScrmOpenChatRecordConvert::privateConvert).collect(Collectors.toList());
            return PageRemoteResponse.success(collect, collect.size(),
                    scrmChatRecordRequest.getPageNum() * scrmChatRecordRequest.getPageSize() >= collect.size());
        }
        if (scrmChatRecordRequest.getQueryType() == 2) {
            List<OperatorHelperGroupChatLog> operatorHelperGroupChatLogs = groupChatLogDomainService.queryChatLogByPage(scrmChatRecordRequest);
            List<ScrmOpenChatRecordDTO> collect = operatorHelperGroupChatLogs.stream().map(ScrmOpenChatRecordConvert::groupConvert).collect(Collectors.toList());
            return PageRemoteResponse.success(collect, collect.size(),
                    scrmChatRecordRequest.getPageNum() * scrmChatRecordRequest.getPageSize() >= collect.size());
        }
        return PageRemoteResponse.fail("查询类型错误");
    }

    private PageRemoteResponse<ScrmOpenChatRecordDTO> checkParam(ScrmChatRecordRequest scrmChatRecordRequest) {
        if (scrmChatRecordRequest == null) {
           return PageRemoteResponse.fail("参数不能为空");
        }
        if (StringUtils.isBlank(scrmChatRecordRequest.getAppId())) {
            return PageRemoteResponse.fail("appId不能为空");
        }
        if (scrmChatRecordRequest.getPageNum() == null || scrmChatRecordRequest.getPageNum() <= 0) {
            return PageRemoteResponse.fail("pageNum不能为空且必须大于0");
        }
        if (scrmChatRecordRequest.getPageSize() == null || scrmChatRecordRequest.getPageSize() <= 0) {
            return PageRemoteResponse.fail("pageSize不能为空且必须大于0");
        }
        if (scrmChatRecordRequest.getPageSize() > 20) {
            return PageRemoteResponse.fail("pageSize不能超过20");
        }
        if (scrmChatRecordRequest.getQueryType() == null) {
            return PageRemoteResponse.fail("查询类型不能空！");
        }
        if (scrmChatRecordRequest.getQueryType() == 2) {
            if (StringUtils.isBlank(scrmChatRecordRequest.getGroupId())) {
                return PageRemoteResponse.fail("wxGroupId不能为空");
            }
        }
        if (scrmChatRecordRequest.getQueryType() == 1) {
            if (StringUtils.isBlank(scrmChatRecordRequest.getReceiveWxUserId())
                    || StringUtils.isBlank(scrmChatRecordRequest.getSendWxUserId())) {
                return PageRemoteResponse.fail("发送方或接收方不能为空");
            }
        }
        CorpAppConfig configByAppId = appConfigRepository.getConfigByAppId(scrmChatRecordRequest.getAppId());
        if (configByAppId == null) {
            return PageRemoteResponse.fail("appId不存在");
        }
        return null;
    }


}
