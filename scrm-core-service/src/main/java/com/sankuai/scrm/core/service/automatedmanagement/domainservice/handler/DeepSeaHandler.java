package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/8/13
 */
/*@Component
@Slf4j
public class DeepSeaHandler extends AbstractWxHandler {
    @Resource
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Resource
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Resource
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private ProductManagementService productManagementService;

    @Resource
    private MtUserCenterAclService mtUserCenterAclService;

    @Autowired
    private CouponServiceImpl couponService;

    @Autowired
    private CouponTemplateServiceImpl couponTemplateService;

    @Autowired
    private DsFriendAcl dsFriendAcl;

    @Autowired
    private DsGroupMessageAcl dsGroupMessageAcl;

    @Autowired
    private DsGroupMessageUtils dsGroupMessageUtils;

    @Autowired
    private InformationGatheringService informationGatheringService;

    *//**
     * 处理深海途径群发消息
     *
     * @param processOrchestrationDTO
     * @param executorId
     * @param typeExecuteLogDOMapEntry
     *//*
    public void dealDeepSeaWxMessage(ScrmProcessOrchestrationDTO processOrchestrationDTO, String executorId,
            Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntry) {
        dealNormalDeepSeaWxMessage(processOrchestrationDTO, executorId, typeExecuteLogDOMapEntry.getKey(),
                typeExecuteLogDOMapEntry.getValue());
    }

    public void dealDeepSeaWxGroupMessage(ScrmProcessOrchestrationDTO processOrchestrationDTO, String executorId,
            Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntry) {
        dealGroupDeepSeaWxMessage(processOrchestrationDTO, executorId, typeExecuteLogDOMapEntry.getKey(),
                typeExecuteLogDOMapEntry.getValue());
    }

    public void checkDeepSeaWxStatus(String body, MsgResultEvent msgResultEvent) {
        ScrmAmProcessOrchestrationWxInvokeLogDOExample invokeLogDOExample = new ScrmAmProcessOrchestrationWxInvokeLogDOExample();
        List<Byte> statusList = Lists.newArrayList();
        statusList.add(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        invokeLogDOExample.createCriteria()
                .andTypeIn(ScrmProcessOrchestrationWxTouchTypeEnum.getDeepSeaMessageTouchTypeSet().stream()
                        .map(ScrmProcessOrchestrationWxTouchTypeEnum::getCode).collect(Collectors.toList()))
                .andJobidEqualTo(String.valueOf(msgResultEvent.getTaskId())).andStatusIn(statusList);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> scrmAmProcessOrchestrationWxInvokeLogDOS = wxInvokeLogDOMapper
                .selectByExample(invokeLogDOExample);

        if (CollectionUtils.isEmpty(scrmAmProcessOrchestrationWxInvokeLogDOS)) {
            return;
        }

        for (ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO : scrmAmProcessOrchestrationWxInvokeLogDOS) {
            if (msgResultEvent.getMsgResultType().equalsIgnoreCase(MsgResultTypeTEnum.FRIEND_TASK.getCode())) {
                MsgTaskResultDTO msgTaskResultDTO = msgResultEvent.getMsgTaskDTO();
                if (msgTaskResultDTO.getDetailCount().intValue() == msgTaskResultDTO.getSuccessCount().intValue()) {
                    dealSuccessResult(invokeLogDO, msgResultEvent);
                    invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS
                            .getValue().byteValue());
                } else {
                    dealFailedResult(invokeLogDO, msgResultEvent);
                    invokeLogDO.setStatus(
                            ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER
                                    .getValue().byteValue());
                }
            }

            if (msgResultEvent.getMsgResultType().equalsIgnoreCase(MsgResultTypeTEnum.FRIEND_TASK_DETAIL.getCode())) {
                continue;
            }

            // TODO 需要填充处理逻辑
            if (msgResultEvent.getMsgResultType().equalsIgnoreCase(MsgResultTypeTEnum.GROUP_TASK.getCode())) {
                MsgTaskResultDTO msgTaskResultDTO = msgResultEvent.getMsgTaskDTO();
                if (msgTaskResultDTO.getDetailCount().longValue() == msgTaskResultDTO.getSuccessCount().longValue()) {
                    dealSuccessResult(invokeLogDO, msgResultEvent);
                    invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS
                            .getValue().byteValue());
                } else {
                    dealFailedResult(invokeLogDO, msgResultEvent);
                    invokeLogDO.setStatus(
                            ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER
                                    .getValue().byteValue());
                }
            }

            if (msgResultEvent.getMsgResultType().equalsIgnoreCase(MsgResultTypeTEnum.GROUP_TASK_DETAIL.getCode())) {
                continue;
            }
            invokeLogDO.setUpdateTime(new Date());
            wxInvokeLogDOMapper.updateByPrimaryKey(invokeLogDO);
        }
    }

    private void dealNormalDeepSeaWxMessage(ScrmProcessOrchestrationDTO processOrchestrationDTO, String executorId,
            InvokeDetailKeyObject keyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS) {
        Map<Long, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> executeLogDOMap = totalInvokeDetailDOS.stream()
                .collect(Collectors
                        .groupingBy(ScrmAmProcessOrchestrationWxInvokeDetailDO::getProcessOrchestrationNodeId));
        Map<String, MsgContentDTO> existedAttachmentMap = new ConcurrentHashMap<>();
        CorpAppConfig corpAppConfig = appConfigRepository.getConfigByAppId(processOrchestrationDTO.getAppId());
        if (CollectionUtils.isEmpty(totalInvokeDetailDOS)) {
            return;
        }
        for (Map.Entry<Long, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry : executeLogDOMap.entrySet()) {
            Long nodeId = entry.getKey();
            ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO()
                    .getActionDTO(nodeId);
            if (null == actionDTO) {
                continue;
            }
            List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO()
                    .getActionContentDTOList(actionDTO);
            if (CollectionUtils.isEmpty(contentDTOS)) {
                continue;
            }
            final AtomicInteger totalNums = new AtomicInteger(0);
            contentDTOS.forEach(contentDTO -> {
                List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = processOrchestrationDTO
                        .getNodeMediumDTO().getActionAttachmentDTOList(contentDTO);
                totalNums.getAndAccumulate(
                        actionAttachmentDTOList.size() + (StringUtils.isNotBlank(contentDTO.getContent()) ? 1 : 0),
                        Integer::sum);
            });

            if (totalNums.get() == 0) {
                continue;
            }

            Map<String, AssistantInfo> assistantOfUserIdMap = dsGroupMessageUtils.getAssistantOfUserId(totalNums.get(),
                    processOrchestrationDTO.getExecutorList().stream()
                            .map(ScrmProcessOrchestrationExecutorDTO::getExecutorId).collect(Collectors.toList()),
                    totalInvokeDetailDOS.stream().map(o -> o.getTargetId()).collect(Collectors.toList()),
                    corpAppConfig);
            if (MapUtils.isEmpty(assistantOfUserIdMap)) {
                return;
            }
            for (ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO : totalInvokeDetailDOS) {
                if (StringUtils.isBlank(detailDO.getTargetId())) {
                    continue;
                }
                ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = executeLogDOMapper
                        .selectByPrimaryKey(detailDO.getExecuteLogId());
                SendFriendMsgTRequest request = new SendFriendMsgTRequest();
                request.setBusinessCode(corpAppConfig.getBusinessCode());
                request.setFriendAssistantDTOList(new ArrayList<>());
                FriendAssistantDTO friendAssistantDTO = new FriendAssistantDTO();
                friendAssistantDTO.setWxUserId(detailDO.getTargetId());
                if (false == assistantOfUserIdMap.containsKey(detailDO.getTargetId())) {
                    continue;
                }
                friendAssistantDTO.setAssistantId(assistantOfUserIdMap.get(detailDO.getTargetId()).getAssistantId());
                request.getFriendAssistantDTOList().add(friendAssistantDTO);
                request.setChannelStrategyTEnum(ChannelStrategyTEnum.UNSPECIFIED);

                List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = processOrchestrationDTO
                        .getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));

                if (CollectionUtils.isNotEmpty(actionAttachmentDTOList)) {
                    List<MsgContentDTO> attachments = new ArrayList<>();
                    for (ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO : actionAttachmentDTOList) {
                        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = actionAttachmentDTO
                                .getAttachmentContentDetailDTO();
                        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = actionAttachmentDTO
                                .getAttachmentSupplyDetailDTO();
                        // 以下为深海支持类型
                        buildOfficialMsgDTO(actionAttachmentDTO, contentDetailDTO, attachments);

                        // 以下类型非深海官方支持
                        if (actionAttachmentDTO
                                .getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.SUPPLY
                                        .getValue()) {
                            if (supplyDetailDTO == null) {
                                continue;
                            }

                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.AUTOMATIC_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (StringUtils.isBlank(supplyDetailDTO.getProductId())) {
                                    continue;
                                }
                                List<String> productIds = Arrays
                                        .asList(supplyDetailDTO.getProductId().replace("[;，]", ",").split(","));

                                if (CollectionUtils.isEmpty(productIds)) {
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 1) {
                                    ProductInfoDTO productInfoDTO = productManagementService.chooseProduct(
                                            detailDO.getTargetId(), executeLogDO.getTargetMtUserId(),
                                            processOrchestrationDTO.getAppId(), supplyDetailDTO);
                                    if (null == productInfoDTO) {
                                        continue;
                                    }

                                    if (supplyDetailDTO.getMarketingCopySource() == 1) {
                                        String content = productManagementService.getCommentFromAIGC(productInfoDTO,
                                                executeLogDO.getId(), processOrchestrationDTO.getAppId());
                                        if (StringUtils.isNotBlank(content)) {
                                            MsgContentDTO msgContentDTO = new MsgContentDTO();
                                            msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                            msgContentDTO.setTextDTO(new TextDTO());
                                            msgContentDTO.getTextDTO().setContent(content);
                                            attachments.add(msgContentDTO);
                                        }

                                    }
                                    if (supplyDetailDTO.getMarketingCopySource() == 2) {
                                        MsgContentDTO msgContentDTO = new MsgContentDTO();
                                        msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                        msgContentDTO.setTextDTO(new TextDTO());
                                        msgContentDTO.getTextDTO().setContent(supplyDetailDTO.getMarketingCopy());
                                        attachments.add(msgContentDTO);
                                    }
                                    MsgContentDTO attachmentVO;
                                    if (existedAttachmentMap.containsKey(
                                            supplyDetailDTO.getProductType() + "-" + productInfoDTO.getProductId())) {
                                        attachmentVO = existedAttachmentMap.get(
                                                supplyDetailDTO.getProductType() + "-" + productInfoDTO.getProductId());
                                    } else {
                                        attachmentVO = new MsgContentDTO();
                                        attachmentVO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
                                        // attachmentVO.setLinkDTO(new LinkDTO());
                                        attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
                                        MiniProgramDTO miniProgramDTO = new MiniProgramDTO();
                                        miniProgramDTO.setDescription(productInfoDTO.getProductTitle());
                                        miniProgramDTO.setOriginAppId(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID);
                                        miniProgramDTO.setAppId(GroupDynamicCodeConstants.MX_MINIP_APPID);
                                        miniProgramDTO.setThumbnail(productInfoDTO.getHeadPic());
                                        BizDistributorServiceKeyObject bizDistributorServiceKeyObject = new BizDistributorServiceKeyObject(
                                                actionAttachmentDTO, processOrchestrationDTO.getAppId());
                                        String communityDistributorCode = informationGatheringService
                                                .queryCommunityDistributor(bizDistributorServiceKeyObject);
                                        miniProgramDTO.setPagePath(buildProductUrl(productInfoDTO.getH5Url(),
                                                productInfoDTO.getProductId(), communityDistributorCode,
                                                processOrchestrationDTO.getAppId(),true));
                                        attachmentVO.setMiniProgramDTO(miniProgramDTO);
                                        existedAttachmentMap.put(
                                                supplyDetailDTO.getProductType() + "-" + productInfoDTO.getProductId(),
                                                attachmentVO);
                                    }
                                    attachments.add(attachmentVO);
                                }
                                if (supplyDetailDTO.getProductType() == 2) {
                                    List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = productManagementService
                                            .queryActivityPagesById(
                                                    Arrays.asList(productIds.stream().filter(NumberUtils::isParsable)
                                                            .map(Long::valueOf).findFirst().orElse(0L)));
                                    if (CollectionUtils.isNotEmpty(pageDOS)) {
                                        attachments.addAll(buildActivityPageAttachments(processOrchestrationDTO,
                                                actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap));
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 3) {
                                    continue;
                                }
                            }

                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.MANUAL_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (StringUtils.isBlank(supplyDetailDTO.getProductId())) {
                                    continue;
                                }
                                List<String> productIds = Arrays
                                        .asList(supplyDetailDTO.getProductId().replace("[;，]", ",").split(","));

                                if (CollectionUtils.isEmpty(productIds)) {
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 1) {
                                    for (String productId : productIds) {
                                        if (false == NumberUtils.isParsable(productId)) {
                                            return;
                                        }
                                        ProductInfoDTO productInfoDTO = productManagementService
                                                .queryProductInfoByProductIdAndAppId(Long.valueOf(productId),
                                                        processOrchestrationDTO.getAppId());
                                        if (null == productInfoDTO) {
                                            continue;
                                        }

                                        if (supplyDetailDTO.getMarketingCopySource() == 1) {
                                            String content = productManagementService.getCommentFromAIGC(productInfoDTO,
                                                    executeLogDO.getId(), processOrchestrationDTO.getAppId());
                                            if (StringUtils.isNotBlank(content)) {
                                                MsgContentDTO msgContentDTO = new MsgContentDTO();
                                                msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                                msgContentDTO.setTextDTO(new TextDTO());
                                                msgContentDTO.getTextDTO().setContent(content);
                                                attachments.add(msgContentDTO);
                                            }

                                        }
                                        if (supplyDetailDTO.getMarketingCopySource() == 2) {
                                            MsgContentDTO msgContentDTO = new MsgContentDTO();
                                            msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                            msgContentDTO.setTextDTO(new TextDTO());
                                            msgContentDTO.getTextDTO().setContent(supplyDetailDTO.getMarketingCopy());
                                            attachments.add(msgContentDTO);
                                        }
                                        MsgContentDTO attachmentVO;
                                        if (existedAttachmentMap.containsKey(supplyDetailDTO.getProductType() + "-"
                                                + productInfoDTO.getProductId())) {
                                            attachmentVO = existedAttachmentMap.get(supplyDetailDTO.getProductType()
                                                    + "-" + productInfoDTO.getProductId());
                                        } else {
                                            attachmentVO = new MsgContentDTO();
                                            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
                                            // attachmentVO.setLinkDTO(new LinkDTO());
                                            attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
                                            MiniProgramDTO miniProgramDTO = new MiniProgramDTO();
                                            miniProgramDTO.setDescription(productInfoDTO.getProductTitle());
                                            miniProgramDTO
                                                    .setOriginAppId(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID);
                                            miniProgramDTO.setAppId(GroupDynamicCodeConstants.MX_MINIP_APPID);
                                            miniProgramDTO.setThumbnail(productInfoDTO.getHeadPic());
                                            BizDistributorServiceKeyObject bizDistributorServiceKeyObject = new BizDistributorServiceKeyObject(
                                                    actionAttachmentDTO, processOrchestrationDTO.getAppId());
                                            String communityDistributorCode = informationGatheringService
                                                    .queryCommunityDistributor(bizDistributorServiceKeyObject);
                                            miniProgramDTO.setPagePath(buildProductUrl(productInfoDTO.getH5Url(),
                                                    productInfoDTO.getProductId(), communityDistributorCode,
                                                    processOrchestrationDTO.getAppId(),true));
                                            attachmentVO.setMiniProgramDTO(miniProgramDTO);
                                            existedAttachmentMap.put(supplyDetailDTO.getProductType() + "-"
                                                    + productInfoDTO.getProductId(), attachmentVO);
                                        }
                                        attachments.add(attachmentVO);
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 2) {
                                    List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = productManagementService
                                            .queryActivityPagesById(productIds.stream().filter(NumberUtil::isNumber)
                                                    .map(Long::valueOf).collect(Collectors.toList()));
                                    if (CollectionUtils.isNotEmpty(pageDOS)) {
                                        attachments.addAll(buildActivityPageAttachments(processOrchestrationDTO,
                                                actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap));
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 3) {
                                    continue;
                                }
                            }
                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.CUSTOMIZED_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (supplyDetailDTO.getJumpPageType() == 1) {
                                    MsgContentDTO attachmentVO = getCustomizedProductPromotionAttachmentVO(
                                            processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO,
                                            existedAttachmentMap);
                                    if (attachmentVO == null) {
                                        return;
                                    }
                                    attachments.add(attachmentVO);
                                }
                                if (supplyDetailDTO.getJumpPageType() == 2) {
                                    continue;
                                }
                                if (supplyDetailDTO.getJumpPageType() == 3) {
                                    continue;
                                }
                            }
                        }
                    }
                    request.setMsgContentDTOList(attachments);
                }

                if (CollectionUtils.isEmpty(request.getMsgContentDTOList())) {
                    continue;
                }

                ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
                wxInvokeLogDO.setExecutorId(executorId);
                wxInvokeLogDO.setProcessOrchestrationId(processOrchestrationDTO.getId());
                wxInvokeLogDO.setProcessOrchestrationVersion(processOrchestrationDTO.getValidVersion());
                wxInvokeLogDO.setProcessOrchestrationNodeId(detailDO.getProcessOrchestrationNodeId());
                wxInvokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                        .getValue().byteValue());
                wxInvokeLogDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
                wxInvokeLogDO.setJobid(StringUtils.EMPTY);

                FriendSendMsgResponse friendSendMsgResponse = null;
                if (stopAndLogRequest(JsonUtils.toStr(request),
                        assistantOfUserIdMap.get(detailDO.getTargetId()).getUserId(),
                        processOrchestrationDTO.getAppId())) {
                    friendSendMsgResponse = dsFriendAcl.sendFriendMsg(request);
                }

                if (null == friendSendMsgResponse || friendSendMsgResponse.getTaskId() <= 0) {
                    log.error("execute.groupSendMessage fail, request:{}, response:{}", JsonUtils.toStr(request),
                            JsonUtils.toStr(friendSendMsgResponse));
                    ScrmAmProcessOrchestrationExecuteLogDO updateLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
                    updateLogDO.setExecutorId(assistantOfUserIdMap.get(detailDO.getTargetId()).getUserId());
                    updateLogDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE
                            .getValue().byteValue());
                    ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
                    executeLogDOExample.createCriteria().andIdEqualTo(detailDO.getExecuteLogId());
                    executeLogDOMapper.updateByExampleSelective(updateLogDO, executeLogDOExample);
                } else {
                    wxInvokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND
                            .getValue().byteValue());
                    wxInvokeLogDO.setJobid(String.valueOf(friendSendMsgResponse.getTaskId()));
                    wxInvokeLogDOMapper.insert(wxInvokeLogDO);

                    ScrmAmProcessOrchestrationWxInvokeDetailDOExample wxInvokeDetailDOExample1 = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
                    wxInvokeDetailDOExample1.createCriteria().andExecuteLogIdEqualTo(detailDO.getExecuteLogId())
                            .andStatusEqualTo(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                                    .getValue().byteValue());
                    ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
                    wxInvokeDetailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND
                            .getValue().byteValue());
                    wxInvokeDetailDO.setInvokeLogId(wxInvokeLogDO.getId());
                    wxInvokeDetailDOMapper.updateByExampleSelective(wxInvokeDetailDO, wxInvokeDetailDOExample1);

                    ScrmAmProcessOrchestrationExecuteLogDO updateLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
                    updateLogDO.setExecutorId(assistantOfUserIdMap.get(detailDO.getTargetId()).getUserId());
                    ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
                    executeLogDOExample.createCriteria().andIdEqualTo(detailDO.getExecuteLogId());
                    executeLogDOMapper.updateByExampleSelective(updateLogDO, executeLogDOExample);
                }
            }
        }
    }

    private void dealGroupDeepSeaWxMessage(ScrmProcessOrchestrationDTO processOrchestrationDTO, String executorId,
            InvokeDetailKeyObject keyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS) {
        CorpAppConfig corpAppConfig = appConfigRepository.getConfigByAppId(processOrchestrationDTO.getAppId());
        Map<String, MsgContentDTO> existedAttachmentMap = new ConcurrentHashMap<>();
        if (CollectionUtils.isEmpty(totalInvokeDetailDOS)) {
            return;
        }

        Map<Long, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> wxInvokeDetailDOMap = totalInvokeDetailDOS.stream()
                .collect(Collectors
                        .groupingBy(ScrmAmProcessOrchestrationWxInvokeDetailDO::getProcessOrchestrationNodeId));

        for (Map.Entry<Long, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry : wxInvokeDetailDOMap.entrySet()) {
            Long nodeId = entry.getKey();
            ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO()
                    .getActionDTO(nodeId);
            if (null == actionDTO) {
                continue;
            }
            List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO()
                    .getActionContentDTOList(actionDTO);
            if (CollectionUtils.isEmpty(contentDTOS)) {
                continue;
            }
            final AtomicInteger totalNums = new AtomicInteger(0);
            contentDTOS.forEach(contentDTO -> {
                List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = processOrchestrationDTO
                        .getNodeMediumDTO().getActionAttachmentDTOList(contentDTO);
                totalNums.getAndAccumulate(
                        actionAttachmentDTOList.size() + (StringUtils.isNotBlank(contentDTO.getContent()) ? 1 : 0),
                        Integer::sum);
            });

            if (totalNums.get() == 0) {
                continue;
            }

            Map<String, AssistantInfo> assistantOfSendMsgRequestMap = dsGroupMessageUtils.getAssistantOfChatId(
                    totalNums.get(),
                    processOrchestrationDTO.getExecutorList().stream()
                            .map(ScrmProcessOrchestrationExecutorDTO::getExecutorId).collect(Collectors.toList()),
                    new ArrayList<>(totalInvokeDetailDOS.stream()
                            .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getGroupId).collect(Collectors.toSet())),
                    corpAppConfig);
            if (MapUtils.isEmpty(assistantOfSendMsgRequestMap)) {
                return;
            }
            List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = entry.getValue();

            Map<String, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> groupWxInvokeDetailDOMap = wxInvokeDetailDOS
                    .stream().collect(Collectors.groupingBy(ScrmAmProcessOrchestrationWxInvokeDetailDO::getGroupId));
            for (Map.Entry<String, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> groupEntry : groupWxInvokeDetailDOMap
                    .entrySet()) {
                if (CollectionUtils.isEmpty(groupEntry.getValue())) {
                    continue;
                }
                ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = groupEntry.getValue().get(0);
                SendGroupMsgTRequest request = new SendGroupMsgTRequest();
                request.setBusinessCode(corpAppConfig.getBusinessCode());
                request.setGroupAssistantDTOList(new ArrayList<>());
                GroupAssistantDTO groupAssistantDTO = new GroupAssistantDTO();
                groupAssistantDTO.setGroupId(groupEntry.getKey());
                if (false == assistantOfSendMsgRequestMap.containsKey(groupEntry.getKey())) {
                    continue;
                }
                groupAssistantDTO
                        .setAssistantId(assistantOfSendMsgRequestMap.get(groupEntry.getKey()).getAssistantId());
                request.getGroupAssistantDTOList().add(groupAssistantDTO);
                request.setChannelStrategyTEnum(ChannelStrategyTEnum.UNSPECIFIED);
                if (CollectionUtils.isEmpty(contentDTOS)) {
                    continue;
                }

                List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = processOrchestrationDTO
                        .getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));

                if (CollectionUtils.isNotEmpty(actionAttachmentDTOList)) {
                    List<MsgContentDTO> attachments = new ArrayList<>();
                    for (ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO : actionAttachmentDTOList) {
                        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = actionAttachmentDTO
                                .getAttachmentContentDetailDTO();
                        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = actionAttachmentDTO
                                .getAttachmentSupplyDetailDTO();
                        // 以下为深海支持类型
                        buildOfficialMsgDTO(actionAttachmentDTO, contentDetailDTO, attachments);

                        // 一下类型非深海官方支持
                        if (actionAttachmentDTO
                                .getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.SUPPLY
                                        .getValue()) {
                            if (supplyDetailDTO == null) {
                                continue;
                            }
                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.AUTOMATIC_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (StringUtils.isBlank(supplyDetailDTO.getProductId())) {
                                    continue;
                                }
                                List<String> productIds = Arrays
                                        .asList(supplyDetailDTO.getProductId().replace("[;，]", ",").split(","));

                                if (CollectionUtils.isEmpty(productIds)) {
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 1) {
                                    log.error("群消息不支持单品自动召回 编排id" + processOrchestrationDTO.getId());
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 2) {
                                    List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = productManagementService
                                            .queryActivityPagesById(
                                                    Arrays.asList(productIds.stream().filter(NumberUtils::isParsable)
                                                            .map(Long::valueOf).findFirst().orElse(0L)));
                                    if (CollectionUtils.isNotEmpty(pageDOS)) {
                                        attachments.addAll(buildActivityPageAttachments(processOrchestrationDTO,
                                                actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap));
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 3) {
                                    continue;
                                }
                            }

                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.MANUAL_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (StringUtils.isBlank(supplyDetailDTO.getProductId())) {
                                    continue;
                                }
                                List<String> productIds = Arrays
                                        .asList(supplyDetailDTO.getProductId().replace("[;，]", ",").split(","));

                                if (CollectionUtils.isEmpty(productIds)) {
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 1) {
                                    for (String productId : productIds) {
                                        if (false == NumberUtils.isParsable(productId)) {
                                            return;
                                        }
                                        ProductInfoDTO productInfoDTO = productManagementService
                                                .queryProductInfoByProductIdAndAppId(Long.valueOf(productId),
                                                        processOrchestrationDTO.getAppId());
                                        if (null == productInfoDTO) {
                                            continue;
                                        }

                                        if (supplyDetailDTO.getMarketingCopySource() == 1) {
                                            String content = productManagementService.getCommentFromAIGC(productInfoDTO,
                                                    detailDO.getExecuteLogId(), processOrchestrationDTO.getAppId());
                                            if (StringUtils.isNotBlank(content)) {
                                                MsgContentDTO msgContentDTO = new MsgContentDTO();
                                                msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                                msgContentDTO.setTextDTO(new TextDTO());
                                                msgContentDTO.getTextDTO().setContent(content);
                                                attachments.add(msgContentDTO);
                                            }

                                        }
                                        if (supplyDetailDTO.getMarketingCopySource() == 2) {
                                            MsgContentDTO msgContentDTO = new MsgContentDTO();
                                            msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                                            msgContentDTO.setTextDTO(new TextDTO());
                                            msgContentDTO.getTextDTO().setContent(supplyDetailDTO.getMarketingCopy());
                                            attachments.add(msgContentDTO);
                                        }
                                        MsgContentDTO attachmentVO;
                                        if (existedAttachmentMap.containsKey(supplyDetailDTO.getProductType() + "-"
                                                + productInfoDTO.getProductId())) {
                                            attachmentVO = existedAttachmentMap.get(supplyDetailDTO.getProductType()
                                                    + "-" + productInfoDTO.getProductId());
                                        } else {
                                            attachmentVO = new MsgContentDTO();
                                            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
                                            // attachmentVO.setLinkDTO(new LinkDTO());
                                            attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
                                            MiniProgramDTO miniProgramDTO = new MiniProgramDTO();
                                            miniProgramDTO.setDescription(productInfoDTO.getProductTitle());
                                            miniProgramDTO
                                                    .setOriginAppId(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID);
                                            miniProgramDTO.setAppId(GroupDynamicCodeConstants.MX_MINIP_APPID);
                                            miniProgramDTO.setThumbnail(productInfoDTO.getHeadPic());
                                            BizDistributorServiceKeyObject bizDistributorServiceKeyObject = new BizDistributorServiceKeyObject(
                                                    actionAttachmentDTO, processOrchestrationDTO.getAppId());
                                            String communityDistributorCode = informationGatheringService
                                                    .queryCommunityDistributor(bizDistributorServiceKeyObject);
                                            miniProgramDTO.setPagePath(buildProductUrl(productInfoDTO.getH5Url(),
                                                    productInfoDTO.getProductId(), communityDistributorCode,
                                                    processOrchestrationDTO.getAppId(),true));
                                            attachmentVO.setMiniProgramDTO(miniProgramDTO);
                                            existedAttachmentMap.put(supplyDetailDTO.getProductType() + "-"
                                                    + productInfoDTO.getProductId(), attachmentVO);
                                        }
                                        attachments.add(attachmentVO);
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 2) {
                                    List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = productManagementService
                                            .queryActivityPagesById(productIds.stream().filter(NumberUtil::isNumber)
                                                    .map(Long::valueOf).collect(Collectors.toList()));
                                    if (CollectionUtils.isNotEmpty(pageDOS)) {
                                        attachments.addAll(buildActivityPageAttachments(processOrchestrationDTO,
                                                actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap));
                                    }
                                    continue;
                                }
                                if (supplyDetailDTO.getProductType() == 3) {
                                    continue;
                                }
                            }
                            if (ScrmProcessOrchestrationContentSupplyTypeEnum.CUSTOMIZED_PRODUCT_PROMOTION.getValue()
                                    .equals(supplyDetailDTO.getSupplyType())) {
                                if (supplyDetailDTO.getJumpPageType() == 1) {
                                    MsgContentDTO attachmentVO = getCustomizedProductPromotionAttachmentVO(
                                            processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO,
                                            existedAttachmentMap);
                                    if (attachmentVO == null) {
                                        return;
                                    }
                                    attachments.add(attachmentVO);
                                }
                                if (supplyDetailDTO.getJumpPageType() == 2) {
                                    continue;
                                }
                                if (supplyDetailDTO.getJumpPageType() == 3) {
                                    continue;
                                }
                            }
                        }
                    }
                    request.setMsgContentDTOList(attachments);
                }

                if (CollectionUtils.isEmpty(request.getMsgContentDTOList())) {
                    continue;
                }

                ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
                wxInvokeLogDO.setExecutorId(executorId);
                wxInvokeLogDO.setProcessOrchestrationId(processOrchestrationDTO.getId());
                wxInvokeLogDO.setProcessOrchestrationVersion(processOrchestrationDTO.getValidVersion());
                wxInvokeLogDO.setProcessOrchestrationNodeId(detailDO.getProcessOrchestrationNodeId());
                wxInvokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                        .getValue().byteValue());
                wxInvokeLogDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
                wxInvokeLogDO.setJobid(StringUtils.EMPTY);

                SendGroupMsgTResponse sendGroupMsgTResponse = dsGroupMessageAcl.sendGroupMsg(request);
                if (null == sendGroupMsgTResponse || sendGroupMsgTResponse.getTaskId() <= 0) {
                    log.error("execute.groupSendMessage fail, request:{}, response:{}", JsonUtils.toStr(request),
                            JsonUtils.toStr(sendGroupMsgTResponse));
                    ScrmAmProcessOrchestrationExecuteLogDO updateLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
                    updateLogDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE
                            .getValue().byteValue());
                    updateLogDO.setExecutorId(assistantOfSendMsgRequestMap.get(groupEntry.getKey()).getUserId());
                    ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
                    executeLogDOExample.createCriteria()
                            .andIdIn(groupEntry.getValue().stream()
                                    .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getExecuteLogId)
                                    .collect(Collectors.toList()));
                    executeLogDOMapper.updateByExampleSelective(updateLogDO, executeLogDOExample);
                } else {
                    wxInvokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND
                            .getValue().byteValue());
                    wxInvokeLogDO.setJobid(String.valueOf(sendGroupMsgTResponse.getTaskId()));
                    wxInvokeLogDOMapper.insert(wxInvokeLogDO);

                    ScrmAmProcessOrchestrationWxInvokeDetailDOExample wxInvokeDetailDOExample1 = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
                    wxInvokeDetailDOExample1.createCriteria().andIdIn(groupEntry.getValue().stream()
                            .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getId).collect(Collectors.toList()))
                            .andStatusEqualTo(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE
                                    .getValue().byteValue());
                    ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
                    wxInvokeDetailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND
                            .getValue().byteValue());
                    wxInvokeDetailDO.setInvokeLogId(wxInvokeLogDO.getId());
                    wxInvokeDetailDOMapper.updateByExampleSelective(wxInvokeDetailDO, wxInvokeDetailDOExample1);

                    ScrmAmProcessOrchestrationExecuteLogDO updateLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
                    updateLogDO.setExecutorId(assistantOfSendMsgRequestMap.get(groupEntry.getKey()).getUserId());
                    ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
                    executeLogDOExample.createCriteria()
                            .andIdIn(groupEntry.getValue().stream()
                                    .map(ScrmAmProcessOrchestrationWxInvokeDetailDO::getExecuteLogId)
                                    .collect(Collectors.toList()));
                    executeLogDOMapper.updateByExampleSelective(updateLogDO, executeLogDOExample);
                }

            }
        }
    }

    private static void buildOfficialMsgDTO(ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO,
            ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO, List<MsgContentDTO> attachments) {
        if (actionAttachmentDTO.getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE
                .getValue()) {
            MsgContentDTO attachmentVO = new MsgContentDTO();
            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.IMAGE);
            attachmentVO.setImageDTO(new ImageDTO());
            attachmentVO.getImageDTO().setUrl(contentDetailDTO.getPicUrl());
            attachments.add(attachmentVO);
        }

        if (actionAttachmentDTO.getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue()) {
            MsgContentDTO attachmentVO = new MsgContentDTO();
            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.LINK);
            attachmentVO.setLinkDTO(new LinkDTO());
            attachmentVO.getLinkDTO().setUrl(contentDetailDTO.getContentUrl());
            attachmentVO.getLinkDTO().setTitle(contentDetailDTO.getTitle());
            attachmentVO.getLinkDTO().setThumbUrl(contentDetailDTO.getPicUrl());
            attachmentVO.getLinkDTO().setDescription(contentDetailDTO.getDesc());
            attachments.add(attachmentVO);
        }

        if (actionAttachmentDTO.getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.MINI_PROGRAM
                .getValue()) {
            MsgContentDTO attachmentVO = new MsgContentDTO();
            attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
            attachmentVO.getMiniProgramDTO().setOriginAppId(contentDetailDTO.getOriginAppId());
            attachmentVO.getMiniProgramDTO().setAppId(contentDetailDTO.getAppId());
            attachmentVO.getMiniProgramDTO().setDescription(contentDetailDTO.getDesc());
            attachmentVO.getMiniProgramDTO().setThumbnail(contentDetailDTO.getPicUrl());
            attachmentVO.getMiniProgramDTO().setPagePath(contentDetailDTO.getContentUrl());
            attachments.add(attachmentVO);
        }

        if (actionAttachmentDTO.getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue()) {
            MsgContentDTO attachmentVO = new MsgContentDTO();
            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.VIDEO);
            attachmentVO.setVideoDTO(new VideoDTO());
            attachmentVO.getVideoDTO().setUrl(contentDetailDTO.getContentUrl());
            attachments.add(attachmentVO);
        }

        if (actionAttachmentDTO.getAttachmentTypeId() == ScrmProcessOrchestrationAttachmentTypeEnum.TEXT.getValue()) {
            MsgContentDTO attachmentVO = new MsgContentDTO();
            attachmentVO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
            attachmentVO.setTextDTO(new TextDTO());
            attachmentVO.getTextDTO().setContent(contentDetailDTO.getContentUrl());
            attachments.add(attachmentVO);
        }
    }

    private void dealFailedResult(ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO, MsgResultEvent msgResultEvent) {
        ScrmAmProcessOrchestrationWxInvokeDetailDOExample failedExample = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
        failedExample.createCriteria()
                .andStatusEqualTo(
                        ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue())
                .andProcessOrchestrationIdEqualTo(invokeLogDO.getProcessOrchestrationId())
                .andProcessOrchestrationVersionEqualTo(invokeLogDO.getProcessOrchestrationVersion())
                .andInvokeLogIdEqualTo(invokeLogDO.getId());
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> failedDetailDOList = wxInvokeDetailDOMapper
                .selectByExample(failedExample);
        if (CollectionUtils.isEmpty(failedDetailDOList)) {
            return;
        }
        ScrmAmProcessOrchestrationWxInvokeDetailDO failedInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        failedInvokeDetailDO
                .setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_PREVENT_DISTURBANCE
                        .getValue().byteValue());
        wxInvokeDetailDOMapper.updateByExampleSelective(failedInvokeDetailDO, failedExample);

        ScrmAmProcessOrchestrationExecuteLogDOExample failedLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
        failedLogDOExample.createCriteria()
                .andIdIn(failedDetailDOList.stream().map(o -> o.getExecuteLogId()).collect(Collectors.toList()));
        ScrmAmProcessOrchestrationExecuteLogDO failedLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        failedLogDO.setStatus(
                ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE.getValue().byteValue());
        executeLogDOMapper.updateByExampleSelective(failedLogDO, failedLogDOExample);
    }

    private void dealSuccessResult(ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO, MsgResultEvent msgResultEvent) {
        ScrmAmProcessOrchestrationWxInvokeDetailDOExample successExample = new ScrmAmProcessOrchestrationWxInvokeDetailDOExample();
        successExample.createCriteria()
                .andStatusEqualTo(
                        ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue())
                .andProcessOrchestrationIdEqualTo(invokeLogDO.getProcessOrchestrationId())
                .andProcessOrchestrationVersionEqualTo(invokeLogDO.getProcessOrchestrationVersion())
                .andInvokeLogIdEqualTo(invokeLogDO.getId());
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> successDetailDOList = wxInvokeDetailDOMapper
                .selectByExample(successExample);
        if (CollectionUtils.isEmpty(successDetailDOList)) {
            return;
        }
        ScrmAmProcessOrchestrationWxInvokeDetailDO successInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        successInvokeDetailDO.setStatus(
                ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue());
        wxInvokeDetailDOMapper.updateByExampleSelective(successInvokeDetailDO, successExample);

        // 打点 数据已经发送成功，标记用户已接受消息
        informationGatheringService.logUserStatusChangeDetail(successDetailDOList,
                ProcessOrchestrationInformationGatheringEnum.MESSAGE_RECEIVED);

        ScrmAmProcessOrchestrationExecuteLogDOExample successLogDOExample = new ScrmAmProcessOrchestrationExecuteLogDOExample();
        successLogDOExample.createCriteria()
                .andIdIn(successDetailDOList.stream().map(o -> o.getExecuteLogId()).collect(Collectors.toList()));
        ScrmAmProcessOrchestrationExecuteLogDO failedLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        failedLogDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_CHECK.getValue().byteValue());
        executeLogDOMapper.updateByExampleSelective(failedLogDO, successLogDOExample);
    }

    private List<MsgContentDTO> buildActivityPageAttachments(ScrmProcessOrchestrationDTO processOrchestrationDTO,
            ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO,
            ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO,
            List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS,
            Map<String, MsgContentDTO> existedAttachmentMap) {
        List<MsgContentDTO> attachments = new ArrayList<>();
        for (ScrmAmProcessOrchestrationProductActivityPageDO pageInfo : pageDOS) {
            MsgContentDTO attachmentVO = getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO,
                    supplyDetailDTO, pageInfo, existedAttachmentMap);
            if (attachmentVO == null) {
                continue;
            }
            attachments.add(attachmentVO);
        }
        if (supplyDetailDTO.getMarketingCopySource() == 1) {
            // String content =
            // productManagementService.getCommentFromAIGC(productInfoDTO,processOrchestrationDTO.getAppId());
            String content = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(content)) {
                MsgContentDTO msgContentDTO = new MsgContentDTO();
                msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                msgContentDTO.setTextDTO(new TextDTO());
                msgContentDTO.getTextDTO().setContent(content);
                attachments.add(msgContentDTO);
            }

        }
        if (supplyDetailDTO.getMarketingCopySource() == 2) {
            MsgContentDTO msgContentDTO = new MsgContentDTO();
            msgContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
            msgContentDTO.setTextDTO(new TextDTO());
            msgContentDTO.getTextDTO().setContent(supplyDetailDTO.getMarketingCopy());
            attachments.add(msgContentDTO);
        }
        return attachments;
    }

    @Nullable
    private MsgContentDTO getActivityPageAttachmentVO(ScrmProcessOrchestrationDTO processOrchestrationDTO,
            ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO,
            ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO,
            ScrmAmProcessOrchestrationProductActivityPageDO pageInfo, Map<String, MsgContentDTO> existedAttachmentMap) {
        if (existedAttachmentMap.containsKey(supplyDetailDTO.getProductType() + "-" + pageInfo.getId())) {
            return existedAttachmentMap.get(supplyDetailDTO.getProductType() + "-" + pageInfo.getId());
        }

        MsgContentDTO attachmentVO = new MsgContentDTO();
        attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
        attachmentVO.getMiniProgramDTO().setOriginAppId(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID);
        attachmentVO.getMiniProgramDTO().setAppId(GroupDynamicCodeConstants.MX_MINIP_APPID);
        attachmentVO.getMiniProgramDTO().setDescription(pageInfo.getActivityTitle());
        attachmentVO.getMiniProgramDTO().setThumbnail(pageInfo.getThumbPicUrl());
        BizDistributorServiceKeyObject bizDistributorServiceKeyObject = new BizDistributorServiceKeyObject(
                actionAttachmentDTO, processOrchestrationDTO.getAppId());
        String communityDistributorCode = informationGatheringService
                .queryCommunityDistributor(bizDistributorServiceKeyObject);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = productManagementService.getActivSceneCodeDO(
                actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode,
                processOrchestrationDTO.getAppId());
        attachmentVO.getMiniProgramDTO()
                .setPagePath(buildPageUrl(sceneCodeDO, communityDistributorCode, processOrchestrationDTO.getAppId(),true));
        existedAttachmentMap.put(supplyDetailDTO.getProductType() + "-" + pageInfo.getId(), attachmentVO);
        return attachmentVO;
    }

    @Nullable
    private MsgContentDTO getCustomizedProductPromotionAttachmentVO(ScrmProcessOrchestrationDTO processOrchestrationDTO,
            ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO,
            ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO,
            Map<String, MsgContentDTO> existedAttachmentMap) {
        if (existedAttachmentMap.containsKey(supplyDetailDTO.getProductType() + "-" + supplyDetailDTO.getJumpUrl())) {
            return existedAttachmentMap.get(supplyDetailDTO.getProductType() + "-" + supplyDetailDTO.getJumpUrl());
        }
        MsgContentDTO attachmentVO = new MsgContentDTO();
        attachmentVO.setMiniProgramDTO(new MiniProgramDTO());
        attachmentVO.getMiniProgramDTO().setOriginAppId(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID);
        attachmentVO.getMiniProgramDTO().setAppId(GroupDynamicCodeConstants.MX_MINIP_APPID);
        attachmentVO.getMiniProgramDTO().setDescription(supplyDetailDTO.getShelfName());
        attachmentVO.getMiniProgramDTO().setThumbnail(supplyDetailDTO.getHeadpicUrl());
        BizDistributorServiceKeyObject bizDistributorServiceKeyObject = new BizDistributorServiceKeyObject(
                actionAttachmentDTO, processOrchestrationDTO.getAppId());
        String communityDistributorCode = informationGatheringService
                .queryCommunityDistributor(bizDistributorServiceKeyObject);
        attachmentVO.getMiniProgramDTO().setPagePath(
                buildPageUrl(supplyDetailDTO, communityDistributorCode, processOrchestrationDTO.getAppId(),true));
        existedAttachmentMap.put(supplyDetailDTO.getProductType() + "-" + supplyDetailDTO.getJumpUrl(), attachmentVO);
        return attachmentVO;
    }

    @Override
    protected void logRequest(String request, String appId) {
        log.info("DeepSeaHandler appid : " + appId + " request:" + request);
    }
}*/
