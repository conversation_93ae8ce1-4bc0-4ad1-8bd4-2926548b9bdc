package com.sankuai.dz.srcm.friend.dynamiccode.request;

import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendTagBindInfo;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.CashBackDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendWelcomeMessageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FriendChannelCodeInfoRequest implements Serializable {
    private String appId;
    private Integer type;
    private String dynamicCodePoster;
    private String friendCodeName;
    private List<String> userIdList;
    private List<Integer> departmentIds;
    private Integer memberScope;
    private String accessToken;
    private Long friendChannelId;
    private Integer welcomeMsgType;
    private Long welcomeMsgId;
    private String welcomeMsgName;
    private FriendWelcomeMessageDTO welcomeMessageDTO;
    private FriendTagBindInfo tagBindInfo;
    private CashBackDTO cashBack;
}
