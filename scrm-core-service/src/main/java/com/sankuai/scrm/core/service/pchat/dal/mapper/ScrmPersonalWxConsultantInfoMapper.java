package com.sankuai.scrm.core.service.pchat.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxConsultantInfo;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxConsultantInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScrmPersonalWxConsultantInfoMapper extends MybatisBaseMapper<ScrmPersonalWxConsultantInfo, ScrmPersonalWxConsultantInfoExample, Long> {
    int batchInsert(@Param("list") List<ScrmPersonalWxConsultantInfo> list);
}