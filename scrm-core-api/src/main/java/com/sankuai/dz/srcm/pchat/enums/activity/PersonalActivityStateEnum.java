package com.sankuai.dz.srcm.pchat.enums.activity;

import lombok.Getter;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/12/19 15:46
 * @Version v1.0.0
 */
@Getter
public enum PersonalActivityStateEnum {
    CREATE("1", "待发布"),
    PUBLISH("2", "活动中"),
    FINISHED("3", "已结束"),
    ;

    private final String code;

    private final String desc;

    PersonalActivityStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PersonalActivityStateEnum fromCode(String code) {
        for (PersonalActivityStateEnum enumValue : PersonalActivityStateEnum.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }
}
