package com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/5/9
 */
@Data
public class ScrmProcessOrchestrationExecutePlanDTO implements Serializable {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: process_orchestration_type
     *   说明: 任务类型(人群包更新任务/定时任务/周期任务)
     */
    private Byte processOrchestrationType;

    /**
     *   字段: task_start_time
     *   说明: 预计任务正式开始时间
     */
    private Date taskStartTime;

    /**
     *   字段: status
     *   说明: 任务状态（未执行/执行中/执行失败/执行成功/未按时完成)
     */
    private Byte status;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}
