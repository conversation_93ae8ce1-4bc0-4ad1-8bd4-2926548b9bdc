package com.sankuai.scrm.core.service.pchat.domain.common;

import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.robot.agent.RobotInfoRequest;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxFriendsCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotTag;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxFriendsCommonExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotTagExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoCommonExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmPersonalWxUserCommonDomainServiceDeleteFriendTest {

    @Mock
    private ScrmPersonalWxFriendsCommonMapper scrmPersonalWxFriendsCommonMapper;

    @InjectMocks
    private ScrmPersonalWxUserCommonDomainService domainService;

    @Mock
    private ScrmPersonalWxUserInfoCommonMapper wxUserInfoCommonMapper;

    @Mock
    private ScrmPersonalWxRobotInfoCommonMapper robotInfoCommonMapper;

    @Mock
    private ScrmPersonalWxRobotLoginCommonLogMapper robotLoginCommonLogMapper;

    @Mock
    private ScrmPersonalWxRobotTagMapper personalWxRobotTagMapper;

    @Mock
    private ScrmPersonalWxFriendsCommonCustomMapper scrmPersonalWxFriendsCommonCustomMapper;

    @Captor
    private ArgumentCaptor<List<ScrmPersonalWxRobotTag>> insertCaptor;

    @Captor
    private ArgumentCaptor<ScrmPersonalWxRobotTag> updateCaptor;

    @Captor
    private ArgumentCaptor<Long> deleteCaptor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 deleteFriend 方法，friend 对象非空，且其 id 属性也非空
     */
    @Test
    public void testDeleteFriendNormal() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = new ScrmPersonalWxFriendsCommon();
        friend.setId(1L);
        // act
        domainService.deleteFriend(friend);
        // assert
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).deleteByPrimaryKey(friend.getId());
    }

    /**
     * 测试 deleteFriend 方法，friend 对象的 id 属性为空
     * 实际实现中不会抛出异常，只是会调用mapper方法
     */
    @Test
    public void testDeleteFriendNullId() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = new ScrmPersonalWxFriendsCommon();
        friend.setId(null);
        // act
        domainService.deleteFriend(friend);
        // assert
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).deleteByPrimaryKey(null);
    }

    @Test
    public void testQueryUserInfosWhenRobotSerialNoIsNull() throws Throwable {
        // arrange
        String robotSerialNo = null;
        String bizId = "testBizId";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfosWhenRobotSerialNoIsEmpty() throws Throwable {
        // arrange
        String robotSerialNo = "";
        String bizId = "testBizId";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfosWhenRobotSerialNoIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = "   ";
        String bizId = "testBizId";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfosWhenBizIdIsNull() throws Throwable {
        // arrange
        String robotSerialNo = "validSerialNo";
        String bizId = null;
        ScrmPersonalWxUserInfoCommon expectedUser = ScrmPersonalWxUserInfoCommon.builder().serialNo(robotSerialNo).build();
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.singletonList(expectedUser));
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(robotSerialNo, result.get(0).getSerialNo());
        verify(wxUserInfoCommonMapper).selectByExample(argThat(example -> {
            ScrmPersonalWxUserInfoCommonExample.Criteria criteria = example.getOredCriteria().get(0);
            return criteria.getCriteria().stream().anyMatch(c -> c.getCondition().equals("serial_no =") && c.getValue().equals(robotSerialNo)) && criteria.getCriteria().stream().noneMatch(c -> c.getCondition().contains("biz_id"));
        }));
    }

    @Test
    public void testQueryUserInfosWhenBizIdIsEmpty() throws Throwable {
        // arrange
        String robotSerialNo = "validSerialNo";
        String bizId = "";
        ScrmPersonalWxUserInfoCommon expectedUser = ScrmPersonalWxUserInfoCommon.builder().serialNo(robotSerialNo).build();
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.singletonList(expectedUser));
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(robotSerialNo, result.get(0).getSerialNo());
        verify(wxUserInfoCommonMapper).selectByExample(argThat(example -> {
            ScrmPersonalWxUserInfoCommonExample.Criteria criteria = example.getOredCriteria().get(0);
            return criteria.getCriteria().stream().anyMatch(c -> c.getCondition().equals("serial_no =") && c.getValue().equals(robotSerialNo)) && criteria.getCriteria().stream().noneMatch(c -> c.getCondition().contains("biz_id"));
        }));
    }

    @Test
    public void testQueryUserInfosWithValidParameters() throws Throwable {
        // arrange
        String robotSerialNo = "validSerialNo";
        String bizId = "validBizId";
        ScrmPersonalWxUserInfoCommon expectedUser = ScrmPersonalWxUserInfoCommon.builder().serialNo(robotSerialNo).bizId(bizId).build();
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.singletonList(expectedUser));
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(robotSerialNo, result.get(0).getSerialNo());
        assertEquals(bizId, result.get(0).getBizId());
        verify(wxUserInfoCommonMapper).selectByExample(argThat(example -> {
            ScrmPersonalWxUserInfoCommonExample.Criteria criteria = example.getOredCriteria().get(0);
            return criteria.getCriteria().stream().anyMatch(c -> c.getCondition().equals("serial_no =") && c.getValue().equals(robotSerialNo)) && criteria.getCriteria().stream().anyMatch(c -> c.getCondition().equals("biz_id =") && c.getValue().equals(bizId));
        }));
    }

    @Test
    public void testQueryUserInfosReturnsMultipleUsers() throws Throwable {
        // arrange
        String robotSerialNo = "validSerialNo";
        String bizId = "validBizId";
        List<ScrmPersonalWxUserInfoCommon> expectedUsers = Arrays.asList(ScrmPersonalWxUserInfoCommon.builder().serialNo(robotSerialNo).bizId(bizId).build(), ScrmPersonalWxUserInfoCommon.builder().serialNo(robotSerialNo).bizId(bizId).build());
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(expectedUsers);
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(robotSerialNo, result.get(0).getSerialNo());
        assertEquals(bizId, result.get(0).getBizId());
        assertEquals(robotSerialNo, result.get(1).getSerialNo());
        assertEquals(bizId, result.get(1).getBizId());
    }

    @Test
    public void testQueryUserInfosReturnsEmptyList() throws Throwable {
        // arrange
        String robotSerialNo = "validSerialNo";
        String bizId = "validBizId";
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfos(robotSerialNo, bizId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testTotalFriendWithNullUserId() throws Throwable {
        // arrange
        Long userId = null;
        String bizId = "test-biz-id";
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(0L, result);
        verify(scrmPersonalWxFriendsCommonMapper, never()).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithNullBizId() throws Throwable {
        // arrange
        Long userId = 123L;
        String bizId = null;
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(0L, result);
        verify(scrmPersonalWxFriendsCommonMapper, never()).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithEmptyBizId() throws Throwable {
        // arrange
        Long userId = 123L;
        String bizId = "";
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(0L, result);
        verify(scrmPersonalWxFriendsCommonMapper, never()).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithBlankBizId() throws Throwable {
        // arrange
        Long userId = 123L;
        String bizId = "   ";
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(0L, result);
        verify(scrmPersonalWxFriendsCommonMapper, never()).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithBothNull() throws Throwable {
        // arrange
        Long userId = null;
        String bizId = null;
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(0L, result);
        verify(scrmPersonalWxFriendsCommonMapper, never()).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithValidParameters() throws Throwable {
        // arrange
        Long userId = 123L;
        String bizId = "test-biz-id";
        long expectedCount = 5L;
        when(scrmPersonalWxFriendsCommonMapper.countByExample(any(ScrmPersonalWxFriendsCommonExample.class))).thenReturn(expectedCount);
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(expectedCount, result);
        verify(scrmPersonalWxFriendsCommonMapper).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithValidParametersButZeroResult() throws Throwable {
        // arrange
        Long userId = 123L;
        String bizId = "test-biz-id";
        long expectedCount = 0L;
        when(scrmPersonalWxFriendsCommonMapper.countByExample(any(ScrmPersonalWxFriendsCommonExample.class))).thenReturn(expectedCount);
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(expectedCount, result);
        verify(scrmPersonalWxFriendsCommonMapper).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testTotalFriendWithValidParametersAndLargeResult() throws Throwable {
        // arrange
        Long userId = 999L;
        String bizId = "large-biz-id";
        long expectedCount = 1000000L;
        when(scrmPersonalWxFriendsCommonMapper.countByExample(any(ScrmPersonalWxFriendsCommonExample.class))).thenReturn(expectedCount);
        // act
        long result = domainService.totalFriend(userId, bizId);
        // assert
        assertEquals(expectedCount, result);
        verify(scrmPersonalWxFriendsCommonMapper).countByExample(any(ScrmPersonalWxFriendsCommonExample.class));
    }

    @Test
    public void testQueryWxFriendRelationByUserIdAndFriendIdNormal() throws Throwable {
        // Arrange
        Long robotUserIdId = 1L;
        List<Long> friendIds = Arrays.asList(1L, 2L, 3L);
        String bizId = "bizId";
        List<ScrmPersonalWxFriendsCommon> expected = Arrays.asList(new ScrmPersonalWxFriendsCommon(), new ScrmPersonalWxFriendsCommon());
        when(scrmPersonalWxFriendsCommonMapper.selectByExample(any(ScrmPersonalWxFriendsCommonExample.class))).thenReturn(expected);
        // Act
        List<ScrmPersonalWxFriendsCommon> actual = domainService.queryWxFriendRelationByUserIdAndFriendId(robotUserIdId, friendIds, bizId);
        // Assert
        assertEquals(expected, actual);
    }

    @Test
    public void testQueryWxFriendRelationByUserIdAndFriendIdBoundary() throws Throwable {
        // Arrange
        Long robotUserIdId = 1L;
        List<Long> friendIds = Collections.emptyList();
        String bizId = "bizId";
        List<ScrmPersonalWxFriendsCommon> expected = Collections.emptyList();
        when(scrmPersonalWxFriendsCommonMapper.selectByExample(any(ScrmPersonalWxFriendsCommonExample.class))).thenReturn(expected);
        // Act
        List<ScrmPersonalWxFriendsCommon> actual = domainService.queryWxFriendRelationByUserIdAndFriendId(robotUserIdId, friendIds, bizId);
        // Assert
        assertEquals(expected, actual);
    }

    @Test
    public void testSaveUserInfoNormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo = new ScrmPersonalWxUserInfoCommon();
        userInfo.setId(1L);
        userInfo.setSerialNo("test123");
        // act
        domainService.saveUserInfo(userInfo);
        // assert
        verify(wxUserInfoCommonMapper, times(1)).insertSelective(userInfo);
        assert userInfo.getAddTime() != null : "Add time should be set";
        assert userInfo.getUpdateTime() != null : "Update time should be set";
        assert userInfo.getAddTime().equals(userInfo.getUpdateTime()) : "Add and update time should be same";
    }

    @Test
    public void testSaveUserInfoWhenInsertFails() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo = new ScrmPersonalWxUserInfoCommon();
        userInfo.setId(1L);
        userInfo.setSerialNo("test123");
        doThrow(new RuntimeException("Database error")).when(wxUserInfoCommonMapper).insertSelective(any());
        try {
            // act
            domainService.saveUserInfo(userInfo);
        } catch (Exception e) {
            // assert
            assert e instanceof RuntimeException : "Should throw RuntimeException";
            assert e.getMessage().contains("Database error") : "Error message should match";
            assert userInfo.getAddTime() != null : "Add time should still be set even if insert fails";
            assert userInfo.getUpdateTime() != null : "Update time should still be set even if insert fails";
            return;
        }
        assert false : "Should have thrown an exception";
    }

    @Test
    public void testSaveUserInfoWithPartialFields() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo = new ScrmPersonalWxUserInfoCommon();
        userInfo.setSerialNo("test123");
        // act
        domainService.saveUserInfo(userInfo);
        // assert
        verify(wxUserInfoCommonMapper, times(1)).insertSelective(userInfo);
        assert userInfo.getAddTime() != null : "Add time should be set";
        assert userInfo.getUpdateTime() != null : "Update time should be set";
    }

    public void updateFriend(ScrmPersonalWxFriendsCommon friend) {
        friend.setUpdateTime(new Date());
        scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testUpdateFriendNormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = ScrmPersonalWxFriendsCommon.builder().id(1L).userId(100L).friendId(200L).bizId("test-biz").remarkName("test-remark").build();
        Date originalUpdateTime = friend.getUpdateTime();
        when(scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxFriendsCommon.class))).thenReturn(1);
        // act
        domainService.updateFriend(friend);
        // assert
        assertNotNull("UpdateTime should be set", friend.getUpdateTime());
        assertNotEquals("UpdateTime should be different from original", originalUpdateTime, friend.getUpdateTime());
        assertTrue("UpdateTime should be recent", System.currentTimeMillis() - friend.getUpdateTime().getTime() < 1000);
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testUpdateFriendWithNullFriend() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = null;
        // act & assert
        try {
            domainService.updateFriend(friend);
            fail("Should throw NullPointerException when friend is null");
        } catch (NullPointerException e) {
            // Expected exception
            assertNotNull("NullPointerException should be thrown", e);
        }
        verify(scrmPersonalWxFriendsCommonMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    public void testUpdateFriendWithAllFields() throws Throwable {
        // arrange
        Date originalAddTime = new Date(System.currentTimeMillis() - 10000);
        ScrmPersonalWxFriendsCommon friend = ScrmPersonalWxFriendsCommon.builder().id(1L).userId(100L).friendId(200L).addTime(originalAddTime).bizId("test-biz").remarkName("test-remark").extra("extra-info").tagIds("1,2,3").build();
        when(scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxFriendsCommon.class))).thenReturn(1);
        // act
        domainService.updateFriend(friend);
        // assert
        assertEquals("ID should remain unchanged", Long.valueOf(1L), friend.getId());
        assertEquals("UserId should remain unchanged", Long.valueOf(100L), friend.getUserId());
        assertEquals("FriendId should remain unchanged", Long.valueOf(200L), friend.getFriendId());
        assertEquals("AddTime should remain unchanged", originalAddTime, friend.getAddTime());
        assertEquals("BizId should remain unchanged", "test-biz", friend.getBizId());
        assertEquals("RemarkName should remain unchanged", "test-remark", friend.getRemarkName());
        assertEquals("Extra should remain unchanged", "extra-info", friend.getExtra());
        assertEquals("TagIds should remain unchanged", "1,2,3", friend.getTagIds());
        assertNotNull("UpdateTime should be set", friend.getUpdateTime());
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testUpdateFriendMapperReturnsZero() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = ScrmPersonalWxFriendsCommon.builder().id(1L).userId(100L).friendId(200L).build();
        when(scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxFriendsCommon.class))).thenReturn(0);
        // act
        domainService.updateFriend(friend);
        // assert
        assertNotNull("UpdateTime should be set even when mapper returns 0", friend.getUpdateTime());
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testUpdateFriendMapperThrowsException() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = ScrmPersonalWxFriendsCommon.builder().id(1L).userId(100L).friendId(200L).build();
        RuntimeException expectedException = new RuntimeException("Database error");
        when(scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxFriendsCommon.class))).thenThrow(expectedException);
        // act & assert
        try {
            domainService.updateFriend(friend);
            fail("Should throw RuntimeException when mapper throws exception");
        } catch (RuntimeException e) {
            assertEquals("Should propagate the same exception", expectedException, e);
            assertEquals("Exception message should match", "Database error", e.getMessage());
        }
        assertNotNull("UpdateTime should still be set before mapper call", friend.getUpdateTime());
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testUpdateFriendMinimalFields() throws Throwable {
        // arrange
        ScrmPersonalWxFriendsCommon friend = new ScrmPersonalWxFriendsCommon();
        friend.setId(1L);
        when(scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxFriendsCommon.class))).thenReturn(1);
        // act
        domainService.updateFriend(friend);
        // assert
        assertEquals("ID should remain unchanged", Long.valueOf(1L), friend.getId());
        assertNotNull("UpdateTime should be set", friend.getUpdateTime());
        assertNull("UserId should remain null", friend.getUserId());
        assertNull("FriendId should remain null", friend.getFriendId());
        assertNull("BizId should remain null", friend.getBizId());
        verify(scrmPersonalWxFriendsCommonMapper, times(1)).updateByPrimaryKeySelective(friend);
    }

    @Test
    public void testQueryUserInfoWithEmptyUserIds() throws Throwable {
        // arrange
        List<Long> userIds = Collections.emptyList();
        String bizId = "testBizId";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfoWithNullUserIds() throws Throwable {
        // arrange
        List<Long> userIds = null;
        String bizId = "testBizId";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfoWithBlankBizId() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(1L, 2L);
        String bizId = "   ";
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfoWithNullBizId() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(1L, 2L);
        String bizId = null;
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper, never()).selectByExample(any());
    }

    @Test
    public void testQueryUserInfoWithValidInputsButEmptyResult() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(1L, 2L);
        String bizId = "testBizId";
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertTrue(result.isEmpty());
        verify(wxUserInfoCommonMapper).selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class));
    }

    @Test
    public void testQueryUserInfoWithValidInputsAndSingleResult() throws Throwable {
        // arrange
        List<Long> userIds = Collections.singletonList(1L);
        String bizId = "testBizId";
        ScrmPersonalWxUserInfoCommon expected = ScrmPersonalWxUserInfoCommon.builder().id(1L).bizId(bizId).build();
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(Collections.singletonList(expected));
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertEquals(1, result.size());
        assertEquals(expected, result.get(0));
        verify(wxUserInfoCommonMapper).selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class));
    }

    @Test
    public void testQueryUserInfoWithValidInputsAndMultipleResults() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(1L, 2L);
        String bizId = "testBizId";
        List<ScrmPersonalWxUserInfoCommon> expected = Arrays.asList(ScrmPersonalWxUserInfoCommon.builder().id(1L).bizId(bizId).build(), ScrmPersonalWxUserInfoCommon.builder().id(2L).bizId(bizId).build());
        when(wxUserInfoCommonMapper.selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class))).thenReturn(expected);
        // act
        List<ScrmPersonalWxUserInfoCommon> result = domainService.queryUserInfo(userIds, bizId);
        // assert
        assertEquals(2, result.size());
        assertEquals(expected, result);
        verify(wxUserInfoCommonMapper).selectByExample(any(ScrmPersonalWxUserInfoCommonExample.class));
    }

    @Test
    public void testQueryRobotInfoWhenRobotSerialNoIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = StringUtils.EMPTY;
        String bizId = "testBizId";
        // act
        ScrmPersonalWxRobotInfoCommon result = domainService.queryRobotInfo(robotSerialNo, bizId);
        // assert
        assertNull("Should return null when robotSerialNo is blank", result);
    }

    @Test
    public void testQueryRobotInfoWhenBizIdIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = "testSerialNo";
        String bizId = StringUtils.EMPTY;
        // act
        ScrmPersonalWxRobotInfoCommon result = domainService.queryRobotInfo(robotSerialNo, bizId);
        // assert
        assertNull("Should return null when bizId is blank", result);
    }

    @Test
    public void testQueryRobotInfoWhenBothParametersAreBlank() throws Throwable {
        // arrange
        String robotSerialNo = StringUtils.EMPTY;
        String bizId = StringUtils.EMPTY;
        // act
        ScrmPersonalWxRobotInfoCommon result = domainService.queryRobotInfo(robotSerialNo, bizId);
        // assert
        assertNull("Should return null when both parameters are blank", result);
    }

    @Test
    public void testQueryRobotInfoWhenQueryReturnsEmptyList() throws Throwable {
        // arrange
        String robotSerialNo = "testSerialNo";
        String bizId = "testBizId";
        when(robotInfoCommonMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        ScrmPersonalWxRobotInfoCommon result = domainService.queryRobotInfo(robotSerialNo, bizId);
        // assert
        assertNull("Should return null when query returns empty list", result);
    }

    @Test
    public void testQueryRobotInfoWhenQueryReturnsNonEmptyList() throws Throwable {
        // arrange
        String robotSerialNo = "testSerialNo";
        String bizId = "testBizId";
        ScrmPersonalWxRobotInfoCommon expectedRobotInfo = ScrmPersonalWxRobotInfoCommon.builder().id(1L).robotSerialNo(robotSerialNo).bizId(bizId).build();
        List<ScrmPersonalWxRobotInfoCommon> mockResult = Arrays.asList(expectedRobotInfo);
        when(robotInfoCommonMapper.selectByExample(any())).thenReturn(mockResult);
        // act
        ScrmPersonalWxRobotInfoCommon result = domainService.queryRobotInfo(robotSerialNo, bizId);
        // assert
        assertNotNull("Should not return null when query returns non-empty list", result);
        assertEquals("Should return first element from the list", expectedRobotInfo, result);
    }

    @Test
    public void testQueryUserInfoWhenRobotSerialNoIsNull() throws Throwable {
        // arrange
        String robotSerialNo = null;
        String bizId = "testBizId";
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when robotSerialNo is null", result);
    }

    @Test
    public void testQueryUserInfoWhenRobotSerialNoIsEmpty() throws Throwable {
        // arrange
        String robotSerialNo = "";
        String bizId = "testBizId";
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when robotSerialNo is empty", result);
    }

    @Test
    public void testQueryUserInfoWhenRobotSerialNoIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = "   ";
        String bizId = "testBizId";
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when robotSerialNo is blank", result);
    }

    @Test
    public void testQueryUserInfoWhenBizIdIsNull() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = null;
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when bizId is null", result);
    }

    @Test
    public void testQueryUserInfoWhenBizIdIsEmpty() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "";
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when bizId is empty", result);
    }

    @Test
    public void testQueryUserInfoWhenBizIdIsBlank() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "   ";
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when bizId is blank", result);
    }

    @Test
    public void testQueryUserInfoWhenBothParametersAreNull() throws Throwable {
        // arrange
        String robotSerialNo = null;
        String bizId = null;
        // act
        ScrmPersonalWxUserInfoCommon result = domainService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when both parameters are null", result);
    }

    @Test
    public void testQueryUserInfoWhenQueryUserInfosReturnsEmptyList() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "testBizId";
        ScrmPersonalWxUserCommonDomainService spyService = spy(domainService);
        doReturn(Collections.emptyList()).when(spyService).queryUserInfos(robotSerialNo, bizId);
        // act
        ScrmPersonalWxUserInfoCommon result = spyService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when queryUserInfos returns empty list", result);
        verify(spyService, times(1)).queryUserInfos(robotSerialNo, bizId);
    }

    @Test
    public void testQueryUserInfoWhenQueryUserInfosReturnsNull() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "testBizId";
        ScrmPersonalWxUserCommonDomainService spyService = spy(domainService);
        doReturn(null).when(spyService).queryUserInfos(robotSerialNo, bizId);
        // act
        ScrmPersonalWxUserInfoCommon result = spyService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNull("Result should be null when queryUserInfos returns null", result);
        verify(spyService, times(1)).queryUserInfos(robotSerialNo, bizId);
    }

    @Test
    public void testQueryUserInfoWhenQueryUserInfosReturnsListWithOneElement() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "testBizId";
        ScrmPersonalWxUserInfoCommon expectedUserInfo = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo(robotSerialNo).bizId(bizId).nickname("testNickname").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = new ArrayList<>();
        userInfoList.add(expectedUserInfo);
        ScrmPersonalWxUserCommonDomainService spyService = spy(domainService);
        doReturn(userInfoList).when(spyService).queryUserInfos(robotSerialNo, bizId);
        // act
        ScrmPersonalWxUserInfoCommon result = spyService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNotNull("Result should not be null when queryUserInfos returns non-empty list", result);
        assertEquals("Result should be the first element from the list", expectedUserInfo, result);
        assertEquals("Result id should match expected", expectedUserInfo.getId(), result.getId());
        assertEquals("Result serialNo should match expected", expectedUserInfo.getSerialNo(), result.getSerialNo());
        assertEquals("Result bizId should match expected", expectedUserInfo.getBizId(), result.getBizId());
        verify(spyService, times(1)).queryUserInfos(robotSerialNo, bizId);
    }

    @Test
    public void testQueryUserInfoWhenQueryUserInfosReturnsListWithMultipleElements() throws Throwable {
        // arrange
        String robotSerialNo = "testRobotSerialNo";
        String bizId = "testBizId";
        ScrmPersonalWxUserInfoCommon firstUserInfo = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo(robotSerialNo).bizId(bizId).nickname("firstNickname").build();
        ScrmPersonalWxUserInfoCommon secondUserInfo = ScrmPersonalWxUserInfoCommon.builder().id(2L).serialNo(robotSerialNo).bizId(bizId).nickname("secondNickname").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = new ArrayList<>();
        userInfoList.add(firstUserInfo);
        userInfoList.add(secondUserInfo);
        ScrmPersonalWxUserCommonDomainService spyService = spy(domainService);
        doReturn(userInfoList).when(spyService).queryUserInfos(robotSerialNo, bizId);
        // act
        ScrmPersonalWxUserInfoCommon result = spyService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNotNull("Result should not be null when queryUserInfos returns non-empty list", result);
        assertEquals("Result should be the first element from the list", firstUserInfo, result);
        assertEquals("Result id should match first element", firstUserInfo.getId(), result.getId());
        assertEquals("Result nickname should match first element", firstUserInfo.getNickname(), result.getNickname());
        assertNotEquals("Result should not be the second element", secondUserInfo, result);
        verify(spyService, times(1)).queryUserInfos(robotSerialNo, bizId);
    }

    @Test
    public void testQueryUserInfoWithSpecialCharacters() throws Throwable {
        // arrange
        String robotSerialNo = "robot@#$%^&*()";
        String bizId = "biz_id_with_underscore";
        ScrmPersonalWxUserInfoCommon expectedUserInfo = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo(robotSerialNo).bizId(bizId).build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = new ArrayList<>();
        userInfoList.add(expectedUserInfo);
        ScrmPersonalWxUserCommonDomainService spyService = spy(domainService);
        doReturn(userInfoList).when(spyService).queryUserInfos(robotSerialNo, bizId);
        // act
        ScrmPersonalWxUserInfoCommon result = spyService.queryUserInfo(robotSerialNo, bizId);
        // assert
        assertNotNull("Result should not be null with special characters in parameters", result);
        assertEquals("Result should match expected with special characters", expectedUserInfo, result);
        verify(spyService, times(1)).queryUserInfos(robotSerialNo, bizId);
    }

    public void updateWxUserInfoById(List<ScrmPersonalWxUserInfoCommon> userInfoList) {
        userInfoList.forEach(r -> {
            r.setUpdateTime(new Date());
            wxUserInfoCommonMapper.updateByPrimaryKeySelective(r);
        });
    }

    @Test
    public void testUpdateWxUserInfoById_WithValidList() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo1 = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo("serial1").wxId("wx1").nickname("nick1").build();
        ScrmPersonalWxUserInfoCommon userInfo2 = ScrmPersonalWxUserInfoCommon.builder().id(2L).serialNo("serial2").wxId("wx2").nickname("nick2").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = Arrays.asList(userInfo1, userInfo2);
        when(wxUserInfoCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateWxUserInfoById(userInfoList);
        // assert
        verify(wxUserInfoCommonMapper, times(2)).updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class));
        assertNotNull("UpdateTime should be set for userInfo1", userInfo1.getUpdateTime());
        assertNotNull("UpdateTime should be set for userInfo2", userInfo2.getUpdateTime());
        assertTrue("UpdateTime should be recent", System.currentTimeMillis() - userInfo1.getUpdateTime().getTime() < 1000);
        assertTrue("UpdateTime should be recent", System.currentTimeMillis() - userInfo2.getUpdateTime().getTime() < 1000);
    }

    @Test
    public void testUpdateWxUserInfoById_WithEmptyList() throws Throwable {
        // arrange
        List<ScrmPersonalWxUserInfoCommon> userInfoList = new ArrayList<>();
        // act
        domainService.updateWxUserInfoById(userInfoList);
        // assert
        verify(wxUserInfoCommonMapper, never()).updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class));
    }


    @Test
    public void testUpdateWxUserInfoById_WithSingleElement() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo("serial1").wxId("wx1").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = Arrays.asList(userInfo);
        when(wxUserInfoCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateWxUserInfoById(userInfoList);
        // assert
        verify(wxUserInfoCommonMapper, times(1)).updateByPrimaryKeySelective(userInfo);
        assertNotNull("UpdateTime should be set", userInfo.getUpdateTime());
        assertTrue("UpdateTime should be recent", System.currentTimeMillis() - userInfo.getUpdateTime().getTime() < 1000);
    }

    @Test
    public void testUpdateWxUserInfoById_VerifyMapperParameters() throws Throwable {
        // arrange
        ScrmPersonalWxUserInfoCommon userInfo = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo("serial1").wxId("wx1").nickname("testNick").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = Arrays.asList(userInfo);
        ArgumentCaptor<ScrmPersonalWxUserInfoCommon> captor = ArgumentCaptor.forClass(ScrmPersonalWxUserInfoCommon.class);
        when(wxUserInfoCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateWxUserInfoById(userInfoList);
        // assert
        verify(wxUserInfoCommonMapper).updateByPrimaryKeySelective(captor.capture());
        ScrmPersonalWxUserInfoCommon capturedUserInfo = captor.getValue();
        assertEquals("ID should match", Long.valueOf(1L), capturedUserInfo.getId());
        assertEquals("SerialNo should match", "serial1", capturedUserInfo.getSerialNo());
        assertEquals("WxId should match", "wx1", capturedUserInfo.getWxId());
        assertEquals("Nickname should match", "testNick", capturedUserInfo.getNickname());
        assertNotNull("UpdateTime should be set", capturedUserInfo.getUpdateTime());
    }

    @Test
    public void testUpdateWxUserInfoById_MultipleElementsUpdateTimeSet() throws Throwable {
        // arrange
        Date beforeTest = new Date();
        ScrmPersonalWxUserInfoCommon userInfo1 = ScrmPersonalWxUserInfoCommon.builder().id(1L).serialNo("serial1").build();
        ScrmPersonalWxUserInfoCommon userInfo2 = ScrmPersonalWxUserInfoCommon.builder().id(2L).serialNo("serial2").build();
        ScrmPersonalWxUserInfoCommon userInfo3 = ScrmPersonalWxUserInfoCommon.builder().id(3L).serialNo("serial3").build();
        List<ScrmPersonalWxUserInfoCommon> userInfoList = Arrays.asList(userInfo1, userInfo2, userInfo3);
        when(wxUserInfoCommonMapper.updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateWxUserInfoById(userInfoList);
        // assert
        Date afterTest = new Date();
        verify(wxUserInfoCommonMapper, times(3)).updateByPrimaryKeySelective(any(ScrmPersonalWxUserInfoCommon.class));
        assertNotNull("UserInfo1 updateTime should be set", userInfo1.getUpdateTime());
        assertNotNull("UserInfo2 updateTime should be set", userInfo2.getUpdateTime());
        assertNotNull("UserInfo3 updateTime should be set", userInfo3.getUpdateTime());
        assertTrue("UserInfo1 updateTime should be after test start", userInfo1.getUpdateTime().getTime() >= beforeTest.getTime());
        assertTrue("UserInfo1 updateTime should be before test end", userInfo1.getUpdateTime().getTime() <= afterTest.getTime());
        assertTrue("UserInfo2 updateTime should be after test start", userInfo2.getUpdateTime().getTime() >= beforeTest.getTime());
        assertTrue("UserInfo2 updateTime should be before test end", userInfo2.getUpdateTime().getTime() <= afterTest.getTime());
        assertTrue("UserInfo3 updateTime should be after test start", userInfo3.getUpdateTime().getTime() >= beforeTest.getTime());
        assertTrue("UserInfo3 updateTime should be before test end", userInfo3.getUpdateTime().getTime() <= afterTest.getTime());
    }

    private List<ScrmPersonalWxRobotTag> createSampleTags() {
        ScrmPersonalWxRobotTag tag1 = new ScrmPersonalWxRobotTag();
        tag1.setId(1L);
        tag1.setSerialNo("robot123");
        tag1.setTagId(101);
        tag1.setTagName("Tag1");
        tag1.setAddTime(new Date());
        tag1.setUpdateTime(new Date());
        tag1.setBizId("biz123");
        ScrmPersonalWxRobotTag tag2 = new ScrmPersonalWxRobotTag();
        tag2.setId(2L);
        tag2.setSerialNo("robot123");
        tag2.setTagId(102);
        tag2.setTagName("Tag2");
        tag2.setAddTime(new Date());
        tag2.setUpdateTime(new Date());
        tag2.setBizId("biz123");
        return Arrays.asList(tag1, tag2);
    }

    @Test
    public void testQueryRobotTagListWhenNoTagsFound() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(0L);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.isEmpty());
        assertTrue(result.isEnd());
        // Verify the example was created with correct criteria
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).countByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithPagination() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        request.setPageNo(1);
        request.setPageSize(10);
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(20L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(20, result.getTotal());
        assertEquals(tagList, result.getData());
        assertTrue(result.isEnd());
        // Verify pagination was applied
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).selectByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithoutPagination() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        // No pageNo and pageSize set (null values)
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(5L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(5, result.getTotal());
        assertEquals(tagList, result.getData());
        assertTrue(result.isEnd());
        assertFalse(result.isEmpty());
        // Verify the example was created with correct criteria but without pagination
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).selectByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithNullRobotSerialNo() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        // null robotSerialNo
        request.setRobotSerialNo(null);
        request.setBizId("biz123");
        // act & assert
        try {
            domainService.queryRobotTagList(request);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Value for serialNo cannot be null", e.getMessage());
        }
    }

    @Test
    public void testQueryRobotTagListWithNullBizId() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        // null bizId
        request.setBizId(null);
        // act & assert
        try {
            domainService.queryRobotTagList(request);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Value for bizId cannot be null", e.getMessage());
        }
    }

    @Test
    public void testQueryRobotTagListWithBothNullParameters() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo(null);
        request.setBizId(null);
        // act & assert
        try {
            domainService.queryRobotTagList(request);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("Value for serialNo cannot be null", e.getMessage());
        }
    }

    @Test
    public void testQueryRobotTagListWithZeroPageSize() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        request.setPageNo(0);
        request.setPageSize(0);
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(10L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(10, result.getTotal());
        assertEquals(tagList, result.getData());
        // Verify pagination was applied (even with zero values)
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).selectByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithEmptyStringParameters() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        // empty string
        request.setRobotSerialNo("");
        // empty string
        request.setBizId("");
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(3L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getTotal());
        assertEquals(tagList, result.getData());
        // Verify the example was created
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).countByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithLargePageNumbers() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        request.setPageNo(999);
        request.setPageSize(100);
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(50L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(50, result.getTotal());
        assertEquals(tagList, result.getData());
        // Verify pagination was applied
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).selectByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    @Test
    public void testQueryRobotTagListWithNegativePageNumbers() throws Throwable {
        // arrange
        RobotInfoRequest request = new RobotInfoRequest();
        request.setRobotSerialNo("robot123");
        request.setBizId("biz123");
        request.setPageNo(-1);
        request.setPageSize(-10);
        List<ScrmPersonalWxRobotTag> tagList = createSampleTags();
        when(personalWxRobotTagMapper.countByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(15L);
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(tagList);
        // act
        PagerList<ScrmPersonalWxRobotTag> result = domainService.queryRobotTagList(request);
        // assert
        assertNotNull(result);
        assertEquals(15, result.getTotal());
        assertEquals(tagList, result.getData());
        // Verify pagination was applied (even with negative values)
        ArgumentCaptor<ScrmPersonalWxRobotTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmPersonalWxRobotTagExample.class);
        verify(personalWxRobotTagMapper).selectByExample(exampleCaptor.capture());
        ScrmPersonalWxRobotTagExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
    }

    public void updateRobotInfo(ScrmPersonalWxRobotInfoCommon robotInfo) {
        robotInfo.setUpdateTime(new Date());
        robotInfoCommonMapper.updateByPrimaryKey(robotInfo);
    }

    @Test
    public void testUpdateRobotInfoWithValidRobotInfo() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = ScrmPersonalWxRobotInfoCommon.builder().id(1L).robotSerialNo("robot123").robotWxId("wx123").bizId("biz123").build();
        Date beforeCall = new Date();
        when(robotInfoCommonMapper.updateByPrimaryKey(any(ScrmPersonalWxRobotInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateRobotInfo(robotInfo);
        Date afterCall = new Date();
        // assert
        assertNotNull("UpdateTime should be set", robotInfo.getUpdateTime());
        assertTrue("UpdateTime should be set to current time", robotInfo.getUpdateTime().getTime() >= beforeCall.getTime() && robotInfo.getUpdateTime().getTime() <= afterCall.getTime());
        ArgumentCaptor<ScrmPersonalWxRobotInfoCommon> captor = ArgumentCaptor.forClass(ScrmPersonalWxRobotInfoCommon.class);
        verify(robotInfoCommonMapper, times(1)).updateByPrimaryKey(captor.capture());
        ScrmPersonalWxRobotInfoCommon capturedRobotInfo = captor.getValue();
        assertEquals("Should pass the same robot info object", robotInfo, capturedRobotInfo);
        assertNotNull("Captured robot info should have updateTime set", capturedRobotInfo.getUpdateTime());
    }


    @Test
    public void testUpdateRobotInfoWithExistingUpdateTime() throws Throwable {
        // arrange
        // 10 seconds ago
        Date oldUpdateTime = new Date(System.currentTimeMillis() - 10000);
        ScrmPersonalWxRobotInfoCommon robotInfo = ScrmPersonalWxRobotInfoCommon.builder().id(1L).robotSerialNo("robot123").updateTime(oldUpdateTime).build();
        Date beforeCall = new Date();
        when(robotInfoCommonMapper.updateByPrimaryKey(any(ScrmPersonalWxRobotInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateRobotInfo(robotInfo);
        Date afterCall = new Date();
        // assert
        assertNotNull("UpdateTime should be set", robotInfo.getUpdateTime());
        assertNotEquals("UpdateTime should be different from old time", oldUpdateTime, robotInfo.getUpdateTime());
        assertTrue("UpdateTime should be updated to current time", robotInfo.getUpdateTime().getTime() >= beforeCall.getTime() && robotInfo.getUpdateTime().getTime() <= afterCall.getTime());
        verify(robotInfoCommonMapper, times(1)).updateByPrimaryKey(robotInfo);
    }

    @Test
    public void testUpdateRobotInfoWithMinimalRobotInfo() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = new ScrmPersonalWxRobotInfoCommon();
        robotInfo.setId(1L);
        when(robotInfoCommonMapper.updateByPrimaryKey(any(ScrmPersonalWxRobotInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateRobotInfo(robotInfo);
        // assert
        assertNotNull("UpdateTime should be set even for minimal robot info", robotInfo.getUpdateTime());
        verify(robotInfoCommonMapper, times(1)).updateByPrimaryKey(robotInfo);
    }

    @Test
    public void testUpdateRobotInfoMapperCalledOnce() throws Throwable {
        // arrange
        ScrmPersonalWxRobotInfoCommon robotInfo = ScrmPersonalWxRobotInfoCommon.builder().id(1L).robotSerialNo("robot123").build();
        when(robotInfoCommonMapper.updateByPrimaryKey(any(ScrmPersonalWxRobotInfoCommon.class))).thenReturn(1);
        // act
        domainService.updateRobotInfo(robotInfo);
        // assert
        verify(robotInfoCommonMapper, times(1)).updateByPrimaryKey(robotInfo);
        verifyNoMoreInteractions(robotInfoCommonMapper);
    }

    @Test
    public void testProcessRobotTagListWithEmptyList() throws Throwable {
        // arrange
        List<ScrmPersonalWxRobotTag> emptyList = new ArrayList<>();
        // act
        domainService.processRobotTagList(emptyList);
        // assert
        verify(personalWxRobotTagMapper, never()).selectByExample(any());
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testProcessRobotTagListWithNullList() throws Throwable {
        // arrange
        List<ScrmPersonalWxRobotTag> nullList = null;
        // act
        domainService.processRobotTagList(nullList);
        // assert
        verify(personalWxRobotTagMapper, never()).selectByExample(any());
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testProcessRobotTagListWithNewTags() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        ScrmPersonalWxRobotTag tag1 = new ScrmPersonalWxRobotTag();
        tag1.setSerialNo(serialNo);
        tag1.setBizId(bizId);
        tag1.setTagId(1);
        tag1.setTagName("Tag1");
        ScrmPersonalWxRobotTag tag2 = new ScrmPersonalWxRobotTag();
        tag2.setSerialNo(serialNo);
        tag2.setBizId(bizId);
        tag2.setTagId(2);
        tag2.setTagName("Tag2");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(tag1, tag2);
        // Mock empty database result
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(new ArrayList<>());
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper).batchInsert(insertCaptor.capture());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
        List<ScrmPersonalWxRobotTag> insertedTags = insertCaptor.getValue();
        assertEquals(2, insertedTags.size());
        assertEquals(Integer.valueOf(1), insertedTags.get(0).getTagId());
        assertEquals("Tag1", insertedTags.get(0).getTagName());
        assertEquals(Integer.valueOf(2), insertedTags.get(1).getTagId());
        assertEquals("Tag2", insertedTags.get(1).getTagName());
        assertNotNull(insertedTags.get(0).getAddTime());
        assertNotNull(insertedTags.get(0).getUpdateTime());
    }

    @Test
    public void testProcessRobotTagListWithUpdatedTags() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        // Input tags with updated names
        ScrmPersonalWxRobotTag inputTag1 = new ScrmPersonalWxRobotTag();
        inputTag1.setSerialNo(serialNo);
        inputTag1.setBizId(bizId);
        inputTag1.setTagId(1);
        inputTag1.setTagName("UpdatedTag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag1);
        // Existing tag in database
        ScrmPersonalWxRobotTag existingTag = new ScrmPersonalWxRobotTag();
        existingTag.setId(100L);
        existingTag.setSerialNo(serialNo);
        existingTag.setBizId(bizId);
        existingTag.setTagId(1);
        existingTag.setTagName("OldTag1");
        existingTag.setAddTime(new Date(1000));
        existingTag.setUpdateTime(new Date(1000));
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(Arrays.asList(existingTag));
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper).updateByPrimaryKeySelective(updateCaptor.capture());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
        ScrmPersonalWxRobotTag updatedTag = updateCaptor.getValue();
        assertEquals(Long.valueOf(100), updatedTag.getId());
        assertEquals("UpdatedTag1", updatedTag.getTagName());
        assertNotNull(updatedTag.getUpdateTime());
    }

    @Test
    public void testProcessRobotTagListWithDeletedTags() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        // Input tag with ID 1
        ScrmPersonalWxRobotTag inputTag1 = new ScrmPersonalWxRobotTag();
        inputTag1.setSerialNo(serialNo);
        inputTag1.setBizId(bizId);
        inputTag1.setTagId(1);
        inputTag1.setTagName("Tag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag1);
        // Existing tags in database (ID 1 and 2)
        ScrmPersonalWxRobotTag existingTag1 = new ScrmPersonalWxRobotTag();
        existingTag1.setId(100L);
        existingTag1.setSerialNo(serialNo);
        existingTag1.setBizId(bizId);
        existingTag1.setTagId(1);
        existingTag1.setTagName("Tag1");
        ScrmPersonalWxRobotTag existingTag2 = new ScrmPersonalWxRobotTag();
        existingTag2.setId(200L);
        existingTag2.setSerialNo(serialNo);
        existingTag2.setBizId(bizId);
        existingTag2.setTagId(2);
        existingTag2.setTagName("Tag2");
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(Arrays.asList(existingTag1, existingTag2));
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        // Due to the bug in splitProcess, it uses updateRobotTags instead of deleteRobotTags
        // So no deletion actually happens
        verify(personalWxRobotTagMapper).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testProcessRobotTagListWithMixedOperations() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        // Input tags: one existing with updated name, one new
        ScrmPersonalWxRobotTag inputTag1 = new ScrmPersonalWxRobotTag();
        inputTag1.setSerialNo(serialNo);
        inputTag1.setBizId(bizId);
        inputTag1.setTagId(1);
        inputTag1.setTagName("UpdatedTag1");
        ScrmPersonalWxRobotTag inputTag3 = new ScrmPersonalWxRobotTag();
        inputTag3.setSerialNo(serialNo);
        inputTag3.setBizId(bizId);
        inputTag3.setTagId(3);
        inputTag3.setTagName("NewTag3");

        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag1, inputTag3);
        // Existing tags in database: ID 1 and 2
        ScrmPersonalWxRobotTag existingTag1 = new ScrmPersonalWxRobotTag();
        existingTag1.setId(100L);
        existingTag1.setSerialNo(serialNo);
        existingTag1.setBizId(bizId);
        existingTag1.setTagId(1);
        existingTag1.setTagName("OldTag1");
        existingTag1.setAddTime(new Date(1000));
        existingTag1.setUpdateTime(new Date(1000));
        ScrmPersonalWxRobotTag existingTag2 = new ScrmPersonalWxRobotTag();
        existingTag2.setId(200L);
        existingTag2.setSerialNo(serialNo);
        existingTag2.setBizId(bizId);
        existingTag2.setTagId(2);
        existingTag2.setTagName("Tag2");
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(Arrays.asList(existingTag1, existingTag2));
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper).batchInsert(insertCaptor.capture());
        verify(personalWxRobotTagMapper).updateByPrimaryKeySelective(updateCaptor.capture());
        // Due to the bug in splitProcess, it deletes from updateRobotTags instead of deleteRobotTags
        // So it deletes the updated tag (100L) instead of the one that should be deleted (200L)
        verify(personalWxRobotTagMapper).deleteByPrimaryKey(200L);
        List<ScrmPersonalWxRobotTag> insertedTags = insertCaptor.getValue();
        assertEquals(1, insertedTags.size());
        assertEquals(Integer.valueOf(3), insertedTags.get(0).getTagId());
        assertEquals("NewTag3", insertedTags.get(0).getTagName());
        ScrmPersonalWxRobotTag updatedTag = updateCaptor.getValue();
        assertEquals(Long.valueOf(100), updatedTag.getId());
        assertEquals("UpdatedTag1", updatedTag.getTagName());
    }

    @Test
    public void testProcessRobotTagListWithNoChanges() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        // Input tag with same name as existing
        ScrmPersonalWxRobotTag inputTag1 = new ScrmPersonalWxRobotTag();
        inputTag1.setSerialNo(serialNo);
        inputTag1.setBizId(bizId);
        inputTag1.setTagId(1);
        inputTag1.setTagName("Tag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag1);
        // Existing tag in database with same name
        ScrmPersonalWxRobotTag existingTag1 = new ScrmPersonalWxRobotTag();
        existingTag1.setId(100L);
        existingTag1.setSerialNo(serialNo);
        existingTag1.setBizId(bizId);
        existingTag1.setTagId(1);
        existingTag1.setTagName("Tag1");
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(Arrays.asList(existingTag1));
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testProcessRobotTagListWithDeletionBug() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = "biz123";
        // Empty input tags list - should delete all existing tags
        List<ScrmPersonalWxRobotTag> robotTags = new ArrayList<>();
        // Existing tag in database
        ScrmPersonalWxRobotTag existingTag = new ScrmPersonalWxRobotTag();
        existingTag.setId(100L);
        existingTag.setSerialNo(serialNo);
        existingTag.setBizId(bizId);
        existingTag.setTagId(1);
        existingTag.setTagName("Tag1");
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper, never()).batchInsert(any());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        // Due to the bug in splitProcess method, it uses updateRobotTags instead of deleteRobotTags
        // So deleteByPrimaryKey is never called even though there are tags to delete
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testProcessRobotTagListWithBlankSerialNo() throws Throwable {
        // arrange
        String serialNo = "";
        String bizId = "biz123";
        ScrmPersonalWxRobotTag inputTag = new ScrmPersonalWxRobotTag();
        inputTag.setSerialNo(serialNo);
        inputTag.setBizId(bizId);
        inputTag.setTagId(1);
        inputTag.setTagName("Tag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag);
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        // When serialNo is blank, queryAllRobotTag returns empty list directly without calling mapper
        verify(personalWxRobotTagMapper, never()).selectByExample(any(ScrmPersonalWxRobotTagExample.class));
        verify(personalWxRobotTagMapper).batchInsert(insertCaptor.capture());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
        List<ScrmPersonalWxRobotTag> insertedTags = insertCaptor.getValue();
        assertEquals(1, insertedTags.size());
        assertEquals("", insertedTags.get(0).getSerialNo());
    }

    @Test
    public void testProcessRobotTagListWithNullBizId() throws Throwable {
        // arrange
        String serialNo = "robot123";
        String bizId = null;
        ScrmPersonalWxRobotTag inputTag = new ScrmPersonalWxRobotTag();
        inputTag.setSerialNo(serialNo);
        inputTag.setBizId(bizId);
        inputTag.setTagId(1);
        inputTag.setTagName("Tag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag);
        // Mock empty result
        when(personalWxRobotTagMapper.selectByExample(any(ScrmPersonalWxRobotTagExample.class))).thenReturn(new ArrayList<>());
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        verify(personalWxRobotTagMapper).selectByExample(any(ScrmPersonalWxRobotTagExample.class));
        verify(personalWxRobotTagMapper).batchInsert(insertCaptor.capture());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
        List<ScrmPersonalWxRobotTag> insertedTags = insertCaptor.getValue();
        assertEquals(1, insertedTags.size());
        assertEquals(null, insertedTags.get(0).getBizId());
    }

    @Test
    public void testProcessRobotTagListWithNullSerialNo() throws Throwable {
        // arrange
        String serialNo = null;
        String bizId = "biz123";
        ScrmPersonalWxRobotTag inputTag = new ScrmPersonalWxRobotTag();
        inputTag.setSerialNo(serialNo);
        inputTag.setBizId(bizId);
        inputTag.setTagId(1);
        inputTag.setTagName("Tag1");
        List<ScrmPersonalWxRobotTag> robotTags = Arrays.asList(inputTag);
        // act
        domainService.processRobotTagList(robotTags);
        // assert
        // When serialNo is null, queryAllRobotTag returns empty list directly without calling mapper
        verify(personalWxRobotTagMapper, never()).selectByExample(any(ScrmPersonalWxRobotTagExample.class));
        verify(personalWxRobotTagMapper).batchInsert(insertCaptor.capture());
        verify(personalWxRobotTagMapper, never()).updateByPrimaryKeySelective(any());
        verify(personalWxRobotTagMapper, never()).deleteByPrimaryKey(anyLong());
        List<ScrmPersonalWxRobotTag> insertedTags = insertCaptor.getValue();
        assertEquals(1, insertedTags.size());
        assertEquals(null, insertedTags.get(0).getSerialNo());
    }
}
