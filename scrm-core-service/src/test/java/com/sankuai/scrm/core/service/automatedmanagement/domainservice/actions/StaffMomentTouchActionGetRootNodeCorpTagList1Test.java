package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentTaskResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGetMomentSendResultRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentSendResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentTaskResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.CustomerListDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.ResultDTO;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class StaffMomentTouchActionGetRootNodeCorpTagList1Test {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private WxGetMomentSendResultAcl wxGetMomentSendResultAcl;

    @Mock
    private WxGetMomentTaskResultAcl wxGetMomentTaskResultAcl;

    @Mock
    private InformationGatheringService informationGatheringService;

    /**
     * Helper method to invoke the private method using reflection.
     */
    private Set<String> invokeGetRootNodeCorpTagList(StaffMomentTouchAction action, List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList) throws Exception {
        Method method = StaffMomentTouchAction.class.getDeclaredMethod("getRootNodeCorpTagList", List.class);
        // Make the private method accessible
        method.setAccessible(true);
        return (Set<String>) method.invoke(action, strategyDetailDTOList);
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for empty input list.
     */
    @Test
    public void testGetRootNodeCorpTagListEmptyInput() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.emptyList();
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with no corporate tags.
     */
    @Test
    public void testGetRootNodeCorpTagListNoCorpTags() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO.getFilterFieldId()).thenReturn(ScrmUserTagEnum.IS_FRIEND.getTagId());
        when(detailDTO.getParam()).thenReturn(Arrays.asList("param1", "param2"));
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.singletonList(detailDTO);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with one corporate tag.
     */
    @Test
    public void testGetRootNodeCorpTagListOneCorpTag() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO.getParam()).thenReturn(Arrays.asList("corpTag1"));
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.singletonList(detailDTO);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("corpTag1"));
    }

    /**
     * Test case for input list with multiple corporate tags in different groups.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsDifferentGroups() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(Arrays.asList("corpTag2"));
        when(detailDTO2.getGroupId()).thenReturn(2);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains("corpTag1"));
        assertTrue(result.contains("corpTag2"));
    }

    /**
     * Test case for input list with multiple corporate tags in the same group.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsSameGroup() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(Arrays.asList("corpTag2"));
        when(detailDTO2.getGroupId()).thenReturn(1);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with corporate tags and other tags in the same group.
     */
    @Test
    public void testGetRootNodeCorpTagListMixedTagsSameGroup() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.IS_FRIEND.getTagId());
        when(detailDTO2.getParam()).thenReturn(Arrays.asList("param1", "param2"));
        when(detailDTO2.getGroupId()).thenReturn(1);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with corporate tags and empty param list.
     */
    @Test
    public void testGetRootNodeCorpTagListEmptyParamList() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO.getParam()).thenReturn(Collections.emptyList());
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.singletonList(detailDTO);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with null param list.
     */
    @Test
    public void testGetRootNodeCorpTagListNullParamList() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO.getParam()).thenReturn(null);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.singletonList(detailDTO);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for input list with multiple corporate tags in different groups, one with empty param list.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsDifferentGroupsOneEmptyParam() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(Collections.emptyList());
        when(detailDTO2.getGroupId()).thenReturn(2);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("corpTag1"));
    }

    /**
     * Test case for input list with multiple corporate tags in different groups, one with null param list.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsDifferentGroupsOneNullParam() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(null);
        when(detailDTO2.getGroupId()).thenReturn(2);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("corpTag1"));
    }

    /**
     * Test case for input list with multiple corporate tags in different groups, one with mixed tags.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsDifferentGroupsOneMixedTags() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(Arrays.asList("corpTag2"));
        when(detailDTO2.getGroupId()).thenReturn(2);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO3 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO3.getFilterFieldId()).thenReturn(ScrmUserTagEnum.IS_FRIEND.getTagId());
        when(detailDTO3.getParam()).thenReturn(Arrays.asList("param1", "param2"));
        when(detailDTO3.getGroupId()).thenReturn(2);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2, detailDTO3);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("corpTag1"));
    }

    /**
     * Test case for input list with multiple corporate tags in different groups, one with multiple corporate tags.
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleCorpTagsDifferentGroupsOneMultipleCorpTags() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO1 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO1.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO1.getParam()).thenReturn(Arrays.asList("corpTag1"));
        when(detailDTO1.getGroupId()).thenReturn(1);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO2 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO2.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO2.getParam()).thenReturn(Arrays.asList("corpTag2"));
        when(detailDTO2.getGroupId()).thenReturn(2);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO3 = mock(ScrmCrowdPackUpdateStrategyDetailDTO.class);
        when(detailDTO3.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        when(detailDTO3.getParam()).thenReturn(Arrays.asList("corpTag3"));
        when(detailDTO3.getGroupId()).thenReturn(2);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(detailDTO1, detailDTO2, detailDTO3);
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(action, strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("corpTag1"));
    }

    /**
     * Test case for when the update time is more than 24 hours ago.
     * The log should be marked as failed.
     */
    @Test
    public void testCheckStatusSubUpdateTimeMoreThan24Hours() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setUpdateTime(new Date(System.currentTimeMillis() - 25 * 3600 * 1000));
        // WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 5);
        invokeLogDO.setId(1L);
        List<Byte> statusList = new ArrayList<>();
        statusList.add((byte) 1);
        statusList.add((byte) 2);
        String appId = "appId";
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        when(wxInvokeLogDOMapper.selectByPrimaryKey(invokeLogDO.getId())).thenReturn(invokeLogDO);
        // act
        staffMomentTouchAction.checkStatusSub(invokeLogDO, statusList, appId, processOrchestrationId, processOrchestrationVersion);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKeySelective(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * Test case for when the update time is within the last 8 hours.
     * finalComplete should be set to false.
     */
    @Test
    public void testCheckStatusSubUpdateTimeWithin8Hours() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setUpdateTime(new Date(System.currentTimeMillis() - 7 * 3600 * 1000));
        // WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 5);
        invokeLogDO.setId(1L);
        List<Byte> statusList = new ArrayList<>();
        statusList.add((byte) 1);
        statusList.add((byte) 2);
        String appId = "appId";
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        when(wxInvokeLogDOMapper.selectByPrimaryKey(invokeLogDO.getId())).thenReturn(invokeLogDO);
        // act
        staffMomentTouchAction.checkStatusSub(invokeLogDO, statusList, appId, processOrchestrationId, processOrchestrationVersion);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test case for when the status is WAIT_FOR_CREATE_RESULT.
     * The method should check the moment task result and update the status.
     */
    @Test
    public void testCheckStatusSubStatusWaitForCreateResult() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // WAIT_FOR_CREATE_RESULT
        invokeLogDO.setStatus((byte) 2);
        invokeLogDO.setUpdateTime(new Date(System.currentTimeMillis() - 9 * 3600 * 1000));
        invokeLogDO.setId(1L);
        invokeLogDO.setJobid("job123");
        List<Byte> statusList = new ArrayList<>();
        statusList.add((byte) 1);
        statusList.add((byte) 2);
        String appId = "appId";
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        WxGetMomentTaskResultResponse response = new WxGetMomentTaskResultResponse();
        response.setErrcode(0);
        response.setStatus(0);
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setMomentId("moment123");
        response.setResult(resultDTO);
        when(wxInvokeLogDOMapper.selectByPrimaryKey(invokeLogDO.getId())).thenReturn(invokeLogDO);
        when(wxGetMomentTaskResultAcl.getMomentTaskResult(any(), eq(appId))).thenReturn(response);
        // act
        staffMomentTouchAction.checkStatusSub(invokeLogDO, statusList, appId, processOrchestrationId, processOrchestrationVersion);
        // assert
        verify(wxGetMomentTaskResultAcl, times(1)).getMomentTaskResult(any(), eq(appId));
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKeySelective(invokeLogDO);
    }
}
