package com.sankuai.scrm.core.service.user.domain.score.strategy;

import com.sankuai.dz.srcm.user.score.CalcScoreContext;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/4/3 14:45
 * @Version v1.0.0
 */
public interface CalcScoreStrategy {

    int calc(CalcScoreContext<ScrmUserGrowthYimeiLiveUserTag> context);
    String supportType();
}
