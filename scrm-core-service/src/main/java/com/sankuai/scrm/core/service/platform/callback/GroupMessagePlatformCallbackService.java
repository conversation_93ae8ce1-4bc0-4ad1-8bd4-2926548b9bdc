package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.platform.anno.NTypePlatformCallbackMappingAnno;


/**
 * @Description
 * <AUTHOR>
 * @Create On 2023/11/7 17:21
 * @Version v1.0.0
 */
public interface GroupMessagePlatformCallbackService extends PlatformCallbackService {

    /**
     * 群聊消息发送结果回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 5002, desc = "群聊消息发送结果回调接口")
    RemoteResponse do5002(String strContext);

    /**
     * 机器人撤回消息结果回调接口
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 5006, desc = "机器人撤回消息结果回调接口")
    RemoteResponse do5006(String strContext);

    /**
     * 群内实时消息回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 5003, desc = "群内实时消息回调")
    RemoteResponse do5003(String strContext);

    /**
     * 设置群消息免打扰接口回调
     *
     * @param strContext
     */
    @NTypePlatformCallbackMappingAnno(nType = 4530, desc = "设置群消息免打扰接口回调")
    RemoteResponse do4530(String strContext);

}

