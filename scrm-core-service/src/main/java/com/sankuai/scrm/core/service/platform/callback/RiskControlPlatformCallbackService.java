package com.sankuai.scrm.core.service.platform.callback;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.pchat.anno.NTypeCallbackMapping;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/3/7 14:52
 * @Version v1.0.0
 */
public interface RiskControlCbService extends PlatformCallbackService {
    /**
     *
     * @param strContext
     * @return
     */
    @NTypeCallbackMapping(nType = 9201, desc = "敏感词限制回调接口")
    RemoteResponse do9201(String strContext);

    /**
     *
     * @param strContext
     * @return
     */
    @NTypeCallbackMapping(nType = 1039, desc = "机器人被限制回调接口")
    RemoteResponse do1039(String strContext);
}

