package com.sankuai.dz.srcm.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserConsumptionAbilityResult implements Serializable {
    private String userPk;
    private String appId;
    private String valueSegment;
    private String frequencySegment;
    private String stabilitySegment;
}
