package com.sankuai.scrm.core.service.message.push.service.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushDetail;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushDomainService;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushTaskDomainService;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushSceneType;
import com.sankuai.scrm.core.service.message.push.mq.event.MsgPushTaskEvent;
import com.sankuai.scrm.core.service.message.push.mq.producer.MsgPushRealTimeTaskProducer;
import com.sankuai.scrm.core.service.message.push.mq.producer.MsgPushTaskProducer;
import com.sankuai.scrm.core.service.message.push.request.MsgPushCheckReceiverRequest;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.request.MsgPushResultRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MsgUnifiedPushServiceImpl implements MsgUnifiedPushService {

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Autowired
    private MsgPushTaskDomainService msgPushTaskDomainService;

    @Autowired
    private MsgPushDomainService msgPushDomainService;

    @Autowired
    private MsgPushTaskProducer producer;

    @Autowired
    private MsgPushRealTimeTaskProducer realTimeTaskProducer;

    @Override
    public MsgPushResponse<Long> saveMsgPushTask(MsgPushRequest request) {
        log.info("MsgUnifiedPushService.push request={}", request);
        String checkResult = checkParam(request);
        if (StringUtils.isNotEmpty(checkResult)) {
            return MsgPushResponse.fail(checkResult);
        }
        try {
            Long taskId = msgPushTaskDomainService.saveMsgPushTask(request);
            if (taskId != null) {
                sendTaskEvent(taskId, request.getSceneType());
                return MsgPushResponse.success(taskId);
            }
            return MsgPushResponse.fail("消息推送任务创建失败");
        } catch (Exception e) {
            log.error("MsgUnifiedPushService.push has exception, request={}", request, e);
            return MsgPushResponse.fail("消息推送任务创建失败:" + e.getMessage());
        }
    }

    @Override
    public MsgPushResponse<List<MsgTaskDetailResultDTO>> queryTaskResult(MsgPushResultRequest request) {
        if (request == null || request.getOffset() < 0 || request.getRows() <= 0) {
            return MsgPushResponse.fail("参数错误");
        }
        List<MsgPushDetail> detailList = msgPushTaskDomainService.queryDetailList(request.getTaskId(),
                request.getOffset(), request.getRows());
        List<MsgTaskDetailResultDTO> resultDTOList = detailList.stream()
                .map(detail -> msgPushDomainService.buildMsgTaskDetailResultDTO(detail))
                .collect(Collectors.toList());
        return MsgPushResponse.success(resultDTOList);
    }

    @Override
    public MsgPushResponse<List<MsgTaskDetailResultDTO>> queryTaskResultFromMaster(MsgPushResultRequest request) {
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            return queryTaskResult(request);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    @Override
    public MsgPushResponse<Long> queryReceiverTaskId(MsgPushCheckReceiverRequest request) {
        if (request == null || request.getReceiver() == null || CollectionUtils.isEmpty(request.getTaskIds())) {
            return MsgPushResponse.fail("参数错误");
        }
        Long taskId = msgPushTaskDomainService.queryTaskIdByReceiverAmongTaskIds(request.getTaskIds(), request.getReceiver());
        return MsgPushResponse.success(taskId);
    }

    private String checkParam(MsgPushRequest request) {
        if (request == null) {
            return "Request为空";
        }
        if (StringUtils.isEmpty(request.getAppId())) {
            return "AppId为空";
        }
        String corpId = appConfigRepository.getCorpIdByAppId(request.getAppId());
        if (StringUtils.isEmpty(corpId)) {
            return "AppId无效";
        }
        if (request.getSceneType() == null) {
            return "推送场景为空";
        }
        if (request.getChatType() == null) {
            return "推送的聊天类型为空";
        }
        if (CollectionUtils.isEmpty(request.getReceiverIdList())) {
            return "接收者为空";
        }
        if (CollectionUtils.isEmpty(request.getMsgPushContentDTO())) {
            return "推送内容为空";
        }
        return null;
    }

    private void sendTaskEvent(Long taskId, MsgPushSceneType sceneType) {
        if (taskId == null) {
            return;
        }
        MsgPushTaskEvent event = new MsgPushTaskEvent();
        event.setTaskId(taskId);

        //判断消息推送场景送入不同topic
        if (sceneType == MsgPushSceneType.REALTIME_SCENE) {
            realTimeTaskProducer.sendMsg(JsonUtils.toStr(event));
        } else {
            producer.sendMsg(JsonUtils.toStr(event));
        }
    }

}
