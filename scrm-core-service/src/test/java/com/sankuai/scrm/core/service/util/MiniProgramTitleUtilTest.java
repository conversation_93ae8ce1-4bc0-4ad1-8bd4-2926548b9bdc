package com.sankuai.scrm.core.service.util;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.gson.JsonSyntaxException;
import com.sankuai.scrm.core.service.message.push.dto.MiniProgramTitleDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

class MiniProgramTitleUtilTest {

    /**
     * 测试当Lion返回空字符串时，方法应返回null
     */
    @Test
    public void testGetTitlesWhenLionReturnsEmptyString() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            // arrange
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn("");
            // act
            Map<String, MiniProgramTitleDTO> result = MiniProgramTitleUtil.getTitles();
            // assert
            assertNull(result);
        }
    }

    /**
     * 测试当Lion返回无效JSON字符串时，方法应抛出JsonSyntaxException
     */
    @Test
    public void testGetTitlesWhenLionReturnsInvalidJson() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            // arrange
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn("invalid json");
            // act & assert
            assertThrows(JsonSyntaxException.class, () -> MiniProgramTitleUtil.getTitles());
        }
    }

    /**
     * 测试当Lion返回有效JSON字符串时，方法应正确解析并返回Map
     */
    @Test
    public void testGetTitlesWhenLionReturnsValidJson() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            // arrange
            String validJson = "[{\"title\":\"test1\",\"originAppId\":\"origin1\",\"appId\":\"app1\"}," + "{\"title\":\"test2\",\"originAppId\":\"origin2\",\"appId\":\"app2\"}]";
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn(validJson);
            // act
            Map<String, MiniProgramTitleDTO> result = MiniProgramTitleUtil.getTitles();
            // assert
            assertNotNull(result);
            assertEquals(2, result.size());
            assertTrue(result.containsKey("app1"));
            assertTrue(result.containsKey("app2"));
            assertEquals("test1", result.get("app1").getTitle());
            assertEquals("origin1", result.get("app1").getOriginAppId());
            assertEquals("test2", result.get("app2").getTitle());
            assertEquals("origin2", result.get("app2").getOriginAppId());
        }
    }

    /**
     * 测试当Environment.getAppName()返回null时，方法应返回null
     */
    @Test
    public void testGetTitlesWhenAppNameIsNull() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            // arrange
            mockedEnvironment.when(Environment::getAppName).thenReturn(null);
            mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn(null);
            // act
            Map<String, MiniProgramTitleDTO> result = MiniProgramTitleUtil.getTitles();
            // assert
            assertNull(result);
        }
    }

    /**
     * 测试当JSON字符串包含空appId时，该条目仍会被放入结果Map中
     */
    @Test
    public void testGetTitlesWhenJsonContainsEmptyAppId() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            // arrange
            String jsonWithEmptyAppId = "[{\"title\":\"test1\",\"originAppId\":\"origin1\",\"appId\":\"\"}," + "{\"title\":\"test2\",\"originAppId\":\"origin2\",\"appId\":\"app2\"}]";
            mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
            mockedLion.when(() -> Lion.getString(anyString(), anyString())).thenReturn(jsonWithEmptyAppId);
            // act
            Map<String, MiniProgramTitleDTO> result = MiniProgramTitleUtil.getTitles();
            // assert
            assertNotNull(result);
            assertEquals(2, result.size());
            assertTrue(result.containsKey(""));
            assertTrue(result.containsKey("app2"));
            assertEquals("test1", result.get("").getTitle());
            assertEquals("origin1", result.get("").getOriginAppId());
            assertEquals("test2", result.get("app2").getTitle());
            assertEquals("origin2", result.get("app2").getOriginAppId());
        }
    }
}