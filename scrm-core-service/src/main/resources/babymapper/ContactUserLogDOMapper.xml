<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.data.statistics.dal.entity.ContactUserLogDO">
        <id column="Id" jdbcType="BIGINT" property="id" />
        <result column="ExternalUserId" jdbcType="VARCHAR" property="externalUserId" />
        <result column="UnionId" jdbcType="VARCHAR" property="unionId" />
        <result column="CorpId" jdbcType="VARCHAR" property="corpId" />
        <result column="OrgId" jdbcType="BIGINT" property="orgId" />
        <result column="StaffId" jdbcType="VARCHAR" property="staffId" />
        <result column="Source" jdbcType="VARCHAR" property="source" />
        <result column="ActionType" jdbcType="INTEGER" property="actionType" />
        <result column="ActionTime" jdbcType="TIMESTAMP" property="actionTime" />
    </resultMap>
    <select id="countContactUserLogForDay"
            resultType="com.sankuai.dz.srcm.data.statistics.dto.FriendStatisticsIndicatorDTO">
             select
                (select count(1) as total_1 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{startDate} and #{endDate}) as currentFriendTimes,
                (select count(1) as total_3 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{previousDay} and #{startDate}) as preDayCurrentFriendTimes,
                (select count(1) as total_5 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{startDayLastWeek} and #{endDayLastWeek}) as lastWeekFriendTimes,
                (select count(1) as total_5 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{previousDayLastWeek} and #{startDayLastWeek}) as preDayLastWeekFriendTimes,
                (select count(1) as total_5 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{startDayOfLastMonth} and #{endDayOfLastMonth}) as lastMonthFriendTimes,
                (select count(1) as total_5 from Baby_OperatorHelper_ContactUserLog  where CorpId = #{corpId} and ActionType = 1 and ActionTime between #{previousDayLastMonth} and #{startDayOfLastMonth}) as preDayLastMonthFriendTimes
    </select>
    <select id="countContactUserLog"
            resultType="com.sankuai.dz.srcm.data.statistics.dto.FriendStatisticsIndicatorDTO">
        select
            (select count(1) as total_1 from Baby_OperatorHelper_ContactUserLog where CorpId = #{corpId} and ActionType = #{status} and ActionTime between #{startDate} and #{endDate}) as currentPeriodFriendTimes,
            (select count(1) as total_2 from Baby_OperatorHelper_ContactUserLog where CorpId = #{corpId} and ActionType = #{status} and ActionTime between #{lastPeriodStart} and #{lastPeriodEnd} and #{lastPeriodStart} is not null and #{lastPeriodEnd} is not null) as lastPeriodFriendTimes,
            (select count(1) as total_3 from Baby_OperatorHelper_ContactUserLog where CorpId = #{corpId} and ActionType = #{status} and ActionTime between #{beforeLastPeriodStart} and #{beforeLastPeriodEnd} and #{beforeLastPeriodStart} is not null and #{beforeLastPeriodEnd} is not null) as beforeLastPeriodFriendTimes
    </select>


    <select id="countTotalFriendFrequency" resultType="java.lang.Long">
        select count(1) as total_1 from Baby_OperatorHelper_ContactUserLog where CorpId = #{corpId} and ActionType = 1 and ActionTime &lt;= #{endDate};
    </select>

    <select id="selectByUnionId" resultType="com.sankuai.scrm.core.service.data.statistics.dal.entity.ContactUserLogDO">
        SELECT
            UnionId,
            ActionType,
            ActionTime
        FROM
            Baby_OperatorHelper_ContactUserLog
        WHERE
            ActionTime >= #{time}
        AND
            UnionId = #{unionId}
    </select>

</mapper>