package com.sankuai.scrm.core.service.group.template.dao.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GroupBatchCreateTaskDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public GroupBatchCreateTaskDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public GroupBatchCreateTaskDetailExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public GroupBatchCreateTaskDetailExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public GroupBatchCreateTaskDetailExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNull() {
            addCriterion("group_name is null");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNotNull() {
            addCriterion("group_name is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNameEqualTo(String value) {
            addCriterion("group_name =", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotEqualTo(String value) {
            addCriterion("group_name <>", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThan(String value) {
            addCriterion("group_name >", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("group_name >=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThan(String value) {
            addCriterion("group_name <", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThanOrEqualTo(String value) {
            addCriterion("group_name <=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLike(String value) {
            addCriterion("group_name like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotLike(String value) {
            addCriterion("group_name not like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameIn(List<String> values) {
            addCriterion("group_name in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotIn(List<String> values) {
            addCriterion("group_name not in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameBetween(String value1, String value2) {
            addCriterion("group_name between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotBetween(String value1, String value2) {
            addCriterion("group_name not between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(String value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(String value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(String value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(String value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(String value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLike(String value) {
            addCriterion("group_id like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotLike(String value) {
            addCriterion("group_id not like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<String> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<String> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(String value1, String value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(String value1, String value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNull() {
            addCriterion("owner_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNotNull() {
            addCriterion("owner_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdEqualTo(String value) {
            addCriterion("owner_id =", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotEqualTo(String value) {
            addCriterion("owner_id <>", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThan(String value) {
            addCriterion("owner_id >", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_id >=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThan(String value) {
            addCriterion("owner_id <", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanOrEqualTo(String value) {
            addCriterion("owner_id <=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLike(String value) {
            addCriterion("owner_id like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotLike(String value) {
            addCriterion("owner_id not like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIn(List<String> values) {
            addCriterion("owner_id in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotIn(List<String> values) {
            addCriterion("owner_id not in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdBetween(String value1, String value2) {
            addCriterion("owner_id between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotBetween(String value1, String value2) {
            addCriterion("owner_id not between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNull() {
            addCriterion("owner_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNotNull() {
            addCriterion("owner_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualTo(String value) {
            addCriterion("owner_name =", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualTo(String value) {
            addCriterion("owner_name <>", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThan(String value) {
            addCriterion("owner_name >", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_name >=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThan(String value) {
            addCriterion("owner_name <", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualTo(String value) {
            addCriterion("owner_name <=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLike(String value) {
            addCriterion("owner_name like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLike(String value) {
            addCriterion("owner_name not like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIn(List<String> values) {
            addCriterion("owner_name in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotIn(List<String> values) {
            addCriterion("owner_name not in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetween(String value1, String value2) {
            addCriterion("owner_name between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetween(String value1, String value2) {
            addCriterion("owner_name not between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andAdminListIsNull() {
            addCriterion("admin_list is null");
            return (Criteria) this;
        }

        public Criteria andAdminListIsNotNull() {
            addCriterion("admin_list is not null");
            return (Criteria) this;
        }

        public Criteria andAdminListEqualTo(String value) {
            addCriterion("admin_list =", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListNotEqualTo(String value) {
            addCriterion("admin_list <>", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListGreaterThan(String value) {
            addCriterion("admin_list >", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListGreaterThanOrEqualTo(String value) {
            addCriterion("admin_list >=", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListLessThan(String value) {
            addCriterion("admin_list <", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListLessThanOrEqualTo(String value) {
            addCriterion("admin_list <=", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListLike(String value) {
            addCriterion("admin_list like", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListNotLike(String value) {
            addCriterion("admin_list not like", value, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListIn(List<String> values) {
            addCriterion("admin_list in", values, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListNotIn(List<String> values) {
            addCriterion("admin_list not in", values, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListBetween(String value1, String value2) {
            addCriterion("admin_list between", value1, value2, "adminList");
            return (Criteria) this;
        }

        public Criteria andAdminListNotBetween(String value1, String value2) {
            addCriterion("admin_list not between", value1, value2, "adminList");
            return (Criteria) this;
        }

        public Criteria andMemberListIsNull() {
            addCriterion("member_list is null");
            return (Criteria) this;
        }

        public Criteria andMemberListIsNotNull() {
            addCriterion("member_list is not null");
            return (Criteria) this;
        }

        public Criteria andMemberListEqualTo(String value) {
            addCriterion("member_list =", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListNotEqualTo(String value) {
            addCriterion("member_list <>", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListGreaterThan(String value) {
            addCriterion("member_list >", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListGreaterThanOrEqualTo(String value) {
            addCriterion("member_list >=", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListLessThan(String value) {
            addCriterion("member_list <", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListLessThanOrEqualTo(String value) {
            addCriterion("member_list <=", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListLike(String value) {
            addCriterion("member_list like", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListNotLike(String value) {
            addCriterion("member_list not like", value, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListIn(List<String> values) {
            addCriterion("member_list in", values, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListNotIn(List<String> values) {
            addCriterion("member_list not in", values, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListBetween(String value1, String value2) {
            addCriterion("member_list between", value1, value2, "memberList");
            return (Criteria) this;
        }

        public Criteria andMemberListNotBetween(String value1, String value2) {
            addCriterion("member_list not between", value1, value2, "memberList");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgIsNull() {
            addCriterion("notice_msg is null");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgIsNotNull() {
            addCriterion("notice_msg is not null");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgEqualTo(String value) {
            addCriterion("notice_msg =", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgNotEqualTo(String value) {
            addCriterion("notice_msg <>", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgGreaterThan(String value) {
            addCriterion("notice_msg >", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgGreaterThanOrEqualTo(String value) {
            addCriterion("notice_msg >=", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgLessThan(String value) {
            addCriterion("notice_msg <", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgLessThanOrEqualTo(String value) {
            addCriterion("notice_msg <=", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgLike(String value) {
            addCriterion("notice_msg like", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgNotLike(String value) {
            addCriterion("notice_msg not like", value, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgIn(List<String> values) {
            addCriterion("notice_msg in", values, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgNotIn(List<String> values) {
            addCriterion("notice_msg not in", values, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgBetween(String value1, String value2) {
            addCriterion("notice_msg between", value1, value2, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andNoticeMsgNotBetween(String value1, String value2) {
            addCriterion("notice_msg not between", value1, value2, "noticeMsg");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeIsNull() {
            addCriterion("welcome_code is null");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeIsNotNull() {
            addCriterion("welcome_code is not null");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeEqualTo(Integer value) {
            addCriterion("welcome_code =", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeNotEqualTo(Integer value) {
            addCriterion("welcome_code <>", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeGreaterThan(Integer value) {
            addCriterion("welcome_code >", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("welcome_code >=", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeLessThan(Integer value) {
            addCriterion("welcome_code <", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeLessThanOrEqualTo(Integer value) {
            addCriterion("welcome_code <=", value, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeIn(List<Integer> values) {
            addCriterion("welcome_code in", values, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeNotIn(List<Integer> values) {
            addCriterion("welcome_code not in", values, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeBetween(Integer value1, Integer value2) {
            addCriterion("welcome_code between", value1, value2, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andWelcomeCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("welcome_code not between", value1, value2, "welcomeCode");
            return (Criteria) this;
        }

        public Criteria andTagListIsNull() {
            addCriterion("tag_list is null");
            return (Criteria) this;
        }

        public Criteria andTagListIsNotNull() {
            addCriterion("tag_list is not null");
            return (Criteria) this;
        }

        public Criteria andTagListEqualTo(String value) {
            addCriterion("tag_list =", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListNotEqualTo(String value) {
            addCriterion("tag_list <>", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListGreaterThan(String value) {
            addCriterion("tag_list >", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListGreaterThanOrEqualTo(String value) {
            addCriterion("tag_list >=", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListLessThan(String value) {
            addCriterion("tag_list <", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListLessThanOrEqualTo(String value) {
            addCriterion("tag_list <=", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListLike(String value) {
            addCriterion("tag_list like", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListNotLike(String value) {
            addCriterion("tag_list not like", value, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListIn(List<String> values) {
            addCriterion("tag_list in", values, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListNotIn(List<String> values) {
            addCriterion("tag_list not in", values, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListBetween(String value1, String value2) {
            addCriterion("tag_list between", value1, value2, "tagList");
            return (Criteria) this;
        }

        public Criteria andTagListNotBetween(String value1, String value2) {
            addCriterion("tag_list not between", value1, value2, "tagList");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdIsNull() {
            addCriterion("live_group_id is null");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdIsNotNull() {
            addCriterion("live_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdEqualTo(Long value) {
            addCriterion("live_group_id =", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdNotEqualTo(Long value) {
            addCriterion("live_group_id <>", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdGreaterThan(Long value) {
            addCriterion("live_group_id >", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("live_group_id >=", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdLessThan(Long value) {
            addCriterion("live_group_id <", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("live_group_id <=", value, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdIn(List<Long> values) {
            addCriterion("live_group_id in", values, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdNotIn(List<Long> values) {
            addCriterion("live_group_id not in", values, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdBetween(Long value1, Long value2) {
            addCriterion("live_group_id between", value1, value2, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andLiveGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("live_group_id not between", value1, value2, "liveGroupId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andReceiptIsNull() {
            addCriterion("receipt is null");
            return (Criteria) this;
        }

        public Criteria andReceiptIsNotNull() {
            addCriterion("receipt is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptEqualTo(Long value) {
            addCriterion("receipt =", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptNotEqualTo(Long value) {
            addCriterion("receipt <>", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptGreaterThan(Long value) {
            addCriterion("receipt >", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptGreaterThanOrEqualTo(Long value) {
            addCriterion("receipt >=", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptLessThan(Long value) {
            addCriterion("receipt <", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptLessThanOrEqualTo(Long value) {
            addCriterion("receipt <=", value, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptIn(List<Long> values) {
            addCriterion("receipt in", values, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptNotIn(List<Long> values) {
            addCriterion("receipt not in", values, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptBetween(Long value1, Long value2) {
            addCriterion("receipt between", value1, value2, "receipt");
            return (Criteria) this;
        }

        public Criteria andReceiptNotBetween(Long value1, Long value2) {
            addCriterion("receipt not between", value1, value2, "receipt");
            return (Criteria) this;
        }

        public Criteria andDsStatusIsNull() {
            addCriterion("ds_status is null");
            return (Criteria) this;
        }

        public Criteria andDsStatusIsNotNull() {
            addCriterion("ds_status is not null");
            return (Criteria) this;
        }

        public Criteria andDsStatusEqualTo(Integer value) {
            addCriterion("ds_status =", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusNotEqualTo(Integer value) {
            addCriterion("ds_status <>", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusGreaterThan(Integer value) {
            addCriterion("ds_status >", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("ds_status >=", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusLessThan(Integer value) {
            addCriterion("ds_status <", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusLessThanOrEqualTo(Integer value) {
            addCriterion("ds_status <=", value, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusIn(List<Integer> values) {
            addCriterion("ds_status in", values, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusNotIn(List<Integer> values) {
            addCriterion("ds_status not in", values, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusBetween(Integer value1, Integer value2) {
            addCriterion("ds_status between", value1, value2, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andDsStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("ds_status not between", value1, value2, "dsStatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}