package com.sankuai.scrm.core.service.pchat.thirdparty.consultant;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.UserTaskSummaryDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GtvLiveConsultantSummaryServiceTest {

    @InjectMocks
    private GtvLiveConsultantSummaryService gtvLiveConsultantSummaryService;

    @Mock
    private PrivateLiveConsultantSummaryService privateLiveConsultantSummaryService;

    @Mock
    private PrivateLiveConsultantSummaryService newPrivateLiveConsultantSummaryService;

    @BeforeEach
    public void setUp() {
        // Set the useNewAppkey field using reflection
        ReflectionTestUtils.setField(gtvLiveConsultantSummaryService, "useNewAppkey", false);
    }

    /**
     * Test when liveId is empty or userIdList is empty
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithEmptyLiveIdOrUserIdList() throws Throwable {
        // arrange
        // act
        List<UserTaskSummaryDTO> result1 = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("", Arrays.asList(1L, 2L));
        List<UserTaskSummaryDTO> result2 = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", new ArrayList<>());
        List<UserTaskSummaryDTO> result3 = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(null, Arrays.asList(1L, 2L));
        // assert
        assertEquals(new ArrayList<>(), result1);
        assertEquals(new ArrayList<>(), result2);
        assertEquals(new ArrayList<>(), result3);
    }

    /**
     * Test when RemoteResponse is null
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithNullRemoteResponse() throws Throwable {
        // arrange
        when(privateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(anyString(), anyList())).thenReturn(null);
        // act
        List<UserTaskSummaryDTO> result = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", Arrays.asList(1L, 2L));
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test when RemoteResponse is unsuccessful
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithUnsuccessfulRemoteResponse() throws Throwable {
        // arrange
        RemoteResponse<List<UserTaskSummaryDTO>> remoteResponse = RemoteResponse.fail("error");
        when(privateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(anyString(), anyList())).thenReturn(remoteResponse);
        // act
        List<UserTaskSummaryDTO> result = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", Arrays.asList(1L, 2L));
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test when RemoteResponse is successful
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithSuccessfulRemoteResponse() throws Throwable {
        // arrange
        UserTaskSummaryDTO dto = new UserTaskSummaryDTO();
        List<UserTaskSummaryDTO> dtoList = Collections.singletonList(dto);
        RemoteResponse<List<UserTaskSummaryDTO>> remoteResponse = RemoteResponse.success(dtoList);
        when(privateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(anyString(), anyList())).thenReturn(remoteResponse);
        // act
        List<UserTaskSummaryDTO> result = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", Arrays.asList(1L, 2L));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(dto, result.get(0));
    }

    /**
     * Test when useNewAppkey is true and RemoteResponse is successful
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithNewAppkeyAndSuccessfulRemoteResponse() throws Throwable {
        // arrange
        ReflectionTestUtils.setField(gtvLiveConsultantSummaryService, "useNewAppkey", true);
        UserTaskSummaryDTO dto = new UserTaskSummaryDTO();
        List<UserTaskSummaryDTO> dtoList = Collections.singletonList(dto);
        RemoteResponse<List<UserTaskSummaryDTO>> remoteResponse = RemoteResponse.success(dtoList);
        when(newPrivateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(anyString(), anyList())).thenReturn(remoteResponse);
        // act
        List<UserTaskSummaryDTO> result = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", Arrays.asList(1L, 2L));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(dto, result.get(0));
    }

    /**
     * Test when useNewAppkey is true and RemoteResponse is unsuccessful
     */
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListWithNewAppkeyAndUnsuccessfulRemoteResponse() throws Throwable {
        // arrange
        ReflectionTestUtils.setField(gtvLiveConsultantSummaryService, "useNewAppkey", true);
        RemoteResponse<List<UserTaskSummaryDTO>> remoteResponse = RemoteResponse.fail("error");
        when(newPrivateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(anyString(), anyList())).thenReturn(remoteResponse);
        // act
        List<UserTaskSummaryDTO> result = gtvLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList("liveId", Arrays.asList(1L, 2L));
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }
}
