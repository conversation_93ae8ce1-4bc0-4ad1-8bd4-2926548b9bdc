package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: scrm_a_m_process_orchestration_branch_exe_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmProcessOrchestrationBranchExeLogDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: process_orchestration_id
     *   说明: 流程编排主键
     */
    private Long processOrchestrationId;

    /**
     *   字段: process_orchestration_version
     *   说明: 流程编排版本
     */
    private String processOrchestrationVersion;

    /**
     *   字段: node_id
     *   说明: 本节点id
     */
    private Long nodeId;

    /**
     *   字段: pre_node_id
     *   说明: 上级节点id
     */
    private Long preNodeId;

    /**
     *   字段: node_type
     *   说明: 节点类型 (0:人群包更新/1:条件/2:动作)
     */
    private Integer nodeType;

    /**
     *   字段: external_user_wx_union_id
     *   说明: 用户unionid
     */
    private String externalUserWxUnionId;

    /**
     *   字段: external_user_id
     *   说明: 外部联系人id
     */
    private String externalUserId;

    /**
     *   字段: is_place_order
     *   说明: 是否下单 0否 1是
     */
    private Byte isPlaceOrder;

    /**
     *   字段: is_browse
     *   说明: 是否浏览 0否 1是
     */
    private Byte isBrowse;

    /**
     *   字段: is_quit_group
     *   说明: 是否退群 0否 1是
     */
    private Byte isQuitGroup;

    /**
     *   字段: is_delete_friend
     *   说明: 是否删好友 0否 1是
     */
    private Byte isDeleteFriend;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}