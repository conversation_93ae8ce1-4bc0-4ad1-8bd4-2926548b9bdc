package com.sankuai.dz.srcm.flowV2.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExtraConfigDTO implements Serializable {

    private List<ShopDisplayDTO> shopDisplayConfig;

    private String sourceChannel;

    private Date startTime;

    private Date endTime;

}
