package com.sankuai.scrm.core.service.aigc.service.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.aigc.service.dto.UserActionScoreConfigDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class UserActionScoreConfigInit1Test {

    private UserActionScoreConfig userActionScoreConfig;

    private MockedStatic<Lion> mockedLion;

    private MockedStatic<Environment> mockedEnvironment;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    private ConfigRepository mockConfigRepository;

    private static final String LION_KEY = "user.score.rules.config";

    @BeforeEach
    void setUp() throws Exception {
        mockConfigRepository = mock(ConfigRepository.class);
        mockedLion = mockStatic(Lion.class);
        mockedEnvironment = mockStatic(Environment.class);
        mockedJsonUtils = mockStatic(JsonUtils.class);
        userActionScoreConfig = new UserActionScoreConfig();
        // Inject mock configRepository via reflection
        Field configRepositoryField = UserActionScoreConfig.class.getDeclaredField("configRepository");
        configRepositoryField.setAccessible(true);
        configRepositoryField.set(userActionScoreConfig, mockConfigRepository);
    }

    @AfterEach
    void tearDown() {
        mockedLion.close();
        mockedEnvironment.close();
        mockedJsonUtils.close();
    }

    private void invokeInitMethod() throws Exception {
        Method initMethod = UserActionScoreConfig.class.getDeclaredMethod("init");
        initMethod.setAccessible(true);
        initMethod.invoke(userActionScoreConfig);
    }

    private UserActionScoreConfigDTO getConfigDTO() throws Exception {
        Field configField = UserActionScoreConfig.class.getDeclaredField("userActionScoreConfigDTO");
        configField.setAccessible(true);
        return (UserActionScoreConfigDTO) configField.get(userActionScoreConfig);
    }

    /**
     * Test normal initialization with valid config DTO
     */
    @Test
    void testInitNormalCase() throws Throwable {
        // arrange
        String appName = "test-app";
        UserActionScoreConfigDTO expectedConfig = new UserActionScoreConfigDTO();
        mockedEnvironment.when(Environment::getAppName).thenReturn(appName);
        mockedLion.when(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class)).thenReturn(expectedConfig);
        mockedJsonUtils.when(() -> JsonUtils.toStr(expectedConfig)).thenReturn("config-json");
        // act
        invokeInitMethod();
        // assert
        mockedEnvironment.verify(Environment::getAppName);
        mockedLion.verify(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class));
        mockedJsonUtils.verify(() -> JsonUtils.toStr(expectedConfig));
        verify(mockConfigRepository).addConfigListener(LION_KEY, userActionScoreConfig);
        assertEquals(expectedConfig, getConfigDTO());
    }

    /**
     * Test when app name is null
     */
    @Test
    void testInitWhenAppNameIsNull() throws Throwable {
        // arrange
        mockedEnvironment.when(Environment::getAppName).thenReturn(null);
        mockedLion.when(() -> Lion.getBean(null, LION_KEY, UserActionScoreConfigDTO.class)).thenThrow(new NullPointerException("App name cannot be null"));
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            try {
                invokeInitMethod();
            } catch (Exception e) {
                if (e.getCause() instanceof NullPointerException) {
                    throw (NullPointerException) e.getCause();
                }
                throw e;
            }
        });
        mockedEnvironment.verify(Environment::getAppName);
        verifyNoInteractions(mockConfigRepository);
    }

    /**
     * Test when Lion.getBean() returns null
     */
    @Test
    void testInitWhenLionReturnsNull() throws Throwable {
        // arrange
        String appName = "test-app";
        mockedEnvironment.when(Environment::getAppName).thenReturn(appName);
        mockedLion.when(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class)).thenReturn(null);
        // act
        invokeInitMethod();
        // assert
        mockedEnvironment.verify(Environment::getAppName);
        mockedLion.verify(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class));
        verify(mockConfigRepository).addConfigListener(LION_KEY, userActionScoreConfig);
        assertNull(getConfigDTO());
    }

    /**
     * Test when JsonUtils.toStr() throws exception - should still complete initialization
     */
    @Test
    void testInitWhenJsonUtilsFails() throws Throwable {
        // arrange
        String appName = "test-app";
        UserActionScoreConfigDTO expectedConfig = new UserActionScoreConfigDTO();
        mockedEnvironment.when(Environment::getAppName).thenReturn(appName);
        mockedLion.when(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class)).thenReturn(expectedConfig);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenThrow(new RuntimeException("JSON serialization failed"));
        // act & assert
        try {
            invokeInitMethod();
            // If we get here, the exception was caught in the method
            fail("Expected exception was not thrown");
        } catch (Exception e) {
            // Verify the exception is what we expect
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("JSON serialization failed", e.getCause().getMessage());
        }
        mockedEnvironment.verify(Environment::getAppName);
        mockedLion.verify(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class));
        mockedJsonUtils.verify(() -> JsonUtils.toStr(any()));
        // The method should not reach the point of adding the listener
        verifyNoInteractions(mockConfigRepository);
    }

    /**
     * Test when configRepository.addConfigListener() throws exception
     */
    @Test
    void testInitWhenListenerRegistrationFails() throws Throwable {
        // arrange
        String appName = "test-app";
        UserActionScoreConfigDTO expectedConfig = new UserActionScoreConfigDTO();
        mockedEnvironment.when(Environment::getAppName).thenReturn(appName);
        mockedLion.when(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class)).thenReturn(expectedConfig);
        mockedJsonUtils.when(() -> JsonUtils.toStr(expectedConfig)).thenReturn("config-json");
        doThrow(new RuntimeException("Listener registration failed")).when(mockConfigRepository).addConfigListener(anyString(), any());
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokeInitMethod());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Listener registration failed", exception.getCause().getMessage());
        mockedEnvironment.verify(Environment::getAppName);
        mockedLion.verify(() -> Lion.getBean(appName, LION_KEY, UserActionScoreConfigDTO.class));
        mockedJsonUtils.verify(() -> JsonUtils.toStr(expectedConfig));
        verify(mockConfigRepository).addConfigListener(LION_KEY, userActionScoreConfig);
        assertEquals(expectedConfig, getConfigDTO());
    }
}
