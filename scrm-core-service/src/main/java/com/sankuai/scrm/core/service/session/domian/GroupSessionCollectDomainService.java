package com.sankuai.scrm.core.service.session.domian;

import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.srcm.session.dto.GroupSessionCollectListDTO;
import com.sankuai.dz.srcm.session.dto.GroupSessionInfoListDTO;
import com.sankuai.dz.srcm.session.enums.CustomerMessageType;
import com.sankuai.dz.srcm.session.enums.SessionCollectMatchReasonEnum;
import com.sankuai.dz.srcm.session.request.PublicSentimentCollectListRequest;
import com.sankuai.scrm.core.service.session.dao.entity.GroupSessionCollectPageList;
import com.sankuai.scrm.core.service.session.dao.entity.ScrmPublicSentimentCollect;
import com.sankuai.scrm.core.service.session.dao.example.ScrmPublicSentimentCollectExample;
import com.sankuai.scrm.core.service.session.dao.mapper.ScrmPublicSentimentCollectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class GroupSessionCollectDomainService {


    @Resource
    private ScrmPublicSentimentCollectMapper sentimentCollectMapper;

    public List<GroupSessionCollectListDTO> querySessionCollectList(PublicSentimentCollectListRequest request) {
        try {
            log.info("GroupSessionCollectDomainService.queryPublicSentimentCollectList: 下载群会话收集详情, pageSize={}, pageNum={}", request.getPageSize(), request.getPageNum());
            Integer start = (request.getPageNum() - 1) * request.getPageSize();
            request.setPageNum(start);
            List<GroupSessionCollectPageList> sessionCollectListDTO = sentimentCollectMapper.selectByPage(request);
            if (CollectionUtils.isEmpty(sessionCollectListDTO)) {
                log.error("GroupSessionCollectDomainService.queryPublicSentimentCollectList: 分页查询列表为空, request={}", request);
                return Collections.emptyList();
            }

            List<GroupSessionCollectListDTO> sessionListDto = new ArrayList<>();

            if (ObjectUtils.isNotEmpty(request.getStartTime()) && ObjectUtils.isNotEmpty(request.getEndTime())) {
                //总计计算百分比
                log.info("GroupSessionCollectDomainService.queryPublicSentimentCollectList: 按日期条件查询统计, startTime={},endTime={}", request.getStartTime(), request.getEndTime());
                buildGroupSessionStatistics(request, sessionCollectListDTO, sessionListDto);
                return sessionListDto;
            }

            for (GroupSessionCollectPageList sessionCollectDto : sessionCollectListDTO) {
                buildGroupSessionCollectList(sessionListDto, sessionCollectDto);
            }
            return sessionListDto;
        } catch (Exception e) {
            log.error("GroupSessionCollectDomainService.queryPublicSentimentCollectList:  查询会话收集列表出现异常", e);
            return Collections.emptyList();
        }
    }

    private void buildGroupSessionCollectList(List<GroupSessionCollectListDTO> sessionListDto, GroupSessionCollectPageList sessionCollectDto) {
        Date collectTime = sessionCollectDto.getCollectTime();
        String collectTimeStr = new SimpleDateFormat("yyyy-MM-dd").format(collectTime);
        GroupSessionCollectListDTO sessionCollectStatistics = GroupSessionCollectListDTO.builder()
                .collectTime(collectTimeStr)
                .sessionCount(sessionCollectDto.getSessionCount())
                .complaintSuggestion(String.valueOf(sessionCollectDto.getComplaintSuggestion()))
                .excessiveDisturbance(String.valueOf(sessionCollectDto.getExcessiveDisturbance()))
                .aestheticConsultation(String.valueOf(sessionCollectDto.getAestheticConsultation()))
                .preSalesConsultation(String.valueOf(sessionCollectDto.getPreSalesConsultation()))
                .eventConsultation(String.valueOf(sessionCollectDto.getEventConsultation()))
                .otherCategories(String.valueOf(sessionCollectDto.getOtherCategories()))
                .build();
        sessionListDto.add(sessionCollectStatistics);
    }

    private void buildGroupSessionStatistics(PublicSentimentCollectListRequest request, List<GroupSessionCollectPageList> sessionCollectListDTO, List<GroupSessionCollectListDTO> sessionListDto) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.buildGroupSessionStatistics(com.sankuai.dz.srcm.session.request.PublicSentimentCollectListRequest,java.util.List,java.util.List)");
        GroupSessionCollectPageList sessionCollectDTO = sessionCollectListDTO.get(0);
        BigDecimal totalSessionCount = BigDecimal.valueOf(sessionCollectDTO.getSessionCount());
        BigDecimal totalComplaintSuggestion = BigDecimal.valueOf(sessionCollectDTO.getComplaintSuggestion());
        BigDecimal totalExcessiveDisturbance = BigDecimal.valueOf(sessionCollectDTO.getExcessiveDisturbance());
        BigDecimal totalAestheticConsultation = BigDecimal.valueOf(sessionCollectDTO.getAestheticConsultation());
        BigDecimal totalPreSalesConsultation = BigDecimal.valueOf(sessionCollectDTO.getPreSalesConsultation());
        BigDecimal totalEventConsultation = BigDecimal.valueOf(sessionCollectDTO.getEventConsultation());
        BigDecimal totalOtherCategories = BigDecimal.valueOf(sessionCollectDTO.getOtherCategories());



        BigDecimal complaintSuggestionRate = calculateRate(totalComplaintSuggestion, totalSessionCount);
        BigDecimal excessiveDisturbanceRate = calculateRate(totalExcessiveDisturbance, totalSessionCount);
        BigDecimal aestheticConsultationRate = calculateRate(totalAestheticConsultation, totalSessionCount);
        BigDecimal preSalesConsultationRate = calculateRate(totalPreSalesConsultation, totalSessionCount);
        BigDecimal eventConsultationRate = calculateRate(totalEventConsultation, totalSessionCount);
        BigDecimal otherCategoriesRate = calculateRate(totalOtherCategories, totalSessionCount);

        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();
        String startTimeStr = new SimpleDateFormat("yyyy-MM-dd").format(startTime);
        String endTimeStr = new SimpleDateFormat("yyyy-MM-dd").format(endTime);


        String collectTime = startTimeStr + "到" + endTimeStr;
        GroupSessionCollectListDTO sessionCollectStatistics = GroupSessionCollectListDTO.builder()
                .collectTime(collectTime)
                .sessionCount(totalSessionCount.intValue())
                .complaintSuggestion(totalComplaintSuggestion + "|(" + complaintSuggestionRate.stripTrailingZeros().toPlainString() + "%)")
                .excessiveDisturbance(totalExcessiveDisturbance + "|(" + excessiveDisturbanceRate.stripTrailingZeros().toPlainString() + "%)")
                .aestheticConsultation(totalAestheticConsultation + "|(" + aestheticConsultationRate.stripTrailingZeros().toPlainString() + "%)")
                .preSalesConsultation(totalPreSalesConsultation + "|(" + preSalesConsultationRate.stripTrailingZeros().toPlainString() + "%)")
                .eventConsultation(totalEventConsultation + "|(" + eventConsultationRate.stripTrailingZeros().toPlainString() + "%)")
                .otherCategories(totalOtherCategories + "|(" + otherCategoriesRate.stripTrailingZeros().toPlainString() + "%)")
                .build();
        sessionListDto.add(sessionCollectStatistics);
    }

    private BigDecimal calculateRate(BigDecimal total, BigDecimal divisor) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.calculateRate(java.math.BigDecimal,java.math.BigDecimal)");
        return total.divide(divisor, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
    }


    public long querySessionCollateCount(PublicSentimentCollectListRequest request) {
        return sentimentCollectMapper.queryCollectTotal(request);
    }

    public String downloadGroupSessionInfo(String appId, String collateTime) {
//        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.downloadGroupSessionInfo(java.lang.String,java.lang.String)");
        try {
            log.info("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo: 下载群会话收集详情, collateTime={}", collateTime);
            Date collectDate = new SimpleDateFormat("yyyy-MM-dd").parse(collateTime);

            ScrmPublicSentimentCollectExample example = new ScrmPublicSentimentCollectExample();
            example.createCriteria().andAppIdEqualTo(appId).andCollateTimeEqualTo(collectDate);
            List<ScrmPublicSentimentCollect> sessionCollateInfos = sentimentCollectMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(sessionCollateInfos)) {
                log.warn("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo: 查询群收集列表详情未成功");
                return null;
            }

            List<GroupSessionInfoListDTO> sessionInfoList = buildGroupSessionInfoListDTOS(sessionCollateInfos);

            String downloadUrl = getDownloadUrl(sessionInfoList);
            if (StringUtils.isBlank(downloadUrl)) {
                log.error("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo: 生成会话详情url未成功,collateTime={}", collateTime);
                return null;
            }
            return downloadUrl;
        } catch (Exception e) {
            log.error("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo: 生成会话详情url出现异常", e);
            return null;
        }

    }

    private List<GroupSessionInfoListDTO> buildGroupSessionInfoListDTOS(List<ScrmPublicSentimentCollect> sessionCollectInfos) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.buildGroupSessionInfoListDTOS(java.util.List)");
        List<GroupSessionInfoListDTO> sessionInfoList = new ArrayList<>();
        int serialNumber = 1;
        for (ScrmPublicSentimentCollect sessionCollectInfo : sessionCollectInfos) {
            String startTime = getStrDate(sessionCollectInfo.getSendTime());
            String endTime = getStrDate(sessionCollectInfo.getEndTime());
            GroupSessionInfoListDTO sessionCollateDTO = GroupSessionInfoListDTO.builder()
                    .serialNumber(serialNumber)
                    .sendTime(startTime)
                    .endTime(endTime)
                    .matchingReason(SessionCollectMatchReasonEnum.getDescByCode(sessionCollectInfo.getMatchingReason()))
                    .userName(sessionCollectInfo.getUserName())
                    .receiveName(sessionCollectInfo.getReceiverName())
                    .content(sessionCollectInfo.getContent())
                    .contentType(CustomerMessageType.getDescByCode(sessionCollectInfo.getContentType()))
                    .build();


            sessionInfoList.add(sessionCollateDTO);
            serialNumber++;
        }
        return sessionInfoList;
    }


    private String getDownloadUrl(List<GroupSessionInfoListDTO> sessionInfoList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.getDownloadUrl(java.util.List)");
        log.info("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo: 生成会话收集明细url, sessionInfoList={}", sessionInfoList);
        if (CollectionUtils.isEmpty(sessionInfoList)) {
            return null;
        }
        Map<String, List<List<String>>> sheetData = Maps.newHashMap();
        List<List<String>> rowData = Lists.newArrayList();

        rowData.add(Lists.newArrayList("序号", "发起时间", "结束时间", "匹配原因", "分类", "客户", "小助手/群名称", "内容"));

        sessionInfoList.forEach(item -> {
            if (item == null) {
                return;
            }
            List<String> row = Lists.newArrayList();
            row.add(String.valueOf(item.getSerialNumber()));
            row.add(String.valueOf(item.getSendTime()));
            row.add(String.valueOf(item.getEndTime()));
            row.add(String.valueOf(item.getMatchingReason()));
            row.add(String.valueOf(item.getContentType()));
            row.add(String.valueOf(item.getUserName()));
            row.add(String.valueOf(item.getReceiveName()));
            row.add(String.valueOf(item.getContent()));

            rowData.add(row);
        });
        sheetData.put("result", rowData);
        try {
            FileBody fileBody = FileBodyBuilder.buildExcelFileBody(String.valueOf(System.currentTimeMillis()), sheetData);
            return fileBody.getUrl();
        } catch (Exception e) {
            log.error("GroupSessionCollectDomainService.downloadPublicSentimentSessionInfo.getDownloadUrl.FileBodyBuilder.buildExcelFileBody fail", e);
            return null;
        }
    }

    private String getStrDate(Date time) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.session.domian.GroupSessionCollectDomainService.getStrDate(java.util.Date)");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZoneId timeZone = ZoneId.of("Asia/Shanghai");
        if (time != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(time.toInstant(), timeZone);
            String strDate = formatter.format(localDateTime);
            return strDate;
        } else {
            return null;
        }

    }

}
