package com.sankuai.scrm.core.service.user.predict.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import com.sankuai.scrm.core.service.user.dal.entity.userView.BusinessScoreConfig;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import com.sankuai.scrm.core.service.user.util.BusinessScoreConfigUtil;
import com.sankuai.scrm.core.service.user.util.UserDataFetchUtil;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserIntendVisitView;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserTradeView;
import java.math.RoundingMode;
import org.mockito.junit.*;

/**
 * Test cases for the calculateRepeatSearch method in UserHesitationAnalysisServiceImpl
 */
@ExtendWith(MockitoExtension.class)
public class UserHesitationAnalysisServiceImplCalculateRepeatSearchTest {

    @Mock
    private UserDataFetchUtil userDataFetchUtil;

    @Mock
    private BusinessScoreConfigUtil businessScoreConfigUtil;

    @InjectMocks
    private UserHesitationAnalysisServiceImpl userHesitationAnalysisService;

    private final UserHesitationAnalysisServiceImpl service = new UserHesitationAnalysisServiceImpl();

    /**
     * Helper method to invoke the private calculateRepeatSearch method using reflection
     */
    private int invokeCalculateRepeatSearch(UserSearchView searchData, BusinessScoreConfig.HesitationConfig config) throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateRepeatSearch", UserSearchView.class, BusinessScoreConfig.HesitationConfig.class);
        method.setAccessible(true);
        return (int) method.invoke(userHesitationAnalysisService, searchData, config);
    }

    /**
     * Test when searchData is null, should return 0
     */
    @Test
    public void testCalculateRepeatSearchWithNullSearchData() throws Throwable {
        // arrange
        UserSearchView searchData = null;
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(3.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when config is null, should return 0
     */
    @Test
    public void testCalculateRepeatSearchWithNullConfig() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(3L);
        BusinessScoreConfig.HesitationConfig config = null;
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when search7dKeywordCnt is 0, should return 0
     */
    @Test
    public void testCalculateRepeatSearchWithZeroKeywordCount() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(0L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(3.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when repeatSearchMultiplier is null, should use default value 3.0 and return 1 when condition met
     */
    @Test
    public void testCalculateRepeatSearchWithNullMultiplierAndConditionMet() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(3L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(null);
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(1, result);
    }

    /**
     * Test when repeatSearchMultiplier is null, should use default value 3.0 and return 0 when condition not met
     */
    @Test
    public void testCalculateRepeatSearchWithNullMultiplierAndConditionNotMet() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(9L);
        searchData.setSearchKeywordCnt7d(3L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(null);
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when search count exceeds threshold with custom multiplier, should return 1
     */
    @Test
    public void testCalculateRepeatSearchWithCustomMultiplierAndConditionMet() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(11L);
        searchData.setSearchKeywordCnt7d(5L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(1, result);
    }

    /**
     * Test when search count does not exceed threshold with custom multiplier, should return 0
     */
    @Test
    public void testCalculateRepeatSearchWithCustomMultiplierAndConditionNotMet() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(5L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when search7dCnt is null, should default to 0 and return 0
     */
    @Test
    public void testCalculateRepeatSearchWithNullSearchCount() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(null);
        searchData.setSearchKeywordCnt7d(5L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when search7dKeywordCnt is null, should default to 0 and return 0
     */
    @Test
    public void testCalculateRepeatSearchWithNullKeywordCount() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(null);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setRepeatSearchMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when an exception occurs during calculation, should return 0
     */
    @Test
    public void testCalculateRepeatSearchWithException() throws Throwable {
        // arrange
        UserSearchView searchData = new UserSearchView();
        searchData.setQv7d(10L);
        searchData.setSearchKeywordCnt7d(5L);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig() {

            @Override
            public BigDecimal getRepeatSearchMultiplier() {
                throw new RuntimeException("Test exception");
            }
        };
        // act
        int result = invokeCalculateRepeatSearch(searchData, config);
        // assert
        assertEquals(0, result);
    }

    private int invokeCalculatePriceHesitation(UserTradeView tradeData, UserIntendVisitView visitData, BusinessScoreConfig.HesitationConfig config) throws Exception {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculatePriceHesitation", UserTradeView.class, UserIntendVisitView.class, BusinessScoreConfig.HesitationConfig.class);
        method.setAccessible(true);
        return (int) method.invoke(userHesitationAnalysisService, tradeData, visitData, config);
    }

    @Test
    public void testCalculatePriceHesitation_NullInputs() throws Throwable {
        // arrange
        // act & assert
        assertEquals(0, invokeCalculatePriceHesitation(null, null, null));
        assertEquals(0, invokeCalculatePriceHesitation(new UserTradeView(), null, null));
        assertEquals(0, invokeCalculatePriceHesitation(null, new UserIntendVisitView(), null));
        assertEquals(0, invokeCalculatePriceHesitation(null, null, new BusinessScoreConfig.HesitationConfig()));
    }

    @Test
    public void testCalculatePriceHesitation_NullVisitAmount() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(null);
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradeCntNull() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(null);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradeCntZero() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(0L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradeAmtNull() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(null);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradeAmtZero() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.ZERO);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_NullMultiplier_VisitAmountGreaterThanThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // > 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(null);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculatePriceHesitation_NullMultiplier_VisitAmountLessThanThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // < 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(30));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(null);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_NullMultiplier_VisitAmountEqualsThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // = 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(40));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(null);
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_CustomMultiplier_VisitAmountGreaterThanThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // > 20*1.5=30
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(31));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(1.5));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculatePriceHesitation_CustomMultiplier_VisitAmountLessThanThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // < 20*1.5=30
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(29));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(1.5));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradePeriod15Days() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade15dCnt(5L);
        tradeData.setTrade15dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // > 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(15);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculatePriceHesitation_TradePeriod60Days() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade60dCnt(5L);
        tradeData.setTrade60dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // > 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(60);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculatePriceHesitation_UnsupportedTradePeriod() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        tradeData.setTrade30dAmt(BigDecimal.valueOf(100));
        UserIntendVisitView visitData = new UserIntendVisitView();
        // > 20*2=40
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        // unsupported
        config.setPriceHesitationTradePeriod(45);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculatePriceHesitation_ExceptionHandling() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(5L);
        // trade30dAmt deliberately left null to cause NPE in division
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setVisit7dProductAvgAmt(BigDecimal.valueOf(50));
        BusinessScoreConfig.HesitationConfig config = new BusinessScoreConfig.HesitationConfig();
        config.setPriceHesitationTradePeriod(30);
        config.setPriceHesitationMultiplier(BigDecimal.valueOf(2.0));
        // act
        int result = invokeCalculatePriceHesitation(tradeData, visitData, config);
        // assert
        assertEquals(0, result);
    }

    private int invokeCalculateBrowseNoBuy(UserTradeView tradeData, UserIntendVisitView visitData) throws Exception {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateBrowseNoBuy", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        return (int) method.invoke(service, tradeData, visitData);
    }

    @Test
    public void testCalculateBrowseNoBuyBothNull() throws Throwable {
        // arrange
        UserTradeView tradeData = null;
        UserIntendVisitView visitData = null;
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyTradeDataNull() throws Throwable {
        // arrange
        UserTradeView tradeData = null;
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(1);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyVisitDataNull() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(1);
        UserIntendVisitView visitData = null;
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyBothDaysNull() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        UserIntendVisitView visitData = new UserIntendVisitView();
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyVisitDaysLessThanBuyDays() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(3);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(2);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculateBrowseNoBuyVisitDaysEqualsBuyDays() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(2);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(2);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyVisitDaysGreaterThanBuyDays() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(1);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(2);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyVisitDaysNull() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(1);
        UserIntendVisitView visitData = new UserIntendVisitView();
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateBrowseNoBuyBuyDaysNull() throws Throwable {
        // arrange
        // last2tdBuySucDays=null -> MAX_VALUE
        UserTradeView tradeData = new UserTradeView();
        UserIntendVisitView visitData = new UserIntendVisitView();
        // 1 < MAX_VALUE -> true -> return 1
        visitData.setLast2tdVisitDays(1);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        // 修改预期结果为1
        assertEquals(1, result);
    }

    @Test
    public void testCalculateBrowseNoBuyMinVisitDaysMaxBuyDays() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(Integer.MAX_VALUE);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(Integer.MIN_VALUE);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(1, result);
    }

    @Test
    public void testCalculateBrowseNoBuyMaxVisitDaysMinBuyDays() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setLast2tdBuySucDays(Integer.MIN_VALUE);
        UserIntendVisitView visitData = new UserIntendVisitView();
        visitData.setLast2tdVisitDays(Integer.MAX_VALUE);
        // act
        int result = invokeCalculateBrowseNoBuy(tradeData, visitData);
        // assert
        assertEquals(0, result);
    }

    @Test
    public void testCalculateNoRecentPurchaseWhenTradeDataOrVisitDataIsNull() throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateNoRecentPurchase", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        int result = (int) method.invoke(userHesitationAnalysisService, null, mock(UserIntendVisitView.class));
        assertEquals(0, result);
        result = (int) method.invoke(userHesitationAnalysisService, mock(UserTradeView.class), null);
        assertEquals(0, result);
    }

    @Test
    public void testCalculateNoRecentPurchaseWhenTrade7dCntIsZeroAndVisit7dPvIsGreaterThanZero() throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateNoRecentPurchase", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        UserTradeView tradeData = mock(UserTradeView.class);
        UserIntendVisitView visitData = mock(UserIntendVisitView.class);
        when(tradeData.getTrade7dCnt()).thenReturn(0L);
        when(visitData.getVisit7dPv()).thenReturn(1L);
        int result = (int) method.invoke(userHesitationAnalysisService, tradeData, visitData);
        assertEquals(1, result);
    }

    @Test
    public void testCalculateNoRecentPurchaseWhenTrade7dCntIsNotZeroAndVisit7dPvIsGreaterThanZero() throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateNoRecentPurchase", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        UserTradeView tradeData = mock(UserTradeView.class);
        UserIntendVisitView visitData = mock(UserIntendVisitView.class);
        when(tradeData.getTrade7dCnt()).thenReturn(1L);
        when(visitData.getVisit7dPv()).thenReturn(1L);
        int result = (int) method.invoke(userHesitationAnalysisService, tradeData, visitData);
        assertEquals(0, result);
    }

    @Test
    public void testCalculateNoRecentPurchaseWhenTrade7dCntIsZeroAndVisit7dPvIsZero() throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateNoRecentPurchase", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        UserTradeView tradeData = mock(UserTradeView.class);
        UserIntendVisitView visitData = mock(UserIntendVisitView.class);
        when(tradeData.getTrade7dCnt()).thenReturn(0L);
        when(visitData.getVisit7dPv()).thenReturn(0L);
        int result = (int) method.invoke(userHesitationAnalysisService, tradeData, visitData);
        assertEquals(0, result);
    }

    @Test
    public void testCalculateNoRecentPurchaseWhenTrade7dCntIsNotZeroAndVisit7dPvIsZero() throws Throwable {
        Method method = UserHesitationAnalysisServiceImpl.class.getDeclaredMethod("calculateNoRecentPurchase", UserTradeView.class, UserIntendVisitView.class);
        method.setAccessible(true);
        UserTradeView tradeData = mock(UserTradeView.class);
        UserIntendVisitView visitData = mock(UserIntendVisitView.class);
        when(tradeData.getTrade7dCnt()).thenReturn(1L);
        when(visitData.getVisit7dPv()).thenReturn(0L);
        int result = (int) method.invoke(userHesitationAnalysisService, tradeData, visitData);
        assertEquals(0, result);
    }
}
