package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationExecutePlanDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationExecutePlanDO, ScrmAmProcessOrchestrationExecutePlanDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationExecutePlanDO> list);
}