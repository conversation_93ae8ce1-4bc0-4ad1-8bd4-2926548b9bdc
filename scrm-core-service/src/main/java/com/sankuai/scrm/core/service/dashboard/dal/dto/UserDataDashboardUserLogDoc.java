package com.sankuai.scrm.core.service.dashboard.dal.dto;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.scrm.core.service.dashboard.annotation.FieldToCount;
import com.sankuai.scrm.core.service.dashboard.dal.enums.CustomerActionResultTypeEnum;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.sankuai.scrm.core.service.dashboard.constants.DashBoardFieldsToCountConstant.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDataDashboardUserLogDoc {
    /**
     * 由versionTime和corpId和unionId拼接
     */
    private String id;
    @JSONField(format = "yyyy-MM-dd")
    private Date versionTime;
    private String corpId;
    private String unionId;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "customerIncrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isNewCustomer;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "customerDecrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isLostCustomer;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "inGroupUserIncrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isNewGroupMember;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "inGroupUserDecrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isLostGroupMember;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "friendIncrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isNewCorpWxFriend;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "friendDecrement", groupTag = USER_DATA_DELTA_GROUP_TAG)
    private Boolean isLostCorpWxFriend;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "groupDynamicCodeUserDecrement", groupTag = USER_DATA_LEAVE_SOURCE_GROUP_TAG)
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "groupDynamicLiveCodeUserIncrement", groupTag = USER_DATA_ENTER_SOURCE_GROUP_TAG)
    private CustomerActionResultTypeEnum fromGroupDynamicCode;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "corpWxDynamicCodeUserDecrement", groupTag = USER_DATA_LEAVE_SOURCE_GROUP_TAG)
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "corpWxDynamicCodeUserIncrement", groupTag = USER_DATA_ENTER_SOURCE_GROUP_TAG)
    private CustomerActionResultTypeEnum fromCorpWxDynamicCode;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "innerStationUserDecrement", groupTag = USER_DATA_LEAVE_SOURCE_GROUP_TAG)
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "innerStationUserIncrement", groupTag = USER_DATA_ENTER_SOURCE_GROUP_TAG)
    private CustomerActionResultTypeEnum fromInnerStation;
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "activityUserDecrement", groupTag = USER_DATA_LEAVE_SOURCE_GROUP_TAG)
    @FieldToCount(mapToClass = UserDataDashBoardUserLogChangeDTO.class, mapToField = "activityUserIncrement", groupTag = USER_DATA_ENTER_SOURCE_GROUP_TAG)
    private CustomerActionResultTypeEnum fromActivity;

    public static String concatId(Date versionTime, String corpId, String unionId) {
        String versionTimeStr = DateUtil.format(versionTime, DatePattern.NORM_DATE_PATTERN);
        return String.format("%s-%s-%s", versionTimeStr, corpId, unionId);
    }

}
