package com.sankuai.scrm.core.service.infrastructure.acl.minip.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class SchemeInfoResponse {
    @JsonProperty("appid")
    private String appId;
    @JsonProperty("path")
    private String path;
    @JsonProperty("query")
    private String query;
    @JsonProperty("create_time")
    private long createTime;
    @JsonProperty("expire_time")
    private long expireTime;
    @JsonProperty("env_version")
    private String envVersion;
}
