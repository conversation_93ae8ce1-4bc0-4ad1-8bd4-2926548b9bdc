package com.sankuai.scrm.core.service.pchat.domain.common;

import cn.hutool.core.collection.CollectionUtil;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.robot.agent.RobotInfoRequest;
import com.sankuai.dz.srcm.pchat.request.robot.agent.RobotListRequest;
import com.sankuai.dz.srcm.pchat.response.robot.agent.RobotFriendResponse;
import com.sankuai.dz.srcm.pchat.response.robot.agent.RobotInfoDTO;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxFriendsCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotTag;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfoCommon;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxFriendsCommonExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotInfoCommonExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotTagExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserInfoCommonExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.*;
import com.sankuai.scrm.core.service.pchat.enums.PersonalRobotStatusEnum;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/7/18 17:09
 * @Version v1.0.0
 */
@Slf4j
@Service
public class ScrmPersonalWxUserCommonDomainService {
    @Resource
    private ScrmPersonalWxRobotLoginCommonLogMapper robotLoginCommonLogMapper;

    @Resource
    private ScrmPersonalWxFriendsCommonMapper scrmPersonalWxFriendsCommonMapper;
    @Resource
    private ScrmPersonalWxRobotInfoCommonMapper robotInfoCommonMapper;
    @Resource
    private ScrmPersonalWxUserInfoCommonMapper wxUserInfoCommonMapper;
    @Resource
    private ScrmPersonalWxRobotTagMapper personalWxRobotTagMapper;
    @Resource
    private ScrmPersonalWxFriendsCommonCustomMapper scrmPersonalWxFriendsCommonCustomMapper;
    @MdpConfig(value = "scrm.wx.delete.friend.relation.switch:false")
    private boolean isDeleteFriend;

    public PagerList<RobotFriendResponse> queryRobotFriendList(RobotInfoRequest request) {
        ScrmPersonalWxRobotInfoCommon scrmPersonalWxRobotInfoCommon = queryRobotInfo(request.getRobotSerialNo(), request.getBizId());
        if (scrmPersonalWxRobotInfoCommon == null) {
            return PagerList.empty();
        }
        ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfoCommon = queryUserInfo(request.getRobotSerialNo(), request.getBizId());
        if (scrmPersonalWxUserInfoCommon == null) {
            return PagerList.empty();
        }
        long total = scrmPersonalWxFriendsCommonCustomMapper.totalByTags(scrmPersonalWxUserInfoCommon.getId(), request.getBizId(), SqlUtil.like(request.getFriendName()), request.getTagIds(), request.getWithoutTag(), false);
        if (total == 0) {
            return PagerList.empty();
        }
        List<ScrmPersonalWxFriendsCommon> scrmPersonalWxFriendsCommons = scrmPersonalWxFriendsCommonCustomMapper.selectByTags(scrmPersonalWxUserInfoCommon.getId(), request.getBizId(), SqlUtil.like(request.getFriendName()), request.getTagIds(), request.getWithoutTag(), false, request.getPageNo() * request.getPageSize(), request.getPageSize());

        if (CollectionUtils.isEmpty(scrmPersonalWxFriendsCommons)) {
            return PagerList.empty();
        }
        List<ScrmPersonalWxUserInfoCommon> scrmPersonalWxUserInfoCommons = queryUserInfo(scrmPersonalWxFriendsCommons.stream().map(ScrmPersonalWxFriendsCommon::getFriendId).collect(Collectors.toList()), request.getBizId());
        if (CollectionUtils.isEmpty(scrmPersonalWxUserInfoCommons)) {
            return PagerList.empty();
        }
        Map<Long, ScrmPersonalWxFriendsCommon> friendsCommonMap = scrmPersonalWxFriendsCommons.stream().collect(Collectors.toMap(ScrmPersonalWxFriendsCommon::getFriendId, Function.identity(), (a, b) -> a));
        List<RobotFriendResponse> robotFriendResponses = scrmPersonalWxUserInfoCommons.stream().map(t -> buildRobotFriendResponse(t, friendsCommonMap)).collect(Collectors.toList());
        return PagerList.of(total, robotFriendResponses);

    }

    private RobotFriendResponse buildRobotFriendResponse(ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfoCommon, Map<Long, ScrmPersonalWxFriendsCommon> friendsCommonMap) {
        RobotFriendResponse robotFriendResponse = new RobotFriendResponse();
        robotFriendResponse.setWxId(scrmPersonalWxUserInfoCommon.getWxId());
        robotFriendResponse.setSex(scrmPersonalWxUserInfoCommon.getSex());
        robotFriendResponse.setAvatar(scrmPersonalWxUserInfoCommon.getHeadimgUrl());
        robotFriendResponse.setWxNickname(scrmPersonalWxUserInfoCommon.getNickname());
        robotFriendResponse.setWxAlias(scrmPersonalWxUserInfoCommon.getWxAlias());
        if (friendsCommonMap.containsKey(scrmPersonalWxUserInfoCommon.getId())) {
            ScrmPersonalWxFriendsCommon scrmPersonalWxFriendsCommon = friendsCommonMap.get(scrmPersonalWxUserInfoCommon.getId());
            robotFriendResponse.setWxRemarkName(scrmPersonalWxFriendsCommon.getRemarkName());
            robotFriendResponse.setTagIds(scrmPersonalWxFriendsCommon.getTagIds());
        }
        return robotFriendResponse;
    }

    public ScrmPersonalWxRobotInfoCommon queryRobotInfo(String robotSerialNo, String bizId) {
        if (StringUtils.isBlank(robotSerialNo) || StringUtils.isBlank(bizId)) {
            return null;
        }
        List<ScrmPersonalWxRobotInfoCommon> result = queryRobotInfos(robotSerialNo, bizId);
        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }

    public List<ScrmPersonalWxRobotInfoCommon> queryRobotInfos(String robotSerialNo) {
        return queryRobotInfos(robotSerialNo, null);
    }

    public List<ScrmPersonalWxRobotInfoCommon> queryRobotInfos(String robotSerialNo, String bizId) {
        if (StringUtils.isBlank(robotSerialNo)) {
            return Collections.emptyList();
        }
        ScrmPersonalWxRobotInfoCommonExample example = new ScrmPersonalWxRobotInfoCommonExample();
        ScrmPersonalWxRobotInfoCommonExample.Criteria criteria = example.createCriteria();
        criteria.andRobotSerialNoEqualTo(robotSerialNo);
        if (StringUtils.isNotBlank(bizId)) {
            criteria.andBizIdEqualTo(bizId);
        }

        return robotInfoCommonMapper.selectByExample(example);
    }

    public ScrmPersonalWxUserInfoCommon queryUserInfo(String robotSerialNo, String bizId) {
        if (StringUtils.isBlank(robotSerialNo) || StringUtils.isBlank(bizId)) {
            return null;
        }
        List<ScrmPersonalWxUserInfoCommon> result = queryUserInfos(robotSerialNo, bizId);
        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }

    public List<ScrmPersonalWxUserInfoCommon> queryUserInfos(String robotSerialNo, String bizId) {
        if (StringUtils.isBlank(robotSerialNo)) {
            return Collections.emptyList();
        }
        ScrmPersonalWxUserInfoCommonExample example = new ScrmPersonalWxUserInfoCommonExample();
        ScrmPersonalWxUserInfoCommonExample.Criteria criteria = example.createCriteria();
        criteria.andSerialNoEqualTo(robotSerialNo);
        if (StringUtils.isNotBlank(bizId)) {
            criteria.andBizIdEqualTo(bizId);
        }
        return wxUserInfoCommonMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxUserInfoCommon> queryUserInfos(List<String> robotSerialNos, String bizId) {
        if (CollectionUtils.isEmpty(robotSerialNos)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxUserInfoCommonExample example = new ScrmPersonalWxUserInfoCommonExample();
        ScrmPersonalWxUserInfoCommonExample.Criteria criteria = example.createCriteria();
        criteria.andSerialNoIn(robotSerialNos);
        if (StringUtils.isNotBlank(bizId)) {
            criteria.andBizIdEqualTo(bizId);
        }
        return wxUserInfoCommonMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxUserInfoCommon> queryUserInfo(List<Long> userIds, String bizId) {
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(bizId)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxUserInfoCommonExample example = new ScrmPersonalWxUserInfoCommonExample();
        example.createCriteria().andIdIn(userIds).andBizIdEqualTo(bizId);
        return wxUserInfoCommonMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxFriendsCommon> queryFriend(Long userId, String bizId, Integer pageNo, Integer pageSize) {
        if (userId == null || StringUtils.isBlank(bizId)) {
            return Collections.emptyList();
        }
        ScrmPersonalWxFriendsCommonExample example = new ScrmPersonalWxFriendsCommonExample();
        example.createCriteria().andUserIdEqualTo(userId).andBizIdEqualTo(bizId);
        if (pageNo != null && pageSize != null) {
            example.page(pageNo, pageSize);
        }
        return scrmPersonalWxFriendsCommonMapper.selectByExample(example);
    }

    public List<ScrmPersonalWxFriendsCommon> queryWxFriendRelationByUserIdAndFriendId(Long robotUserIdId, List<Long> friendIds, String bizId) {
        ScrmPersonalWxFriendsCommonExample friendsExample = new ScrmPersonalWxFriendsCommonExample();
        friendsExample.createCriteria().andBizIdEqualTo(bizId).andUserIdEqualTo(robotUserIdId).andFriendIdIn(friendIds);
        return scrmPersonalWxFriendsCommonMapper.selectByExample(friendsExample);
    }

    public long totalFriend(Long userId, String bizId) {
        if (userId == null || StringUtils.isBlank(bizId)) {
            return 0;
        }
        ScrmPersonalWxFriendsCommonExample example = new ScrmPersonalWxFriendsCommonExample();
        example.createCriteria().andUserIdEqualTo(userId).andBizIdEqualTo(bizId);
        return scrmPersonalWxFriendsCommonMapper.countByExample(example);
    }

    public PagerList<ScrmPersonalWxRobotTag> queryRobotTagList(RobotInfoRequest request) {
        ScrmPersonalWxRobotTagExample example = new ScrmPersonalWxRobotTagExample();
        example.createCriteria().andSerialNoEqualTo(request.getRobotSerialNo()).andBizIdEqualTo(request.getBizId());
        long total = personalWxRobotTagMapper.countByExample(example);
        if (total == 0) {
            return PagerList.empty();
        }
        if (request.getPageNo() != null && request.getPageSize() != null) {
            example.page(request.getPageNo(), request.getPageSize());
        }
        List<ScrmPersonalWxRobotTag> result = personalWxRobotTagMapper.selectByExample(example);
        return PagerList.of(total, result);
    }

    public void processRobotTagList(List<ScrmPersonalWxRobotTag> robotTags) {
        if (CollectionUtils.isEmpty(robotTags)) {
            return;
        }
        String robotSerialNo = robotTags.get(0).getSerialNo();
        String bizId = robotTags.get(0).getBizId();
        List<ScrmPersonalWxRobotTag> dbRobotTags = queryAllRobotTag(robotSerialNo, bizId);
        List<ScrmPersonalWxRobotTag> updateRobotTags = new ArrayList<>();
        List<ScrmPersonalWxRobotTag> deleteRobotTags = new ArrayList<>();
        List<ScrmPersonalWxRobotTag> insertRobotTags = new ArrayList<>();
        // 将数据拆分组 新增 删除 修改
        splitFillGroup(robotTags, dbRobotTags, updateRobotTags, deleteRobotTags, insertRobotTags);
        // 分组的数据 处理
        splitProcess(updateRobotTags, deleteRobotTags, insertRobotTags);
    }

    private void splitProcess(List<ScrmPersonalWxRobotTag> updateRobotTags, List<ScrmPersonalWxRobotTag> deleteRobotTags, List<ScrmPersonalWxRobotTag> insertRobotTags) {
        if (CollectionUtils.isNotEmpty(insertRobotTags)) {
            personalWxRobotTagMapper.batchInsert(insertRobotTags);
        }
        if (CollectionUtils.isNotEmpty(updateRobotTags)) {
            for (ScrmPersonalWxRobotTag robotTag : updateRobotTags) {
                personalWxRobotTagMapper.updateByPrimaryKeySelective(robotTag);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteRobotTags)) {
            for (ScrmPersonalWxRobotTag robotTag : deleteRobotTags) {
                personalWxRobotTagMapper.deleteByPrimaryKey(robotTag.getId());
            }
        }
    }

    private void splitFillGroup(List<ScrmPersonalWxRobotTag> robotTags, List<ScrmPersonalWxRobotTag> dbRobotTag, List<ScrmPersonalWxRobotTag> updateRobotTags, List<ScrmPersonalWxRobotTag> deleteRobotTags, List<ScrmPersonalWxRobotTag> insertRobotTags) {
        Map<Integer, ScrmPersonalWxRobotTag> needHandleRobotTagMap = robotTags.stream().collect(Collectors.toMap(ScrmPersonalWxRobotTag::getTagId, Function.identity(), (a, b) -> a));
        Map<Integer, ScrmPersonalWxRobotTag> dbRobotTagMap = dbRobotTag.stream().collect(Collectors.toMap(ScrmPersonalWxRobotTag::getTagId, Function.identity(), (a, b) -> a));
        // 处理新增和修改 ，从需要处理的数据遍历
        for (ScrmPersonalWxRobotTag robotTag : robotTags) {
            // 数据库里有的，需改
            if (dbRobotTagMap.containsKey(robotTag.getTagId())) {
                ScrmPersonalWxRobotTag updateRobotTag = dbRobotTagMap.get(robotTag.getTagId());
                if (updateRobotTag.getTagName().equals(robotTag.getTagName())) {// 标签名没改，不增加
                    continue;
                }
                updateRobotTag.setUpdateTime(new Date());
                updateRobotTag.setTagName(robotTag.getTagName());
                updateRobotTags.add(updateRobotTag);
            } else {
                robotTag.setAddTime(new Date());
                robotTag.setUpdateTime(new Date());
                insertRobotTags.add(robotTag);
            }
        }
        // 处理删除 从数据中数据遍历
        for (ScrmPersonalWxRobotTag robotTag : dbRobotTag) {
            if (!needHandleRobotTagMap.containsKey(robotTag.getTagId())) {
                deleteRobotTags.add(robotTag);
            }
        }
    }

    private List<ScrmPersonalWxRobotTag> queryAllRobotTag(String robotSerialNo, String bizId) {
        if (StringUtils.isBlank(robotSerialNo)) {
            return new ArrayList<>();
        }
        ScrmPersonalWxRobotTagExample example = new ScrmPersonalWxRobotTagExample();
        ScrmPersonalWxRobotTagExample.Criteria criteria = example.createCriteria();
        criteria.andSerialNoEqualTo(robotSerialNo);
        if (StringUtils.isNotBlank(bizId)) {
            criteria.andBizIdEqualTo(bizId);
        }
        return personalWxRobotTagMapper.selectByExample(example);
    }

    public void updateRobotInfo(ScrmPersonalWxRobotInfoCommon robotInfo) {
        robotInfo.setUpdateTime(new Date());
        robotInfoCommonMapper.updateByPrimaryKey(robotInfo);
    }

    public void saveRobotInfo(ScrmPersonalWxRobotInfoCommon robotInfo) {
        robotInfo.setAddTime(new Date());
        robotInfo.setUpdateTime(new Date());
        robotInfoCommonMapper.insertSelective(robotInfo);
    }

    public void updateUserInfo(ScrmPersonalWxUserInfoCommon wxUserInfo) {
        wxUserInfo.setUpdateTime(new Date());
        wxUserInfoCommonMapper.updateByPrimaryKey(wxUserInfo);
    }

    public void saveUserInfo(ScrmPersonalWxUserInfoCommon scrmPersonalWxUserInfo) {
        scrmPersonalWxUserInfo.setAddTime(new Date());
        scrmPersonalWxUserInfo.setUpdateTime(new Date());
        wxUserInfoCommonMapper.insertSelective(scrmPersonalWxUserInfo);
    }

    /**
     * 保存用户信息
     *
     * @param userInfos
     * @return
     */

    public List<ScrmPersonalWxUserInfoCommon> saveOrUpdateWxUserInfo(List<ScrmPersonalWxUserInfoCommon> userInfos) {
        if (CollectionUtils.isEmpty(userInfos)) {
            return new ArrayList<>();
        }
        String bizId = userInfos.get(0).getBizId();
        List<String> userSerialNoSet = userInfos.stream().map(ScrmPersonalWxUserInfoCommon::getSerialNo).collect(Collectors.toList());
        List<ScrmPersonalWxUserInfoCommon> existedUserInfos = queryUserInfos(userSerialNoSet, bizId);
        //<serialNo,ScrmPersonalWxUserInfo>
        Map<String, ScrmPersonalWxUserInfoCommon> existedSerialNoMap = existedUserInfos.stream().collect(Collectors.toMap(ScrmPersonalWxUserInfoCommon::getSerialNo, Function.identity(), (r1, r2) -> r1));
        List<ScrmPersonalWxUserInfoCommon> saveUserList = new ArrayList<>();
        List<ScrmPersonalWxUserInfoCommon> updateUserList = new ArrayList<>();
        userInfos.forEach(user -> {
            ScrmPersonalWxUserInfoCommon userInfo = existedSerialNoMap.get(user.getSerialNo());
            if (userInfo == null) {
                saveUserList.add(user);
                return;
            }
            user.setAddTime(userInfo.getAddTime());
            user.setId(userInfo.getId());
            updateUserList.add(user);

        });
        if (CollectionUtil.isNotEmpty(saveUserList)) {
            wxUserInfoCommonMapper.batchInsert(saveUserList);
        }

        updateWxUserInfoById(updateUserList);

        List<ScrmPersonalWxUserInfoCommon> all = new ArrayList<>(saveUserList);
        all.addAll(updateUserList);
        return all;
    }

    public void updateWxUserInfoById(List<ScrmPersonalWxUserInfoCommon> userInfoList) {
        userInfoList.forEach(r -> {
            r.setUpdateTime(new Date());
            wxUserInfoCommonMapper.updateByPrimaryKeySelective(r);
        });
    }

    public void saveOrUpdateWxFriends(String serialNo, List<ScrmPersonalWxUserInfoCommon> scrmPersonalWxUserInfoCommons, Map<String, ScrmPersonalWxFriendsCommon> friendInfoMap) {
        if (CollectionUtils.isEmpty(scrmPersonalWxUserInfoCommons)) {
            return;
        }
        String bizId = scrmPersonalWxUserInfoCommons.get(0).getBizId();
        ScrmPersonalWxUserInfoCommon userInfos = queryUserInfo(serialNo, bizId);
        List<ScrmPersonalWxFriendsCommon> dbFriends = queryFriend(userInfos.getId(), bizId, null, null);
        Map<Long, ScrmPersonalWxFriendsCommon> dbFriendMap = dbFriends.stream().filter(d -> d.getFriendId() != null).collect(Collectors.toMap(ScrmPersonalWxFriendsCommon::getFriendId, Function.identity(), (r1, r2) -> r1));

        List<ScrmPersonalWxFriendsCommon> saveFriendList = new ArrayList<>();
        List<ScrmPersonalWxFriendsCommon> updateFriendList = new ArrayList<>();
        List<Long> deleteFriendList = new ArrayList<>();
        // 遍历用户信息列表 save or update
        for (ScrmPersonalWxUserInfoCommon personalWxUserInfoCommon : scrmPersonalWxUserInfoCommons) {
            ScrmPersonalWxFriendsCommon friendList = friendInfoMap.get(personalWxUserInfoCommon.getSerialNo());
            if (dbFriendMap.containsKey(personalWxUserInfoCommon.getId())) {
                ScrmPersonalWxFriendsCommon dbFriend = dbFriendMap.get(personalWxUserInfoCommon.getId());
                if (isChangeFriendCommon(friendList, dbFriend, personalWxUserInfoCommon)) {
                    updateFriendList.add(dbFriend);
                }
            } else {
                ScrmPersonalWxFriendsCommon scrmPersonalWxFriendsCommon = buildWxFriendCommon(friendList, bizId, userInfos, personalWxUserInfoCommon);
                saveFriendList.add(scrmPersonalWxFriendsCommon);
            }
        }
        // 删除好友 from db

        Set<Long> friendIds = scrmPersonalWxUserInfoCommons.stream().map(ScrmPersonalWxUserInfoCommon::getId).collect(Collectors.toSet());
        dbFriendMap.keySet().forEach(dbFriend -> {
            if (!friendIds.contains(dbFriend)) {
                deleteFriendList.add(dbFriend);
            }
        });

        // 处理 insert update delete
        if (CollectionUtils.isNotEmpty(saveFriendList)) {
            scrmPersonalWxFriendsCommonMapper.batchInsert(saveFriendList);
        }
        if (CollectionUtils.isNotEmpty(updateFriendList)) {
            for (ScrmPersonalWxFriendsCommon scrmPersonalWxFriendsCommon : updateFriendList) {
                updateFriend(scrmPersonalWxFriendsCommon);
            }
        }
        // 删除好友，服务商推送是分页推送的数据，pageSize=1000，如果超出的1000的好友，会出现删除问题
        if (isDeleteFriend && CollectionUtils.isNotEmpty(deleteFriendList)) {
            ScrmPersonalWxFriendsCommonExample example = new ScrmPersonalWxFriendsCommonExample();
            example.createCriteria().andIdIn(deleteFriendList).andBizIdEqualTo(bizId);
            scrmPersonalWxFriendsCommonMapper.deleteByExample(example);
        }

    }

    public void updateFriend(ScrmPersonalWxFriendsCommon friend) {
        friend.setUpdateTime(new Date());
        scrmPersonalWxFriendsCommonMapper.updateByPrimaryKeySelective(friend);
    }

    public void deleteFriend(ScrmPersonalWxFriendsCommon friend) {
        scrmPersonalWxFriendsCommonMapper.deleteByPrimaryKey(friend.getId());
    }

    private static boolean isChangeFriendCommon(ScrmPersonalWxFriendsCommon friendList, ScrmPersonalWxFriendsCommon dbFriend, ScrmPersonalWxUserInfoCommon personalWxUserInfoCommon) {
        boolean changed = false;
        if (friendList == null) {
            return false;
        }
        if (!Objects.equals(friendList.getRemarkName(), dbFriend.getRemarkName())) {
            dbFriend.setRemarkName(friendList.getRemarkName());
            changed = true;
        }
        if (!Objects.equals(friendList.getTagIds(), dbFriend.getTagIds())) {
            dbFriend.setTagIds(friendList.getTagIds());
            changed = true;
        }
        return changed;
    }

    @NotNull
    private static ScrmPersonalWxFriendsCommon buildWxFriendCommon(ScrmPersonalWxFriendsCommon friendList, String bizId, ScrmPersonalWxUserInfoCommon userInfo, ScrmPersonalWxUserInfoCommon friendUserInfo) {

        ScrmPersonalWxFriendsCommon friendsCommon = new ScrmPersonalWxFriendsCommon();
        friendsCommon.setUserId(userInfo.getId());
        friendsCommon.setFriendId(friendUserInfo.getId());
        if (friendList != null) {
            friendsCommon.setRemarkName(friendList.getRemarkName());
            friendsCommon.setTagIds(friendList.getTagIds());
        }
        friendsCommon.setBizId(bizId);
        friendsCommon.setAddTime(new Date());
        friendsCommon.setUpdateTime(new Date());
        return friendsCommon;
    }


    public PagerList<RobotInfoDTO> queryRobotList(RobotListRequest request) {
        if (StringUtils.isBlank(request.getBizId())) {
            return PagerList.empty();
        }
        ScrmPersonalWxRobotInfoCommonExample example = buildQueryRobotExample(request);

        long total = robotInfoCommonMapper.countByExample(example);
        if (total <= 0) {
            return PagerList.empty();
        }
        example.setOffset(request.getOffset());
        example.setRows(request.getPageSize());
        List<ScrmPersonalWxRobotInfoCommon> robotInfoCommons = robotInfoCommonMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(robotInfoCommons)) {
            return PagerList.empty();
        }
        Map<String, ScrmPersonalWxRobotInfoCommon> robotInfoCommonMap = robotInfoCommons.stream().collect(Collectors.toMap(ScrmPersonalWxRobotInfoCommon::getRobotSerialNo, Function.identity(), (r1, r2) -> r1));
        List<String> serialNos = robotInfoCommons.stream().map(ScrmPersonalWxRobotInfoCommon::getRobotSerialNo).distinct().collect(Collectors.toList());
        List<ScrmPersonalWxUserInfoCommon> scrmPersonalWxUserInfoCommons = queryUserInfos(serialNos, request.getBizId());
        List<RobotInfoDTO> result = scrmPersonalWxUserInfoCommons.stream().map(s -> buildRobotInfoDTO(robotInfoCommonMap, s)).filter(Objects::nonNull).collect(Collectors.toList());
        return PagerList.of(total, result);
    }

    @NotNull
    private static ScrmPersonalWxRobotInfoCommonExample buildQueryRobotExample(RobotListRequest request) {
        ScrmPersonalWxRobotInfoCommonExample example = new ScrmPersonalWxRobotInfoCommonExample();
        ScrmPersonalWxRobotInfoCommonExample.Criteria criteria = example.createCriteria();
        criteria.andBizIdEqualTo(request.getBizId()).andStatusEqualTo(PersonalRobotStatusEnum.NORMAL.getCode());
        if (request.getValid() != null) {
            criteria.andValidEqualTo(String.valueOf(request.getValid()));
        }
        if (request.getOnline() != null) {
            criteria.andOnlineEqualTo(String.valueOf(request.getOnline()));
        }
        if (CollectionUtils.isNotEmpty(request.getRobotSerialNos())) {
            criteria.andRobotSerialNoIn(request.getRobotSerialNos());
        }
        return example;
    }

    private RobotInfoDTO buildRobotInfoDTO(Map<String, ScrmPersonalWxRobotInfoCommon> robotInfoCommonMap, ScrmPersonalWxUserInfoCommon s) {
        RobotInfoDTO robotInfo = new RobotInfoDTO();
        robotInfo.setWxId(s.getWxId());
        robotInfo.setAvatar(s.getHeadimgUrl());
        robotInfo.setWxNickname(s.getNickname());
        robotInfo.setWxSerialNo(s.getSerialNo());
        robotInfo.setQr(s.getQr());
        robotInfo.setWxAlias(s.getWxAlias());
        robotInfo.setWxNickname(s.getNickname());
        if (robotInfoCommonMap.containsKey(s.getSerialNo())) {
            ScrmPersonalWxRobotInfoCommon robotInfoCommon = robotInfoCommonMap.get(s.getSerialNo());
            robotInfo.setValid(robotInfoCommon.getValid());
            robotInfo.setOnline(robotInfoCommon.getOnline());
        } else {
            return null;
        }
        return robotInfo;
    }
}
