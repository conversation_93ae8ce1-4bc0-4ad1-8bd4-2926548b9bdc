<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActionAttachmentDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_id" jdbcType="INTEGER" property="contentId" />
    <result column="process_orchestration_id" jdbcType="BIGINT" property="processOrchestrationId" />
    <result column="process_orchestration_version" jdbcType="VARCHAR" property="processOrchestrationVersion" />
    <result column="process_orchestration_node_id" jdbcType="BIGINT" property="processOrchestrationNodeId" />
    <result column="attachment_type_id" jdbcType="INTEGER" property="attachmentTypeId" />
    <result column="attachment_content" jdbcType="VARCHAR" property="attachmentContent" />
    <result column="action_id" jdbcType="INTEGER" property="actionId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, content_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
    attachment_type_id, attachment_content, action_id, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionAttachmentDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_action_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_a_m_process_orchestration_action_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_a_m_process_orchestration_action_attachment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionAttachmentDOExample">
    delete from scrm_a_m_process_orchestration_action_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_action_attachment (content_id, process_orchestration_id, 
      process_orchestration_version, process_orchestration_node_id, 
      attachment_type_id, attachment_content, action_id, 
      update_time)
    values (#{contentId,jdbcType=INTEGER}, #{processOrchestrationId,jdbcType=BIGINT}, 
      #{processOrchestrationVersion,jdbcType=VARCHAR}, #{processOrchestrationNodeId,jdbcType=BIGINT}, 
      #{attachmentTypeId,jdbcType=INTEGER}, #{attachmentContent,jdbcType=VARCHAR}, #{actionId,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_a_m_process_orchestration_action_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        content_id,
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id,
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version,
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id,
      </if>
      <if test="attachmentTypeId != null">
        attachment_type_id,
      </if>
      <if test="attachmentContent != null">
        attachment_content,
      </if>
      <if test="actionId != null">
        action_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        #{contentId,jdbcType=INTEGER},
      </if>
      <if test="processOrchestrationId != null">
        #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="attachmentTypeId != null">
        #{attachmentTypeId,jdbcType=INTEGER},
      </if>
      <if test="attachmentContent != null">
        #{attachmentContent,jdbcType=VARCHAR},
      </if>
      <if test="actionId != null">
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionAttachmentDOExample" resultType="java.lang.Long">
    select count(*) from scrm_a_m_process_orchestration_action_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_a_m_process_orchestration_action_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.contentId != null">
        content_id = #{record.contentId,jdbcType=INTEGER},
      </if>
      <if test="record.processOrchestrationId != null">
        process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="record.processOrchestrationVersion != null">
        process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.processOrchestrationNodeId != null">
        process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="record.attachmentTypeId != null">
        attachment_type_id = #{record.attachmentTypeId,jdbcType=INTEGER},
      </if>
      <if test="record.attachmentContent != null">
        attachment_content = #{record.attachmentContent,jdbcType=VARCHAR},
      </if>
      <if test="record.actionId != null">
        action_id = #{record.actionId,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_a_m_process_orchestration_action_attachment
    set id = #{record.id,jdbcType=BIGINT},
      content_id = #{record.contentId,jdbcType=INTEGER},
      process_orchestration_id = #{record.processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{record.processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{record.processOrchestrationNodeId,jdbcType=BIGINT},
      attachment_type_id = #{record.attachmentTypeId,jdbcType=INTEGER},
      attachment_content = #{record.attachmentContent,jdbcType=VARCHAR},
      action_id = #{record.actionId,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO">
    update scrm_a_m_process_orchestration_action_attachment
    <set>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=INTEGER},
      </if>
      <if test="processOrchestrationId != null">
        process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      </if>
      <if test="processOrchestrationVersion != null">
        process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      </if>
      <if test="processOrchestrationNodeId != null">
        process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      </if>
      <if test="attachmentTypeId != null">
        attachment_type_id = #{attachmentTypeId,jdbcType=INTEGER},
      </if>
      <if test="attachmentContent != null">
        attachment_content = #{attachmentContent,jdbcType=VARCHAR},
      </if>
      <if test="actionId != null">
        action_id = #{actionId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO">
    update scrm_a_m_process_orchestration_action_attachment
    set content_id = #{contentId,jdbcType=INTEGER},
      process_orchestration_id = #{processOrchestrationId,jdbcType=BIGINT},
      process_orchestration_version = #{processOrchestrationVersion,jdbcType=VARCHAR},
      process_orchestration_node_id = #{processOrchestrationNodeId,jdbcType=BIGINT},
      attachment_type_id = #{attachmentTypeId,jdbcType=INTEGER},
      attachment_content = #{attachmentContent,jdbcType=VARCHAR},
      action_id = #{actionId,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_a_m_process_orchestration_action_attachment
    (content_id, process_orchestration_id, process_orchestration_version, process_orchestration_node_id, 
      attachment_type_id, attachment_content, action_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.contentId,jdbcType=INTEGER}, #{item.processOrchestrationId,jdbcType=BIGINT}, 
        #{item.processOrchestrationVersion,jdbcType=VARCHAR}, #{item.processOrchestrationNodeId,jdbcType=BIGINT}, 
        #{item.attachmentTypeId,jdbcType=INTEGER}, #{item.attachmentContent,jdbcType=VARCHAR}, 
        #{item.actionId,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>