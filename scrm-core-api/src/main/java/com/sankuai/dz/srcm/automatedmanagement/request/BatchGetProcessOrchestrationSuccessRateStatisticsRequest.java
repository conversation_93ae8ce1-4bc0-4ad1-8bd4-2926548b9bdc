package com.sankuai.dz.srcm.automatedmanagement.request;

import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/5/11
 */
@Data
public class BatchGetProcessOrchestrationSuccessRateStatisticsRequest implements Serializable {

    /**
     * 业务id 必填
     */
    private String appId;

    /**
     *   字段: process_orchestration_type
     *   说明: 任务类型 选填
     *   @see com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum
     */
    private Integer processOrchestrationType;

    /**
     * 任务状态
     * @see ScrmProcessOrchestrationStatusTypeEnum
     */
    private Integer status;

    /**
     * 创建人 选填
     */
    private String creatorId;

    /**
     * 最后更新人 选填
     */
    private String lastUpdaterId;

    /**
     * 开始生效时间yyyy-MM-dd HH:mm:ss
     */
    private String beginTime;

    /**
     * 结束生效时间yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 指定编排信息
     */
    private List<QueryProcessOrchestrationSuccessRateStatisticsRequest> processOrchestrationList;
}
