package com.sankuai.scrm.core.service.pchat.enums.im;

import lombok.Getter;

/**
 * @Description im 连接类型
 * <AUTHOR>
 * @Create On 2024/1/16 14:29
 * @Version v1.0.0
 */
@Getter
public enum ImRobotOnlineStateType {
    CONNECT("1", "在线"),
    DIS_CONNECT("0", "不在线");

    private final String code;

    private final String desc;

    ImRobotOnlineStateType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImRobotOnlineStateType fromCode(String code) {
        for (ImRobotOnlineStateType enumValue : ImRobotOnlineStateType.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }
}
