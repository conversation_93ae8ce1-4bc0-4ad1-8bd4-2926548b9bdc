package com.sankuai.dz.srcm.group.dynamiccode.dto;

import com.sankuai.dz.srcm.activity.fission.dto.GroupFissionQRCodePageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class QueryQRCodePageInfoResponse implements Serializable {
    private int qrCodePageType;
    private GroupFissionQRCodePageDTO groupFissionQRCodePage;
    private GroupDynamicCodeQrCodeDTO dynamicCodeQrCodePage;
}
