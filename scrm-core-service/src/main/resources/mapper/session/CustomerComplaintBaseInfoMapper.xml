<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.workorder.customer.dal.mapper.CustomerComplaintBaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.workorder.customer.dal.entity.CustomerComplaintBaseInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="work_order_name" jdbcType="VARCHAR" property="workOrderName" />
    <result column="external_user_id" jdbcType="VARCHAR" property="externalUserId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="appeal_first_level_type" jdbcType="VARCHAR" property="appealFirstLevelType" />
    <result column="appeal_second_level_type" jdbcType="VARCHAR" property="appealSecondLevelType" />
    <result column="appeal_third_level_type" jdbcType="VARCHAR" property="appealThirdLevelType" />
    <result column="appeal_time" jdbcType="TIMESTAMP" property="appealTime" />
    <result column="buy_time" jdbcType="TIMESTAMP" property="buyTime" />
    <result column="item" jdbcType="VARCHAR" property="item" />
    <result column="institution" jdbcType="VARCHAR" property="institution" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="chat_record_list" jdbcType="VARCHAR" property="chatRecordList" />
    <result column="image_list" jdbcType="VARCHAR" property="imageList" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, work_order_name, external_user_id, name, phone, order_number, appeal_first_level_type, 
    appeal_second_level_type, appeal_third_level_type, appeal_time, buy_time, item, institution, 
    description, chat_record_list, image_list, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.example.CustomerComplaintBaseInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from customer_complaint_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from customer_complaint_base_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from customer_complaint_base_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.example.CustomerComplaintBaseInfoExample">
    delete from customer_complaint_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.entity.CustomerComplaintBaseInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into customer_complaint_base_info (app_id, work_order_name, external_user_id, 
      name, phone, order_number, 
      appeal_first_level_type, appeal_second_level_type, 
      appeal_third_level_type, appeal_time, buy_time, 
      item, institution, description, 
      chat_record_list, image_list, add_time, 
      update_time)
    values (#{appId,jdbcType=VARCHAR}, #{workOrderName,jdbcType=VARCHAR}, #{externalUserId,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{orderNumber,jdbcType=VARCHAR}, 
      #{appealFirstLevelType,jdbcType=VARCHAR}, #{appealSecondLevelType,jdbcType=VARCHAR}, 
      #{appealThirdLevelType,jdbcType=VARCHAR}, #{appealTime,jdbcType=TIMESTAMP}, #{buyTime,jdbcType=TIMESTAMP}, 
      #{item,jdbcType=VARCHAR}, #{institution,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{chatRecordList,jdbcType=VARCHAR}, #{imageList,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.entity.CustomerComplaintBaseInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into customer_complaint_base_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="workOrderName != null">
        work_order_name,
      </if>
      <if test="externalUserId != null">
        external_user_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="orderNumber != null">
        order_number,
      </if>
      <if test="appealFirstLevelType != null">
        appeal_first_level_type,
      </if>
      <if test="appealSecondLevelType != null">
        appeal_second_level_type,
      </if>
      <if test="appealThirdLevelType != null">
        appeal_third_level_type,
      </if>
      <if test="appealTime != null">
        appeal_time,
      </if>
      <if test="buyTime != null">
        buy_time,
      </if>
      <if test="item != null">
        item,
      </if>
      <if test="institution != null">
        institution,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="chatRecordList != null">
        chat_record_list,
      </if>
      <if test="imageList != null">
        image_list,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="workOrderName != null">
        #{workOrderName,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="appealFirstLevelType != null">
        #{appealFirstLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealSecondLevelType != null">
        #{appealSecondLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealThirdLevelType != null">
        #{appealThirdLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealTime != null">
        #{appealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyTime != null">
        #{buyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item != null">
        #{item,jdbcType=VARCHAR},
      </if>
      <if test="institution != null">
        #{institution,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="chatRecordList != null">
        #{chatRecordList,jdbcType=VARCHAR},
      </if>
      <if test="imageList != null">
        #{imageList,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.example.CustomerComplaintBaseInfoExample" resultType="java.lang.Long">
    select count(*) from customer_complaint_base_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update customer_complaint_base_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.workOrderName != null">
        work_order_name = #{record.workOrderName,jdbcType=VARCHAR},
      </if>
      <if test="record.externalUserId != null">
        external_user_id = #{record.externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNumber != null">
        order_number = #{record.orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.appealFirstLevelType != null">
        appeal_first_level_type = #{record.appealFirstLevelType,jdbcType=VARCHAR},
      </if>
      <if test="record.appealSecondLevelType != null">
        appeal_second_level_type = #{record.appealSecondLevelType,jdbcType=VARCHAR},
      </if>
      <if test="record.appealThirdLevelType != null">
        appeal_third_level_type = #{record.appealThirdLevelType,jdbcType=VARCHAR},
      </if>
      <if test="record.appealTime != null">
        appeal_time = #{record.appealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.buyTime != null">
        buy_time = #{record.buyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.item != null">
        item = #{record.item,jdbcType=VARCHAR},
      </if>
      <if test="record.institution != null">
        institution = #{record.institution,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.chatRecordList != null">
        chat_record_list = #{record.chatRecordList,jdbcType=VARCHAR},
      </if>
      <if test="record.imageList != null">
        image_list = #{record.imageList,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update customer_complaint_base_info
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      work_order_name = #{record.workOrderName,jdbcType=VARCHAR},
      external_user_id = #{record.externalUserId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      order_number = #{record.orderNumber,jdbcType=VARCHAR},
      appeal_first_level_type = #{record.appealFirstLevelType,jdbcType=VARCHAR},
      appeal_second_level_type = #{record.appealSecondLevelType,jdbcType=VARCHAR},
      appeal_third_level_type = #{record.appealThirdLevelType,jdbcType=VARCHAR},
      appeal_time = #{record.appealTime,jdbcType=TIMESTAMP},
      buy_time = #{record.buyTime,jdbcType=TIMESTAMP},
      item = #{record.item,jdbcType=VARCHAR},
      institution = #{record.institution,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      chat_record_list = #{record.chatRecordList,jdbcType=VARCHAR},
      image_list = #{record.imageList,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.entity.CustomerComplaintBaseInfo">
    update customer_complaint_base_info
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="workOrderName != null">
        work_order_name = #{workOrderName,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        external_user_id = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        order_number = #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="appealFirstLevelType != null">
        appeal_first_level_type = #{appealFirstLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealSecondLevelType != null">
        appeal_second_level_type = #{appealSecondLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealThirdLevelType != null">
        appeal_third_level_type = #{appealThirdLevelType,jdbcType=VARCHAR},
      </if>
      <if test="appealTime != null">
        appeal_time = #{appealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyTime != null">
        buy_time = #{buyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item != null">
        item = #{item,jdbcType=VARCHAR},
      </if>
      <if test="institution != null">
        institution = #{institution,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="chatRecordList != null">
        chat_record_list = #{chatRecordList,jdbcType=VARCHAR},
      </if>
      <if test="imageList != null">
        image_list = #{imageList,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.workorder.customer.dal.entity.CustomerComplaintBaseInfo">
    update customer_complaint_base_info
    set app_id = #{appId,jdbcType=VARCHAR},
      work_order_name = #{workOrderName,jdbcType=VARCHAR},
      external_user_id = #{externalUserId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      order_number = #{orderNumber,jdbcType=VARCHAR},
      appeal_first_level_type = #{appealFirstLevelType,jdbcType=VARCHAR},
      appeal_second_level_type = #{appealSecondLevelType,jdbcType=VARCHAR},
      appeal_third_level_type = #{appealThirdLevelType,jdbcType=VARCHAR},
      appeal_time = #{appealTime,jdbcType=TIMESTAMP},
      buy_time = #{buyTime,jdbcType=TIMESTAMP},
      item = #{item,jdbcType=VARCHAR},
      institution = #{institution,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      chat_record_list = #{chatRecordList,jdbcType=VARCHAR},
      image_list = #{imageList,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into customer_complaint_base_info
    (app_id, work_order_name, external_user_id, name, phone, order_number, appeal_first_level_type, 
      appeal_second_level_type, appeal_third_level_type, appeal_time, buy_time, item, 
      institution, description, chat_record_list, image_list, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.workOrderName,jdbcType=VARCHAR}, #{item.externalUserId,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.orderNumber,jdbcType=VARCHAR}, 
        #{item.appealFirstLevelType,jdbcType=VARCHAR}, #{item.appealSecondLevelType,jdbcType=VARCHAR}, 
        #{item.appealThirdLevelType,jdbcType=VARCHAR}, #{item.appealTime,jdbcType=TIMESTAMP}, 
        #{item.buyTime,jdbcType=TIMESTAMP}, #{item.item,jdbcType=VARCHAR}, #{item.institution,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.chatRecordList,jdbcType=VARCHAR}, 
        #{item.imageList,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>