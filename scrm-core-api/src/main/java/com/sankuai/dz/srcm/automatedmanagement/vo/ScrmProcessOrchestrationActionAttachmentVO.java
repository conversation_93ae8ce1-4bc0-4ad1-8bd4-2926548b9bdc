package com.sankuai.dz.srcm.automatedmanagement.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 流程编排行为内容附件
 *
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/16
 */
@Data
public class ScrmProcessOrchestrationActionAttachmentVO implements Serializable {

    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: attachment_type_id
     *   说明: 附件类型
     */
    private Integer attachmentTypeId;

    /**
     *   字段: attachmen_content
     *   说明:
     */
    private String attachmentContent;
}
