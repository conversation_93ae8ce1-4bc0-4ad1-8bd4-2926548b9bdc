package com.sankuai.dz.srcm.coupon.dto;

import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.CouponModuleInfoDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * Next发券预校验结果
 * @Description
 * <AUTHOR>
 * @Create On 2024/12/17 10:54
 * @Version v1.0.0
 */
@Data
public class ValidNextCouponResultDTO implements Serializable {
    /**
     * 是否可领取
     */
    private boolean acquirable;

    /**
     * 是否从未领取过
     */
    private boolean neverClaimed;

    /**
     * 券模块信息
     */
    private CouponModuleInfoDTO couponModuleInfo;;
    /**
     * 优惠金额
     *//*
    private BigDecimal discountAmount;
    *//**
     * 券名称
     *//*
    private String couponGroupName;
    *//**
     * 券批次id
     *//*
    private String couponGroupId;

    *//**
     * 券使用门槛
     *//*
    private BigDecimal priceLimit;

    *//**
     * 使用时间开始时间
     *//*
    private Date validBeginTime;

    *//**
     * 使用时间结束时间
     *//*
    private Date validEndTime;

    *//**
     * 已领券信息
     *//*
    private List<NextCouponInfoDTO> existedCouponInfos;*/
}
