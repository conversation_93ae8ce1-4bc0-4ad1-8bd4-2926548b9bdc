<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.external.contact.dal.mapper.ScrmMobileAddFriendTaskDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ds_task_id" jdbcType="VARCHAR" property="dsTaskId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="add_number" jdbcType="VARCHAR" property="addNumber" />
    <result column="number_type" jdbcType="INTEGER" property="numberType" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="fail_type" jdbcType="INTEGER" property="failType" />
    <result column="ds_task_status" jdbcType="INTEGER" property="dsTaskStatus" />
    <result column="ds_fail_type" jdbcType="INTEGER" property="dsFailType" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="welcome_content" jdbcType="VARCHAR" property="welcomeContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="add_number_md5" jdbcType="CHAR" property="addNumberMd5" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ds_task_id, account_id, app_id, add_number, number_type, task_status, fail_type, 
    ds_task_status, ds_fail_type, execute_time, welcome_content, create_time, update_time, 
    add_number_md5
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from scrm_friend_add_friend_real_time_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from scrm_friend_add_friend_real_time_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from scrm_friend_add_friend_real_time_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample">
    delete from scrm_friend_add_friend_real_time_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_friend_add_friend_real_time_task (ds_task_id, account_id, app_id, 
      add_number, number_type, task_status, 
      fail_type, ds_task_status, ds_fail_type, 
      execute_time, welcome_content, create_time, 
      update_time, add_number_md5)
    values (#{dsTaskId,jdbcType=VARCHAR}, #{accountId,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{addNumber,jdbcType=VARCHAR}, #{numberType,jdbcType=INTEGER}, #{taskStatus,jdbcType=INTEGER}, 
      #{failType,jdbcType=INTEGER}, #{dsTaskStatus,jdbcType=INTEGER}, #{dsFailType,jdbcType=INTEGER}, 
      #{executeTime,jdbcType=TIMESTAMP}, #{welcomeContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{addNumberMd5,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into scrm_friend_add_friend_real_time_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dsTaskId != null">
        ds_task_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="addNumber != null">
        add_number,
      </if>
      <if test="numberType != null">
        number_type,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="failType != null">
        fail_type,
      </if>
      <if test="dsTaskStatus != null">
        ds_task_status,
      </if>
      <if test="dsFailType != null">
        ds_fail_type,
      </if>
      <if test="executeTime != null">
        execute_time,
      </if>
      <if test="welcomeContent != null">
        welcome_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="addNumberMd5 != null">
        add_number_md5,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dsTaskId != null">
        #{dsTaskId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="addNumber != null">
        #{addNumber,jdbcType=VARCHAR},
      </if>
      <if test="numberType != null">
        #{numberType,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="failType != null">
        #{failType,jdbcType=INTEGER},
      </if>
      <if test="dsTaskStatus != null">
        #{dsTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="dsFailType != null">
        #{dsFailType,jdbcType=INTEGER},
      </if>
      <if test="executeTime != null">
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="welcomeContent != null">
        #{welcomeContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addNumberMd5 != null">
        #{addNumberMd5,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample" resultType="java.lang.Long">
    select count(*) from scrm_friend_add_friend_real_time_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update scrm_friend_add_friend_real_time_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dsTaskId != null">
        ds_task_id = #{record.dsTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.addNumber != null">
        add_number = #{record.addNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.numberType != null">
        number_type = #{record.numberType,jdbcType=INTEGER},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.failType != null">
        fail_type = #{record.failType,jdbcType=INTEGER},
      </if>
      <if test="record.dsTaskStatus != null">
        ds_task_status = #{record.dsTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.dsFailType != null">
        ds_fail_type = #{record.dsFailType,jdbcType=INTEGER},
      </if>
      <if test="record.executeTime != null">
        execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.welcomeContent != null">
        welcome_content = #{record.welcomeContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addNumberMd5 != null">
        add_number_md5 = #{record.addNumberMd5,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update scrm_friend_add_friend_real_time_task
    set id = #{record.id,jdbcType=BIGINT},
      ds_task_id = #{record.dsTaskId,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      add_number = #{record.addNumber,jdbcType=VARCHAR},
      number_type = #{record.numberType,jdbcType=INTEGER},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      fail_type = #{record.failType,jdbcType=INTEGER},
      ds_task_status = #{record.dsTaskStatus,jdbcType=INTEGER},
      ds_fail_type = #{record.dsFailType,jdbcType=INTEGER},
      execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      welcome_content = #{record.welcomeContent,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      add_number_md5 = #{record.addNumberMd5,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO">
    update scrm_friend_add_friend_real_time_task
    <set>
      <if test="dsTaskId != null">
        ds_task_id = #{dsTaskId,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="addNumber != null">
        add_number = #{addNumber,jdbcType=VARCHAR},
      </if>
      <if test="numberType != null">
        number_type = #{numberType,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="failType != null">
        fail_type = #{failType,jdbcType=INTEGER},
      </if>
      <if test="dsTaskStatus != null">
        ds_task_status = #{dsTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="dsFailType != null">
        ds_fail_type = #{dsFailType,jdbcType=INTEGER},
      </if>
      <if test="executeTime != null">
        execute_time = #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="welcomeContent != null">
        welcome_content = #{welcomeContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addNumberMd5 != null">
        add_number_md5 = #{addNumberMd5,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO">
    update scrm_friend_add_friend_real_time_task
    set ds_task_id = #{dsTaskId,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      add_number = #{addNumber,jdbcType=VARCHAR},
      number_type = #{numberType,jdbcType=INTEGER},
      task_status = #{taskStatus,jdbcType=INTEGER},
      fail_type = #{failType,jdbcType=INTEGER},
      ds_task_status = #{dsTaskStatus,jdbcType=INTEGER},
      ds_fail_type = #{dsFailType,jdbcType=INTEGER},
      execute_time = #{executeTime,jdbcType=TIMESTAMP},
      welcome_content = #{welcomeContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_number_md5 = #{addNumberMd5,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into scrm_friend_add_friend_real_time_task
    (ds_task_id, account_id, app_id, add_number, number_type, task_status, fail_type, 
      ds_task_status, ds_fail_type, execute_time, welcome_content, create_time, update_time, 
      add_number_md5)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dsTaskId,jdbcType=VARCHAR}, #{item.accountId,jdbcType=VARCHAR}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.addNumber,jdbcType=VARCHAR}, #{item.numberType,jdbcType=INTEGER}, #{item.taskStatus,jdbcType=INTEGER}, 
        #{item.failType,jdbcType=INTEGER}, #{item.dsTaskStatus,jdbcType=INTEGER}, #{item.dsFailType,jdbcType=INTEGER}, 
        #{item.executeTime,jdbcType=TIMESTAMP}, #{item.welcomeContent,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.addNumberMd5,jdbcType=CHAR}
        )
    </foreach>
  </insert>
</mapper>