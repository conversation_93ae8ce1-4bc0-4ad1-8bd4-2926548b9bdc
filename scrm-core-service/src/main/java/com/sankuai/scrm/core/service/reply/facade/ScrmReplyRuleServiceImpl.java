package com.sankuai.scrm.core.service.reply.facade;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.reply.dto.ScrmReplyRuleDTO;
import com.sankuai.dz.srcm.reply.facade.ScrmReplyRuleService;
import com.sankuai.scrm.core.service.reply.domain.ScrmReplyRuleDomain;
import com.sankuai.scrm.core.service.reply.domain.entity.ScrmReplyRuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

@Slf4j
@MdpPigeonServer
public class ScrmReplyRuleServiceImpl implements ScrmReplyRuleService {

    @Resource
    private ScrmReplyRuleDomain scrmReplyRuleDomain;

    @Override
    public RemoteResponse<ScrmReplyRuleDTO> queryScrmRule(String appId) {
        try {
            if (StringUtils.isBlank(appId)) {
                return RemoteResponse.fail("参数异常,appId为空");
            }

            ScrmReplyRuleEntity scrmReplyRuleEntity = scrmReplyRuleDomain.queryReplyRule(appId);
            ScrmReplyRuleDTO scrmReplyRuleDTO = buildScrmReplyRuleDTO(scrmReplyRuleEntity);
            return RemoteResponse.success(scrmReplyRuleDTO);
        } catch (Exception e) {
            log.error("ScrmReplyRuleServiceImpl.queryScrmRule has exception", e);
            return RemoteResponse.fail("系统异常");
        }
    }

    private ScrmReplyRuleDTO buildScrmReplyRuleDTO(ScrmReplyRuleEntity scrmReplyRuleEntity) {
        ScrmReplyRuleDTO scrmReplyRuleDTO = new ScrmReplyRuleDTO();
        scrmReplyRuleDTO.setGroupRestDayOvertime(scrmReplyRuleEntity.getGroupRestDayOvertime());
        scrmReplyRuleDTO.setGroupWorkDayOvertime(scrmReplyRuleEntity.getGroupWorkDayOvertime());
        scrmReplyRuleDTO.setPrivateRestDayOvertime(scrmReplyRuleEntity.getPrivateRestDayOvertime());
        scrmReplyRuleDTO.setPrivateWorkDayOvertime(scrmReplyRuleEntity.getPrivateWorkDayOvertime());
        return scrmReplyRuleDTO;
    }
}
