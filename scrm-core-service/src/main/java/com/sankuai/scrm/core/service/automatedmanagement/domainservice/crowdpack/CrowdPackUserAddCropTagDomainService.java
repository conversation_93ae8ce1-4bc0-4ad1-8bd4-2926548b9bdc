package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.CrowdPackUserAddCorpTagRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAddCorpTagFailLog;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAddCorpTagLog;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAddCorpTagFailLogExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackDetailInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAddCorpTagFailLogMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAddCorpTagLogMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.mq.entity.ScrmPackUserBatchAddCorpTagMessage;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.ScrmPackUserBatchAddCorpTagProducer;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 人群包相关
 *
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/4/22
 */
@Slf4j
@Component
public class CrowdPackUserAddCropTagDomainService {

    @Resource
    private ExtScrmAmCrowdPackDetailInfoDOMapper detailInfoDOMapper;

    @Resource
    private ScrmAmCrowdPackAddCorpTagLogMapper addCorpTagLogMapper;

    @Resource
    private ScrmAmCrowdPackAddCorpTagFailLogMapper addCorpTagFailLogMapper;

    @Autowired
    private ScrmPackUserBatchAddCorpTagProducer scrmPackUserBatchAddCorpTagProducer;

    @Autowired
    private CorpAppConfigRepository appConfigRepository;

    @Resource
    private ExternalContactTagDomainService externalContactTagDomainService;

    public boolean insertPackUserBatchAddCorpTagLogAndSendMsg(CrowdPackUserAddCorpTagRequest request, ScrmCrowdPackDTO scrmCrowdPackDTO) {

        Long detailInfoId = getFirstPackDetailInfoByPackId(request.getPackId());
        if (Objects.isNull(detailInfoId)) {
            log.info("CrowdPackUserAddCropTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg 人群包中不包含用户");
            return false;
        }
        ScrmAmCrowdPackAddCorpTagLog scrmAmCrowdPackAddCorpTagLog = getScrmAmCrowdPackAddCorpTagLog(request, scrmCrowdPackDTO);
        boolean insertResult = insertCrowdPackAddCorpTagLog(scrmAmCrowdPackAddCorpTagLog);
        if (!insertResult) {
            return false;
        }

        sendPackUserBatchAddCorpTagMessage(request.getPackId(), scrmAmCrowdPackAddCorpTagLog.getId(), detailInfoId - 1);
        return true;
    }

    public void handleCrowdPackUserBatchAddCorpTagMessage(String message) {

        log.info("handleCrowdPackUserBatchAddCorpTagMessage message is {}", message);
        if (StringUtils.isBlank(message)) {
            return;
        }
        ScrmPackUserBatchAddCorpTagMessage batchAddCorpTagMessage = JSON.parseObject(message, ScrmPackUserBatchAddCorpTagMessage.class);
        if (Objects.isNull(batchAddCorpTagMessage)) {
            return;
        }

        if (batchAddCorpTagMessage.getPackId() == null) {
            return;
        }
        if (batchAddCorpTagMessage.getPackAddTagLogId() == null) {
            return;
        }
        if (batchAddCorpTagMessage.getCurrentPackDetailId() == null) {
            return;
        }
        crowdPackUserBatchAddCorpTag(batchAddCorpTagMessage);
    }

    public void crowdPackUserBatchAddCorpTag(ScrmPackUserBatchAddCorpTagMessage batchAddCorpTagMessage) {
        try {
            ScrmAmCrowdPackAddCorpTagLog scrmAmCrowdPackAddCorpTagLog = queryCrowdPackAddCorpTagLogById(batchAddCorpTagMessage.getPackAddTagLogId());
            if (Objects.isNull(scrmAmCrowdPackAddCorpTagLog)) {
                return;
            }
            int limit = 100;

            List<ScrmAmCrowdPackAddCorpTagFailLog> scrmAmCrowdPackAddCorpTagFailLogList = queryScrmAmCrowdPackAddCorpTagLogList(batchAddCorpTagMessage.getPackAddTagLogId());
            if (CollectionUtils.isNotEmpty(scrmAmCrowdPackAddCorpTagFailLogList)) {
                limit = limit - scrmAmCrowdPackAddCorpTagFailLogList.size();
            }

            List<ScrmAmCrowdPackDetailInfoDO> needAddTagDetailInfoList = new ArrayList<>();
            if (limit > 0) {
                needAddTagDetailInfoList = getNeedAddTagDetailInfo(batchAddCorpTagMessage.getPackId(), batchAddCorpTagMessage.getCurrentPackDetailId(), limit);
                if (needAddTagDetailInfoList.size() == limit) {
                    ScrmAmCrowdPackDetailInfoDO detailInfoDO = needAddTagDetailInfoList.get(needAddTagDetailInfoList.size() - 1);
                    long currentPackDetailId = detailInfoDO.getId();
                    sendPackUserBatchAddCorpTagMessage(batchAddCorpTagMessage.getPackId(), batchAddCorpTagMessage.getPackAddTagLogId(), currentPackDetailId);
                }
            } else {
                sendPackUserBatchAddCorpTagMessage(batchAddCorpTagMessage.getPackId(), batchAddCorpTagMessage.getPackAddTagLogId(), batchAddCorpTagMessage.getCurrentPackDetailId());
            }

            String appId = scrmAmCrowdPackAddCorpTagLog.getAppId();
            String corpId = appConfigRepository.getCorpIdByAppId(appId);
            if (StringUtils.isBlank(corpId)) {
                return;
            }

            //开始并发处理打标签操作
            List<CompletableFuture<ExternalContactTagDomainService.AddCorpTagResult>> futureList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(needAddTagDetailInfoList)) {
                for (ScrmAmCrowdPackDetailInfoDO detailInfoDO : needAddTagDetailInfoList) {
                    String unionId = detailInfoDO.getExternalUserWxUnionId();
                    String tagId = scrmAmCrowdPackAddCorpTagLog.getCorpTagId();
                    CompletableFuture<ExternalContactTagDomainService.AddCorpTagResult> future = CompletableFuture.supplyAsync(() -> addCorpTag(appId, corpId, unionId, tagId), ThreadPoolUtils.SCRM_BATCH_ADD_CORP_TAG_EXECUTOR);
                    futureList.add(future);
                }
            }
            if (CollectionUtils.isNotEmpty(scrmAmCrowdPackAddCorpTagFailLogList)) {
                for (ScrmAmCrowdPackAddCorpTagFailLog failLog : scrmAmCrowdPackAddCorpTagFailLogList) {
                    String unionId = failLog.getExternalUserId();
                    String tagId = scrmAmCrowdPackAddCorpTagLog.getCorpTagId();
                    CompletableFuture<ExternalContactTagDomainService.AddCorpTagResult> future = CompletableFuture.supplyAsync(() -> addCorpTag(appId, corpId, unionId, tagId), ThreadPoolUtils.SCRM_BATCH_ADD_CORP_TAG_EXECUTOR);
                    futureList.add(future);
                }
            }

            for (CompletableFuture<ExternalContactTagDomainService.AddCorpTagResult> future : futureList) {
                ExternalContactTagDomainService.AddCorpTagResult addCorpTagResult = future.join();
                if (!addCorpTagResult.isResult()) {
                    //写入失败记录表
                    insertOrUpdateAddCorpTagFailLog(scrmAmCrowdPackAddCorpTagLog.getId(), addCorpTagResult.getUnionId(), scrmAmCrowdPackAddCorpTagLog.getAppId());
                }
            }

        } catch (Exception e) {
            log.error("crowdPackUserBatchAddCorpTag error", e);
        }
    }

    private ExternalContactTagDomainService.AddCorpTagResult addCorpTag(String appId, String corpId, String unionId, String tagId) {
        return externalContactTagDomainService.addCorpTag(appId, corpId, unionId, tagId);
    }

    private void insertOrUpdateAddCorpTagFailLog(long crowdPackAddCorpTagLogId, String externalUserId, String appId) {
        ScrmAmCrowdPackAddCorpTagFailLogExample example = new ScrmAmCrowdPackAddCorpTagFailLogExample();
        example.createCriteria().
                andCrowdPackAddCorpTagLogIdEqualTo(crowdPackAddCorpTagLogId).
                andExternalUserIdEqualTo(externalUserId);
        List<ScrmAmCrowdPackAddCorpTagFailLog> failLogList = addCorpTagFailLogMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(failLogList)) {
            ScrmAmCrowdPackAddCorpTagFailLog tagFailLog = failLogList.get(0);
            tagFailLog.setExecutedSize(tagFailLog.getExecutedSize() + 1);
            addCorpTagFailLogMapper.updateByPrimaryKey(tagFailLog);
        } else {
            ScrmAmCrowdPackAddCorpTagFailLog failLog = new ScrmAmCrowdPackAddCorpTagFailLog();
            failLog.setCrowdPackAddCorpTagLogId(crowdPackAddCorpTagLogId);
            failLog.setExternalUserId(externalUserId);
            failLog.setExecutedSize(1);
            failLog.setAppId(appId);
            addCorpTagFailLogMapper.insert(failLog);
        }
    }

    private ScrmAmCrowdPackAddCorpTagLog queryCrowdPackAddCorpTagLogById(long packAddTagLogId) {
        return addCorpTagLogMapper.selectByPrimaryKey(packAddTagLogId);
    }

    private List<ScrmAmCrowdPackAddCorpTagFailLog> queryScrmAmCrowdPackAddCorpTagLogList(long crowdPackAddCorpTagLogId) {
        int executedSize = 2;
        ScrmAmCrowdPackAddCorpTagFailLogExample example = new ScrmAmCrowdPackAddCorpTagFailLogExample();
        example.createCriteria().
                andCrowdPackAddCorpTagLogIdEqualTo(crowdPackAddCorpTagLogId).
                andExecutedSizeLessThan(executedSize);
        return addCorpTagFailLogMapper.selectByExample(example);
    }

    private ScrmAmCrowdPackAddCorpTagLog getScrmAmCrowdPackAddCorpTagLog(CrowdPackUserAddCorpTagRequest request, ScrmCrowdPackDTO scrmCrowdPackDTO) {
        ScrmAmCrowdPackAddCorpTagLog scrmAmCrowdPackAddCorpTagLog = new ScrmAmCrowdPackAddCorpTagLog();
        scrmAmCrowdPackAddCorpTagLog.setCorpTagId(request.getTagId());
        scrmAmCrowdPackAddCorpTagLog.setCorpTagGroupId(request.getTagGroupId());
        scrmAmCrowdPackAddCorpTagLog.setPackId(request.getPackId());
        scrmAmCrowdPackAddCorpTagLog.setAppId(request.getAppId());
        scrmAmCrowdPackAddCorpTagLog.setMisId(request.getMisId());
        scrmAmCrowdPackAddCorpTagLog.setExecutedUserCount(0L);
        scrmAmCrowdPackAddCorpTagLog.setPackUserCount(scrmCrowdPackDTO.getCrowdCount());
        return scrmAmCrowdPackAddCorpTagLog;
    }

    public boolean insertCrowdPackAddCorpTagLog(ScrmAmCrowdPackAddCorpTagLog crowdPackAddCorpTagLog) {
        return addCorpTagLogMapper.insert(crowdPackAddCorpTagLog) > 0;
    }

    private Long getFirstPackDetailInfoByPackId(Long packId) {
        ScrmAmCrowdPackDetailInfoDOExample example = new ScrmAmCrowdPackDetailInfoDOExample();
        example.createCriteria().andPackIdEqualTo(packId);
        example.setOrderByClause("id asc");
        example.setOffset(0);
        example.setRows(20);
        List<Long> detailIdList = detailInfoDOMapper.getPackDetailInfoByPackId(example);
        if (CollectionUtils.isEmpty(detailIdList)) {
            return null;
        }
        return detailIdList.get(0);
    }

    private List<ScrmAmCrowdPackDetailInfoDO> getNeedAddTagDetailInfo(Long packId, Long currentPackDetailId, Integer limit) {
        ScrmAmCrowdPackDetailInfoDOExample example = new ScrmAmCrowdPackDetailInfoDOExample();
        example.createCriteria().andPackIdEqualTo(packId).andIdGreaterThan(currentPackDetailId);
        example.setOrderByClause("id asc");
        example.setOffset(0);
        example.setRows(limit);
        return detailInfoDOMapper.selectByExample(example);
    }

    private void sendPackUserBatchAddCorpTagMessage(long packId, long packAddTagLogId, long currentPackDetailId) {
        ScrmPackUserBatchAddCorpTagMessage batchAddCorpTagMessage = new ScrmPackUserBatchAddCorpTagMessage();
        batchAddCorpTagMessage.setPackId(packId);
        batchAddCorpTagMessage.setPackAddTagLogId(packAddTagLogId);
        batchAddCorpTagMessage.setCurrentPackDetailId(currentPackDetailId);
        scrmPackUserBatchAddCorpTagProducer.sendAsyncDelayMessage(batchAddCorpTagMessage, 5 * 1000L);
    }

}
