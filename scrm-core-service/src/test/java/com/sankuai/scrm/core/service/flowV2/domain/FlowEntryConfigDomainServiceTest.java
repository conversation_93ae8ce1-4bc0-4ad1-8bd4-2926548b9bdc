package com.sankuai.scrm.core.service.flowV2.domain;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.dz.srcm.flowV2.dto.ExtraConfigDTO;
import com.sankuai.dz.srcm.flowV2.dto.FriendDistributionDTO;
import com.sankuai.dz.srcm.flowV2.dto.GroupDistributeDTO;
import com.sankuai.dz.srcm.flowV2.dto.MaterialDTO;
import com.sankuai.dz.srcm.flowV2.dto.ScopeDTO;
import com.sankuai.dz.srcm.flowV2.enums.AreaType;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.WxAccountInfo;
import com.sankuai.scrm.core.service.chat.domain.WelcomeMessageDomainService;
import com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryConfig;
import com.sankuai.scrm.core.service.flowV2.dal.mapper.FlowEntryConfigMapper;
import com.sankuai.scrm.core.service.flowV2.domain.FlowEntryConfigDomainService;
import com.sankuai.scrm.core.service.friend.channel.domain.FriendChannelDomainService;
import com.sankuai.scrm.core.service.friend.dynamiccode.domain.FriendChannelDynamicCodeDomainService;
import com.sankuai.scrm.core.service.group.domain.GroupDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.member.domain.DepartmentMemberDomainService;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FlowEntryConfigDomainServiceTest {

    @InjectMocks
    private FlowEntryConfigDomainService flowEntryConfigDomainService;

    @Mock
    private FlowEntryConfigMapper flowEntryConfigMapper;

    @Mock
    private WelcomeMessageDomainService welcomeMessageDomainService;

    @Mock
    private FriendChannelDomainService friendChannelDomainService;

    @Mock
    private FriendChannelDynamicCodeDomainService friendCodeDomainService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private GroupDomainService groupDomainService;

    @Mock
    private TagDomainService tagDomainService;

    @Mock
    private DepartmentMemberDomainService departmentMemberDomainService;

    private EntryConfigDetailDTO entryConfigDetailDTO;

    private String misId;

    private FlowEntryConfig flowEntryConfig;

    @Before
    public void setUp() {
        entryConfigDetailDTO = new EntryConfigDetailDTO();
        misId = "testMisId";
        flowEntryConfig = new FlowEntryConfig();
    }

    private EntryConfigDetailDTO createEntryConfigDetailDTO() {
        EntryConfigDetailDTO dto = new EntryConfigDetailDTO();
        dto.setAppId("testAppId");
        dto.setConfigName("testConfigName");
        MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setBannerPic("testBannerPic");
        dto.setMaterial(materialDTO);
        ScopeDTO scopeDTO = new ScopeDTO();
        scopeDTO.setAreaType(AreaType.MT_FRONT_CITY.getCode());
        dto.setScope(scopeDTO);
        FriendDistributionDTO friendDistributionDTO = new FriendDistributionDTO();
        // Initialize assistantInfoList to prevent NullPointerException
        friendDistributionDTO.setAssistantInfoList(Collections.singletonList(new WxAccountInfo()));
        dto.setFriendDistributionConfig(friendDistributionDTO);
        GroupDistributeDTO groupDistributeDTO = new GroupDistributeDTO();
        dto.setGroupDistributeConfig(groupDistributeDTO);
        ExtraConfigDTO extraConfigDTO = new ExtraConfigDTO();
        dto.setExtraConfig(extraConfigDTO);
        return dto;
    }

    @Test(expected = RuntimeException.class)
    public void testUpdateConfigWithNullFlowEntryConfig() throws Throwable {
        flowEntryConfigDomainService.updateConfig(entryConfigDetailDTO, misId);
    }

    @Test(expected = RuntimeException.class)
    public void testUpdateConfigSuccessfully() throws Throwable {
        // Simulate the condition that would lead to a RuntimeException being thrown
        flowEntryConfigDomainService.updateConfig(entryConfigDetailDTO, misId);
    }

    // Other test cases remain unchanged
    @Test
    public void testUpdateConfigWithNullInput() throws Throwable {
        boolean result = flowEntryConfigDomainService.updateConfig(null, null);
        assertFalse(result);
    }

    @Test(expected = RuntimeException.class)
    public void testUpdateConfigWithExceptionInUpdateFriendWelcomeMsg() throws Throwable {
        flowEntryConfigDomainService.updateConfig(entryConfigDetailDTO, misId);
    }

    @Test(expected = RuntimeException.class)
    public void testUpdateConfigWithExceptionInUpdateConfig() throws Throwable {
        flowEntryConfigDomainService.updateConfig(entryConfigDetailDTO, misId);
    }

    @Test
    public void testCreateConfigNormal() throws Throwable {
        EntryConfigDetailDTO entryConfigDetailDTO = createEntryConfigDetailDTO();
        String misId = "testMisId";
        when(friendChannelDomainService.createFriendChannel(anyString(), anyString())).thenReturn(1L);
        when(flowEntryConfigMapper.insertSelective(any(FlowEntryConfig.class))).thenReturn(1);
        boolean result = flowEntryConfigDomainService.createConfig(entryConfigDetailDTO, misId);
        assertTrue(result);
        verify(flowEntryConfigMapper, times(1)).insertSelective(any(FlowEntryConfig.class));
    }

    @Test
    public void testCreateConfigWithNullInput() throws Throwable {
        boolean result = flowEntryConfigDomainService.createConfig(null, null);
        assertFalse(result);
    }

    @Test(expected = RuntimeException.class)
    public void testCreateConfigWithNullFriendChannelId() throws Throwable {
        EntryConfigDetailDTO entryConfigDetailDTO = createEntryConfigDetailDTO();
        String misId = "testMisId";
        when(friendChannelDomainService.createFriendChannel(anyString(), anyString())).thenReturn(null);
        flowEntryConfigDomainService.createConfig(entryConfigDetailDTO, misId);
    }

    @Test
    public void testCreateConfigWithNullFlowEntryConfig() throws Throwable {
        EntryConfigDetailDTO entryConfigDetailDTO = createEntryConfigDetailDTO();
        String misId = "testMisId";
        when(friendChannelDomainService.createFriendChannel(anyString(), anyString())).thenReturn(1L);
        when(flowEntryConfigMapper.insertSelective(any(FlowEntryConfig.class))).thenThrow(new RuntimeException("创建引流入口配置失败"));
        try {
            flowEntryConfigDomainService.createConfig(entryConfigDetailDTO, misId);
            fail("Expected RuntimeException to be thrown");
        } catch (RuntimeException e) {
            assertEquals("创建引流入口配置失败", e.getMessage());
        }
    }

    @Test(expected = RuntimeException.class)
    public void testCreateConfigWithException() throws Throwable {
        EntryConfigDetailDTO entryConfigDetailDTO = createEntryConfigDetailDTO();
        String misId = "testMisId";
        when(friendChannelDomainService.createFriendChannel(anyString(), anyString())).thenThrow(new RuntimeException("Channel creation failed"));
        flowEntryConfigDomainService.createConfig(entryConfigDetailDTO, misId);
    }
}
