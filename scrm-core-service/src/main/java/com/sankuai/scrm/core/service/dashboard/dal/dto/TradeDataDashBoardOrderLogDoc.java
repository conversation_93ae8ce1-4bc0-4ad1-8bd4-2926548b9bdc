package com.sankuai.scrm.core.service.dashboard.dal.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TradeDataDashBoardOrderLogDoc {
    /**
     * id由orderId+unionId拼接
     */
    private String id;
    private String orderId;
    private String corpId;
    @JSONField(format = "yyyy-MM-dd")
    private Date versionTime;
    private String unionId;
    private Long mtUserId;
    private String eventCode;
    private Long eventTime;
    private String totalAmount;
    private String actualAmount;
    private Integer platform;
    /**
     * 标识用户是否符合宽口径的统计逻辑：统计客户活跃或新增后7个自然日内的交易用户数
     */
    private Boolean isWideCaliber;
    /**
     * 标识用户是否符合窄口径的统计逻辑：通过系统推送的供给、券、会场，且在目标设置时间内完成交易
     */
    private Boolean isNarrowCaliber;


    public static String concatId(String orderId, String unionId) {
        return String.format("%s-%s", orderId, unionId);
    }


}
