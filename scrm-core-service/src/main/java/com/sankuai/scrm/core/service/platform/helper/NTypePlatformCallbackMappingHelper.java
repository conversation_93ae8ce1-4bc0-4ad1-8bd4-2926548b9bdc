package com.sankuai.scrm.core.service.platform.callback;

import cn.hutool.core.map.MapUtil;
import com.sankuai.scrm.core.service.platform.anno.NTypePlatformCallbackMappingAnno;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Description 探鲸商家后台callback接口处理映射服务
 * <AUTHOR>
 * @Create On 2023/11/5 17:04
 * @Version v1.0.0
 */
@Service
@Slf4j
public class NTypePlatformCallbackMapping implements SmartInitializingSingleton, ApplicationContextAware {

    private ApplicationContext applicationContext;
    private Map<Integer, Processor> cache = new ConcurrentHashMap<>();
    AtomicBoolean initFlag = new AtomicBoolean(false);

    /**
     * 根据ntype获取消息处理方法
     *
     * @param ntype
     * @return
     */
    public Processor getMethod(Integer ntype) {
        init();
        return cache.computeIfAbsent(ntype, integer -> new EmptyProcessor());
    }

    @Override
    public void afterSingletonsInstantiated() {
        init();
    }

    private void init() {
        if (initFlag.get()) {
            return;
        }
        Map<String, PlatformCallbackService> platformCallbackServiceMap = applicationContext.getBeansOfType(PlatformCallbackService.class);
        if (MapUtil.isEmpty(platformCallbackServiceMap)) {
            return;
        }
        platformCallbackServiceMap.entrySet().forEach(entry -> {
            Object value = entry.getValue();

            Method[] methods;
            if (AopUtils.isAopProxy(value)) {
                Class<?> targetClass = AopUtils.getTargetClass(value);
                methods = targetClass.getMethods();
            } else {
                methods = value.getClass().getMethods();
            }
            for (Method method : methods) {
                NTypePlatformCallbackMappingAnno annotation = findAnnotation(method);
                if (annotation != null) {
                    cache.put(annotation.nType(), new Processor(entry.getValue(), method, annotation.desc()));
                }
            }

        });
        initFlag.compareAndSet(false, true);
    }

    private NTypePlatformCallbackMappingAnno findAnnotation(Method method) {
        NTypePlatformCallbackMappingAnno annotation = method.getAnnotation(NTypePlatformCallbackMappingAnno.class);
        if (annotation != null) {
            return annotation;
        }
        Class[] interfaces = method.getDeclaringClass().getInterfaces();
        if (interfaces == null || interfaces.length == 0) {
            return null;
        }
        for (Class pcls : interfaces) {
            try {
                Method declaredMethod = pcls.getDeclaredMethod(method.getName(), method.getParameterTypes());
                if (declaredMethod != null) {
                    annotation = declaredMethod.getAnnotation(NTypePlatformCallbackMappingAnno.class);
                    if (annotation != null) {
                        return annotation;
                    }
                }

            } catch (NoSuchMethodException e) {
                log.debug(e.getMessage(), e);
            }
        }
        return null;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static class EmptyProcessor extends Processor {

    }

    public static class Processor {

        private transient Object bean;

        private transient Method method;

        private String beanName;

        private String methodName;

        private String methodDesc;

        public Processor() {
        }

        public Processor(Object bean, Method method,String methodDesc) {
            this.bean = bean;
            this.method = method;
            this.beanName = bean.getClass() != null ? bean.getClass().getName() : "";
            this.methodName = method != null ? method.getName() : "";
            this.methodDesc=methodDesc;
        }

        public Object getBean() {
            return bean;
        }

        public void setBean(Object bean) {
            this.bean = bean;
        }

        public Method getMethod() {
            return method;
        }

        public void setMethod(Method method) {
            this.method = method;
        }

        public String getBeanName() {
            return beanName;
        }

        public void setBeanName(String beanName) {
            this.beanName = beanName;
        }

        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getMethodDesc() {
            return methodDesc;
        }
        public String toJsonStr() {
            return String.format("{\"beanName\":\"%s\",\"methodName\":\"%s\",\"methodDesc\":\"%s\"}", beanName, methodName,methodDesc);
        }

    }
}
