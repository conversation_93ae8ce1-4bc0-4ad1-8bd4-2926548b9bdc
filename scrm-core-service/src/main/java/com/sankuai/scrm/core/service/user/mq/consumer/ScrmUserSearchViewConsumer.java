package com.sankuai.scrm.core.service.user.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserSearchView;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;


@Slf4j
@Component
public class ScrmUserSearchViewConsumer implements InitializingBean, DisposableBean {
    @Autowired
    private PorosRestHighLevelClient porosRestHighLevelClient;

    private ConsumeStatus recvMessage(MafkaMessage<String> message, MessagetContext messagetContext) {
        try {
            log.debug("Received user search view message: {}", message.getBody());
            if(message == null || message.getBody() == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 解析消息
            String messageBody = message.getBody();

            UserSearchView userSearchView = JSONObject.parseObject(messageBody, UserSearchView.class);

            if (userSearchView == null) {
                log.warn("Failed to parse user search view message, skip processing");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            insertToES(userSearchView);

            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (Exception e) {
            log.error("user search view message exception", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    private void insertToES(UserSearchView userSearchView) {
        try {
            if (userSearchView == null) {
                return;
            }
            Map<String, Object> document = buildESDocument(userSearchView);
            String documentId = generateDocumentId(userSearchView);
            // 创建索引请求
            IndexRequest indexRequest = new IndexRequest("scrm_user_search_view")
                    .id(documentId)
                    .source(document, XContentType.JSON);
            // 执行插入
            IndexResponse indexResponse = porosRestHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);

            log.info("Successfully inserted UserSearchView to ES: userId={}, docId={}",
                    userSearchView.getUserId(), indexResponse.getId());
        } catch (IOException e) {
            log.error("Failed to insert UserSearchView to ES: userId={}", userSearchView.getUserId(), e);
            throw new RuntimeException("ES插入失败", e);
        }
    }

    private String generateDocumentId(UserSearchView userSearchView) {
        // 使用userId, appId 和BizlnBuCode组合作为文档ID，确保唯一性
        String platform = "mt";
        if (userSearchView.getCChain() == 0) {
            platform = "dp";
        }
        // 使用日期 + userId + appId + BizlnBuCode组合作为文档ID，确保唯一性
        return "userSearchView_" + platform + "_" + userSearchView.getUserId() + "_" + userSearchView.getAppId() + "_" + userSearchView.getBizlnBuCode();
    }
    
    private Map<String, Object> buildESDocument(UserSearchView userSearchView) {
        Map<String, Object> document = new HashMap<>();
        
        // 分区和统计相关
        document.put("partitionDate", userSearchView.getPartitionDate());
        document.put("calDim", userSearchView.getCalDim());
        document.put("bizlnBuCode", userSearchView.getBizlnBuCode());
        document.put("sourceInfo", userSearchView.getSourceInfo());
        document.put("isDirect", userSearchView.getIsDirect());
        document.put("itemType", userSearchView.getItemType());
        document.put("cChain", userSearchView.getCChain());
        document.put("userId", userSearchView.getUserId());
        
        // 1天搜索数据
        document.put("searchKeywordCnt1d", userSearchView.getSearchKeywordCnt1d());
        document.put("clickQv1d", userSearchView.getClickQv1d());
        document.put("qv1d", userSearchView.getQv1d());
        document.put("clickItemCnt1d", userSearchView.getClickItemCnt1d());
        document.put("viewItemCnt1d", userSearchView.getViewItemCnt1d());
        
        // 3天搜索数据
        document.put("searchKeywordCnt3d", userSearchView.getSearchKeywordCnt3d());
        document.put("clickQv3d", userSearchView.getClickQv3d());
        document.put("qv3d", userSearchView.getQv3d());
        document.put("clickItemCnt3d", userSearchView.getClickItemCnt3d());
        document.put("viewItemCnt3d", userSearchView.getViewItemCnt3d());
        
        // 5天搜索数据
        document.put("searchKeywordCnt5d", userSearchView.getSearchKeywordCnt5d());
        document.put("clickQv5d", userSearchView.getClickQv5d());
        document.put("qv5d", userSearchView.getQv5d());
        document.put("clickItemCnt5d", userSearchView.getClickItemCnt5d());
        document.put("viewItemCnt5d", userSearchView.getViewItemCnt5d());
        
        // 7天搜索数据
        document.put("searchKeywordCnt7d", userSearchView.getSearchKeywordCnt7d());
        document.put("clickQv7d", userSearchView.getClickQv7d());
        document.put("qv7d", userSearchView.getQv7d());
        document.put("clickItemCnt7d", userSearchView.getClickItemCnt7d());
        document.put("viewItemCnt7d", userSearchView.getViewItemCnt7d());
        
        // 15天搜索数据
        document.put("searchKeywordCnt15d", userSearchView.getSearchKeywordCnt15d());
        document.put("clickQv15d", userSearchView.getClickQv15d());
        document.put("qv15d", userSearchView.getQv15d());
        document.put("clickItemCnt15d", userSearchView.getClickItemCnt15d());
        document.put("viewItemCnt15d", userSearchView.getViewItemCnt15d());
        
        // 30天搜索数据
        document.put("searchKeywordCnt30d", userSearchView.getSearchKeywordCnt30d());
        document.put("clickQv30d", userSearchView.getClickQv30d());
        document.put("qv30d", userSearchView.getQv30d());
        document.put("clickItemCnt30d", userSearchView.getClickItemCnt30d());
        document.put("viewItemCnt30d", userSearchView.getViewItemCnt30d());
        
        // 60天搜索数据
        document.put("searchKeywordCnt60d", userSearchView.getSearchKeywordCnt60d());
        document.put("clickQv60d", userSearchView.getClickQv60d());
        document.put("qv60d", userSearchView.getQv60d());
        document.put("clickItemCnt60d", userSearchView.getClickItemCnt60d());
        document.put("viewItemCnt60d", userSearchView.getViewItemCnt60d());
        
        // 90天搜索数据
        document.put("searchKeywordCnt90d", userSearchView.getSearchKeywordCnt90d());
        document.put("clickQv90d", userSearchView.getClickQv90d());
        document.put("qv90d", userSearchView.getQv90d());
        document.put("clickItemCnt90d", userSearchView.getClickItemCnt90d());
        document.put("viewItemCnt90d", userSearchView.getViewItemCnt90d());
        
        // 180天搜索数据
        document.put("searchKeywordCnt180d", userSearchView.getSearchKeywordCnt180d());
        document.put("clickQv180d", userSearchView.getClickQv180d());
        document.put("qv180d", userSearchView.getQv180d());
        document.put("clickItemCnt180d", userSearchView.getClickItemCnt180d());
        document.put("viewItemCnt180d", userSearchView.getViewItemCnt180d());
        
        // 360天搜索数据
        document.put("searchKeywordCnt360d", userSearchView.getSearchKeywordCnt360d());
        document.put("clickQv360d", userSearchView.getClickQv360d());
        document.put("qv360d", userSearchView.getQv360d());
        document.put("clickItemCnt360d", userSearchView.getClickItemCnt360d());
        document.put("viewItemCnt360d", userSearchView.getViewItemCnt360d());
        
        // 最后搜索信息
        document.put("lastSearchDate", userSearchView.getLastSearchDate());
        document.put("lastSearchToNowDays", userSearchView.getLastSearchToNowDays());
        document.put("lastdaySearchKeywordCnt", userSearchView.getLastdaySearchKeywordCnt());
        document.put("lastdayClickQv", userSearchView.getLastdayClickQv());
        document.put("lastdayQv", userSearchView.getLastdayQv());
        document.put("lastdayClickItemCnt", userSearchView.getLastdayClickItemCnt());
        document.put("lastdayViewItemCnt", userSearchView.getLastdayViewItemCnt());
        
        // 业务线信息
        document.put("appId", userSearchView.getAppId());

        return document;
    }

    /**
     * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建.
     * */
    private static IConsumerProcessor consumer;
    @Override
    public void destroy() throws Exception {
        if (consumer != null) {
            consumer.close();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.user.search.ss.view.msg.consumer");

        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "scrm.user.search.ss.view");

        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumer.recvMessageWithParallel(String.class, this::recvMessage);
    }
}
