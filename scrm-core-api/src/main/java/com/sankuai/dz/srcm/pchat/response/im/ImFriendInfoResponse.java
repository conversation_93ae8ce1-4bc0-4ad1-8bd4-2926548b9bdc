package com.sankuai.dz.srcm.pchat.response.im;

import com.sankuai.dz.srcm.pchat.dto.ConsultantInfoDTO;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupInfoResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImFriendInfoResponse implements Serializable {
    private Long friendId;
    private Boolean isStickie;
    /**
     * 微信昵称
     */
    private String wxNickname;

    /**
     * 序列号
     */
    private String wxSerialNo;

    /**
     * 微信id
     */
    private String wxId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 微信号
     */
    private String wxAlias;

    /**
     * 群成员备注名
     */
    private String groupRemark;
    /**
     * 群成员昵称
     */
    private String groupRemarkName;

    /**
     * 机器人给好友的备注名
     */
    private String friendRemarkName;

    private Boolean isFriend;

    /**
     * 咨询师列表
     */
    private List<ConsultantInfoDTO> consultantList;

    /**
     * 共同存在的群
     */
    private List<GroupInfoResponse> shareGoupList;

    /**
     * 群成员状态 0 在群, 1 不在群,  2 已踢出群
     */
    private String memberStatus;


}
