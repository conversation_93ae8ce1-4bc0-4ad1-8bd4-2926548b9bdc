package com.sankuai.scrm.core.service.user.domain;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveUserBindTaskRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveUserIntentionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/8/7 16:25
 * @Version v1.0.0
 */

@Slf4j
@Service
public class PrivateUerDataCommonHandleDomainService {

    @Resource
    private PrivateLiveUserIntentionService privateLiveUserIntentionService;
    @Resource
    private PrivateLiveUserIntentionService newPrivateLiveUserIntentionService;
    @MdpConfig("consultant.service.migration.switch:false")
    private Boolean useNewAppkey;

    @Resource
    private PrivateLiveConsultantVerifyService privateLiveConsultantVerifyService;
    @Resource
    private PrivateLiveConsultantVerifyService newPrivateLiveConsultantVerifyService;

    /**
     * 获取用户在某直播下的咨询师
     *
     * @param userId
     * @param unionId
     * @param projectId
     * @return
     */
    public Long getConsultantTaskId(Long userId, String unionId, String projectId) {
        try {
            PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
            request.setLiveId(projectId);
            request.setMtUserId(userId);
            RemoteResponse<Long> remoteResponse = useNewAppkey ? newPrivateLiveUserIntentionService.queryUserBindTask(request) : privateLiveUserIntentionService.queryUserBindTask(request);
            log.info("getConsultantTaskId result is {}", remoteResponse);
            if (remoteResponse == null || remoteResponse.getData() == null) {
                return 0l;
            }
            return remoteResponse.getData();
        } catch (Exception e) {
            log.error("getConsultantTaskId has exception", e);
            return 0l;
        }
    }

    /**
     * 判断当前用户是否是咨询师
     *
     * @param unionId
     * @param consultantTaskId
     * @return
     */
    public boolean isConsultant(String unionId, Long consultantTaskId) {
        try {
            RemoteResponse<Boolean> remoteResponse = useNewAppkey ? newPrivateLiveConsultantVerifyService.checkByUnionIdAndTaskId(unionId, consultantTaskId) : privateLiveConsultantVerifyService.checkByUnionIdAndTaskId(unionId, consultantTaskId);
            log.info("isConsultant result is {}", remoteResponse);
            if (remoteResponse == null || remoteResponse.getData() == null) {
                return false;
            }
            return remoteResponse.getData();
        } catch (Exception e) {
            log.error("isConsultant has exception", e);
            return false;
        }
    }
}
