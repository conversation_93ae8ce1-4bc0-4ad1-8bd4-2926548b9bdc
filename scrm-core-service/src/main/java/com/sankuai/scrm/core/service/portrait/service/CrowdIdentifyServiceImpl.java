package com.sankuai.scrm.core.service.portrait.service;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.data.ups.thrift.LabelData;
import com.meituan.data.ups.thrift.QueryResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.portrait.service.CrowdIdentifyService;
import com.sankuai.persona.common.ResponseStatus;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.UserProfileAclService;
import com.sankuai.scrm.core.service.portrait.constant.LabelIdConstant;
import com.sankuai.scrm.core.service.portrait.enums.TenCrowdType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

@Slf4j
@MdpPigeonServer
public class CrowdIdentifyServiceImpl implements CrowdIdentifyService {

    @Autowired
    private UserProfileAclService userProfileAclService;

    @Autowired
    private MtUserCenterAclService mtUserCenterAclService;

    @Override
    public RemoteResponse<Boolean> identifyCollegeStudent(String token) {
        if (StringUtils.isEmpty(token)) {
            log.error("[CrowdIdentifyService][identifyCollegeStudent] token is empty");
            return RemoteResponse.fail("Token为空");
        }
        long mtUserId = mtUserCenterAclService.queryUserIdByToken(token);
        if (mtUserId == 0) {
            log.error("[CrowdIdentifyService][identifyCollegeStudent] mtUserId=0");
            return RemoteResponse.fail("未查询到美团userId");
        }
        QueryResponse response = userProfileAclService.queryByLabelId(true, Lists.newArrayList(mtUserId),
                Lists.newArrayList(LabelIdConstant.MT_COLLEGE_STUDENT_LABEL_ID, LabelIdConstant.MT_TEN_CROWD_LABEL_ID));
        log.info("UserProfileAclService.queryByLabelId response:{}", response);
        if (response == null || !ResponseStatus.SUCCESS.equals(response.getStatus()) || CollectionUtils.isEmpty(response.getValue())) {
            return RemoteResponse.fail("系统异常");
        }
        List<LabelData> labelDataList = response.getValue();
        LabelData tenCrowdLabelData = labelDataList.stream()
                .filter(Objects::nonNull)
                .filter(labelData -> LabelIdConstant.MT_TEN_CROWD_LABEL_ID == labelData.getId())
                .findFirst()
                .orElse(null);
        LabelData collegeStudentLabelData = labelDataList.stream()
                .filter(Objects::nonNull)
                .filter(labelData -> LabelIdConstant.MT_COLLEGE_STUDENT_LABEL_ID == labelData.getId())
                .findFirst()
                .orElse(null);
        if (collegeStudentLabelData == null || tenCrowdLabelData == null) {
            return RemoteResponse.fail("系统异常");
        }
        String crowdLabel = tenCrowdLabelData.getValue();
        String collegeLabel = collegeStudentLabelData.getValue();
        if (TenCrowdType.TOWN_YOUTH.getDesc().equals(crowdLabel) || TenCrowdType.STUDENT.getDesc().equals(crowdLabel) || StringUtils.isNotEmpty(collegeLabel)) {
            return RemoteResponse.success(true);
        }
        return RemoteResponse.success(false);
    }

}
