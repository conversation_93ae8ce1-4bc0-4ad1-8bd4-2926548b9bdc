package com.sankuai.scrm.core.service.activity.fission.service;


import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.activity.fission.dto.activity.*;
import com.sankuai.dz.srcm.activity.fission.enums.ActivityStatusEnum;
import com.sankuai.dz.srcm.activity.fission.enums.StatusEnum;
import com.sankuai.dz.srcm.activity.fission.request.*;
import com.sankuai.dz.srcm.activity.fission.service.GroupFissionActivityService;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.group.dynamiccode.dto.CityVO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeInfoDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.SaveGroupDynamicCodeChannelResult;
import com.sankuai.dz.srcm.group.dynamiccode.enums.CommonOperationResultEnum;
import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeChannelSceneEnum;
import com.sankuai.dz.srcm.message.task.dto.MsgContentADTO;
import com.sankuai.scrm.core.service.activity.draw.fission.dal.entity.FissionActivityDrawConfig;
import com.sankuai.scrm.core.service.activity.draw.fission.dal.mapper.FissionActivityDrawConfigMapper;
import com.sankuai.scrm.core.service.activity.fission.config.FissionActivityMiniPConfig;
import com.sankuai.scrm.core.service.activity.fission.crane.ActivityAwardDistributeCheckTask;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionPrize;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionActivityExample;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionPrizeExample;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionPrizeMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionPrizeService;
import com.sankuai.scrm.core.service.activity.fission.enums.ActivityTypeEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.FissionChainContext;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainMarkEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmActivityAndDSPersonalWxGroupRelationMapDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupInfoDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeChannelLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannel;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfigExample;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxOpenIdResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.minip.common.MiniProgramQRCodeService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.QRCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@MdpPigeonServer
public class GroupFissionActivityServiceImpl implements GroupFissionActivityService {

    @Resource
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Resource
    private GroupDynamicCodeChannelLocalService groupDynamicCodeChannelLocalService;

    @Resource
    private GroupDynamicCodeLocalService groupDynamicCodeLocalService;

    @Resource
    private GroupDynamicCodeChannelConfigMapper codeChannelConfigMapper;

    @Resource
    private GroupFissionPrizeService groupFissionPrizeService;

    @Resource
    private GroupDynamicCodeChannelMapper groupDynamicCodeChannelMapper;

    @Resource
    private GroupFissionPrizeMapper groupFissionPrizeMapper;

    @Resource
    private CorpAppConfigRepository corpAppConfigRepository;

    @Resource
    private MiniProgramQRCodeService miniProgramQRCodeService;

    @Resource
    private CorpWxContactAcl corpWxContactAcl;

    @Resource
    private ActivityAwardDistributeService activityAwardDistributeService;
    @Resource
    private ActivityAwardDistributeCheckTask activityAwardDistributeCheckTask;
    @Resource
    private FissionActivityDrawConfigMapper fissionActivityDrawConfigMapper;

    @Resource
    private FissionActivityMiniPConfig fissionActivityMiniPConfig;
    @Resource
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper personalWxGroupRelationMapDOMapper;
    @Resource
    private ScrmDSPersonalWxGroupInfoDOMapper personalWxGroupInfoDOMapper;

    @Resource
    private FissionChainContext<GroupFissionActivityRequest> fissionChainContext;
    @Resource
    private GroupFissionActivityDomainService groupFissionActivityDomainService;
    @Resource
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    private final ThreadPoolExecutor copyDynamicCodeFromChannlePool = new ThreadPoolExecutor(5, 10, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100), new NamedThreadFactory("copyDynamicCodeFromChannlePool-Pool"), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * Scrm_Activity_And_DP_PersonalWx_Group_Relation_Map 表初始的 group_index
     */
    public final static int INITIAL_START_INDEX = 1;

    @Override
    public RemoteResponse<Boolean> saveGroupFissionActivity(GroupFissionActivityRequest request) {
        log.info("裂变活动开始创建, request={}", request);
        try {
            // 校验参数：FissionCommonValidator -> FissionMixedValidator
            fissionChainContext.validate(FissionChainMarkEnum.COMMON.getMark(), request, FissionChainOperationEnum.INSERT.getOperation());

            ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.fromCode(request.getActivityBaseInfo().getActivityType());
            if (activityTypeEnum == null) {
                return RemoteResponse.fail("活动类型有误");
            }

            switch (activityTypeEnum) {
                case DRAW:
                    return saveDrawFissionActivity(request);
                case GROUP:
                    return saveGroupFissionActivitySub(request);
                case PRIVATE:
                    return savePrivateFissionActivity(request);
                case NON_WECOM_GROUP:
                    return saveNonWeComGroupFissionActivity(request);
                default:
                    return RemoteResponse.fail("处理活动类型出错");
            }

        } catch (FissionValidatorException e) {
            log.error("GroupFissionActivityService.saveGroupFissionActivity 校验参数失败", e);
            return RemoteResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error("GroupFissionActivityService.saveGroupFissionActivity has exception", e);
            return RemoteResponse.fail("创建活动出现异常");
        }
    }

    private RemoteResponse<Boolean> saveNonWeComGroupFissionActivity(GroupFissionActivityRequest request) {
        // 校验参数
        fissionChainContext.validate(FissionChainMarkEnum.NON_WECOM_GROUP.getMark(), request, FissionChainOperationEnum.INSERT.getOperation());

        PosterInfoRequest posterInfo = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();

        // 创建channel
        SaveGroupDynamicCodeChannelResult channelResult = saveDynamicCodeChannelResult(request.getAppId(), activityBaseInfo, DynamicCodeChannelSceneEnum.NON_WECOM_FISSION_ACTIVITY_CHANNEL);
        if (channelResult.getResultCode() != CommonOperationResultEnum.SUCCESS.getCode()) {
            return RemoteResponse.fail(channelResult.getMsg());
        }
        Long channelId = channelResult.getGroupDynamicCodeChannelDTO().getId();

        //创建海报
        boolean saveResult = getSavePosterResult(request, activityBaseInfo, posterInfo, channelId);
        if (!saveResult) {
            return RemoteResponse.fail("活动海报保存未成功, 请核对海报参数");
        }
        Long drawConfigId = getSaveDrawConfigResult(request, activityBaseInfo);

        //创建群裂变活动
        GroupFissionActivity groupFissionActivity = buildFissionActivity(request, activityBaseInfo, posterInfo, rewardInfoList, channelId, drawConfigId);
        int rowNum = groupFissionActivityMapper.insertSelective(groupFissionActivity);
        if (rowNum <= 0) {
            return RemoteResponse.fail("保存群裂变活动数据未成功, 请核对活动基本信息");
        }
        Long activityId = groupFissionActivity.getId();

        //创建裂变活动奖品
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(rewardInfoList, activityId);
        if (!result) {
            return RemoteResponse.fail("保存活动奖品未成功, 请核对奖品信息");
        }

        // 群聊关联活动
        List<Long> groupIdList = request.getPersonalGroupInfo().stream().map(PersonalGroupInfoDTO::getDsGroupId).collect(Collectors.toList());
        int insertCount = groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(activityId, groupIdList, INITIAL_START_INDEX);
        if (insertCount != groupIdList.size()) {
            return RemoteResponse.fail("群裂变活动关联群聊失败");
        }

        // 配置告警接收人
//        List<String> alertMisIdList = request.getCopyDynamicChannelInfo().getAlertMisIdList();
//        if (CollectionUtils.isNotEmpty(alertMisIdList)) {
//            int inserted = groupFissionActivityDomainService.insertGroupFissionRiskAlertReceivers(activityId, alertMisIdList);
//            if (inserted != alertMisIdList.size()) {
//                return RemoteResponse.fail("配置告警接收人失败");
//            }
//        }

        return RemoteResponse.success(true);
    }

    private RemoteResponse<Boolean> saveDrawFissionActivity(GroupFissionActivityRequest request) {
        // 校验参数
        fissionChainContext.validate(FissionChainMarkEnum.DRAW.getMark(), request, FissionChainOperationEnum.INSERT.getOperation());

        PosterInfoRequest posterInfo = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();

        //创建channel
        SaveGroupDynamicCodeChannelResult saveGroupChannelResult = saveGroupDynamicCodeChannelResult(request.getAppId(), activityBaseInfo);
        if (saveGroupChannelResult.getResultCode() != 1) {
            return RemoteResponse.fail("活动名称在渠道表中已存在, 请更换活动名称");
        }
        Long channelId = saveGroupChannelResult.getGroupDynamicCodeChannelDTO().getId();

        //复刻群活码
        copyDynamicCode(request, channelId);

        //创建海报
        boolean saveResult = getSavePosterResult(request, activityBaseInfo, posterInfo, channelId);
        if (!saveResult) {
            return RemoteResponse.fail("活动海报保存未成功, 请核对海报参数");
        }
        Long drawConfigId = getSaveDrawConfigResult(request, activityBaseInfo);

        //创建群裂变活动
        GroupFissionActivity groupFissionActivity = buildFissionActivity(request, activityBaseInfo, posterInfo, rewardInfoList, channelId, drawConfigId);
        int rowNum = groupFissionActivityMapper.insertSelective(groupFissionActivity);
        if (rowNum <= 0) {
            return RemoteResponse.fail("保存群裂变活动数据未成功, 请核对活动基本信息");
        }

        //创建裂变活动奖品
        boolean result = groupFissionPrizeService.saveDrawFissionPrize(rewardInfoList, groupFissionActivity.getId());
        if (!result) {
            return RemoteResponse.fail("保存活动奖品未成功, 请核对奖品信息");
        }

        return RemoteResponse.success(true);
    }

    private RemoteResponse<Boolean> saveGroupFissionActivitySub(GroupFissionActivityRequest request) {
        // 校验参数
        fissionChainContext.validate(FissionChainMarkEnum.GROUP.getMark(), request, FissionChainOperationEnum.INSERT.getOperation());

        PosterInfoRequest posterInfo = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();

        //创建channel
        SaveGroupDynamicCodeChannelResult saveGroupChannelResult = saveGroupDynamicCodeChannelResult(request.getAppId(), activityBaseInfo);
        if (saveGroupChannelResult.getResultCode() != 1) {
            return RemoteResponse.fail("活动名称在渠道表中已存在, 请更换活动名称");
        }
        Long channelId = saveGroupChannelResult.getGroupDynamicCodeChannelDTO().getId();

        //复刻群活码
        copyDynamicCode(request, channelId);

        //创建海报
        boolean saveResult = getSavePosterResult(request, activityBaseInfo, posterInfo, channelId);
        if (!saveResult) {
            return RemoteResponse.fail("活动海报保存未成功, 请核对海报参数");
        }
        Long drawConfigId = getSaveDrawConfigResult(request, activityBaseInfo);

        //创建群裂变活动
        GroupFissionActivity groupFissionActivity = buildFissionActivity(request, activityBaseInfo, posterInfo, rewardInfoList, channelId, drawConfigId);
        int rowNum = groupFissionActivityMapper.insertSelective(groupFissionActivity);
        if (rowNum <= 0) {
            return RemoteResponse.fail("保存群裂变活动数据未成功, 请核对活动基本信息");
        }

        //创建裂变活动奖品
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(rewardInfoList, groupFissionActivity.getId());
        if (!result) {
            return RemoteResponse.fail("保存活动奖品未成功, 请核对奖品信息");
        }

        return RemoteResponse.success(true);
    }

    private RemoteResponse<Boolean> savePrivateFissionActivity(GroupFissionActivityRequest request) {
        // 校验参数
        fissionChainContext.validate(FissionChainMarkEnum.PRIVATE.getMark(), request, FissionChainOperationEnum.INSERT.getOperation());

        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();
        PosterInfoRequest posterInfo = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();

        //创建群裂变活动
        GroupFissionActivity groupFissionActivity = buildFissionActivity(request, activityBaseInfo, posterInfo, rewardInfoList, 0L, null);
        int rowNum = groupFissionActivityMapper.insertSelective(groupFissionActivity);
        if (rowNum <= 0) {
            return RemoteResponse.fail("保存群裂变活动数据未成功, 请核对活动基本信息");
        }
        //创建群裂变活动奖品
        boolean result = groupFissionPrizeService.saveGroupFissionPrize(rewardInfoList, groupFissionActivity.getId());
        if (!result) {
            return RemoteResponse.fail("保存活动奖品未成功, 请核对奖品信息");
        }
        activityAwardDistributeCheckTask.regisActivityCheck(groupFissionActivity.getId());// 只有企微裂变注册巡检任务
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<Boolean> updateGroupFissionActivity(GroupFissionActivityRequest request) {
        log.info("修改裂变活动, request={}", request);
        try {
            // 校验参数：FissionCommonValidator -> FissionMixedValidator
            fissionChainContext.validate(FissionChainMarkEnum.COMMON.getMark(), request, FissionChainOperationEnum.UPDATE.getOperation());

            ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.fromCode(request.getActivityBaseInfo().getActivityType());
            if (activityTypeEnum == null) {
                return RemoteResponse.fail("无效的裂变活动类型");
            }
            switch (activityTypeEnum) {
                case GROUP:
                case DRAW:
                    return updateGroupOrDrawFissionActivity(request);
                case PRIVATE:
                    return updatePrivateFissionActivity(request);
                case NON_WECOM_GROUP:
                    return updateNonWeComGroupFissionActivity(request);
                default:
                    return RemoteResponse.fail("处理活动类型出错");
            }
        } catch (FissionValidatorException ex) {
            log.error("saveGroupFissionActivity 校验参数出错", ex);
            return RemoteResponse.fail(ex.getMessage());
        } catch (Exception e) {
            log.error("GroupFissionActivityService.updateGroupFissionActivity has exception", e);
            return RemoteResponse.fail("修改裂变活动信息失败");
        }
    }

    private RemoteResponse<Boolean> updateNonWeComGroupFissionActivity(GroupFissionActivityRequest request) {
        // 参数校验
        fissionChainContext.validate(FissionChainMarkEnum.NON_WECOM_GROUP.getMark(), request, FissionChainOperationEnum.UPDATE.getOperation());

        GroupFissionActivity groupActivityResult = groupFissionActivityMapper.selectByPrimaryKey(request.getActivityId());
        boolean isActivityStart = System.currentTimeMillis() >= groupActivityResult.getStartTime().getTime();

        // 更新关联的非企微群列表
        if (!updateGroupList(request, isActivityStart)) {
            return RemoteResponse.fail("更新关联的非企微群列表失败");
        }

        // 更新告警人
//        if (!updateAlertReceivers(request)) {
//            return RemoteResponse.fail("更新告警人失败");
//        }

        // 活动开始前更新奖品信息表
        if (!updatePrizeInfo(request, isActivityStart)) {
            return RemoteResponse.fail("活动开始前更新奖品信息表失败");
        }

        // 更新海报
        if (!updatePoster(request, groupActivityResult, isActivityStart)) {
            return RemoteResponse.fail("更新海报失败");
        }

        // 更新活动基本信息
        if (!updateFissionActivity(request, groupActivityResult, isActivityStart)) {
            return RemoteResponse.fail("更新活动基本信息失败");
        }

        return RemoteResponse.success(true);
    }

    private boolean updateFissionActivity(GroupFissionActivityRequest request, GroupFissionActivity groupActivityResult, boolean isActivityStart) {
        PosterInfoRequest posterInfoRe = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();

        if (!isActivityStart) {
            GroupFissionActivity groupFissionActivity = buildGroupFissionActivity(request, activityBaseInfo, posterInfoRe, rewardInfoList, groupActivityResult);
            return groupFissionActivityMapper.updateByPrimaryKeySelective(groupFissionActivity) > 0;
        } else {
            GroupFissionActivity updateActivity = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
            return groupFissionActivityMapper.updateByPrimaryKeySelective(updateActivity) > 0;
        }
    }

    private boolean updatePoster(GroupFissionActivityRequest request, GroupFissionActivity groupActivityResult, boolean isActivityStart) {
        if (!isActivityStart) {
            return getUpdatePosterResult(request, request.getActivityBaseInfo(), request.getPosterInfo(), groupActivityResult);
        } else {
            GroupDynamicCodeChannelConfig codeChannelConfig = GroupDynamicCodeChannelConfig.builder()
                    .id(request.getPosterInfo().getId())
                    .poster(request.getPosterInfo().getPoster())
                    .build();
            return codeChannelConfigMapper.updateByPrimaryKeySelective(codeChannelConfig) > 0;
        }
    }

    private boolean updatePrizeInfo(GroupFissionActivityRequest request, boolean isActivityStart) {
        if (!isActivityStart) {
            deletePrizes(request);
            return groupFissionPrizeService.saveGroupFissionPrize(request.getRewardInfo(), request.getActivityId());
        }
        return true;
    }

//    private boolean updateAlertReceivers(GroupFissionActivityRequest request) {
//        List<String> alertMisIdList = request.getCopyDynamicChannelInfo().getAlertMisIdList();
//        if (alertMisIdList == null) {
//            // 为null不更新，size=0会更新
//            return true;
//        }
//        // 删除告警人
//        groupFissionActivityDomainService.deleteGroupFissionRiskAlertReceivers(request.getActivityId());
//
//        // 新增告警人
//        int inserted = groupFissionActivityDomainService.insertGroupFissionRiskAlertReceivers(request.getActivityId(), alertMisIdList);
//        return inserted == alertMisIdList.size();
//    }

    private boolean updateGroupList(GroupFissionActivityRequest request, boolean isActivityStart) {
        List<Long> updateGroupIds = request.getPersonalGroupInfo().stream().map(PersonalGroupInfoDTO::getDsGroupId).collect(Collectors.toList());
        if (isActivityStart) {
            // 活动开始，只能新增群列表
            List<Long> existGroupIds = personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(request.getActivityId());
            List<Long> needGroupIds = new ArrayList<>();
            for (int i = existGroupIds.size(); i < updateGroupIds.size(); i++) {
                needGroupIds.add(updateGroupIds.get(i));
            }
            // 只更新新增的群列表
            int inserted = groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(
                    request.getActivityId(), needGroupIds, existGroupIds.size() + INITIAL_START_INDEX
            );
            return inserted == needGroupIds.size();
        } else {
            // 活动未开始，可以修改群列表
            // 先删除原来的群列表
            groupFissionActivityDomainService.deleteActivityAndPersonalWxGroupRelation(request.getActivityId());

            // 再插入新的群列表关系
            int inserted = groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(
                    request.getActivityId(), updateGroupIds, INITIAL_START_INDEX
            );
            return inserted == updateGroupIds.size();
        }
    }

    private RemoteResponse<Boolean> updateGroupOrDrawFissionActivity(GroupFissionActivityRequest request) {
        PosterInfoRequest posterInfoRe = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();
        Byte activityType = activityBaseInfo.getActivityType();
        GroupFissionActivity groupActivityResult = groupFissionActivityMapper.selectByPrimaryKey(request.getActivityId());
        GroupDynamicCodeChannel channelResult = groupDynamicCodeChannelMapper.selectByPrimaryKey(groupActivityResult.getChannelId());

        if (Objects.equals(activityType, ActivityTypeEnum.DRAW.getCode())) {
            fissionChainContext.validate(FissionChainMarkEnum.DRAW.getMark(), request, FissionChainOperationEnum.UPDATE.getOperation());
        }

        if (Objects.equals(activityType, ActivityTypeEnum.GROUP.getCode())) {
            fissionChainContext.validate(FissionChainMarkEnum.GROUP.getMark(), request, FissionChainOperationEnum.UPDATE.getOperation());
        }

        //活动开始前正常修改, 开始后只能修改奖品数量和海报
        if (System.currentTimeMillis() < groupActivityResult.getStartTime().getTime()) {
            GroupFissionActivity groupFissionActivity = buildGroupFissionActivity(request, activityBaseInfo, posterInfoRe, rewardInfoList, groupActivityResult);

            //修改群裂变基本信息表
            int row = groupFissionActivityMapper.updateByPrimaryKeySelective(groupFissionActivity);
            if (!(row > 0)) {
                return RemoteResponse.fail("更新活动基本信息失败");
            }
            updateDrawFissionConfig(activityBaseInfo, groupActivityResult, groupFissionActivity);

            String channelName = getChannelName(activityBaseInfo.getActivityName());
            //渠道名称未变化不修改
            if (!channelResult.getName().equals(channelName)) {
                //休改渠道名称
                SaveGroupDynamicCodeChannelResult saveGroupChannelResult = getSaveGroupDynamicCodeChannelResult(channelResult.getStatus(), request, channelName, groupActivityResult.getChannelId());
                if (saveGroupChannelResult.getResultCode() != 1) {
                    return RemoteResponse.fail("修改渠道名称失败");
                }
            }
            boolean flag = getUpdatePosterResult(request, activityBaseInfo, posterInfoRe, groupActivityResult);
            if (!flag) {
                return RemoteResponse.fail("更新海报失败");
            }

            //修改奖品信息表, 先删后插入
            boolean deletePrizesResult = deletePrizes(request);
            if (!deletePrizesResult) {
                return RemoteResponse.fail("奖品信息先删除失败");
            }
            boolean result = false;
            if (Objects.equals(activityType, ActivityTypeEnum.GROUP.getCode())) {
                result = groupFissionPrizeService.saveGroupFissionPrize(rewardInfoList, request.getActivityId());
            } else if (Objects.equals(activityType, ActivityTypeEnum.DRAW.getCode())) {
                result = groupFissionPrizeService.saveDrawFissionPrize(rewardInfoList, request.getActivityId());
            }
            if (!result) {
                return RemoteResponse.fail("奖品信息后修改失败");
            }

        } else {
            GroupFissionActivity updateActivity = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
            int activityRow = groupFissionActivityMapper.updateByPrimaryKeySelective(updateActivity);
            if (activityRow <= 0) {
                return RemoteResponse.fail("活动开始后修改活动参数失败");
            }

            GroupDynamicCodeChannelConfig codeChannelConfig = GroupDynamicCodeChannelConfig.builder()
                    .id(posterInfoRe.getId())
                    .poster(posterInfoRe.getPoster())
                    .build();
            int configRow = codeChannelConfigMapper.updateByPrimaryKeySelective(codeChannelConfig);
            if (configRow <= 0) {
                return RemoteResponse.fail("修改海报失败");
            }
        }
        return RemoteResponse.success(true);
    }

    private RemoteResponse<Boolean> updatePrivateFissionActivity(GroupFissionActivityRequest request) {
        GroupFissionActivity groupActivityResult = groupFissionActivityMapper.selectByPrimaryKey(request.getActivityId());
        ActivityBaseInfoRequest activityBaseInfo = request.getActivityBaseInfo();
        PosterInfoRequest posterInfoRe = request.getPosterInfo();
        List<RewardInfoRequest> rewardInfoList = request.getRewardInfo();

        //活动开始前正常修改, 开始后只能修改奖品数量和海报
        if (System.currentTimeMillis() < groupActivityResult.getStartTime().getTime()) {
            GroupFissionActivity groupFissionActivity = buildGroupFissionActivity(request, activityBaseInfo, posterInfoRe, rewardInfoList, groupActivityResult);

            //修改群裂变基本信息表
            int row = groupFissionActivityMapper.updateByPrimaryKeySelective(groupFissionActivity);
            if (!(row > 0)) {
                return RemoteResponse.fail("更新活动基本信息失败");
            }

            //修改奖品信息表, 先删后插入
            boolean deletePrizesResult = deletePrizes(request);
            if (!deletePrizesResult) {
                return RemoteResponse.fail("奖品信息先删除失败");
            }
            boolean result = groupFissionPrizeService.saveGroupFissionPrize(rewardInfoList, request.getActivityId());
            if (!result) {
                return RemoteResponse.fail("奖品信息后修改失败");
            }
        } else {
            GroupFissionActivity updateActivity = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
            int activityRow = groupFissionActivityMapper.updateByPrimaryKeySelective(updateActivity);
            if (activityRow <= 0) {
                return RemoteResponse.fail("活动开始后修改活动参数失败");
            }
        }
        return RemoteResponse.success(true);
    }

    private GroupFissionActivity buildUpdateGroupFissionActivity(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfoRe) {
        return GroupFissionActivity.builder().id(request.getActivityId()).activityName(activityBaseInfo.getActivityName())
                .activityInnerName(activityBaseInfo.getActivityInnerName()).showRankList(activityBaseInfo.getShowRankList())
                .endTime(new Date(activityBaseInfo.getEndTime())).rule(activityBaseInfo.getRule()).showAvatar(posterInfoRe.getShowAvtar())
                .showNickName(posterInfoRe.getShowNickName()).headImage(activityBaseInfo.getActivityHeadImg())
                .cardMinipName(request.getShareCardInfo().getMinipName()).cardImg(request.getShareCardInfo().getCardImg())
                .cardTitle(request.getShareCardInfo().getCardTitle())
                .backgroundImg(activityBaseInfo.getBackgroundImg())
                .backgroundColor(activityBaseInfo.getBackgroundColor())
                .build();
    }

    private boolean deletePrizes(GroupFissionActivityRequest request) {
        GroupFissionPrizeExample prizeExample = new GroupFissionPrizeExample();
        prizeExample.createCriteria().andActivityIdEqualTo(request.getActivityId());
        return groupFissionPrizeMapper.deleteByExample(prizeExample) > 0;
    }

    @Override
    public RemoteResponse<GroupFissionActivityDTO> queryGroupActivityByActivityId(String appId, Long activityId) {
        try {
            log.info("查询裂变活动,  appId={},activityId={}", appId, activityId);

            if (activityId == null || StringUtils.isBlank(appId) || ObjectUtils.isEmpty(corpAppConfigRepository.getConfigByAppId(appId))) {
                return RemoteResponse.fail("查询裂变活动详情参数错误");
            }
            GroupFissionActivityDTO activityDTO = new GroupFissionActivityDTO();
            activityDTO.setActivityId(activityId);
            activityDTO.setAppId(appId);
            //查询活动基本信息
            GroupFissionActivity groupFissionActivity = groupFissionActivityMapper.selectByPrimaryKey(activityId);
            if (ObjectUtils.isEmpty(groupFissionActivity)) {
                return RemoteResponse.success(activityDTO);
            }
            //非企微群裂变
            if(Objects.equals(ActivityTypeEnum.NON_WECOM_GROUP.getCode(), groupFissionActivity.getActivityType())){
                activityDTO.setPersonalGroupInfo(getPersonalGroupInfo(appId, activityId));
            }
            ActivityBaseInfoDTO activityBaseInfoDTO = buildActivityBaseInfoDTO(groupFissionActivity);
            fillDrawFissionConfig(groupFissionActivity, activityBaseInfoDTO);
            activityDTO.setActivityId(activityId);
            activityDTO.setActivityBaseInfo(activityBaseInfoDTO);

            ShareCardInfo shareCardInfo = buildShareCardInfo(groupFissionActivity);
            activityDTO.setShareCardInfo(shareCardInfo);

            CopyDynamicChannelInfo copyDynamicChannelInfo = buildCopyDynamicChannelInfo(groupFissionActivity);
            activityDTO.setCopyDynamicChannelInfo(copyDynamicChannelInfo);

            boolean isPrivateFission = Objects.equals(activityBaseInfoDTO.getActivityType(), ActivityTypeEnum.PRIVATE.getCode());

            if (!isPrivateFission) {
                //查询海报信息, 根据渠道Id查, 一一对应
                GroupDynamicCodeChannelConfigExample example = new GroupDynamicCodeChannelConfigExample();
                example.createCriteria().andAppIdEqualTo(appId).andChannelIdEqualTo(groupFissionActivity.getChannelId());
                List<GroupDynamicCodeChannelConfig> posterResult = codeChannelConfigMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(posterResult)) {
                    return RemoteResponse.success(activityDTO);
                }
                PosterInfoDTO posterInfoDTO = buildPosterInfoDTO(groupFissionActivity, posterResult.get(0));
                activityDTO.setPosterInfo(posterInfoDTO);
            }

            //查询奖品列表
            List<GroupFissionPrize> groupFissionPrizes = queryGroupFissionPrizes(activityId);
            if (CollectionUtils.isEmpty(groupFissionPrizes)) {
                return RemoteResponse.success(activityDTO);
            }
            List<RewardInfoDTO> rewardInfoList = buildRewardInfoDTOS(groupFissionPrizes);
            activityDTO.setRewardInfo(rewardInfoList);

            return RemoteResponse.success(activityDTO);
        } catch (Exception e) {
            log.error("GroupFissionActivityService.queryGroupActivityByActivityId has exception", e);
            return RemoteResponse.fail("查询裂变活动信息失败");
        }

    }

    private  List<PersonalGroupInfoDTO> getPersonalGroupInfo(String appId, Long activityId){
        String corpId = corpAppConfigRepository.getCorpIdByAppId(appId);
        if(StringUtils.isBlank(corpId)||activityId==null){
            log.info("getPersonalGroupInfo param is wrong");
            return null;
        }
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample mapDOExample = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        mapDOExample.createCriteria().andActivityIdEqualTo(activityId);
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMapDOS = personalWxGroupRelationMapDOMapper.selectByExample(mapDOExample);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(relationMapDOS)){
            log.info("getPersonalGroupInfo relationMapDOS is empty, activityId={}", activityId);
            return null;
        }
        //按照groupIndex升序排序
        List<Long> dsGroupIds = relationMapDOS.stream().sorted(Comparator.comparing(ScrmActivityAndDSPersonalWxGroupRelationMapDO::getGroupIndex)).map(ScrmActivityAndDSPersonalWxGroupRelationMapDO::getDsGroupId).collect(Collectors.toList());
        ScrmDSPersonalWxGroupInfoDOExample groupInfoDOExample = new ScrmDSPersonalWxGroupInfoDOExample();
        groupInfoDOExample.createCriteria().andDsGroupIdIn(dsGroupIds).andCorpIdEqualTo(corpId);
        List<ScrmDSPersonalWxGroupInfoDO> groupInfoDOS = personalWxGroupInfoDOMapper.selectByExample(groupInfoDOExample);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(groupInfoDOS)){
            log.info("getPersonalGroupInfo groupInfoDOS is empty, activityId={}", activityId);
            return null;
        }
        Map<Long, ScrmDSPersonalWxGroupInfoDO> groupInfoMap = groupInfoDOS.stream()
                .collect(Collectors.toMap(ScrmDSPersonalWxGroupInfoDO::getDsGroupId, info -> info ,(k1, k2) -> k1));
        //依据排好序的dsGroupIds得到personalGroupInfo
        List<PersonalGroupInfoDTO> personalGroupInfo = dsGroupIds.stream()
                .map(groupInfoMap::get)
                .filter(Objects::nonNull)
                .map(ScrmDSPersonalWxGroupInfoDO::buildPersonalGroupInfoDTO)
                .collect(Collectors.toList());
        log.info("getPersonalGroupInfo personalGroupInfo:{}",personalGroupInfo);
        return personalGroupInfo;
    }

    @Override
    public PageRemoteResponse<GroupFissionActivityListDTO> queryGroupFissionActivityList(GroupFissionActivityListRequest request) {
        try {
            log.info("查询裂变活动列表,  request={}", request);

            if (ObjectUtils.isEmpty(request) || StringUtils.isBlank(request.getAppId()) || request.getPageSize() == null || request.getPageNum() == null || request.getPageNum() < 1 || request.getPageSize() < 1) {
                return PageRemoteResponse.fail("请求参数错误");
            }

            if (ObjectUtils.isEmpty(corpAppConfigRepository.getConfigByAppId(request.getAppId()))) {
                return PageRemoteResponse.fail("appId错误");
            }

            GroupFissionActivityExample example = new GroupFissionActivityExample();

            List<GroupFissionActivity> groupFissionActivities = getPageActivityList(request, example);
            if (CollectionUtils.isEmpty(groupFissionActivities)) {
                return PageRemoteResponse.success(new ArrayList<>(), 0, true, null);
            }
            List<Long> activityIds = groupFissionActivities.stream().map(GroupFissionActivity::getId).collect(Collectors.toList());

            //根据活动id查询奖品
            List<GroupFissionPrize> groupFissionPrizes = getGroupPrizesByActivityId(activityIds);

            Map<Long, List<GroupFissionPrize>> groupFissionPrizeMaps = groupFissionPrizes.stream().collect(Collectors.groupingBy(GroupFissionPrize::getActivityId));

            //查询活动总记录数
            long total = groupFissionActivityMapper.countByExample(example);

            //封装数据 活动奖励阶段对应的奖品发放数量
            List<GroupFissionActivityListDTO> groupFissionActivityListDTO = buildGroupFissionActivityListDTOS(request.getAppId(), groupFissionActivities, groupFissionPrizeMaps);
            log.info("queryGroupFissionActivityList: groupFissionActivityListDTO={}", groupFissionActivityListDTO);
            return PageRemoteResponse.success(groupFissionActivityListDTO, total, groupFissionActivityListDTO.size() >= total, null);
        } catch (Exception e) {
            log.error("GroupFissionActivityService.queryGroupFissionActivityList has exception", e);
            return PageRemoteResponse.fail("查询裂变活动列表失败");
        }

    }

    @Override
    public RemoteResponse<Boolean> updateGroupActivityStatus(String appId, Long activityId, Integer changeStatus) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.fission.service.GroupFissionActivityServiceImpl.updateGroupActivityStatus(java.lang.String,java.lang.Long,java.lang.Integer)");
        log.info("修改活动状态: appId={}, activityId={}, changeStatus={}", appId, activityId, changeStatus);
        try {
            if (activityId == null || changeStatus == null) {
                return RemoteResponse.fail("请求参数错误");
            }
            if (StringUtils.isBlank(appId) || ObjectUtils.isEmpty(corpAppConfigRepository.getConfigByAppId(appId))) {
                return RemoteResponse.fail("appId错误");
            }

            if (changeStatus != StatusEnum.FINISHED.getCode() && changeStatus != StatusEnum.DELETED.getCode()) {
                return RemoteResponse.fail("状态只能传2(结束)或3(删除)");
            }
            GroupFissionActivity groupFissionActivity = groupFissionActivityMapper.selectByPrimaryKey(activityId);
            if (ObjectUtils.isEmpty(groupFissionActivity)) {
                return RemoteResponse.fail("活动不存在");
            }

            if (!appId.equals(groupFissionActivity.getAppId())) {
                return RemoteResponse.fail("appId输入错误");
            }

            long now = System.currentTimeMillis();
            if (now > groupFissionActivity.getStartTime().getTime() && now < groupFissionActivity.getEndTime().getTime()) {
                if (changeStatus == StatusEnum.DELETED.getCode() && groupFissionActivity.getStatus() != StatusEnum.FINISHED.getCode()) {
                    return RemoteResponse.fail("正在进行中的活动只能先结束再删除");
                }
            }
            GroupFissionActivity groupActivity = GroupFissionActivity.builder().id(activityId).status(changeStatus).build();
            int row = groupFissionActivityMapper.updateByPrimaryKeySelective(groupActivity);
            if (row > 0) {
                groupActivity = groupFissionActivityMapper.selectByPrimaryKey(activityId);
                activityAwardDistributeService.processFinish(groupActivity);
                return RemoteResponse.success(true);
            }
            return RemoteResponse.fail("裂变活动状态修改失败");
        } catch (Exception e) {
            log.error("GroupFissionActivityService.updateGroupActivityStatus has exception", e);
            return RemoteResponse.fail("修改裂变活动状态失败");
        }
    }

    @Override
    public RemoteResponse<Integer> queryStageNum(String appId, Long activityId) {
        try {
            log.info("查询活动总阶段数: appId={}, activityId={}", appId, activityId);
            if (activityId == null && appId == null) {
                return RemoteResponse.fail("请求参数错误");
            }
            if (StringUtils.isBlank(appId) || ObjectUtils.isEmpty(corpAppConfigRepository.getConfigByAppId(appId))) {
                return RemoteResponse.fail("appId错误");
            }

            List<GroupFissionPrize> prizeResults = queryGroupFissionPrizes(activityId);

            if (CollectionUtils.isEmpty(prizeResults)) {
                return RemoteResponse.fail("活动对应奖品阶段不存在或群裂变活动阶段数异常");
            }
            return RemoteResponse.success(prizeResults.size());
        } catch (Exception e) {
            log.error("GroupFissionActivityService.queryStageNum has exception", e);
            return RemoteResponse.fail("查询活动总阶段数出现异常");
        }
    }

    @Override
    public RemoteResponse queryOpenId(String corpId, String externalUserId) {
        WxOpenIdResponse wxOpenIdResponse = corpWxContactAcl.queryOpenIdByExternalUserId(corpId, externalUserId);
        return RemoteResponse.success(wxOpenIdResponse);
    }

    private List<GroupFissionPrize> queryGroupFissionPrizes(Long activityId) {
        GroupFissionPrizeExample prizeExample = new GroupFissionPrizeExample();
        prizeExample.createCriteria().andActivityIdEqualTo(activityId);
        return groupFissionPrizeMapper.selectByExampleWithBLOBs(prizeExample);
    }

    private List<RewardInfoDTO> buildRewardInfoDTOS(List<GroupFissionPrize> groupFissionPrizes) {
        return groupFissionPrizes.stream().map(reward ->
                        RewardInfoDTO.builder()
                                .id(reward.getId())
                                .stage(reward.getStage())
                                .priceName(reward.getPrizeName())
                                .invitationNum(reward.getInvitationNum())
                                .receiveType(reward.getReceiveType())
                                .rewardSize(reward.getPrizeNum())
                                .rewardType(reward.getPrizeType())
                                .price(reward.getPrice())
                                .priceImage(reward.getPrizeImage())
                                .assistantAccount(reward.getAssistantAccount())
                                .assistantQrCode(reward.getAssistantQrCode())
                                .prizeProbability(reward.getPrizeProbability())
                                .couponId(reward.getMktCouponId())
                                .addressUrl(reward.getAddressUrl())
                                .priceDecimal(reward.getPriceDecimal())
                                .msgContentADTOList(JsonUtils.toList(reward.getMsgContent(), MsgContentADTO.class))
                                .build())
                .collect(Collectors.toList());
    }

    private List<GroupFissionActivityListDTO> buildGroupFissionActivityListDTOS(String appId, List<GroupFissionActivity> groupFissionActivities, Map<Long, List<GroupFissionPrize>> groupFissionPrizeMaps) {
        List<GroupFissionActivityListDTO> groupFissionActivityListDTO = new ArrayList<>();


        for (GroupFissionActivity groupActivity : groupFissionActivities) {
            ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.fromCode(groupActivity.getActivityType() == null ? 1 : groupActivity.getActivityType());
            List<RewardInfoListDTO> rewardInfoListDTO = new ArrayList<>();
            if (groupFissionPrizeMaps.containsKey(groupActivity.getId())) {
                List<GroupFissionPrize> rewardInfoList = groupFissionPrizeMaps.get(groupActivity.getId());

                for (GroupFissionPrize groupFissionPrize : rewardInfoList) {

                    RewardInfoListDTO rewardInfoDTO = RewardInfoListDTO.builder()
                            .id(groupFissionPrize.getId())
                            .stage(groupFissionPrize.getStage())
                            .price(groupFissionPrize.getPrice())
                            .priceDecimal(groupFissionPrize.getPriceDecimal())
                            .invitationNum(groupFissionPrize.getInvitationNum())
                            .rewardSize(groupFissionPrize.getPrizeNum())
                            .reminderSize(groupFissionPrize.getPrizeNum())
                            .prizeName(groupFissionPrize.getPrizeName())
                            .prizeProbability(groupFissionPrize.getPrizeProbability())
                            .build();
                    rewardInfoListDTO.add(rewardInfoDTO);
                }
            }

            GroupFissionActivityListDTO activityListDTO = GroupFissionActivityListDTO.builder()
                    .activityId(groupActivity.getId())
                    .activityName(groupActivity.getActivityName())
                    .activityInnerName(groupActivity.getActivityInnerName())
                    .activityStartTime(groupActivity.getStartTime().getTime())
                    .activityEndTime(groupActivity.getEndTime().getTime())
                    .createTime(groupActivity.getAddTime().getTime())
                    .rewardInfo(rewardInfoListDTO)
                    .minipUrl(getMinipPath(appId, groupActivity.getId(), activityTypeEnum))
                    .minipQrCodeUrl(getMiniProgramQRCode(appId, groupActivity.getId(), activityTypeEnum))
                    .newMinipUrl(getNewMiniProgramPath(groupActivity.getAppId(), groupActivity.getId()))
                    .newMinipQrCodeUrl(miniProgramQRCodeService.getNewMiniProgramQRCode("pages/fission-activity/activity", groupActivity.getAppId() + "&" + groupActivity.getId()))
                    .activityType(groupActivity.getActivityType() == null ? ActivityTypeEnum.GROUP.getCode() : groupActivity.getActivityType())
                    .friendChannelDynamicCodeId(groupActivity.getFriendChannelDynamicCodeId())
                    .build();

            //数据库中结束状态,  对应响应的结束状态
            if (groupActivity.getStatus() == StatusEnum.FINISHED.getCode()) {
                activityListDTO.setActivityStatus(ActivityStatusEnum.OVER.getCode());
            }
            //数据库中的有效状态,  响应时分进行中和未开始状态
            if (groupActivity.getStatus() == StatusEnum.EFFECTIvE.getCode()) {
                long now = System.currentTimeMillis();
                if (now < groupActivity.getStartTime().getTime()) {
                    activityListDTO.setActivityStatus(ActivityStatusEnum.NOT_STARTED.getCode());
                }
                if (now > groupActivity.getStartTime().getTime() && now < groupActivity.getEndTime().getTime()) {
                    activityListDTO.setActivityStatus(ActivityStatusEnum.ON_GOING.getCode());
                }
                if (now > groupActivity.getEndTime().getTime()) {
                    activityListDTO.setActivityStatus(ActivityStatusEnum.OVER.getCode());
                }
            }

            groupFissionActivityListDTO.add(activityListDTO);

        }
        return groupFissionActivityListDTO;
    }

    private List<GroupFissionPrize> getGroupPrizesByActivityId(List<Long> activityIds) {
        GroupFissionPrizeExample prizeExample = new GroupFissionPrizeExample();
        GroupFissionPrizeExample.Criteria prizeExampleCriteria = prizeExample.createCriteria();
        prizeExampleCriteria.andActivityIdIn(activityIds);
        return groupFissionPrizeMapper.selectByExample(prizeExample);
    }

    private List<GroupFissionActivity> getPageActivityList(GroupFissionActivityListRequest request, GroupFissionActivityExample example) {
        request.setPageNum(request.getPageNum() - 1);
        GroupFissionActivityExample.Criteria criteria = example.createCriteria().andAppIdEqualTo(request.getAppId());
        example.setOrderByClause("add_time desc");
        if (ObjectUtils.isNotEmpty(request.getActivityType())) {
            criteria.andActivityTypeEqualTo(request.getActivityType());
        } else {
            //排除抽奖裂变互动
            criteria.andActivityTypeNotEqualTo((byte) 3);
        }
        if (request.getPageNum() != null && request.getPageSize() != null) {
            example.page(request.getPageNum(), request.getPageSize());
        }
        if (StringUtils.isNotBlank(request.getActivityName())) {
            criteria.andActivityNameLike("%" + request.getActivityName() + "%");
        }
        criteria.andStatusNotEqualTo(StatusEnum.DELETED.getCode());

        //按活动日期搜索
        if (request.getActivityStartTime() != null && request.getActivityEndTime() != null) {
            criteria.andStartTimeBetween(new Date(request.getActivityStartTime()), new Date(request.getActivityEndTime())).andEndTimeBetween(new Date(request.getActivityStartTime()), new Date(request.getActivityEndTime()));
        }

        //活动创建时间搜索
        if (request.getCreateStartTime() != null && request.getCreateEndTime() != null) {
            criteria.andAddTimeBetween(new Date(request.getCreateStartTime()), new Date(request.getCreateEndTime()));
        }

        if (request.getActivityStatus() != null && request.getActivityStatus() != 0) {
            if (request.getActivityStatus() == ActivityStatusEnum.NOT_STARTED.getCode()) {
                criteria.andStartTimeGreaterThan(new Date());
            }
            if (request.getActivityStatus() == ActivityStatusEnum.ON_GOING.getCode()) {
                Date now = new Date();
                criteria.andStartTimeLessThan(now).andEndTimeGreaterThan(now).andStatusEqualTo(StatusEnum.EFFECTIvE.getCode());
            }
            if (request.getActivityStatus() == ActivityStatusEnum.OVER.getCode()) {
                criteria.andStatusEqualTo(StatusEnum.FINISHED.getCode());
            }
        }
        //分页查询活动信息
        return groupFissionActivityMapper.selectByExample(example);
    }


    private PosterInfoDTO buildPosterInfoDTO(GroupFissionActivity groupFissionActivity, GroupDynamicCodeChannelConfig posterResult) {
        PosterInfoDTO posterInfoDTO = PosterInfoDTO.builder()
                .id(posterResult.getId())
                .poster(posterResult.getPoster())
                .showAvtar(groupFissionActivity.getShowAvatar())
                .showNickName(groupFissionActivity.getShowNickName())
                .dynamicCodeIcon(posterResult.getIcon())
                .build();
// TODO               .dynamicCodeShowType()
        return posterInfoDTO;
    }

    private ShareCardInfo buildShareCardInfo(GroupFissionActivity groupFissionActivity) {
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setCardImg(groupFissionActivity.getCardImg());
        shareCardInfo.setCardTitle(groupFissionActivity.getCardTitle());
        shareCardInfo.setMinipName(groupFissionActivity.getCardMinipName());
        return shareCardInfo;
    }

    private CopyDynamicChannelInfo buildCopyDynamicChannelInfo(GroupFissionActivity groupFissionActivity) {
        CopyDynamicChannelInfo copyDynamicChannelInfo = new CopyDynamicChannelInfo();

        String copyDynamicChannelInfoStr = groupFissionActivity.getCopyDynamicChannelInfoStr();
        if (StringUtils.isBlank(copyDynamicChannelInfoStr)) {
            return copyDynamicChannelInfo;
        }

        return JSONObject.parseObject(copyDynamicChannelInfoStr, CopyDynamicChannelInfo.class);
    }


    private String buildCopyDynamicChannelInfoStr(CopyDynamicChannelInfo copyDynamicChannelInfo) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.fission.service.GroupFissionActivityServiceImpl.buildCopyDynamicChannelInfoStr(com.sankuai.dz.srcm.activity.fission.request.CopyDynamicChannelInfo)");
        if (copyDynamicChannelInfo == null) {
            return "";
        }
        return JSONObject.toJSON(copyDynamicChannelInfo).toString();
    }

    private ActivityBaseInfoDTO buildActivityBaseInfoDTO(GroupFissionActivity groupFissionActivity) {
        return ActivityBaseInfoDTO.builder().activityName(groupFissionActivity.getActivityName())
                .activityInnerName(groupFissionActivity.getActivityInnerName())
                .appId(groupFissionActivity.getAppId()).rule(groupFissionActivity.getRule())
                .quitCheck(groupFissionActivity.getQuitCheck()).newUserCheck(groupFissionActivity.getNewUserCheck())
                .riskCheck(groupFissionActivity.getRiskCheck())
                .startTime(groupFissionActivity.getStartTime().getTime()).endTime(groupFissionActivity.getEndTime().getTime())
                .showRankList(groupFissionActivity.getShowRankList()).status(groupFissionActivity.getStatus())
                .activityHeadImg(groupFissionActivity.getHeadImage())
                .backgroundImg(groupFissionActivity.getBackgroundImg())
                .backgroundColor(groupFissionActivity.getBackgroundColor())
                .activityType(groupFissionActivity.getActivityType() == null ? ActivityTypeEnum.GROUP.getCode() : groupFissionActivity.getActivityType())
                .friendChannelDynamicCodeId(groupFissionActivity.getFriendChannelDynamicCodeId())
                .activityCityIds(JsonUtils.toList(groupFissionActivity.getActivityCityIds(), Integer.class))
                .relevantCityIds(JsonUtils.toList(groupFissionActivity.getRelevantCityIds(), Integer.class))
                .checkCitySwitch(groupFissionActivity.getCheckCitySwitch())
                .build();
    }

    private GroupFissionActivity buildFissionActivity(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfo, List<RewardInfoRequest> rewardInfoList, Long channelId, Long drawConfigId) {
        GroupFissionActivity activity = GroupFissionActivity.builder()
                .activityType(activityBaseInfo.getActivityType())
                .appId(request.getAppId())
                .activityName(activityBaseInfo.getActivityName())
                .activityInnerName(activityBaseInfo.getActivityInnerName())
                .copyDynamicChannelInfoStr(buildCopyDynamicChannelInfoStr(request.getCopyDynamicChannelInfo()))
                .startTime(new Date(activityBaseInfo.getStartTime())).endTime(new Date(activityBaseInfo.getEndTime()))
                .quitCheck(activityBaseInfo.getQuitCheck()).newUserCheck(activityBaseInfo.getNewUserCheck()).riskCheck(activityBaseInfo.getRiskCheck())
                .showRankList(activityBaseInfo.getShowRankList()).rule(activityBaseInfo.getRule()).status(StatusEnum.EFFECTIvE.getCode())  //默认有效
                .channelId(channelId).showAvatar(posterInfo.getShowAvtar()).showNickName(posterInfo.getShowNickName()).stageNum(rewardInfoList.size()).headImage(activityBaseInfo.getActivityHeadImg()).cardImg(request.getShareCardInfo().getCardImg()).cardMinipName(request.getShareCardInfo().getMinipName()).cardTitle(request.getShareCardInfo().getCardTitle())
                .backgroundImg(activityBaseInfo.getBackgroundImg())
                .backgroundColor(activityBaseInfo.getBackgroundColor())
                .friendChannelDynamicCodeId(activityBaseInfo.getFriendChannelDynamicCodeId())
                .activityCityIds(JsonUtils.toStr(activityBaseInfo.getActivityCityIds()))
                .relevantCityIds(JsonUtils.toStr(activityBaseInfo.getRelevantCityIds()))
                .checkCitySwitch(activityBaseInfo.getCheckCitySwitch())
                .build();

        if (Objects.equals(activityBaseInfo.getActivityType(), ActivityTypeEnum.DRAW.getCode())) {
            activity.setActivityType(ActivityTypeEnum.DRAW.getCode());
            activity.setDrawConfigId(drawConfigId);
            activity.setStageNum(0);    //抽奖裂变无活动阶段概念
        }
        return activity;
    }

    private void updateDrawFissionConfig(ActivityBaseInfoRequest activityBaseInfo, GroupFissionActivity groupActivityResult, GroupFissionActivity groupFissionActivity) {
        if (Objects.equals(activityBaseInfo.getActivityType(), ActivityTypeEnum.DRAW.getCode()) && groupActivityResult.getDrawConfigId() != null) {
            FissionActivityDrawConfig drawConfig = FissionActivityDrawConfig.builder()
                    .id(groupFissionActivity.getId())
                    .originalDrawNum(activityBaseInfo.getOriginalDrawNum())
                    .incrDrawNum(activityBaseInfo.getIncrDrawNum())
                    .maxDrawNum(activityBaseInfo.getMaxDrawNum())
                    .isRelationUrl(activityBaseInfo.getIsRelationUrl())
                    .annualReportUrl(activityBaseInfo.getAnnualReportUrl()).build();
            fissionActivityDrawConfigMapper.updateByPrimaryKeySelective(drawConfig);
        }
    }

    private Long getSaveDrawConfigResult(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo) {
        Long drawConfigId = null;
        if (Objects.equals(activityBaseInfo.getActivityType(), ActivityTypeEnum.DRAW.getCode())) {
            FissionActivityDrawConfig drawConfig = FissionActivityDrawConfig.builder()
                    .appId(request.getAppId())
                    .originalDrawNum(activityBaseInfo.getOriginalDrawNum())
                    .incrDrawNum(activityBaseInfo.getIncrDrawNum())
                    .maxDrawNum(activityBaseInfo.getMaxDrawNum())
                    .isRelationUrl(activityBaseInfo.getIsRelationUrl())
                    .annualReportUrl(activityBaseInfo.getAnnualReportUrl()).build();
            fissionActivityDrawConfigMapper.insertSelective(drawConfig);
            drawConfigId = drawConfig.getId();
        }
        return drawConfigId;
    }

    private void fillDrawFissionConfig(GroupFissionActivity groupFissionActivity, ActivityBaseInfoDTO activityBaseInfoDTO) {
        if (Objects.equals(groupFissionActivity.getActivityType(), ActivityTypeEnum.DRAW.getCode()) && groupFissionActivity.getDrawConfigId() != null) {
            FissionActivityDrawConfig drawConfig = fissionActivityDrawConfigMapper.selectByPrimaryKey(groupFissionActivity.getDrawConfigId());
            if (drawConfig != null) {
                activityBaseInfoDTO.setIncrDrawNum(drawConfig.getIncrDrawNum());
                activityBaseInfoDTO.setOriginalDrawNum(drawConfig.getOriginalDrawNum());
                activityBaseInfoDTO.setMaxDrawNum(drawConfig.getMaxDrawNum());
                activityBaseInfoDTO.setIsRelationUrl(drawConfig.getIsRelationUrl());
                activityBaseInfoDTO.setAnnualReportUrl(drawConfig.getAnnualReportUrl());
            }
        }
    }


    private boolean getSavePosterResult(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfo, Long channelId) {
        GroupDynamicCodeChannelConfig codeChannelConfig = GroupDynamicCodeChannelConfig.builder()
                .appId(request.getAppId())
                .channelId(channelId)
                .poster(posterInfo.getPoster())
                .title(activityBaseInfo.getActivityName())
                .icon(posterInfo.getDynamicCodeIcon())
                .defaultPoster(posterInfo.getPoster())
                .build();
        return codeChannelConfigMapper.insertSelective(codeChannelConfig) > 0;
    }

    private SaveGroupDynamicCodeChannelResult saveGroupDynamicCodeChannelResult(String appId, ActivityBaseInfoRequest activityBaseInfo) {
        return saveDynamicCodeChannelResult(appId, activityBaseInfo, DynamicCodeChannelSceneEnum.FISSION_ACTIVITY_CHANNEL);
    }

    private SaveGroupDynamicCodeChannelResult saveDynamicCodeChannelResult(String appId,
                                                                           ActivityBaseInfoRequest activityBaseInfo,
                                                                           @NotNull DynamicCodeChannelSceneEnum sceneEnum) {
        String channelName = getChannelName(activityBaseInfo.getActivityName());
        GroupDynamicCodeChannelDTO groupChannelDTO = new GroupDynamicCodeChannelDTO();
        groupChannelDTO.setName(channelName);
        groupChannelDTO.setAppId(appId);
        groupChannelDTO.setScene(sceneEnum.getCode());
        return groupDynamicCodeChannelLocalService.saveChannel(groupChannelDTO);
    }

    private String getChannelName(String activityName) {
        // 获取当前时间的时分秒
        LocalTime time = LocalTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmss");
        String timeStr = time.format(formatter);
        return activityName + timeStr;
    }

    private SaveGroupDynamicCodeChannelResult getSaveGroupDynamicCodeChannelResult(Integer status, GroupFissionActivityRequest request, String channelName, Long channelId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.activity.fission.service.GroupFissionActivityServiceImpl.getSaveGroupDynamicCodeChannelResult(java.lang.Integer,com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest,java.lang.String,java.lang.Long)");
        GroupDynamicCodeChannelDTO groupChannelDTO = new GroupDynamicCodeChannelDTO();
        groupChannelDTO.setName(channelName);
        groupChannelDTO.setId(channelId);
        groupChannelDTO.setAppId(request.getAppId());
        groupChannelDTO.setStatus(status);

        return groupDynamicCodeChannelLocalService.saveChannel(groupChannelDTO);
    }

    private boolean getUpdatePosterResult(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfo, GroupFissionActivity groupActivityResult) {
        GroupDynamicCodeChannelConfig codeChannelConfig = GroupDynamicCodeChannelConfig.builder()
                .id(posterInfo.getId())
                .title(activityBaseInfo.getActivityName())
                .icon(posterInfo.getDynamicCodeIcon())
                .poster(posterInfo.getPoster())
                .channelId(groupActivityResult.getChannelId())
                .appId(request.getAppId())
                .defaultPoster(posterInfo.getPoster())
                .build();
        return codeChannelConfigMapper.updateByPrimaryKey(codeChannelConfig) > 0;
    }

    private GroupFissionActivity buildGroupFissionActivity(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfo, List<RewardInfoRequest> rewardInfoList, GroupFissionActivity groupActivityResult) {
        GroupFissionActivity groupFissionActivity = GroupFissionActivity.builder()
                .appId(request.getAppId())
                .id(request.getActivityId())
                .activityName(activityBaseInfo.getActivityName())
                .activityInnerName(activityBaseInfo.getActivityInnerName())
                .startTime(new Date(activityBaseInfo.getStartTime()))
                .endTime(new Date(activityBaseInfo.getEndTime()))
                .quitCheck(activityBaseInfo.getQuitCheck())
                .newUserCheck(activityBaseInfo.getNewUserCheck())
                .riskCheck(activityBaseInfo.getRiskCheck())
                .showRankList(activityBaseInfo.getShowRankList()).rule(activityBaseInfo.getRule())
                .channelId(groupActivityResult.getChannelId()).showAvatar(posterInfo.getShowAvtar()).showNickName(posterInfo.getShowNickName()).
                stageNum(rewardInfoList.size()).headImage(activityBaseInfo.getActivityHeadImg()).cardMinipName(request.getShareCardInfo().getMinipName())
                .cardImg(request.getShareCardInfo().getCardImg()).cardTitle(groupActivityResult.getCardTitle())
                .backgroundImg(activityBaseInfo.getBackgroundImg())
                .friendChannelDynamicCodeId(activityBaseInfo.getFriendChannelDynamicCodeId())
                .activityCityIds(JsonUtils.toStr(activityBaseInfo.getActivityCityIds()))
                .relevantCityIds(JsonUtils.toStr(activityBaseInfo.getRelevantCityIds()))
                .checkCitySwitch(activityBaseInfo.getCheckCitySwitch())
                .build();
        if (Objects.equals(activityBaseInfo.getActivityType(), ActivityTypeEnum.DRAW.getCode())) {
            groupFissionActivity.setStageNum(0);
        }
        return groupFissionActivity;

    }

    private String getNewMiniProgramPath(String appId, Long activityId) {
        if (StringUtils.isEmpty(appId) || activityId == null) {
            return null;
        }
        return String.format("/pages/fission-activity/activity?appId=%s&activityId=%s", appId, activityId);
    }

    private void copyDynamicCode(GroupFissionActivityRequest request, long channelId) {
        try {
            CopyDynamicChannelInfo copyDynamicChannelInfo = request.getCopyDynamicChannelInfo();
            if (copyDynamicChannelInfo == null ||
                    copyDynamicChannelInfo.getChannelId() <= 0 ||
                    CollectionUtils.isEmpty(copyDynamicChannelInfo.getActivityCityList())) {
                return;
            }

            List<Long> activityCityIdList = copyDynamicChannelInfo.getActivityCityList().stream().map(CityVO::getCityId).collect(Collectors.toList());
            List<Long> cityIdList = new ArrayList<>();
            cityIdList.addAll(activityCityIdList);

            if (CollectionUtils.isNotEmpty(copyDynamicChannelInfo.getOtherCityList())) {
                List<Long> otherCityIds = copyDynamicChannelInfo.getOtherCityList().stream().map(CityVO::getCityId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherCityIds)) {
                    if (!otherCityIds.containsAll(activityCityIdList)) {
                        cityIdList.addAll(otherCityIds);
                    } else {
                        cityIdList.addAll(otherCityIds.stream().filter(cityId -> !activityCityIdList.contains(cityId)).collect(Collectors.toList()));
                    }
                }
            }

            List<GroupDynamicCodeInfoDTO> groupDynamicCodeInfoDTOList = groupDynamicCodeLocalService.queryAllValidDynamicCodeByChannelIdAndCity(copyDynamicChannelInfo.getChannelId(), cityIdList);
            if (CollectionUtils.isEmpty(groupDynamicCodeInfoDTOList)) {
                return;
            }

            List<CompletableFuture<GroupDynamicCodeInfoDTO>> futureList = new ArrayList<>();
            for (GroupDynamicCodeInfoDTO dynamicCodeInfoDTO : groupDynamicCodeInfoDTOList) {
                if (!cityIdList.contains(dynamicCodeInfoDTO.getMtCityId())) {
                    continue;
                }

                if (CollectionUtils.isEmpty(dynamicCodeInfoDTO.getGroupIdList())) {
                    continue;
                }
                dynamicCodeInfoDTO.setChannelId(channelId);
                dynamicCodeInfoDTO.setId(null);
                CompletableFuture<GroupDynamicCodeInfoDTO> future = CompletableFuture.supplyAsync(() -> groupDynamicCodeLocalService.createCodeFromSys(dynamicCodeInfoDTO), copyDynamicCodeFromChannlePool);
                futureList.add(future);
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        } catch (Exception e) {
            log.error("copyDynamicCode has exception", e);
            throw new RuntimeException("复刻群活码异常");
        }

    }

    private String getMiniProgramQRCode(String appId, Long activityId, ActivityTypeEnum activityTypeEnum) {
        try {
            if (activityTypeEnum == null) {
                return "";
            }
            String miniProgramJumpUrl = String.format(fissionActivityMiniPConfig.getMiniPConfig().getQrCodeUrlFormat(),
                    URLEncoder.encode(getMinipPath(appId, activityId, activityTypeEnum), "UTF-8"));
            return QRCodeUtils.generateQRCodeImage(miniProgramJumpUrl, 300);
        } catch (Exception e) {
            log.error(
                    "GroupFissionActivityService.queryGroupFissionActivityList.getNewMiniProgramQRCode生成美团小程序二维码有异常, appId:{}, activityId:{}, activityTypeEnum={}",
                    appId, activityId, activityTypeEnum, e);
        }
        return null;
    }

    public String getMinipPath(String appId, Long activityId, ActivityTypeEnum activityTypeEnum) {
        try {
            if (StringUtils.isEmpty(appId)) {
                return null;
            }
            String path = getMinipPathByConfig(appId, activityId, activityTypeEnum);
            if (StringUtils.isBlank(path)) {
                return null;
            }
            return String.format(fissionActivityMiniPConfig.getMiniPConfig().getMiniPathFormat(), URLEncoder.encode(path, "UTF-8"));
        } catch (Exception e) {
            log.error(
                    "GroupFissionActivityService.queryGroupFissionActivityList.getMinipPath生成美团小程序二维码有异常, appId:{}, activityId:{}, activityTypeEnum={}",
                    appId, activityId, activityTypeEnum, e);
        }
        return null;
    }

    private String getMinipPathByConfig(String appId, Long activityId, ActivityTypeEnum activityTypeEnum)
            throws UnsupportedEncodingException {
        if (activityTypeEnum == null) {
            return "";
        }
        return URLDecoder.decode(fissionActivityMiniPConfig.getMiniPConfig().getUrl().get(activityTypeEnum.getName())
                .replace("ACTIVITYID", String.valueOf(activityId)).replace("APPID", appId), "UTF-8");
    }

}
