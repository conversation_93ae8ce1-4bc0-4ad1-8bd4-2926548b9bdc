package com.sankuai.scrm.core.service.user.enums;

import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmFilterFieldAlternativeValueVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ScrmUserTagCityType {

    UNKNOWN(-1, "未知");

    private Integer code;

    private String desc;

    public static List<ScrmFilterFieldAlternativeValueVO> getAlternativeValues() {
        return Arrays.stream(ScrmUserTagCityType.values()).map(o->{
            ScrmFilterFieldAlternativeValueVO vo = new ScrmFilterFieldAlternativeValueVO();
            vo.setValue(String.valueOf(o.getCode()));
            vo.setValueDesc(o.getDesc());
            return vo;
        }).collect(Collectors.toList());
    }
}
