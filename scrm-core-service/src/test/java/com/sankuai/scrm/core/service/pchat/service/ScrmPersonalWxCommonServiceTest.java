package com.sankuai.scrm.core.service.pchat.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.pchat.dto.InvokeResultDTO;
import com.sankuai.dz.srcm.pchat.dto.InvokeResultDTO.Call;
import com.sankuai.dz.srcm.pchat.dto.im.WxChatLogDTO;
import com.sankuai.dz.srcm.pchat.enums.TuseInvokeResponseMsg;
import com.sankuai.dz.srcm.pchat.response.scrm.GroupMemberInfoResponse;
import com.sankuai.dz.srcm.pchat.tanjing.GroupManageService;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalGroupFollowDomainService;
import com.sankuai.scrm.core.service.pchat.enums.PersonalGroupStatusEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberJoinTypeEnum;
import com.sankuai.scrm.core.service.pchat.enums.PersonalWxGroupMemberTypeEnum;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPersonalWxCommonServiceTest {

    @InjectMocks
    private ScrmPersonalWxCommonService scrmPersonalWxCommonService;

    @Mock
    private GroupManageService groupManageService;

    @Mock
    private ScrmPersonalGroupFollowDomainService personalGroupFollowDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test the failure path of cancelGroupFollow method.
     * This test case verifies that when groupManageService.robotChatRoomCancel returns a failed InvokeResultDTO,
     * the failure callback is executed (currently empty).
     */
    @Test
    public void testCancelGroupFollowFailurePath() throws Throwable {
        // arrange
        String robotSerialNo = "robot123";
        String chatroomSerialNo = "chatroom123";
        InvokeResultDTO failureResult = new InvokeResultDTO();
        // Assuming 0 indicates failure
        failureResult.setNResult(0);
        when(groupManageService.robotChatRoomCancel(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo)).thenReturn(failureResult);
        // act
        InvokeResultDTO result = scrmPersonalWxCommonService.cancelGroupFollow(robotSerialNo, chatroomSerialNo);
        // assert
        verify(personalGroupFollowDomainService, never()).deleteGroupFollower(chatroomSerialNo, robotSerialNo);
        // Assuming 0 indicates failure
        assertEquals(0L, (long) result.getNResult());
    }

    /**
     * Test the success path of cancelGroupFollow method.
     * This test case verifies that when groupManageService.robotChatRoomCancel returns a successful InvokeResultDTO,
     * the success callback is executed, and deleteGroupFollower is called.
     */
    @Test
    public void testCancelGroupFollowSuccessPath() throws Throwable {
        // arrange
        String robotSerialNo = "robot123";
        String chatroomSerialNo = "chatroom123";
        InvokeResultDTO successResult = new InvokeResultDTO();
        // Assuming 1 indicates success
        successResult.setNResult(1);
        when(groupManageService.robotChatRoomCancel(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo)).thenReturn(successResult);
        // act
        InvokeResultDTO result = scrmPersonalWxCommonService.cancelGroupFollow(robotSerialNo, chatroomSerialNo);
        // assert
        verify(personalGroupFollowDomainService, times(1)).deleteGroupFollower(chatroomSerialNo, robotSerialNo);
        // Assuming 1 indicates success
        assertEquals(1L, (long) result.getNResult());
    }

    /**
     * 测试正常情况下的 groupFollow 方法
     */
    @Test
    public void testGroupFollow_Success() throws Throwable {
        // arrange
        String robotSerialNo = "robot123";
        String chatroomSerialNo = "chatroom123";
        InvokeResultDTO expectedResult = spy(new InvokeResultDTO());
        // 设置成功状态
        expectedResult.setNResult(0);
        expectedResult.setVcResult("Success");
        // Mock isSuccess() 返回 true
        // Mock process 方法，确保它会执行 success 回调
        doAnswer(invocation -> {
            Call successCallback = invocation.getArgument(0);
            successCallback.call();
            return null;
        }).when(expectedResult).process(any(Call.class), any(Call.class));
        when(groupManageService.robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo)).thenReturn(expectedResult);
        // act
        InvokeResultDTO actualResult = scrmPersonalWxCommonService.groupFollow(robotSerialNo, chatroomSerialNo);
        // assert
        assertNotNull(actualResult);
        assertEquals(expectedResult.getNResult(), actualResult.getNResult());
        assertEquals(expectedResult.getVcResult(), actualResult.getVcResult());
        verify(groupManageService, times(1)).robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo);
        verify(personalGroupFollowDomainService, times(1)).saveGroupFollower(chatroomSerialNo, robotSerialNo);
    }

    /**
     * 测试异常情况下的 groupFollow 方法，返回失败的结果
     */
    @Test
    public void testGroupFollow_Failure() throws Throwable {
        // arrange
        String robotSerialNo = "robot123";
        String chatroomSerialNo = "chatroom123";
        InvokeResultDTO expectedResult = spy(new InvokeResultDTO());
        expectedResult.setNResult(TuseInvokeResponseMsg.MSG_4013.getCode());
        expectedResult.setVcResult(TuseInvokeResponseMsg.MSG_4013.getDesc());
        // Mock isSuccess() 返回 false
        // Mock process 方法，确保它会执行 failure 回调
        doAnswer(invocation -> {
            Call failureCallback = invocation.getArgument(1);
            failureCallback.call();
            return null;
        }).when(expectedResult).process(any(Call.class), any(Call.class));
        when(groupManageService.robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo)).thenReturn(expectedResult);
        // act
        InvokeResultDTO actualResult = scrmPersonalWxCommonService.groupFollow(robotSerialNo, chatroomSerialNo);
        // assert
        assertNotNull(actualResult);
        assertEquals(expectedResult.getNResult(), actualResult.getNResult());
        assertEquals(expectedResult.getVcResult(), actualResult.getVcResult());
        verify(groupManageService, times(1)).robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo);
    }

    /**
     * 测试 groupFollow 方法中 groupManageService.robotChatRoomOpen 返回 null 的情况
     */
    @Test
    public void testGroupFollow_NullResult() throws Throwable {
        // arrange
        String robotSerialNo = "robot123";
        String chatroomSerialNo = "chatroom123";
        when(groupManageService.robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo)).thenReturn(null);
        // act
        InvokeResultDTO actualResult = scrmPersonalWxCommonService.groupFollow(robotSerialNo, chatroomSerialNo);
        // assert
        assertNull(actualResult);
        verify(groupManageService, times(1)).robotChatRoomOpen(ApiConstants.merchantNo, robotSerialNo, chatroomSerialNo);
        verify(personalGroupFollowDomainService, never()).saveGroupFollower(anyString(), anyString());
    }

    /**
     * Test normal case with all fields populated
     */
    @Test
    public void testFillGroupMemberInfo_NormalCase() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        groupInfo.setGroupName("TestGroup");
        groupInfo.setMemberCount(10);
        groupInfo.setStatus(PersonalGroupStatusEnum.SUCCESS.getCode());
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setId(1L);
        memberInfo.setMemberNickName("TestMember");
        memberInfo.setWxNickname("WxNick");
        memberInfo.setUserSerialNo("123");
        memberInfo.setGroupId("G001");
        memberInfo.setJoinType(PersonalWxGroupMemberJoinTypeEnum.SCAN_QR.getCode());
        memberInfo.setMemberType(PersonalWxGroupMemberTypeEnum.NORMAL.getCode());
        memberInfo.setEnterTime(new Date());
        memberInfo.setAvatar("avatar.jpg");
        memberInfo.setStatus((byte) 1);
        Map<String, List<WxChatLogDTO>> latestMsgMap = new HashMap<>();
        latestMsgMap.put("123", Arrays.asList(new WxChatLogDTO()));
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, latestMsgMap, memberInfo);
        // assert
        assertNotNull(response);
        assertEquals(groupInfo.getId(), response.getGroupId());
        assertEquals(groupInfo.getGroupName(), response.getChatRoomName());
        assertEquals(memberInfo.getMemberNickName(), response.getChatRoomMemberNickname());
        assertEquals(memberInfo.getWxNickname(), response.getWxNickname());
        assertEquals(memberInfo.getUserSerialNo(), response.getMemberWxSerialNo());
        assertEquals(PersonalWxGroupMemberJoinTypeEnum.SCAN_QR.getDesc(), response.getJoinGroupWay());
        assertEquals(PersonalWxGroupMemberTypeEnum.NORMAL.getDesc(), response.getChatRoomMemberType());
        assertNotNull(response.getLatestSentMsg());
    }

    /**
     * Test case with null enum values
     */
    @Test
    public void testFillGroupMemberInfo_NullEnumValues() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        groupInfo.setStatus(null);
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setJoinType(null);
        memberInfo.setMemberType(null);
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, new HashMap<>(), memberInfo);
        // assert
        assertNotNull(response);
        assertEquals("", response.getGroupState());
        assertNull(response.getJoinGroupWay());
        assertNull(response.getChatRoomMemberType());
    }

    /**
     * Test case with null join time
     */
    @Test
    public void testFillGroupMemberInfo_NullJoinTime() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setEnterTime(null);
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, new HashMap<>(), memberInfo);
        // assert
        assertNotNull(response);
        assertNull(response.getJoinGroupTime());
    }

    /**
     * Test case with empty latestSentMsgMap
     */
    @Test
    public void testFillGroupMemberInfo_EmptyLatestSentMsgMap() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setUserSerialNo("123");
        Map<String, List<WxChatLogDTO>> emptyMap = new HashMap<>();
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, emptyMap, memberInfo);
        // assert
        assertNotNull(response);
        assertNull(response.getLatestSentMsg());
    }

    /**
     * Test case with all status fields populated
     */
    @Test
    public void testFillGroupMemberInfo_AllStatusFields() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        groupInfo.setStatus(PersonalGroupStatusEnum.SUCCESS.getCode());
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setStatus((byte) 1);
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, new HashMap<>(), memberInfo);
        // assert
        assertNotNull(response);
        assertEquals(PersonalGroupStatusEnum.SUCCESS.getDesc(), response.getGroupState());
        assertEquals(String.valueOf(PersonalGroupStatusEnum.SUCCESS.getCode()), response.getGroupStateValue());
        assertEquals("1", response.getMemberStatus());
    }

    /**
     * Test case with all member type fields populated
     */
    @Test
    public void testFillGroupMemberInfo_AllMemberTypeFields() throws Throwable {
        // arrange
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        groupInfo.setId(1L);
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        memberInfo.setMemberType(PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode());
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, new HashMap<>(), memberInfo);
        // assert
        assertNotNull(response);
        assertEquals(PersonalWxGroupMemberTypeEnum.CONSULTANT.getDesc(), response.getChatRoomMemberType());
        assertEquals(String.valueOf(PersonalWxGroupMemberTypeEnum.CONSULTANT.getCode()), response.getChatRoomMemberTypeValue());
    }

    /**
     * Test case with null input parameters
     */
    @Test
    public void testFillGroupMemberInfo_NullInputs() throws Throwable {
        // arrange
        // Create empty object instead of null
        ScrmPersonalWxGroupInfoEntity groupInfo = new ScrmPersonalWxGroupInfoEntity();
        // Create empty object instead of null
        ScrmPersonalWxGroupMemberInfoEntity memberInfo = new ScrmPersonalWxGroupMemberInfoEntity();
        // Create empty map instead of null
        Map<String, List<WxChatLogDTO>> latestMsgMap = new HashMap<>();
        // act
        GroupMemberInfoResponse response = scrmPersonalWxCommonService.fillGroupMemberInfo(groupInfo, latestMsgMap, memberInfo);
        // assert
        assertNotNull(response);
        // Since groupInfo.getId() is null
        assertNull(response.getGroupId());
        assertNull(response.getChatRoomName());
        assertNull(response.getMemberWxSerialNo());
        assertNull(response.getLatestSentMsg());
    }
}
