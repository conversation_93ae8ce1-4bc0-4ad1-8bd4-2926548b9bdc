package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionAttachmentDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationActionAttachmentDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationActionAttachmentDO, ScrmAmProcessOrchestrationActionAttachmentDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationActionAttachmentDO> list);
}