package com.sankuai.scrm.core.service.user.predict.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.user.dto.UserConsumptionAbilityResult;
import com.sankuai.scrm.core.service.user.dal.entity.userView.BusinessScoreConfig;
import com.sankuai.scrm.core.service.user.dal.entity.userView.BusinessScoreConfig.ConsumptionAbilityConfig;
import com.sankuai.scrm.core.service.user.dal.entity.userView.UserTradeView;
import com.sankuai.scrm.core.service.user.util.BusinessScoreConfigUtil;
import com.sankuai.scrm.core.service.user.util.UserDataFetchUtil;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import java.lang.reflect.Method;

/**
 * Unit tests for UserConsumptionAbilityServiceImpl#evaluateConsumptionAbility
 */
@ExtendWith(MockitoExtension.class)
public class UserConsumptionAbilityServiceImplEvaluateConsumptionAbilityTest {

    @Mock
    private UserDataFetchUtil userDataFetchUtil;

    @Mock
    private BusinessScoreConfigUtil businessScoreConfigUtil;

    @InjectMocks
    private UserConsumptionAbilityServiceImpl userConsumptionAbilityService;

    private final UserConsumptionAbilityServiceImpl service = new UserConsumptionAbilityServiceImpl();

    // Helper: create a valid ConsumptionAbilityConfig
    private ConsumptionAbilityConfig createValidConsumptionAbilityConfig() {
        ConsumptionAbilityConfig config = new ConsumptionAbilityConfig();
        config.setValueEvaluationPeriod(30);
        config.setHighValueThreshold(1000);
        config.setMediumValueThreshold(500);
        config.setLowValueThreshold(100);
        config.setFrequencyEvaluationPeriod(30);
        config.setFrequentThreshold(10);
        config.setRegularThreshold(5);
        config.setOccasionalThreshold(1);
        config.setStabilityEvaluationPeriod(30);
        config.setStableThreshold(8);
        config.setModerateThreshold(4);
        return config;
    }

    // Helper: create a valid BusinessScoreConfig with ConsumptionAbilityConfig
    private BusinessScoreConfig createValidBusinessScoreConfig() {
        BusinessScoreConfig config = new BusinessScoreConfig();
        config.setAppId("app1");
        config.setConsumptionAbilityConfig(createValidConsumptionAbilityConfig());
        return config;
    }

    /**
     * 用户主键为null，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_UserPkNull() throws Throwable {
        // arrange
        String userPk = null;
        String appId = "app1";
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When userPk is null, result should be null");
    }

    /**
     * 用户主键为空字符串，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_UserPkEmpty() throws Throwable {
        // arrange
        String userPk = "   ";
        String appId = "app1";
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When userPk is empty, result should be null");
    }

    /**
     * 获取用户交易数据失败，应该返回no_value/inactive/unstable
     */
    @Test
    public void testEvaluateConsumptionAbility_UserTradeDataNull() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(null);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertEquals("no_value", result.getValueSegment());
        assertEquals("inactive", result.getFrequencySegment());
        assertEquals("unstable", result.getStabilitySegment());
    }

    /**
     * 获取业务配置为null，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_BusinessConfigNull() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        UserTradeView tradeView = new UserTradeView();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(null);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When business config is null, result should be null");
    }

    /**
     * 消费能力配置为null，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_ConsumptionAbilityConfigNull() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        UserTradeView tradeView = new UserTradeView();
        BusinessScoreConfig config = new BusinessScoreConfig();
        config.setAppId(appId);
        config.setConsumptionAbilityConfig(null);
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(config);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When consumption ability config is null, result should be null");
    }

    /**
     * 正常路径 - 高价值用户
     * 设置交易数据使其符合高价值、高频次、稳定的消费者特征
     */
    @Test
    public void testEvaluateConsumptionAbility_HighValueUser() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        // 创建高价值用户的交易数据
        UserTradeView tradeView = new UserTradeView();
        tradeView.setUserId(123L);
        tradeView.setAppId(appId);
        // 设置高交易金额
        tradeView.setTrade30dAmt(new BigDecimal("5000"));
        // 设置高交易频次
        tradeView.setTrade30dCnt(20L);
        tradeView.setTrade30dDays(15);
        BusinessScoreConfig config = createValidBusinessScoreConfig();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(config);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        // 由于我们无法直接测试私有方法，这里我们只能验证结果不为null
        assertNotNull(result.getValueSegment());
        assertNotNull(result.getFrequencySegment());
        assertNotNull(result.getStabilitySegment());
    }

    /**
     * 正常路径 - 中等价值用户
     * 设置交易数据使其符合中等价值、中等频次的消费者特征
     */
    @Test
    public void testEvaluateConsumptionAbility_MediumValueUser() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        // 创建中等价值用户的交易数据
        UserTradeView tradeView = new UserTradeView();
        tradeView.setUserId(123L);
        tradeView.setAppId(appId);
        // 设置中等交易金额
        tradeView.setTrade30dAmt(new BigDecimal("700"));
        // 设置中等交易频次
        tradeView.setTrade30dCnt(7L);
        tradeView.setTrade30dDays(5);
        BusinessScoreConfig config = createValidBusinessScoreConfig();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(config);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertNotNull(result.getValueSegment());
        assertNotNull(result.getFrequencySegment());
        assertNotNull(result.getStabilitySegment());
    }

    /**
     * 正常路径 - 低价值用户
     * 设置交易数据使其符合低价值、低频次的消费者特征
     */
    @Test
    public void testEvaluateConsumptionAbility_LowValueUser() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        // 创建低价值用户的交易数据
        UserTradeView tradeView = new UserTradeView();
        tradeView.setUserId(123L);
        tradeView.setAppId(appId);
        // 设置低交易金额
        tradeView.setTrade30dAmt(new BigDecimal("50"));
        // 设置低交易频次
        tradeView.setTrade30dCnt(1L);
        tradeView.setTrade30dDays(1);
        BusinessScoreConfig config = createValidBusinessScoreConfig();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(config);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertNotNull(result.getValueSegment());
        assertNotNull(result.getFrequencySegment());
        assertNotNull(result.getStabilitySegment());
    }

    /**
     * evaluateConsumptionAbility方法内部抛出异常，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_Exception() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenThrow(new RuntimeException("mock exception"));
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When exception occurs, result should be null");
    }

    /**
     * appId为null，业务配置返回null，应该返回null
     */
    @Test
    public void testEvaluateConsumptionAbility_AppIdNull() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = null;
        UserTradeView tradeView = new UserTradeView();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(null);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNull(result, "When appId is null and business config is null, result should be null");
    }

    /**
     * 用户交易数据存在但部分字段为null，仍能正常分层
     */
    @Test
    public void testEvaluateConsumptionAbility_UserTradeViewPartialNull() throws Throwable {
        // arrange
        String userPk = "user1";
        String appId = "app1";
        // 所有字段为null
        UserTradeView tradeView = new UserTradeView();
        BusinessScoreConfig config = createValidBusinessScoreConfig();
        when(userDataFetchUtil.fetchUserTradeData(eq(userPk), eq(appId))).thenReturn(tradeView);
        when(businessScoreConfigUtil.getBusinessConfig(eq(appId))).thenReturn(config);
        // act
        UserConsumptionAbilityResult result = userConsumptionAbilityService.evaluateConsumptionAbility(userPk, appId);
        // assert
        assertNotNull(result);
        assertEquals(userPk, result.getUserPk());
        assertNotNull(result.getValueSegment());
        assertNotNull(result.getFrequencySegment());
        assertNotNull(result.getStabilitySegment());
    }

    private String invokeEvaluateStabilitySegment(UserTradeView tradeData, BusinessScoreConfig.ConsumptionAbilityConfig config) throws Exception {
        UserConsumptionAbilityServiceImpl service = new UserConsumptionAbilityServiceImpl();
        Method method = UserConsumptionAbilityServiceImpl.class.getDeclaredMethod("evaluateStabilitySegment", UserTradeView.class, BusinessScoreConfig.ConsumptionAbilityConfig.class);
        method.setAccessible(true);
        return (String) method.invoke(service, tradeData, config);
    }

    @Test
    public void testEvaluateStabilitySegment_30DaysStable() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dDays(15);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setStabilityEvaluationPeriod(30);
        config.setStableThreshold(10);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("stable", result);
    }

    @Test
    public void testEvaluateStabilitySegment_60DaysModerate() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade60dDays(8);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setStabilityEvaluationPeriod(60);
        config.setStableThreshold(15);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("moderate", result);
    }

    @Test
    public void testEvaluateStabilitySegment_90DaysUnstable() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade90dDays(3);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setStabilityEvaluationPeriod(90);
        config.setStableThreshold(10);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("unstable", result);
    }

    @Test
    public void testEvaluateStabilitySegment_EqualModerateThreshold() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dDays(5);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setStabilityEvaluationPeriod(30);
        config.setStableThreshold(10);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("moderate", result);
    }

    @Test
    public void testEvaluateStabilitySegment_NonStandardPeriodUseDefault() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dDays(8);
        // 这个值不应该被使用
        tradeData.setTrade60dDays(20);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        // 非标准周期
        config.setStabilityEvaluationPeriod(45);
        config.setStableThreshold(10);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("moderate", result);
    }

    @Test
    public void testEvaluateStabilitySegment_NullTradeDaysUseDefault() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        // 设置为null
        tradeData.setTrade30dDays(null);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setStabilityEvaluationPeriod(30);
        config.setStableThreshold(10);
        config.setModerateThreshold(5);
        // act
        String result = invokeEvaluateStabilitySegment(tradeData, config);
        // assert
        assertEquals("unstable", result);
    }

    private String invokeEvaluateFrequencySegment(UserTradeView tradeData, BusinessScoreConfig.ConsumptionAbilityConfig config) throws Exception {
        UserConsumptionAbilityServiceImpl service = new UserConsumptionAbilityServiceImpl();
        Method method = UserConsumptionAbilityServiceImpl.class.getDeclaredMethod("evaluateFrequencySegment", UserTradeView.class, BusinessScoreConfig.ConsumptionAbilityConfig.class);
        method.setAccessible(true);
        return (String) method.invoke(service, tradeData, config);
    }

    @Test
    public void testEvaluateFrequencySegment30DaysFrequent() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(20L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(30);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("frequent", result);
    }

    @Test
    public void testEvaluateFrequencySegment60DaysRegular() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade60dCnt(12L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(60);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("regular", result);
    }

    @Test
    public void testEvaluateFrequencySegment90DaysOccasional() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade90dCnt(6L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(90);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("occasional", result);
    }

    @Test
    public void testEvaluateFrequencySegmentInvalidPeriodInactive() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(2L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        // 无效周期
        config.setFrequencyEvaluationPeriod(45);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("inactive", result);
    }

    @Test
    public void testEvaluateFrequencySegmentNullTradeData() throws Throwable {
        // arrange
        // 所有字段为null
        UserTradeView tradeData = new UserTradeView();
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(30);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("inactive", result);
    }

    @Test
    public void testEvaluateFrequencySegmentThresholdBoundary() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        // 正好等于regular阈值
        tradeData.setTrade30dCnt(10L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(30);
        config.setFrequentThreshold(15);
        config.setRegularThreshold(10);
        config.setOccasionalThreshold(5);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("regular", result);
    }

    @Test
    public void testEvaluateFrequencySegmentAllZeroThresholds() throws Throwable {
        // arrange
        UserTradeView tradeData = new UserTradeView();
        tradeData.setTrade30dCnt(1L);
        BusinessScoreConfig.ConsumptionAbilityConfig config = new BusinessScoreConfig.ConsumptionAbilityConfig();
        config.setFrequencyEvaluationPeriod(30);
        config.setFrequentThreshold(0);
        config.setRegularThreshold(0);
        config.setOccasionalThreshold(0);
        // act
        String result = invokeEvaluateFrequencySegment(tradeData, config);
        // assert
        assertEquals("frequent", result);
    }
}
