package com.sankuai.scrm.core.service.open.producer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.open.msgbody.ScrmOpenGroupMsgBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
public class ScrmOpenEntGroupMessageProducerSendMessageTest {

    @Mock
    private IProducerProcessor<?, String> producer;

    @InjectMocks
    private ScrmOpenEntGroupMessageProducer messageProducer;

    private ScrmOpenGroupMsgBody createTestMessageBody() {
        ScrmOpenGroupMsgBody messageBody = new ScrmOpenGroupMsgBody();
        messageBody.setAppId("testAppId");
        messageBody.setGroupId("testGroupId");
        return messageBody;
    }

    @Test
    public void testSendMessageSuccessfully() throws Throwable {
        // arrange
        ScrmOpenGroupMsgBody messageBody = createTestMessageBody();
        String expectedJson = "{\"appId\":\"testAppId\",\"groupId\":\"testGroupId\"}";
        when(producer.sendMessage(any())).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        // act
        messageProducer.sendMessage(messageBody);
        // assert
        verify(producer).sendMessage(any());
    }

    @Test
    public void testSendMessageWithProducerException() throws Throwable {
        // arrange
        ScrmOpenGroupMsgBody messageBody = createTestMessageBody();
        Exception mockException = new RuntimeException("test exception");
        when(producer.sendMessage(any())).thenThrow(mockException);
        try (MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // act
            messageProducer.sendMessage(messageBody);
            // assert
            verify(producer).sendMessage(any());
            catMock.verify(() -> Cat.logError(eq("ScrmOpenEntGroupMessageProducer.sendMessage error"), eq(mockException)));
        }
    }

    @Test
    public void testSendMessageWithNullBody() throws Throwable {
        // arrange
        when(producer.sendMessage("null")).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        // act
        messageProducer.sendMessage(null);
        // assert
        verify(producer).sendMessage("null");
    }
}
