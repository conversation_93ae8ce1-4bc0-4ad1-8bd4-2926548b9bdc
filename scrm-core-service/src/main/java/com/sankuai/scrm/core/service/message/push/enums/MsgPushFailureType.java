package com.sankuai.scrm.core.service.message.push.enums;

import lombok.Getter;

@Getter
public enum MsgPushFailureType {
    NOT_FRIEND(1, "客户不是好友"),
    RECEIVED_OTHER_MSG(2, "客户已经收到其他群发消息"),
    <PERSON>THER(3, "其他原因");

    private final int code;

    private final String desc;

    MsgPushFailureType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MsgPushFailureType getMsgPushFailureTypeByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MsgPushFailureType type : MsgPushFailureType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
