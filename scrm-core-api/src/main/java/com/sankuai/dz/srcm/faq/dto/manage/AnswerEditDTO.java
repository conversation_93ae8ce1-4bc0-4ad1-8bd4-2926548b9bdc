package com.sankuai.dz.srcm.faq.dto.manage;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.srcm.faq.dto.common.AnswerItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> on 2022/12/7 10:39 AM
 **/
@Builder()
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AnswerEditDTO implements Serializable {

    @FieldDoc(description = "答案ID")
    private Long id;

    @FieldDoc(description = "答案内容")
    private List<AnswerItem> body;

    private Integer type;

}
