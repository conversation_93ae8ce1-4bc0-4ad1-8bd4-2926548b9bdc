package com.sankuai.scrm.core.service.faq.facade;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.beautycontent.taskservice.dto.ServerResponse;
import com.sankuai.beautycontent.taskservice.enums.ExecuteStatusEnum;
import com.sankuai.beautycontent.taskservice.request.ClientRequestBody;
import com.sankuai.beautycontent.taskservice.service.callback.TaskCallbackSpi;
import com.sankuai.beautycontent.taskservice.service.impl.AbstractTaskCallbackSpi;
import com.sankuai.dz.srcm.faq.facade.FaqAdminManageFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2023/2/8 2:04 PM
 **/
@MdpPigeonServer(
        serviceInterface = TaskCallbackSpi.class,
        url = "com.sankuai.scrm.core.service.faq.facade.KeywordRefreshCallback"
)
@Slf4j
public class KeywordRefreshCallback extends AbstractTaskCallbackSpi {
    @Autowired
    private FaqAdminManageFacade manageFacade;

    @Override
    public List<ServerResponse> doExecuteTask(List<ClientRequestBody> clientRequestBodyList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.facade.KeywordRefreshCallback.doExecuteTask(java.util.List)");
        if(CollectionUtils.isEmpty(clientRequestBodyList))return Lists.newArrayList();
        List<ServerResponse> responseList = Lists.newArrayList();
        try{
            for(ClientRequestBody body:clientRequestBodyList){
                if(StringUtils.isEmpty(body.getRequestBody())){
                    responseList.add(buildResponse(body,true));
                    continue;
                }
                String appId = body.getRequestBody();
                if(StringUtils.isEmpty(appId)){
                    Cat.logEvent("KeywordUpdateCallback","emptyAppId");
                    responseList.add(buildResponse(body,true));
                }
                RemoteResponse<Boolean> res = manageFacade.refreshKeywordData(appId);
                boolean refresh = res.isSuccess() && res.getData();
                responseList.add(buildResponse(body,refresh));
                if(!refresh){
                    Cat.logEvent("KeywordUpdateCallbackFail",StringUtils.defaultIfEmpty(res.getMsg(),"empty"));
                }
            }
        }catch(Exception e){
            log.error("KeywordUpdateCallback request={}",clientRequestBodyList,e);
        }
        return responseList;
    }

    private ServerResponse buildResponse(ClientRequestBody body,boolean success){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.scrm.core.service.faq.facade.KeywordRefreshCallback.buildResponse(com.sankuai.beautycontent.taskservice.request.ClientRequestBody,boolean)");
        return ServerResponse.builder()
                .id(body.getId())
                .executeStatus(success? ExecuteStatusEnum.SUCCESS:ExecuteStatusEnum.FAIL)
                .executeTime(new Date())
                .build();
    }
}
