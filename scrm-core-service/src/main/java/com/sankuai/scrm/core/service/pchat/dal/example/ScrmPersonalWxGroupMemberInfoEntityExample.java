package com.sankuai.scrm.core.service.pchat.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmPersonalWxGroupMemberInfoEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmPersonalWxGroupMemberInfoEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmPersonalWxGroupMemberInfoEntityExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxGroupMemberInfoEntityExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmPersonalWxGroupMemberInfoEntityExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andWxIdIsNull() {
            addCriterion("wx_id is null");
            return (Criteria) this;
        }

        public Criteria andWxIdIsNotNull() {
            addCriterion("wx_id is not null");
            return (Criteria) this;
        }

        public Criteria andWxIdEqualTo(String value) {
            addCriterion("wx_id =", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdNotEqualTo(String value) {
            addCriterion("wx_id <>", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdGreaterThan(String value) {
            addCriterion("wx_id >", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdGreaterThanOrEqualTo(String value) {
            addCriterion("wx_id >=", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdLessThan(String value) {
            addCriterion("wx_id <", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdLessThanOrEqualTo(String value) {
            addCriterion("wx_id <=", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdLike(String value) {
            addCriterion("wx_id like", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdNotLike(String value) {
            addCriterion("wx_id not like", value, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdIn(List<String> values) {
            addCriterion("wx_id in", values, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdNotIn(List<String> values) {
            addCriterion("wx_id not in", values, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdBetween(String value1, String value2) {
            addCriterion("wx_id between", value1, value2, "wxId");
            return (Criteria) this;
        }

        public Criteria andWxIdNotBetween(String value1, String value2) {
            addCriterion("wx_id not between", value1, value2, "wxId");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoIsNull() {
            addCriterion("user_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoIsNotNull() {
            addCriterion("user_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoEqualTo(String value) {
            addCriterion("user_serial_no =", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoNotEqualTo(String value) {
            addCriterion("user_serial_no <>", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoGreaterThan(String value) {
            addCriterion("user_serial_no >", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("user_serial_no >=", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoLessThan(String value) {
            addCriterion("user_serial_no <", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoLessThanOrEqualTo(String value) {
            addCriterion("user_serial_no <=", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoLike(String value) {
            addCriterion("user_serial_no like", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoNotLike(String value) {
            addCriterion("user_serial_no not like", value, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoIn(List<String> values) {
            addCriterion("user_serial_no in", values, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoNotIn(List<String> values) {
            addCriterion("user_serial_no not in", values, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoBetween(String value1, String value2) {
            addCriterion("user_serial_no between", value1, value2, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUserSerialNoNotBetween(String value1, String value2) {
            addCriterion("user_serial_no not between", value1, value2, "userSerialNo");
            return (Criteria) this;
        }

        public Criteria andUnionIdIsNull() {
            addCriterion("union_id is null");
            return (Criteria) this;
        }

        public Criteria andUnionIdIsNotNull() {
            addCriterion("union_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnionIdEqualTo(String value) {
            addCriterion("union_id =", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdNotEqualTo(String value) {
            addCriterion("union_id <>", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdGreaterThan(String value) {
            addCriterion("union_id >", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdGreaterThanOrEqualTo(String value) {
            addCriterion("union_id >=", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdLessThan(String value) {
            addCriterion("union_id <", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdLessThanOrEqualTo(String value) {
            addCriterion("union_id <=", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdLike(String value) {
            addCriterion("union_id like", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdNotLike(String value) {
            addCriterion("union_id not like", value, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdIn(List<String> values) {
            addCriterion("union_id in", values, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdNotIn(List<String> values) {
            addCriterion("union_id not in", values, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdBetween(String value1, String value2) {
            addCriterion("union_id between", value1, value2, "unionId");
            return (Criteria) this;
        }

        public Criteria andUnionIdNotBetween(String value1, String value2) {
            addCriterion("union_id not between", value1, value2, "unionId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(String value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(String value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(String value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(String value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(String value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLike(String value) {
            addCriterion("group_id like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotLike(String value) {
            addCriterion("group_id not like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<String> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<String> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(String value1, String value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(String value1, String value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andJoinTypeIsNull() {
            addCriterion("join_type is null");
            return (Criteria) this;
        }

        public Criteria andJoinTypeIsNotNull() {
            addCriterion("join_type is not null");
            return (Criteria) this;
        }

        public Criteria andJoinTypeEqualTo(Integer value) {
            addCriterion("join_type =", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeNotEqualTo(Integer value) {
            addCriterion("join_type <>", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeGreaterThan(Integer value) {
            addCriterion("join_type >", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("join_type >=", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeLessThan(Integer value) {
            addCriterion("join_type <", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeLessThanOrEqualTo(Integer value) {
            addCriterion("join_type <=", value, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeIn(List<Integer> values) {
            addCriterion("join_type in", values, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeNotIn(List<Integer> values) {
            addCriterion("join_type not in", values, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeBetween(Integer value1, Integer value2) {
            addCriterion("join_type between", value1, value2, "joinType");
            return (Criteria) this;
        }

        public Criteria andJoinTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("join_type not between", value1, value2, "joinType");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdIsNull() {
            addCriterion("invite_wx_id is null");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdIsNotNull() {
            addCriterion("invite_wx_id is not null");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdEqualTo(String value) {
            addCriterion("invite_wx_id =", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdNotEqualTo(String value) {
            addCriterion("invite_wx_id <>", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdGreaterThan(String value) {
            addCriterion("invite_wx_id >", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdGreaterThanOrEqualTo(String value) {
            addCriterion("invite_wx_id >=", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdLessThan(String value) {
            addCriterion("invite_wx_id <", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdLessThanOrEqualTo(String value) {
            addCriterion("invite_wx_id <=", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdLike(String value) {
            addCriterion("invite_wx_id like", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdNotLike(String value) {
            addCriterion("invite_wx_id not like", value, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdIn(List<String> values) {
            addCriterion("invite_wx_id in", values, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdNotIn(List<String> values) {
            addCriterion("invite_wx_id not in", values, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdBetween(String value1, String value2) {
            addCriterion("invite_wx_id between", value1, value2, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andInviteWxIdNotBetween(String value1, String value2) {
            addCriterion("invite_wx_id not between", value1, value2, "inviteWxId");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameIsNull() {
            addCriterion("member_nick_name is null");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameIsNotNull() {
            addCriterion("member_nick_name is not null");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameEqualTo(String value) {
            addCriterion("member_nick_name =", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameNotEqualTo(String value) {
            addCriterion("member_nick_name <>", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameGreaterThan(String value) {
            addCriterion("member_nick_name >", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameGreaterThanOrEqualTo(String value) {
            addCriterion("member_nick_name >=", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameLessThan(String value) {
            addCriterion("member_nick_name <", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameLessThanOrEqualTo(String value) {
            addCriterion("member_nick_name <=", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameLike(String value) {
            addCriterion("member_nick_name like", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameNotLike(String value) {
            addCriterion("member_nick_name not like", value, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameIn(List<String> values) {
            addCriterion("member_nick_name in", values, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameNotIn(List<String> values) {
            addCriterion("member_nick_name not in", values, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameBetween(String value1, String value2) {
            addCriterion("member_nick_name between", value1, value2, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andMemberNickNameNotBetween(String value1, String value2) {
            addCriterion("member_nick_name not between", value1, value2, "memberNickName");
            return (Criteria) this;
        }

        public Criteria andWxNicknameIsNull() {
            addCriterion("wx_nickname is null");
            return (Criteria) this;
        }

        public Criteria andWxNicknameIsNotNull() {
            addCriterion("wx_nickname is not null");
            return (Criteria) this;
        }

        public Criteria andWxNicknameEqualTo(String value) {
            addCriterion("wx_nickname =", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameNotEqualTo(String value) {
            addCriterion("wx_nickname <>", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameGreaterThan(String value) {
            addCriterion("wx_nickname >", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameGreaterThanOrEqualTo(String value) {
            addCriterion("wx_nickname >=", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameLessThan(String value) {
            addCriterion("wx_nickname <", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameLessThanOrEqualTo(String value) {
            addCriterion("wx_nickname <=", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameLike(String value) {
            addCriterion("wx_nickname like", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameNotLike(String value) {
            addCriterion("wx_nickname not like", value, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameIn(List<String> values) {
            addCriterion("wx_nickname in", values, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameNotIn(List<String> values) {
            addCriterion("wx_nickname not in", values, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameBetween(String value1, String value2) {
            addCriterion("wx_nickname between", value1, value2, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andWxNicknameNotBetween(String value1, String value2) {
            addCriterion("wx_nickname not between", value1, value2, "wxNickname");
            return (Criteria) this;
        }

        public Criteria andAvatarIsNull() {
            addCriterion("avatar is null");
            return (Criteria) this;
        }

        public Criteria andAvatarIsNotNull() {
            addCriterion("avatar is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarEqualTo(String value) {
            addCriterion("avatar =", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarNotEqualTo(String value) {
            addCriterion("avatar <>", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarGreaterThan(String value) {
            addCriterion("avatar >", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarGreaterThanOrEqualTo(String value) {
            addCriterion("avatar >=", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarLessThan(String value) {
            addCriterion("avatar <", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarLessThanOrEqualTo(String value) {
            addCriterion("avatar <=", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarLike(String value) {
            addCriterion("avatar like", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarNotLike(String value) {
            addCriterion("avatar not like", value, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarIn(List<String> values) {
            addCriterion("avatar in", values, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarNotIn(List<String> values) {
            addCriterion("avatar not in", values, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarBetween(String value1, String value2) {
            addCriterion("avatar between", value1, value2, "avatar");
            return (Criteria) this;
        }

        public Criteria andAvatarNotBetween(String value1, String value2) {
            addCriterion("avatar not between", value1, value2, "avatar");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIsNull() {
            addCriterion("member_type is null");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIsNotNull() {
            addCriterion("member_type is not null");
            return (Criteria) this;
        }

        public Criteria andMemberTypeEqualTo(Byte value) {
            addCriterion("member_type =", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotEqualTo(Byte value) {
            addCriterion("member_type <>", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeGreaterThan(Byte value) {
            addCriterion("member_type >", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("member_type >=", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeLessThan(Byte value) {
            addCriterion("member_type <", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeLessThanOrEqualTo(Byte value) {
            addCriterion("member_type <=", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIn(List<Byte> values) {
            addCriterion("member_type in", values, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotIn(List<Byte> values) {
            addCriterion("member_type not in", values, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeBetween(Byte value1, Byte value2) {
            addCriterion("member_type between", value1, value2, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("member_type not between", value1, value2, "memberType");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkIsNull() {
            addCriterion("group_remark is null");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkIsNotNull() {
            addCriterion("group_remark is not null");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkEqualTo(String value) {
            addCriterion("group_remark =", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkNotEqualTo(String value) {
            addCriterion("group_remark <>", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkGreaterThan(String value) {
            addCriterion("group_remark >", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("group_remark >=", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkLessThan(String value) {
            addCriterion("group_remark <", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkLessThanOrEqualTo(String value) {
            addCriterion("group_remark <=", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkLike(String value) {
            addCriterion("group_remark like", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkNotLike(String value) {
            addCriterion("group_remark not like", value, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkIn(List<String> values) {
            addCriterion("group_remark in", values, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkNotIn(List<String> values) {
            addCriterion("group_remark not in", values, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkBetween(String value1, String value2) {
            addCriterion("group_remark between", value1, value2, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andGroupRemarkNotBetween(String value1, String value2) {
            addCriterion("group_remark not between", value1, value2, "groupRemark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andEnterTimeIsNull() {
            addCriterion("enter_time is null");
            return (Criteria) this;
        }

        public Criteria andEnterTimeIsNotNull() {
            addCriterion("enter_time is not null");
            return (Criteria) this;
        }

        public Criteria andEnterTimeEqualTo(Date value) {
            addCriterion("enter_time =", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeNotEqualTo(Date value) {
            addCriterion("enter_time <>", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeGreaterThan(Date value) {
            addCriterion("enter_time >", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("enter_time >=", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeLessThan(Date value) {
            addCriterion("enter_time <", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeLessThanOrEqualTo(Date value) {
            addCriterion("enter_time <=", value, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeIn(List<Date> values) {
            addCriterion("enter_time in", values, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeNotIn(List<Date> values) {
            addCriterion("enter_time not in", values, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeBetween(Date value1, Date value2) {
            addCriterion("enter_time between", value1, value2, "enterTime");
            return (Criteria) this;
        }

        public Criteria andEnterTimeNotBetween(Date value1, Date value2) {
            addCriterion("enter_time not between", value1, value2, "enterTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTimeTraceIsNull() {
            addCriterion("time_trace is null");
            return (Criteria) this;
        }

        public Criteria andTimeTraceIsNotNull() {
            addCriterion("time_trace is not null");
            return (Criteria) this;
        }

        public Criteria andTimeTraceEqualTo(Long value) {
            addCriterion("time_trace =", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceNotEqualTo(Long value) {
            addCriterion("time_trace <>", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceGreaterThan(Long value) {
            addCriterion("time_trace >", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceGreaterThanOrEqualTo(Long value) {
            addCriterion("time_trace >=", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceLessThan(Long value) {
            addCriterion("time_trace <", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceLessThanOrEqualTo(Long value) {
            addCriterion("time_trace <=", value, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceIn(List<Long> values) {
            addCriterion("time_trace in", values, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceNotIn(List<Long> values) {
            addCriterion("time_trace not in", values, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceBetween(Long value1, Long value2) {
            addCriterion("time_trace between", value1, value2, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andTimeTraceNotBetween(Long value1, Long value2) {
            addCriterion("time_trace not between", value1, value2, "timeTrace");
            return (Criteria) this;
        }

        public Criteria andExitTimeIsNull() {
            addCriterion("exit_time is null");
            return (Criteria) this;
        }

        public Criteria andExitTimeIsNotNull() {
            addCriterion("exit_time is not null");
            return (Criteria) this;
        }

        public Criteria andExitTimeEqualTo(Date value) {
            addCriterion("exit_time =", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeNotEqualTo(Date value) {
            addCriterion("exit_time <>", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeGreaterThan(Date value) {
            addCriterion("exit_time >", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("exit_time >=", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeLessThan(Date value) {
            addCriterion("exit_time <", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeLessThanOrEqualTo(Date value) {
            addCriterion("exit_time <=", value, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeIn(List<Date> values) {
            addCriterion("exit_time in", values, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeNotIn(List<Date> values) {
            addCriterion("exit_time not in", values, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeBetween(Date value1, Date value2) {
            addCriterion("exit_time between", value1, value2, "exitTime");
            return (Criteria) this;
        }

        public Criteria andExitTimeNotBetween(Date value1, Date value2) {
            addCriterion("exit_time not between", value1, value2, "exitTime");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoIsNull() {
            addCriterion("invite_user_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoIsNotNull() {
            addCriterion("invite_user_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoEqualTo(String value) {
            addCriterion("invite_user_serial_no =", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoNotEqualTo(String value) {
            addCriterion("invite_user_serial_no <>", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoGreaterThan(String value) {
            addCriterion("invite_user_serial_no >", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("invite_user_serial_no >=", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoLessThan(String value) {
            addCriterion("invite_user_serial_no <", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoLessThanOrEqualTo(String value) {
            addCriterion("invite_user_serial_no <=", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoLike(String value) {
            addCriterion("invite_user_serial_no like", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoNotLike(String value) {
            addCriterion("invite_user_serial_no not like", value, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoIn(List<String> values) {
            addCriterion("invite_user_serial_no in", values, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoNotIn(List<String> values) {
            addCriterion("invite_user_serial_no not in", values, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoBetween(String value1, String value2) {
            addCriterion("invite_user_serial_no between", value1, value2, "inviteUserSerialNo");
            return (Criteria) this;
        }

        public Criteria andInviteUserSerialNoNotBetween(String value1, String value2) {
            addCriterion("invite_user_serial_no not between", value1, value2, "inviteUserSerialNo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}