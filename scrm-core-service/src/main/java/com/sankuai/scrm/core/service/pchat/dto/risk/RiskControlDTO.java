package com.sankuai.scrm.core.service.pchat.dto.risk;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/3/7 14:56
 * @Version v1.0.0
 */
@Data
public class RiskControlDTO {
    /**
     * 是否影响全部机器人 1-全部 0-不为全部（注：当allrobot不为全部时，才返回userType对应的枚举值）
     */
    private Integer allRobot;
    /**
     * 机器人类型 0.全部机器人，1.平台号，2.扫码号，3.托管号
     */
    private Integer userType;
    /**
     * 1-私聊/群聊/群公告敏感词
     * 2-设置群内昵称敏感词
     * 3-修改群名称敏感词
     * 4-修改个性签名敏感词
     * 5-修改机器人昵称敏感词
     * 6-机器人主动添加好友
     * 7-强加人接口
     */
    private Integer keywordType;
    /**
     * 影响机器人的序列号集合
     */
    private List<String> robotSerialNos;
    /**
     * 规则集合
     */
    private List<LimitRule> limitRules;
    /**
     * 规则名
     */
    private String ruleName;

    @Data
    public static class LimitRule {
        /**
         * ADD-新增 DELETE-删除
         */
        private String type;
        /**
         * 消息类型
         * 2001: 文本  2002 ：图片2013: 小程序 2005：链接
         * (keywordType为1时可用)
         */
        private Integer msgType;
        /**
         * 敏感词
         */
        private String word;
        /**
         * 过期时间(值为空，则无期限)
         */
        private String expireTime;
    }
}
