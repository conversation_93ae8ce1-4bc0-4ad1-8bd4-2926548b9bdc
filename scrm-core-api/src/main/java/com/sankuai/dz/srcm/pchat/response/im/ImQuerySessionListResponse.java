package com.sankuai.dz.srcm.pchat.response.im;

import com.sankuai.dz.srcm.pchat.dto.PagerList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ImQuerySessionListResponse implements Serializable {

    private PagerList<MsgRecordList> sessionList;

    /**
     * 机器人序列号
     */
    private String robotSerialNo;

    /**
     *
     */
    private PagerList<GroupList> groupList;

    /**
     * 联系人列表
     */
    private PagerList<FriendList> friendList;

}
