package com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.joinway;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 企业微信客户群"加入群聊"管理 - 更新客户群进群方式配置接口响应
 * https://developer.work.weixin.qq.com/document/path/92229#%E6%9B%B4%E6%96%B0%E5%AE%A2%E6%88%B7%E7%BE%A4%E8%BF%9B%E7%BE%A4%E6%96%B9%E5%BC%8F%E9%85%8D%E7%BD%AE
 */
@Data
public class WxGroupUpdateJoinWayRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("config_id")
    private String configId;

    private Integer scene;

    private String remark;

    @JsonProperty("auto_create_room")
    private Integer autoCreateRoom;

    @JsonProperty("room_base_name")
    private String roomBaseName;

    @JsonProperty("room_base_id")
    private Integer roomBaseId;

    @JsonProperty("chat_id_list")
    private List<String> chatIdList;

    private String state;
}
