package com.sankuai.scrm.core.service.pchat.dto.robotFunction;

import lombok.Data;

/**
 * 3023
 * @Description 【直接回调】好友回复验证消息回调接口
 * <AUTHOR>
 * @Create On 2023/11/6 17:35
 * @Version v1.0.0
 */
@Data
public class NewFriendReplyValidateMsgDTO {

    /**
     * 回复消息的用户微信ID
     */
    private String vcNewFriendWxId;

    /**
     * 回复消息的用户微信编号（MD5加密的微信ID）
     */
    private String vcNewFriendSerialNo;

    /**
     * 0 未知 1 男 2 女
     */
    private Integer nSex;

    /**
     * 回复消息的用户微信昵称
     */
    private String vcNickName;

    /**
     * Base64编码的回复消息的用户微信昵称
     */
    private String vcBase64NickName;

    /**
     * 回复消息的用户微信号
     */
    private String vcWxAlias;

    /**
     *
     */
    private String vcGexingQianming;

    /**
     * 回复消息的用户微信头像
     */
    private String vcHeadImgUrl;

    /**
     * 用户回复验证消息内容
     */
    private String vcContent;

    /**
     * 用户回复验证消息时间
     */
    private String dtCreateDate;

    /**
     * 回调的时间戳
     */
    private Long nTimeStamp;
}
