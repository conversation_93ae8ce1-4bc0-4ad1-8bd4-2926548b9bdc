package com.sankuai.scrm.core.service.pchat.adapter.service.ent;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.srcm.pchat.response.im.ImRobotListResponse;
import com.sankuai.scrm.core.service.external.contact.bo.StaffFriendNumBO;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserInfo;
import com.sankuai.scrm.core.service.pchat.domain.im.PChatWxImDomainService;
import java.lang.reflect.Method;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EntRobotProcessorTest {

    @InjectMocks
    private EntRobotProcessor entRobotProcessor;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private PChatWxImDomainService wxImDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // Helper methods to create test data
    private ScrmPersonalWxGroupMemberInfoEntity createScrmPersonalWxGroupMemberInfoEntity(String userSerialNo) {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setUserSerialNo(userSerialNo);
        entity.setAvatar("avatar" + userSerialNo.substring(userSerialNo.length() - 1));
        entity.setWxNickname("nickname" + userSerialNo.substring(userSerialNo.length() - 1));
        return entity;
    }

    private ScrmPersonalWxUserInfo createScrmPersonalWxUserInfo(String serialNo, Long id) {
        ScrmPersonalWxUserInfo userInfo = new ScrmPersonalWxUserInfo();
        userInfo.setSerialNo(serialNo);
        userInfo.setId(id);
        return userInfo;
    }

    private AssistantInfo createAssistantInfo(String accountId) {
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAccountId(accountId);
        return assistantInfo;
    }

    private StaffFriendNumBO createStaffFriendNumBO(String staffId, int friendNum) {
        StaffFriendNumBO staffFriendNumBO = new StaffFriendNumBO();
        staffFriendNumBO.setStaffId(staffId);
        staffFriendNumBO.setFriendNum(friendNum);
        return staffFriendNumBO;
    }

    /**
     * 测试 appId 为空的情况
     */
    @Test
    public void testGetRobotAndFriendCountMap_AppIdIsNull() throws Throwable {
        // arrange
        String appId = null;
        Set<String> robotAccountSet = new HashSet<>();
        robotAccountSet.add("robot1");
        // Use reflection to invoke the private method
        Method method = EntRobotProcessor.class.getDeclaredMethod("getRobotAndFriendCountMap", String.class, Set.class);
        method.setAccessible(true);
        Map<String, Integer> result = (Map<String, Integer>) method.invoke(entRobotProcessor, appId, robotAccountSet);
        // assert
        assertEquals(Maps.newHashMap(), result);
    }

    /**
     * 测试 robotAccountSet 为空的情况
     */
    @Test
    public void testGetRobotAndFriendCountMap_RobotAccountSetIsEmpty() throws Throwable {
        // arrange
        String appId = "app1";
        Set<String> robotAccountSet = new HashSet<>();
        // Use reflection to invoke the private method
        Method method = EntRobotProcessor.class.getDeclaredMethod("getRobotAndFriendCountMap", String.class, Set.class);
        method.setAccessible(true);
        Map<String, Integer> result = (Map<String, Integer>) method.invoke(entRobotProcessor, appId, robotAccountSet);
        // assert
        assertEquals(Maps.newHashMap(), result);
    }

    /**
     * 测试 appConfigRepository.getCorpIdByAppId 返回 null 的情况
     */
    @Test
    public void testGetRobotAndFriendCountMap_CorpIdIsNull() throws Throwable {
        // arrange
        String appId = "app1";
        Set<String> robotAccountSet = new HashSet<>();
        robotAccountSet.add("robot1");
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        // Use reflection to invoke the private method
        Method method = EntRobotProcessor.class.getDeclaredMethod("getRobotAndFriendCountMap", String.class, Set.class);
        method.setAccessible(true);
        Map<String, Integer> result = (Map<String, Integer>) method.invoke(entRobotProcessor, appId, robotAccountSet);
        // assert
        assertEquals(Maps.newHashMap(), result);
    }

    /**
     * 测试 contactUserDomain.batchCountFriendNumByStaffId 返回空列表的情况
     */
    @Test
    public void testGetRobotAndFriendCountMap_StaffFriendNumBOListIsEmpty() throws Throwable {
        // arrange
        String appId = "app1";
        Set<String> robotAccountSet = new HashSet<>();
        robotAccountSet.add("robot1");
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corp1");
        when(contactUserDomain.batchCountFriendNumByStaffId("corp1", Lists.newArrayList(robotAccountSet))).thenReturn(Lists.newArrayList());
        // Use reflection to invoke the private method
        Method method = EntRobotProcessor.class.getDeclaredMethod("getRobotAndFriendCountMap", String.class, Set.class);
        method.setAccessible(true);
        Map<String, Integer> result = (Map<String, Integer>) method.invoke(entRobotProcessor, appId, robotAccountSet);
        // assert
        assertEquals(Maps.newHashMap(), result);
    }

    /**
     * 测试正常情况，返回有效的 Map
     */
    @Test
    public void testGetRobotAndFriendCountMap_NormalCase() throws Throwable {
        // arrange
        String appId = "app1";
        Set<String> robotAccountSet = new HashSet<>();
        robotAccountSet.add("robot1");
        robotAccountSet.add("robot2");
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corp1");
        List<StaffFriendNumBO> staffFriendNumBOList = Lists.newArrayList();
        StaffFriendNumBO bo1 = new StaffFriendNumBO();
        bo1.setStaffId("robot1");
        bo1.setFriendNum(10);
        staffFriendNumBOList.add(bo1);
        StaffFriendNumBO bo2 = new StaffFriendNumBO();
        bo2.setStaffId("robot2");
        bo2.setFriendNum(20);
        staffFriendNumBOList.add(bo2);
        when(contactUserDomain.batchCountFriendNumByStaffId("corp1", Lists.newArrayList(robotAccountSet))).thenReturn(staffFriendNumBOList);
        // Use reflection to invoke the private method
        Method method = EntRobotProcessor.class.getDeclaredMethod("getRobotAndFriendCountMap", String.class, Set.class);
        method.setAccessible(true);
        Map<String, Integer> result = (Map<String, Integer>) method.invoke(entRobotProcessor, appId, robotAccountSet);
        // assert
        Map<String, Integer> expected = new HashMap<>();
        expected.put("robot1", 10);
        expected.put("robot2", 20);
        assertEquals(expected, result);
    }

    /**
     * 测试 memberInfoEntityList 为空的情况
     */
    @Test
    public void testQueryRobotList_MemberInfoEntityListIsEmpty() throws Throwable {
        // arrange
        String appId = "testAppId";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = Collections.emptyList();
        // act
        List<ImRobotListResponse> result = entRobotProcessor.queryRobotList(appId, memberInfoEntityList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryRobotList_NormalCase() throws Throwable {
        // arrange
        String appId = "testAppId";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = Arrays.asList(createScrmPersonalWxGroupMemberInfoEntity("user1"), createScrmPersonalWxGroupMemberInfoEntity("user2"), createScrmPersonalWxGroupMemberInfoEntity("user1"));
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(contactUserDomain.batchCountFriendNumByStaffId("corpId", Arrays.asList("user1", "user2"))).thenReturn(Arrays.asList(createStaffFriendNumBO("user1", 10), createStaffFriendNumBO("user2", 5)));
        when(wxImDomainService.batchQueryUserInfo(appId, Arrays.asList("user1", "user2"))).thenReturn(Arrays.asList(createScrmPersonalWxUserInfo("user1", 1L), createScrmPersonalWxUserInfo("user2", 2L)));
        when(dsAssistantAcl.getAssistantList(appId)).thenReturn(Arrays.asList(createAssistantInfo("user1"), createAssistantInfo("user3")));
        // act
        List<ImRobotListResponse> result = entRobotProcessor.queryRobotList(appId, memberInfoEntityList);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        ImRobotListResponse response1 = result.get(0);
        assertEquals(1L, response1.getRobotId().longValue());
        assertEquals("user1", response1.getWxSerialNo());
        assertEquals("user1", response1.getWxId());
        assertEquals("avatar1", response1.getAvatar());
        assertEquals("nickname1", response1.getWxNickname());
        assertEquals(10, response1.getFriendCount().intValue());
        assertEquals(2, response1.getGroupCount().intValue());
        assertEquals("1", response1.getOnline());
        ImRobotListResponse response2 = result.get(1);
        assertEquals(2L, response2.getRobotId().longValue());
        assertEquals("user2", response2.getWxSerialNo());
        assertEquals("user2", response2.getWxId());
        assertEquals("avatar2", response2.getAvatar());
        assertEquals("nickname2", response2.getWxNickname());
        assertEquals(5, response2.getFriendCount().intValue());
        assertEquals(1, response2.getGroupCount().intValue());
        assertEquals("0", response2.getOnline());
    }

    /**
     * 测试 dsAssistantAcl.getAssistantList 返回空列表的情况
     */
    @Test
    public void testQueryRobotList_AssistantListIsEmpty() throws Throwable {
        // arrange
        String appId = "testAppId";
        List<ScrmPersonalWxGroupMemberInfoEntity> memberInfoEntityList = Arrays.asList(createScrmPersonalWxGroupMemberInfoEntity("user1"), createScrmPersonalWxGroupMemberInfoEntity("user2"));
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(contactUserDomain.batchCountFriendNumByStaffId("corpId", Arrays.asList("user1", "user2"))).thenReturn(Arrays.asList(createStaffFriendNumBO("user1", 10), createStaffFriendNumBO("user2", 5)));
        when(wxImDomainService.batchQueryUserInfo(appId, Arrays.asList("user1", "user2"))).thenReturn(Arrays.asList(createScrmPersonalWxUserInfo("user1", 1L), createScrmPersonalWxUserInfo("user2", 2L)));
        when(dsAssistantAcl.getAssistantList(appId)).thenReturn(Collections.emptyList());
        // act
        List<ImRobotListResponse> result = entRobotProcessor.queryRobotList(appId, memberInfoEntityList);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        for (ImRobotListResponse response : result) {
            assertEquals("0", response.getOnline());
        }
    }
}
