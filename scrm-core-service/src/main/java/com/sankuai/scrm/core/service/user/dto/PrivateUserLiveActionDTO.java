package com.sankuai.scrm.core.service.user.dto;

import lombok.Data;

import java.util.Date;

/**
 * https://km.sankuai.com/collabpage/2403887491
 */
@Data
public class PrivateUserLiveActionDTO {
    private Long id;
    /**
     * 来源
     */
    private Integer source;
    /**
     * 直播id
     */
    private String liveId;
    /**
     * openId
     */
    private String senderId;
    private String unionId;
    /**
     * 发送者昵称
     */
    private String senderNickname;
    private String senderToken;
    /**
     * 被回复者id
     */
    private String repliedId;
    /**
     * 被回复者昵称
     */
    private String repliedNickname;
    /**
     * 商品id
     */
    private Long productId;
    private Integer productType;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    //弹幕  评论内容
    private String commentContent;
    /**
     * 分享来源使用
     */
    private String url;
    /**
     * 状态
     */
    private String status;
    /**
     * 不通过原因
     */
    private String rejectReason;
    /**
     * 添加时间
     */
    private Date addTime;


}
