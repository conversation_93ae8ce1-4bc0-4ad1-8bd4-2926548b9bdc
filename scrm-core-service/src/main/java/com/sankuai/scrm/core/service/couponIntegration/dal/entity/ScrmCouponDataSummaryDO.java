package com.sankuai.scrm.core.service.couponIntegration.dal.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: Scrm_Coupon_Data_Summary
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmCouponDataSummaryDO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: corpId
     *   说明: 主体id
     */
    private String corpid;

    /**
     *   字段: coupon_count
     *   说明: 券总数量
     */
    private Long couponCount;

    /**
     *   字段: coupon_amount
     *   说明: 券总金额
     */
    private BigDecimal couponAmount;

    /**
     *   字段: summary_type
     *   说明: 券统计类型：1 发券  2 用券
     */
    private Integer summaryType;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}