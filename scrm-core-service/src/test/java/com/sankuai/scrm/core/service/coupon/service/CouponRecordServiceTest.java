package com.sankuai.scrm.core.service.coupon.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponResult;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.slf4j.Logger;
import com.dianping.cat.Cat;
import org.mockito.MockedStatic;
import org.mockito.Spy;

@ExtendWith(MockitoExtension.class)
class CouponRecordServiceTest {

    @InjectMocks
    private CouponRecordService couponRecordService;

    @Mock
    private Logger log;

    private Object invokePrivateMethod(String methodName, Class<?>[] paramTypes, Object... args) throws Exception {
        Method method = CouponRecordService.class.getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(couponRecordService, args);
    }

    private Object getFieldValue(Object target, String fieldName) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(target);
    }

    /**
     * Test normal case with all fields populated
     */
    @Test
    public void testRecordDetailSyncNormalCase() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("group123");
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        context.setSceneCode("scene123");
        context.setAppId("app123");
        context.setProcessOrchestrationId(456L);
        context.setProcessOrchestrationNodeId(789L);
        MtPoiDTO shopInfo = new MtPoiDTO();
        shopInfo.setName("Test Shop");
        context.setShopInfo(shopInfo);
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setProductTitle("Test Product");
        context.setProductInfo(productInfo);
        context.setCouponValidTime(30);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        // act
        invokePrivateMethod("recordDetailSync", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        // assert - verify the detail was built correctly
        Object detail = invokePrivateMethod("buildRecordDetail", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        assertNotNull(detail);
        assertEquals(123L, getFieldValue(detail, "userId"));
        assertEquals("group123", getFieldValue(detail, "couponGroupId"));
        assertNotNull(getFieldValue(detail, "timestamp"));
    }

    /**
     * Test case with null context (should throw NPE)
     */
    @Test
    public void testRecordDetailSyncNullContext() throws Throwable {
        // arrange
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod("recordDetailSync", new Class[] { CouponRequestContext.class, CouponResult.class }, null, result);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test case with null result (should throw NPE)
     */
    @Test
    public void testRecordDetailSyncNullResult() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod("recordDetailSync", new Class[] { CouponRequestContext.class, CouponResult.class }, context, null);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test case with failed result
     */
    @Test
    public void testRecordDetailSyncFailedResult() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        CouponResult result = new CouponResult();
        result.setSuccess(false);
        result.setErrorCode("ERR_001");
        result.setErrorMessage("Coupon expired");
        // act
        invokePrivateMethod("recordDetailSync", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        // assert - verify error details were set
        Object detail = invokePrivateMethod("buildRecordDetail", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        assertEquals("ERR_001", getFieldValue(detail, "errorCode"));
        assertEquals("Coupon expired", getFieldValue(detail, "errorMessage"));
    }

    /**
     * Test case with null optional fields in context
     */
    @Test
    public void testRecordDetailSyncNullOptionalFields() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.NEXT_PLATFORM);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        // act
        invokePrivateMethod("recordDetailSync", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        // assert - verify extra map is empty
        Object detail = invokePrivateMethod("buildRecordDetail", new Class[] { CouponRequestContext.class, CouponResult.class }, context, result);
        Map<?, ?> extra = (Map<?, ?>) getFieldValue(detail, "extra");
        assertTrue(extra.isEmpty());
    }

    private Object createCouponRecordDetail() throws Exception {
        Class<?>[] declaredClasses = CouponRecordService.class.getDeclaredClasses();
        Class<?> couponRecordDetailClass = null;
        for (Class<?> clazz : declaredClasses) {
            if (clazz.getSimpleName().equals("CouponRecordDetail")) {
                couponRecordDetailClass = clazz;
                break;
            }
        }
        assertNotNull(couponRecordDetailClass, "CouponRecordDetail class not found");
        Constructor<?> constructor = couponRecordDetailClass.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    private void setField(Object obj, String fieldName, Object value) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }

    private Method getRecordToLogMethod() throws Exception {
        Method method = CouponRecordService.class.getDeclaredMethod("recordToLog", Class.forName("com.sankuai.scrm.core.service.coupon.service.CouponRecordService$CouponRecordDetail"));
        method.setAccessible(true);
        return method;
    }

    @Test
    public void testRecordToLogNormalCase() throws Throwable {
        // arrange
        Object detail = createCouponRecordDetail();
        setField(detail, "timestamp", "2023-01-01 00:00:00");
        setField(detail, "userId", 12345L);
        setField(detail, "couponGroupId", "group-001");
        setField(detail, "scene", "promotion");
        setField(detail, "sceneCode", "P001");
        setField(detail, "appId", "app-001");
        setField(detail, "success", true);
        Map<String, Object> extra = new HashMap<>();
        extra.put("key", "value");
        setField(detail, "extra", extra);
        Method method = getRecordToLogMethod();
        // act & assert (just verify no exception thrown)
        assertDoesNotThrow(() -> method.invoke(couponRecordService, detail));
    }

    @Test
    public void testRecordToLogExceptionCase() throws Throwable {
        // arrange
        Object detail = createCouponRecordDetail();
        setField(detail, "timestamp", "2023-01-01 00:00:00");
        setField(detail, "userId", 12345L);
        setField(detail, "couponGroupId", "group-001");
        Method method = getRecordToLogMethod();
        // act & assert (just verify no exception thrown from the method itself)
        assertDoesNotThrow(() -> method.invoke(couponRecordService, detail));
    }

    @Test
    public void testRecordToLogWithNullDetail() throws Throwable {
        // arrange
        Method method = getRecordToLogMethod();
        // act & assert (just verify no exception thrown)
        assertDoesNotThrow(() -> method.invoke(couponRecordService, new Object[] { null }));
    }

    @Test
    public void testRecordToLogWithPartialNullFields() throws Throwable {
        // arrange
        Object detail = createCouponRecordDetail();
        setField(detail, "timestamp", null);
        setField(detail, "userId", null);
        setField(detail, "couponGroupId", null);
        Method method = getRecordToLogMethod();
        // act & assert (just verify no exception thrown)
        assertDoesNotThrow(() -> method.invoke(couponRecordService, detail));
    }

    private void invokeRecordCatEvent(CouponRequestContext context, CouponResult result) throws Exception {
        Method method = CouponRecordService.class.getDeclaredMethod("recordCatEvent", CouponRequestContext.class, CouponResult.class);
        method.setAccessible(true);
        method.invoke(couponRecordService, context, result);
    }

    @Test
    public void testRecordCatEventSuccessCase() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        CouponResult result = CouponResult.success(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    @Test
    public void testRecordCatEventFailedCase() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        CouponResult result = CouponResult.fail("ERROR_001", "Test error");
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    @Test
    public void testRecordCatEventNullContext() throws Throwable {
        // arrange
        CouponResult result = CouponResult.success(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(null, result));
    }

    @Test
    public void testRecordCatEventNullResult() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.NEXT_PLATFORM);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, null));
    }

    @Test
    public void testRecordCatEventNullSceneCode() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(null);
        CouponResult result = CouponResult.success(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    @Test
    public void testRecordCatEventNullErrorCode() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.OTHER);
        CouponResult result = new CouponResult();
        result.setSuccess(false);
        result.setErrorCode(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    @Test
    public void testRecordCatEventCatLogException() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        CouponResult result = CouponResult.success(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    @Test
    public void testRecordCatEventBusinessMetricsException() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        context.setAppId("testApp");
        CouponResult result = CouponResult.success(null);
        // act & assert
        assertDoesNotThrow(() -> invokeRecordCatEvent(context, result));
    }

    private Object invokeBuildRecordDetail(CouponRequestContext context, CouponResult result) throws Exception {
        Method method = CouponRecordService.class.getDeclaredMethod("buildRecordDetail", CouponRequestContext.class, CouponResult.class);
        method.setAccessible(true);
        return method.invoke(couponRecordService, context, result);
    }

    private Object getFieldValue(Object obj, Class<?> clazz, String fieldName) throws Exception {
        java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    @Test
    void testBuildRecordDetailAllFieldsPopulated() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getUserId()).thenReturn(12345L);
        when(context.getCouponGroupId()).thenReturn("GROUP_123");
        when(context.getScene()).thenReturn(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        when(context.getSceneCode()).thenReturn("SCENE_001");
        when(context.getAppId()).thenReturn("APP_001");
        when(context.getProcessOrchestrationId()).thenReturn(1001L);
        when(context.getProcessOrchestrationNodeId()).thenReturn(2001L);
        MtPoiDTO mockShopInfo = mock(MtPoiDTO.class);
        when(context.getShopInfo()).thenReturn(mockShopInfo);
        ProductInfoDTO mockProductInfo = mock(ProductInfoDTO.class);
        when(context.getProductInfo()).thenReturn(mockProductInfo);
        when(context.getCouponValidTime()).thenReturn(30);
        CouponResult result = mock(CouponResult.class);
        when(result.isSuccess()).thenReturn(true);
        when(result.getErrorCode()).thenReturn("SUCCESS");
        when(result.getErrorMessage()).thenReturn("Operation successful");
        // Act
        Object detailObj = invokeBuildRecordDetail(context, result);
        // Assert
        assertNotNull(detailObj);
        assertEquals(12345L, getFieldValue(detailObj, detailObj.getClass(), "userId"));
        assertEquals("GROUP_123", getFieldValue(detailObj, detailObj.getClass(), "couponGroupId"));
        assertEquals("AI_INTELLIGENT_FOLLOW", getFieldValue(detailObj, detailObj.getClass(), "scene"));
        assertEquals("SCENE_001", getFieldValue(detailObj, detailObj.getClass(), "sceneCode"));
        assertEquals("APP_001", getFieldValue(detailObj, detailObj.getClass(), "appId"));
        assertEquals(1001L, getFieldValue(detailObj, detailObj.getClass(), "processOrchestrationId"));
        assertEquals(2001L, getFieldValue(detailObj, detailObj.getClass(), "processOrchestrationNodeId"));
        assertTrue((boolean) getFieldValue(detailObj, detailObj.getClass(), "success"));
        assertEquals("SUCCESS", getFieldValue(detailObj, detailObj.getClass(), "errorCode"));
        assertEquals("Operation successful", getFieldValue(detailObj, detailObj.getClass(), "errorMessage"));
        Map<String, Object> extra = (Map<String, Object>) getFieldValue(detailObj, detailObj.getClass(), "extra");
        assertNotNull(extra);
        assertEquals(mockShopInfo, extra.get("shopInfo"));
        assertEquals(mockProductInfo, extra.get("productInfo"));
        assertEquals(30, extra.get("couponValidTime"));
        // Verify timestamp format
        String timestamp = (String) getFieldValue(detailObj, detailObj.getClass(), "timestamp");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime.parse(timestamp, formatter);
    }

    @Test
    void testBuildRecordDetailOptionalFieldsNull() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getUserId()).thenReturn(67890L);
        when(context.getCouponGroupId()).thenReturn("GROUP_456");
        when(context.getScene()).thenReturn(CouponSceneEnum.PROCESS_ORCHESTRATION);
        when(context.getSceneCode()).thenReturn("SCENE_002");
        when(context.getAppId()).thenReturn("APP_002");
        when(context.getProcessOrchestrationId()).thenReturn(1002L);
        when(context.getProcessOrchestrationNodeId()).thenReturn(2002L);
        when(context.getShopInfo()).thenReturn(null);
        when(context.getProductInfo()).thenReturn(null);
        when(context.getCouponValidTime()).thenReturn(null);
        CouponResult result = mock(CouponResult.class);
        when(result.isSuccess()).thenReturn(false);
        when(result.getErrorCode()).thenReturn("ERROR_001");
        when(result.getErrorMessage()).thenReturn("Operation failed");
        // Act
        Object detailObj = invokeBuildRecordDetail(context, result);
        // Assert
        assertNotNull(detailObj);
        assertEquals(67890L, getFieldValue(detailObj, detailObj.getClass(), "userId"));
        assertEquals("GROUP_456", getFieldValue(detailObj, detailObj.getClass(), "couponGroupId"));
        assertEquals("PROCESS_ORCHESTRATION", getFieldValue(detailObj, detailObj.getClass(), "scene"));
        assertEquals("SCENE_002", getFieldValue(detailObj, detailObj.getClass(), "sceneCode"));
        assertEquals("APP_002", getFieldValue(detailObj, detailObj.getClass(), "appId"));
        assertEquals(1002L, getFieldValue(detailObj, detailObj.getClass(), "processOrchestrationId"));
        assertEquals(2002L, getFieldValue(detailObj, detailObj.getClass(), "processOrchestrationNodeId"));
        assertFalse((boolean) getFieldValue(detailObj, detailObj.getClass(), "success"));
        assertEquals("ERROR_001", getFieldValue(detailObj, detailObj.getClass(), "errorCode"));
        assertEquals("Operation failed", getFieldValue(detailObj, detailObj.getClass(), "errorMessage"));
        Map<String, Object> extra = (Map<String, Object>) getFieldValue(detailObj, detailObj.getClass(), "extra");
        assertNotNull(extra);
        assertNull(extra.get("shopInfo"));
        assertNull(extra.get("productInfo"));
        assertNull(extra.get("couponValidTime"));
    }

    @Test
    void testBuildRecordDetailOnlyShopInfoPresent() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getUserId()).thenReturn(11111L);
        when(context.getCouponGroupId()).thenReturn("GROUP_111");
        when(context.getScene()).thenReturn(CouponSceneEnum.NEXT_PLATFORM);
        when(context.getSceneCode()).thenReturn("SCENE_111");
        when(context.getAppId()).thenReturn("APP_111");
        MtPoiDTO mockShopInfo = mock(MtPoiDTO.class);
        when(context.getShopInfo()).thenReturn(mockShopInfo);
        when(context.getProductInfo()).thenReturn(null);
        when(context.getCouponValidTime()).thenReturn(null);
        CouponResult result = mock(CouponResult.class);
        when(result.isSuccess()).thenReturn(true);
        // Act
        Object detailObj = invokeBuildRecordDetail(context, result);
        // Assert
        assertNotNull(detailObj);
        Map<String, Object> extra = (Map<String, Object>) getFieldValue(detailObj, detailObj.getClass(), "extra");
        assertNotNull(extra);
        assertEquals(mockShopInfo, extra.get("shopInfo"));
        assertNull(extra.get("productInfo"));
        assertNull(extra.get("couponValidTime"));
    }

    @Test
    void testBuildRecordDetailOnlyProductInfoPresent() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getUserId()).thenReturn(22222L);
        when(context.getCouponGroupId()).thenReturn("GROUP_222");
        when(context.getScene()).thenReturn(CouponSceneEnum.OTHER);
        when(context.getSceneCode()).thenReturn("SCENE_222");
        when(context.getAppId()).thenReturn("APP_222");
        ProductInfoDTO mockProductInfo = mock(ProductInfoDTO.class);
        when(context.getProductInfo()).thenReturn(mockProductInfo);
        when(context.getShopInfo()).thenReturn(null);
        when(context.getCouponValidTime()).thenReturn(null);
        CouponResult result = mock(CouponResult.class);
        when(result.isSuccess()).thenReturn(false);
        // Act
        Object detailObj = invokeBuildRecordDetail(context, result);
        // Assert
        assertNotNull(detailObj);
        Map<String, Object> extra = (Map<String, Object>) getFieldValue(detailObj, detailObj.getClass(), "extra");
        assertNotNull(extra);
        assertNull(extra.get("shopInfo"));
        assertEquals(mockProductInfo, extra.get("productInfo"));
        assertNull(extra.get("couponValidTime"));
    }

    @Test
    void testBuildRecordDetailOnlyCouponValidTimePresent() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        when(context.getUserId()).thenReturn(33333L);
        when(context.getCouponGroupId()).thenReturn("GROUP_333");
        when(context.getScene()).thenReturn(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        when(context.getSceneCode()).thenReturn("SCENE_333");
        when(context.getAppId()).thenReturn("APP_333");
        when(context.getCouponValidTime()).thenReturn(15);
        when(context.getShopInfo()).thenReturn(null);
        when(context.getProductInfo()).thenReturn(null);
        CouponResult result = mock(CouponResult.class);
        when(result.isSuccess()).thenReturn(true);
        // Act
        Object detailObj = invokeBuildRecordDetail(context, result);
        // Assert
        assertNotNull(detailObj);
        Map<String, Object> extra = (Map<String, Object>) getFieldValue(detailObj, detailObj.getClass(), "extra");
        assertNotNull(extra);
        assertNull(extra.get("shopInfo"));
        assertNull(extra.get("productInfo"));
        assertEquals(15, extra.get("couponValidTime"));
    }

    @Test
    void testBuildRecordDetailNullContext() throws Throwable {
        // Arrange
        CouponResult result = mock(CouponResult.class);
        // Act & Assert
        assertThrows(Exception.class, () -> {
            invokeBuildRecordDetail(null, result);
        });
    }

    @Test
    void testBuildRecordDetailNullResult() throws Throwable {
        // Arrange
        CouponRequestContext context = mock(CouponRequestContext.class);
        // Act & Assert
        assertThrows(Exception.class, () -> {
            invokeBuildRecordDetail(context, null);
        });
    }

    @Test
    public void testRecordNormal() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("group123");
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        // act & assert - 不应抛出异常
        assertDoesNotThrow(() -> couponRecordService.record(context, result));
    }

    @Test
    public void testRecordFailedResult() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("group123");
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        CouponResult result = new CouponResult();
        result.setSuccess(false);
        result.setErrorCode("ERROR_001");
        result.setErrorMessage("Coupon not available");
        // act & assert - 不应抛出异常
        assertDoesNotThrow(() -> couponRecordService.record(context, result));
    }

    @Test
    public void testRecordWithException() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        // 设置scene为null，触发NPE
        context.setScene(null);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            // act
            couponRecordService.record(context, result);
            // assert - 验证Cat.logError被调用
            mockedCat.verify(() -> Cat.logError(eq("CouponRecordService record error"), any(NullPointerException.class)));
        }
    }

    @Test
    public void testRecordWithNullContext() throws Throwable {
        // arrange
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            // act
            couponRecordService.record(null, result);
            // assert - 验证Cat.logError被调用
            mockedCat.verify(() -> Cat.logError(eq("CouponRecordService record error"), any(NullPointerException.class)));
        }
    }

    @Test
    public void testRecordWithNullResult() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        try (MockedStatic<Cat> mockedCat = mockStatic(Cat.class)) {
            // act
            couponRecordService.record(context, null);
            // assert - 验证Cat.logError被调用
            mockedCat.verify(() -> Cat.logError(eq("CouponRecordService record error"), any(NullPointerException.class)));
        }
    }

    @Test
    public void testRecordAlreadyReceivedCoupon() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setCouponGroupId("group123");
        context.setScene(CouponSceneEnum.AI_INTELLIGENT_FOLLOW);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        result.setNewCouponReceived(false);
        // act & assert - 不应抛出异常
        assertDoesNotThrow(() -> couponRecordService.record(context, result));
    }

    @Test
    public void testRecordWithExtraInfo() throws Throwable {
        // arrange
        CouponRequestContext context = new CouponRequestContext();
        context.setUserId(123L);
        context.setProcessOrchestrationId(456L);
        context.setProcessOrchestrationNodeId(789L);
        context.setScene(CouponSceneEnum.PROCESS_ORCHESTRATION);
        CouponResult result = new CouponResult();
        result.setSuccess(true);
        // act & assert - 不应抛出异常
        assertDoesNotThrow(() -> couponRecordService.record(context, result));
    }
}
