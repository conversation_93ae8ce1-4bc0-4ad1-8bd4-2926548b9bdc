package com.sankuai.scrm.core.service.message.task.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.scrm.core.service.message.task.dal.entity.ScrmFriendCycleMsgTask;
import com.sankuai.scrm.core.service.message.task.dal.example.ScrmFriendCycleMsgTaskExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ScrmFriendCycleMsgTaskMapper extends MybatisBLOBsMapper<ScrmFriendCycleMsgTask, ScrmFriendCycleMsgTaskExample, Long> {
    int batchInsert(@Param("list") List<ScrmFriendCycleMsgTask> list);
}