package com.sankuai.scrm.core.service.pchat.service.tanjing;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.dz.srcm.pchat.dto.*;
import com.sankuai.dz.srcm.pchat.request.MerchantRobotPageRequest;
import com.sankuai.dz.srcm.pchat.tanjing.RobotInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 机器人信息接口
 * <AUTHOR>
 * @Create On 2023/11/3 11:13
 * @Version v1.0.0
 */
@Slf4j
@Service
public class RobotInfoServiceImpl implements RobotInfoService {

    @Autowired
    private TanjingRemoteInvokeService remoteInvokeService;

    @Autowired
    private ApiMappingService apiMappingService;


    @Override
    public AsyncInvokeResultDTO getMerchantRobotPageList(MerchantRobotPageRequest request) {
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), BeanUtil.beanToMap(request), AsyncInvokeResultDTO.class);
        return resultDTO;
    }

    @Override
    public SyncInvokeResultDTO<String> isInBlackList(String vcMerchantNo, String[] vcUserSerialNos) {
        Map<String, Object> param = new HashMap<>();
        param.put("vcMerchantNo", vcMerchantNo);
        param.put("vcUserSerialNos", vcUserSerialNos);
        SyncInvokeResultDTO<String> resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), param, new TypeReference<SyncInvokeResultDTO<String>>() {
        });
        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO getOrUpdatePersonalCard(String merchant_no, String robot_serial_no, Integer type) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("type", type);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public InvokeResultDTO modifyRobotRegion(String robot_serial_no, String merchant_no, String region_code) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("region_code", region_code);
        InvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, InvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO getBatchFriendList(String robot_serial_no, String merchant_no) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("robot_serial_no", robot_serial_no);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO getFriendListAsync(String vcMerchantNo, String vcRobotSerialNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vcMerchantNo", vcMerchantNo);
        paramMap.put("vcRobotSerialNo", vcRobotSerialNo);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public SyncInvokeResultDTO<FriendListDTO> getFriendList(String vcMerchantNo, String vcRobotSerialNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vcMerchantNo", vcMerchantNo);
        paramMap.put("vcRobotSerialNo", vcRobotSerialNo);
        SyncInvokeResultDTO<FriendListDTO> resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, new TypeReference<SyncInvokeResultDTO<FriendListDTO>>() {
        });

        return resultDTO;
    }


    @Override
    public SyncInvokeResultDTO<GroupListDTO> getChatRoomList(String vcMerchantNo, String vcRobotSerialNo, String vcChatRoomSerialNo, Integer isOpenMessage) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vcMerchantNo", vcMerchantNo);
        paramMap.put("vcRobotSerialNo", vcRobotSerialNo);
        paramMap.put("vcChatRoomSerialNo", vcChatRoomSerialNo);
        paramMap.put("isOpenMessage", isOpenMessage);
        SyncInvokeResultDTO<GroupListDTO> resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, new TypeReference<SyncInvokeResultDTO<GroupListDTO>>() {
        });

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO modifyRobotGender(String robot_serial_no, String merchant_no, Integer sex) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("sex", sex);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO modifyRobotHeadimg(String robot_serial_no, String merchant_no, String head_img_url) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("head_img_url", head_img_url);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO modifyRobotNickname(String robot_serial_no, String merchant_no, String nick_name) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("nick_name", nick_name);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

    @Override
    public AsyncInvokeResultDTO modifyRobotWhatsUp(String robot_serial_no, String merchant_no, String whats_up) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("robot_serial_no", robot_serial_no);
        paramMap.put("merchant_no", merchant_no);
        paramMap.put("whats_up", whats_up);
        AsyncInvokeResultDTO resultDTO = remoteInvokeService.request(apiMappingService.getPathStr(RobotInfoService.class), paramMap, AsyncInvokeResultDTO.class);

        return resultDTO;
    }

}
