package com.sankuai.scrm.core.service.reply.domain;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.srcm.reply.enums.MsgContentType;
import com.sankuai.dz.srcm.reply.enums.SenderType;
import com.sankuai.dz.srcm.reply.enums.SessionType;
import com.sankuai.scrm.core.service.reply.domain.entity.CorpSessionMsgEntity;
import com.sankuai.scrm.core.service.reply.domain.entity.LirenShangjiaWxDocDataEvent;
import com.sankuai.scrm.core.service.reply.domain.entity.MinipContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ScrmLirenShangjiaAutoReplyDomainService {

    @Resource
    private ScrmAutoReplyDomainService autoReplyDomainService;

    public static final String LIREN_SHANGJIA_CORPID = "ww5dda411253d10341";

    public void autoReply(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                log.error("ScrmLirenShangjiaAutoReplyDomainService.autoReply msg is null");
                return;
            }
            LirenShangjiaWxDocDataEvent msgEvent = JSONObject.parseObject(msg, LirenShangjiaWxDocDataEvent.class);
            if (msgEvent == null) {
                return;
            }

            CorpSessionMsgEntity corpSessionMsgEntity = buildCorpSessionMsgEntity(msgEvent);

            //不处理私聊
            if (corpSessionMsgEntity.getSessionType() == SessionType.FRIEND_SESSION.code) {
                return;
            }

            //文本消息
            if (msgEvent.getContentType() == 1) {
                autoReplyDomainService.autoReply(corpSessionMsgEntity);
            }

            //只处理外部用户发言
            if (corpSessionMsgEntity.getSenderType() == SenderType.INNER_USER.code) {
                return;
            }

            //小程序消息
            if (msgEvent.getContentType() == 10) {
                autoReplyDomainService.warnReply(corpSessionMsgEntity);
            }
        } catch (Exception e) {
            log.error("ScrmLirenShangjiaAutoReplyDomainService.autoReply has exception", e);
        }
    }

    private CorpSessionMsgEntity buildCorpSessionMsgEntity(LirenShangjiaWxDocDataEvent msgEvent) {
        CorpSessionMsgEntity corpSessionMsgEntity = new CorpSessionMsgEntity();
        corpSessionMsgEntity.setCorpId(LIREN_SHANGJIA_CORPID);
        corpSessionMsgEntity.setGroupId(msgEvent.getWechatGroupId());
        corpSessionMsgEntity.setMsgId(msgEvent.getIdentifier());

        if (msgEvent.getContentType() == 1) {
            //文本
            corpSessionMsgEntity.setContentType(MsgContentType.TEXT.getCode());
            corpSessionMsgEntity.setContent(msgEvent.getContent());
        } else if (msgEvent.getContentType() == 10) {
            //小程序
            corpSessionMsgEntity.setContentType(MsgContentType.MINIPROGRAM.getCode());
            JSONObject minipJson = JSONObject.parseObject(msgEvent.getContent());
            String userName = minipJson.getString("username");
            String[] userNameSplitArr = userName.split("@");
            String originalId = userNameSplitArr[0];

            MinipContent minipContent = new MinipContent();
            minipContent.setOriginalId(originalId);
            minipContent.setDisplayName(minipJson.getString("displayname"));
            minipContent.setTitle(minipJson.getString("title"));
            minipContent.setDescription(minipJson.getString("description"));
            corpSessionMsgEntity.setMinipContent(minipContent);
        }

        String fromUserId = msgEvent.getSenderWechatUserId();
        if (fromUserId.startsWith("wb") || fromUserId.startsWith("wo") || fromUserId.startsWith("wm")) {
            corpSessionMsgEntity.setExternalUserId(fromUserId);
            corpSessionMsgEntity.setSenderType(SenderType.OUTER_USER.code);
        } else {
            corpSessionMsgEntity.setAccountId(fromUserId);
            corpSessionMsgEntity.setSenderType(SenderType.INNER_USER.code);
        }

        corpSessionMsgEntity.setSessionType(SessionType.GROUP_SESSION.getCode());
        if (msgEvent.getConversationType() == 1) {
            corpSessionMsgEntity.setSessionType(SessionType.FRIEND_SESSION.code);
        }

        return corpSessionMsgEntity;
    }

}
