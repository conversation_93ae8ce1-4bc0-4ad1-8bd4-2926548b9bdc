package com.sankuai.scrm.core.service.activity.user.domain;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dz.srcm.activity.user.request.ScrmActivityUserInfoRequest;
import com.sankuai.dz.srcm.activity.user.request.ScrmActivityUserQueryRequest;
import com.sankuai.dz.srcm.activity.user.response.ScrmActivityUserInfoResponse;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendChannelCodeInfo;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.WxAccountInfo;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.activity.user.dal.entity.ScrmActivityUserInfo;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.friend.dynamiccode.constant.FriendDynamicCodeConstant;
import com.sankuai.scrm.core.service.friend.dynamiccode.domain.FriendChannelDynamicCodeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.GeneralSecurityException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScrmActivityUserDomainService {

    @Resource
    private FriendChannelDynamicCodeDomainService friendChannelDynamicCodeDomainService;
    @Resource
    private ContactUserDomain contactUserDomain;
    @Resource
    private IEncryptService phoneEncryptService;
    @Resource
    private ScrmActivityUserDomain scrmActivityUserDomain;

    public boolean verifyScrmActivityUser(String appId, String unionId, String corpId, Long channelCodeId) {
        //根据渠道码Id查询出, 成员账号(可能多个),
        FriendChannelCodeInfo channelCodeInfo = friendChannelDynamicCodeDomainService.queryFriendChannelCodeInfo(appId, channelCodeId);
        if (CollectionUtils.isEmpty(channelCodeInfo.getUserInfoList())) {
            return false;
        }
        String state = FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + channelCodeInfo.getFriendChannelId() + "_" + channelCodeId;
        List<String> staffIdList = channelCodeInfo.getUserInfoList().stream().map(WxAccountInfo::getUserId).collect(Collectors.toList());
        //用unionId和成员账号去contactUser表比较添加好友的时间, 若多个逐个比较有一个是好友即可
        List<ContactUser> contactUsers = contactUserDomain.queryContactUserByUnionIdAndStaffId(corpId, unionId, staffIdList, state);
        if (CollectionUtils.isEmpty(contactUsers)) {
            return false;
        }
        return verifyAddStaffTime(contactUsers);
    }

    public String saveScrmActivityUserInfo(ScrmActivityUserInfoRequest request, String corpId) {
        //校验是否已经存在该用户地址信息, 避免重复提交
        ScrmActivityUserInfo existUserInfo = scrmActivityUserDomain.queryScrmActivityUser(request.getAppId(), request.getUnionId());
        if (ObjectUtils.isNotEmpty(existUserInfo)) {
            return "您已提交过地址信息, 请勿重复提交!";
        }

        String encryptMobileNo = "";
        try {
            encryptMobileNo = phoneEncryptService.encryptUTF8String(request.getMobileNo());
        } catch (GeneralSecurityException e) {
            log.error("saveScrmActivityUserInfo: mobileNo encrypt fail, request:{}, corrpId={}", request, corpId, e);
        }
        //校验添加好有时间
        boolean verifyResult = verifyScrmActivityUser(request.getAppId(), request.getUnionId(), corpId, request.getChannelCodeId());
        if (!verifyResult) {
            log.error("saveScrmActivityUserInfo: 添加企微成员超过10分钟, 无法提交, request:{}, crpId={}", request, corpId);
            return "据您添加企微成员超过10分钟, 无法提交!";
        }
        ScrmActivityUserInfo userInfo = buildScrmActivityUserInfo(request, encryptMobileNo);
        boolean saveResult = scrmActivityUserDomain.saveScrmActivityUser(userInfo);
        if (!saveResult) {
            log.error("saveScrmActivityUserInfo: save fail, request:{}, crpId={}", request, corpId);
            return "save fail";
        }
        return null;
    }

    public ScrmActivityUserInfoResponse queryScrmActivityUserInfo(ScrmActivityUserQueryRequest request) {

        ScrmActivityUserInfo userInfo = scrmActivityUserDomain.queryScrmActivityUser(request.getAppId(), request.getUnionId());
        if (ObjectUtils.isEmpty(userInfo)) {
            return null;
        }

        String realMobileNo = "";
        try {
            realMobileNo = phoneEncryptService.decryptUTF8String(userInfo.getMobileNo());
        } catch (GeneralSecurityException e) {
            log.error("queryScrmActivityUserInfo: decrypt mobileNo fail, request={}, mobileNo:{}", request, userInfo.getMobileNo(), e);
        }
        return buildScrmActivityUserInfoResponse(userInfo, realMobileNo);
    }

    public String getConfigForImg() {
        String imageUrl = Lion.getString(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.welcome.msg.form.prize.picture");
        if (StringUtils.isEmpty(imageUrl)) {
            return null;
        }
        return imageUrl;
    }

    private ScrmActivityUserInfoResponse buildScrmActivityUserInfoResponse(ScrmActivityUserInfo userInfo, String realMobileNo) {
        return ScrmActivityUserInfoResponse.builder()
                .id(userInfo.getId())
                .appId(userInfo.getAppId())
                .unionId(userInfo.getUnionId())
                .provinceName(userInfo.getProvinceName())
                .cityName(userInfo.getCityName())
                .detailAddress(userInfo.getDetailAddress())
                .receiver(userInfo.getReceiver())
                .mobileNo(realMobileNo)
                .imageUrl(getConfigForImg())
                .build();
    }


    private boolean verifyAddStaffTime(List<ContactUser> contactUsers) {
        long currentTime = new Date().getTime();
        long tenMinutesInMillis = 10 * 60 * 1000L;

        for (ContactUser user : contactUsers) {
            long updateTime = user.getUpdateTime().getTime();
            long timeDifference = currentTime - updateTime;

            if (timeDifference < tenMinutesInMillis) {
                return true;
            }
        }
        return false;
    }

    private ScrmActivityUserInfo buildScrmActivityUserInfo(ScrmActivityUserInfoRequest request, String encryptMobileNo) {
        return ScrmActivityUserInfo.builder()
                .appId(request.getAppId())
                .unionId(request.getUnionId())
                .provinceName(request.getProvinceName())
                .cityName(request.getCityName())
                .detailAddress(request.getDetailAddress())
                .receiver(request.getReceiver())
                .mobileNo(encryptMobileNo).build();
    }
}
