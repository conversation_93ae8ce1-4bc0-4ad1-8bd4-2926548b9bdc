package com.sankuai.scrm.core.service.activity.fission.service;

import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponGroupDTO;
import com.dianping.unified.coupon.manage.api.response.CouponGroupStockQueryResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.activity.fission.enums.StatusEnum;
import com.sankuai.dz.srcm.activity.fission.service.ActivityFissionService;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/4/3 15:15
 * @Version v1.0.0
 */


@Slf4j
@MdpPigeonServer
public class ActivityFissionServiceImpl implements ActivityFissionService {
    @Resource
    private CouponDomainService couponDomainService;
    @Resource
    private ActivityAwardDistributeService activityAwardDistributeService;
    @Resource
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Override
    public RemoteResponse<MktCouponInfoDTO> queryMktCouponInfo(String couponId) {
        if (couponId == null) {
            return RemoteResponse.fail("couponId不能为空");
        }
        UnifiedCouponGroupDTO unifiedCouponGroupDTO = couponDomainService.loadCouponGroup(couponId);
        if (unifiedCouponGroupDTO != null) {
            MktCouponInfoDTO dto = new MktCouponInfoDTO();
            dto.setCouponId(couponId);
            dto.setCouponGroupName(unifiedCouponGroupDTO.getCouponGroupName());
            dto.setDiscountAmount(unifiedCouponGroupDTO.getDiscountAmount());
            dto.setPriceLimit(unifiedCouponGroupDTO.getPriceLimit());
            dto.setValidBeginTime(unifiedCouponGroupDTO.getValidBeginTime());
            dto.setValidEndTime(unifiedCouponGroupDTO.getValidEndTime());
            CouponGroupStockQueryResponse couponGroupStockQueryResponse = couponDomainService.queryStockInfo(couponId);
            if (couponGroupStockQueryResponse != null) {
                dto.setRemainStock((int) couponGroupStockQueryResponse.getTotalStockInfo().getRemainStock());
            }
            return RemoteResponse.success(dto);
        }
        return RemoteResponse.success(null);
    }

    @Override
    public RemoteResponse<String> processActivityFinish(Long activityId) {
        GroupFissionActivity groupFissionActivity = groupFissionActivityMapper.selectByPrimaryKey(activityId);
        if (groupFissionActivity == null) {
            return RemoteResponse.fail("活动不存在");
        }
        groupFissionActivity.setStatus(StatusEnum.FINISHED.getCode());
        activityAwardDistributeService.processFinish(groupFissionActivity);
        return RemoteResponse.success("success");
    }
}
