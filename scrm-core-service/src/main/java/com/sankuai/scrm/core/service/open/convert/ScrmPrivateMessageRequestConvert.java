package com.sankuai.scrm.core.service.open.convert;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.open.dto.MessageDataDTO;
import com.sankuai.dz.srcm.open.request.ScrmPrivateMessageRequest;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushChatType;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushSceneType;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.MsgContentDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.*;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class ScrmPrivateMessageRequestConvert {

    public static MsgPushRequest convert(ScrmPrivateMessageRequest request) {
        MsgPushRequest msgPushRequest = new MsgPushRequest();
        msgPushRequest.setAppId(request.getAppId());
        msgPushRequest.setSceneType(MsgPushSceneType.REALTIME_SCENE);
        msgPushRequest.setChatType(MsgPushChatType.PRIVATE);
        msgPushRequest.setSenderIdList(Lists.newArrayList(request.getSendWxUserId()));
        msgPushRequest.setReceiverIdList(Lists.newArrayList(request.getReceiveWxUserId()));
        List<MsgPushContentDTO> messageDto = request.getMsgData().stream()
                .map(ScrmPrivateMessageRequestConvert::convert)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        msgPushRequest.setMsgPushContentDTO(messageDto);

        return msgPushRequest;
    }


    private static MsgPushContentDTO convert(MessageDataDTO msgDataDTO) {
        if (msgDataDTO == null || msgDataDTO.getMsgType() == null) {
            return null;
        }
        MsgPushContentDTO msgPushContentDTO = new MsgPushContentDTO();
        String msgType = msgDataDTO.getMsgType();
        switch (msgType) {
            case "1":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.TEXT);
                msgPushContentDTO.setTextDTO(createTextDTO(msgDataDTO));
                break;
            case "2":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.IMAGE);
                msgPushContentDTO.setImageDTO(createImageDTO(msgDataDTO));
                break;
            case "3":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
                msgPushContentDTO.setMiniProgramDTO(createMiniProgramDTO(msgDataDTO));
                break;
            case "4":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.LINK);
                msgPushContentDTO.setLinkDTO(createLinkDTO(msgDataDTO));
                break;
            case "5":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.VIDEO);
                msgPushContentDTO.setVideoDTO(createVideoDTO(msgDataDTO));
                break;
            case "6":
                msgPushContentDTO.setContentTypeTEnum(ContentTypeTEnum.VOICE);
                msgPushContentDTO.setVoiceDTO(createVoiceDTO(msgDataDTO));
                break;
            default:
                return null;
        }
        return msgPushContentDTO;
    }

    private static TextDTO createTextDTO(MessageDataDTO msgDataDTO) {
        TextDTO textDTO = new TextDTO();
        textDTO.setContent(msgDataDTO.getText().getContent());
        textDTO.setAtAllIndex(msgDataDTO.getText().getAtAllIndex());
        textDTO.setCanDowngradeAtAll(msgDataDTO.getText().getCanDowngradeAtAll());
        textDTO.setContainsAtPerson(msgDataDTO.getText().getContainsAtPerson());
        return textDTO;
    }

    private static ImageDTO createImageDTO(MessageDataDTO msgDataDTO) {
        ImageDTO imageDTO = new ImageDTO();
        imageDTO.setUrl(msgDataDTO.getImage().getUrl());
        return imageDTO;
    }

    private static LinkDTO createLinkDTO(MessageDataDTO msgDataDTO) {
        LinkDTO linkDTO = new LinkDTO();
        linkDTO.setTitle(msgDataDTO.getLink().getTitle());
        linkDTO.setUrl(msgDataDTO.getLink().getUrl());
        linkDTO.setDescription(msgDataDTO.getLink().getDescription());
        linkDTO.setThumbUrl(msgDataDTO.getLink().getThumbUrl());
        return linkDTO;
    }

    private static MiniProgramDTO createMiniProgramDTO(MessageDataDTO msgDataDTO) {
        MiniProgramDTO miniProgramDTO = new MiniProgramDTO();
        miniProgramDTO.setOriginAppId(msgDataDTO.getMiniProgram().getOriginAppId());
        miniProgramDTO.setAppId(msgDataDTO.getMiniProgram().getAppId());
        miniProgramDTO.setIcon(msgDataDTO.getMiniProgram().getIcon());
        miniProgramDTO.setTitle(msgDataDTO.getMiniProgram().getTitle());
        miniProgramDTO.setDescription(msgDataDTO.getMiniProgram().getDescription());
        miniProgramDTO.setThumbnail(msgDataDTO.getMiniProgram().getThumbnail());
        miniProgramDTO.setPagePath(msgDataDTO.getMiniProgram().getPagePath());
        return miniProgramDTO;
    }

    private static VideoDTO createVideoDTO(MessageDataDTO msgDataDTO) {
        VideoDTO videoDTO = new VideoDTO();
        videoDTO.setUrl(msgDataDTO.getVideo().getUrl());
        videoDTO.setDuration(msgDataDTO.getVideo().getDuration());
        videoDTO.setCoverUrl(msgDataDTO.getVideo().getCoverUrl());
        return videoDTO;
    }

    private static VoiceDTO createVoiceDTO(MessageDataDTO msgDataDTO) {
        VoiceDTO voiceDTO = new VoiceDTO();
        voiceDTO.setUrl(msgDataDTO.getVoice().getUrl());
        voiceDTO.setDuration(Integer.valueOf(msgDataDTO.getVoice().getDuration()));
        voiceDTO.setText(msgDataDTO.getVoice().getContent());
        return voiceDTO;
    }

}
