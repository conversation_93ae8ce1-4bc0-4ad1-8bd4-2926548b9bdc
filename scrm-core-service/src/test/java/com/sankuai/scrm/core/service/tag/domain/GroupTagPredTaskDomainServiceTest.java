package com.sankuai.scrm.core.service.tag.domain;

import com.sankuai.scrm.core.service.group.dal.babymapper.GroupInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.GroupInfoEntityExample;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagMapping;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagRuleTask;
import com.sankuai.scrm.core.service.tag.dal.entity.GroupTagRuleTaskLog;
import com.sankuai.scrm.core.service.tag.dal.example.GroupPredTagRecordExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupPredTagRecordMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagMappingMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagRuleTaskLogMapper;
import com.sankuai.scrm.core.service.tag.dal.mapper.GroupTagRuleTaskMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class GroupTagPredTaskDomainServiceTest {

    @InjectMocks
    private GroupTagPredTaskDomainService groupTagPredTaskDomainService;

    @Mock(lenient = true)
    private GroupTagRuleTaskMapper groupTagRuleTaskMapper;
    @Mock(lenient = true)
    private GroupPredTagRecordMapper groupPredTagRecordMapper;
    @Mock(lenient = true)
    private GroupTagDomainService groupTagDomainService;
    @Mock(lenient = true)
    private GroupInfoEntityMapper groupInfoEntityMapper;
    @Mock(lenient = true)
    private GroupTagRuleTaskLogMapper groupTagRuleTaskLogMapper;
    @Mock(lenient = true)
    private GroupTagMappingMapper groupTagMappingMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试bindGroupTag方法，当tagIdList为空时，不进行任何操作
     */
    @Test
    public void testBindGroupTagWithEmptyTagIdList() {
        GroupTagRuleTask task = new GroupTagRuleTask();
        GroupInfoEntity groupInfoEntity = new GroupInfoEntity();
        List<String> tagIdList = Collections.emptyList();

        groupTagPredTaskDomainService.bindGroupTag(task, groupInfoEntity, tagIdList);

        verify(groupTagMappingMapper, never()).selectByExample(any());
    }

    /**
     * 测试bindGroupTag方法，当存在新的标签ID时，进行插入操作
     */
    @Test
    public void testBindGroupTagWithNewTagIds() {
        GroupTagRuleTask task = new GroupTagRuleTask(1L, "ruleName", "tagSetSerialNo", "appId", "rule", 1, 1, 1, "runTime", "creator", "updater", 1, 1, new Date(), "resultUrl", false, new Date(), new Date());
        GroupInfoEntity groupInfoEntity = new GroupInfoEntity(1, "corpId", 1L, "groupId", "groupName", "groupNotice", 10, "owner", new Date(), new Date(), false, "adminList");
        List<String> tagIdList = Arrays.asList("tagId1", "tagId2");

        when(groupTagMappingMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(groupTagMappingMapper.batchInsert(anyList())).thenReturn(2);

        groupTagPredTaskDomainService.bindGroupTag(task, groupInfoEntity, tagIdList);

        verify(groupTagMappingMapper, times(1)).batchInsert(anyList());
    }

    /**
     * 测试bindGroupTag方法，当所有标签ID已存在时，不进行插入操作
     */
    @Test
    public void testBindGroupTagWithAllExistingTagIds() {
        GroupTagRuleTask task = new GroupTagRuleTask(1L, "ruleName", "tagSetSerialNo", "appId", "rule", 1, 1, 1, "runTime", "creator", "updater", 1, 1, new Date(), "resultUrl", false, new Date(), new Date());
        GroupInfoEntity groupInfoEntity = new GroupInfoEntity(1, "corpId", 1L, "groupId", "groupName", "groupNotice", 10, "owner", new Date(), new Date(), false, "adminList");
        List<String> tagIdList = Arrays.asList("tagId1", "tagId2");
        GroupTagMapping existingMapping1 = new GroupTagMapping();
        existingMapping1.setGroupTagSerialNo("tagId1");
        GroupTagMapping existingMapping2 = new GroupTagMapping();
        existingMapping2.setGroupTagSerialNo("tagId2");
        List<GroupTagMapping> existingMappings = Arrays.asList(existingMapping1, existingMapping2);

        when(groupTagMappingMapper.selectByExample(any())).thenReturn(existingMappings);

        groupTagPredTaskDomainService.bindGroupTag(task, groupInfoEntity, tagIdList);

        verify(groupTagMappingMapper, never()).batchInsert(anyList());
    }

    /**
     * 测试queryGroupInfo方法，当传入的页码和页面大小都是正常值时
     */
    @Test
    public void testQueryGroupInfoWithValidPageNoAndPageSize() {
        // arrange
        int pageNo = 1;
        int pageSize = 10;
        List<GroupInfoEntity> expected = new ArrayList<>();
        expected.add(new GroupInfoEntity());
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(expected);

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("AppId", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(expected.size(), result.size());
    }

    /**
     * 测试queryGroupInfo方法，当传入的页码或页面大小为负值时
     */
    @Test
    public void testQueryGroupInfoWithInvalidPageNoOrPageSize() {
        // arrange
        int pageNo = -1;
        int pageSize = -10;
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(new ArrayList<>());

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryGroupInfo方法，当数据库中没有数据时
     */
    @Test
    public void testQueryGroupInfoWhenNoData() {
        // arrange
        int pageNo = 1;
        int pageSize = 10;
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(new ArrayList<>());

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryGroupInfo方法，当pageSize为0时
     */
    @Test
    public void testQueryGroupInfoWithPageSizeZero() {
        // arrange
        int pageNo = 1;
        int pageSize = 0;
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(new ArrayList<>());

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryGroupInfo方法，当pageNo为0时
     */
    @Test
    public void testQueryGroupInfoWithPageNoZero() {
        // arrange
        int pageNo = 0;
        int pageSize = 10;
        List<GroupInfoEntity> expected = new ArrayList<>();
        expected.add(new GroupInfoEntity());
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(expected);

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("AppId", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(expected.size(), result.size());
    }

    /**
     * 测试queryGroupInfo方法，当传入的页码超过实际页数时
     */
    @Test
    public void testQueryGroupInfoWithPageNoExceed() {
        // arrange
        int pageNo = 100;
        int pageSize = 10;
        when(groupInfoEntityMapper.selectByExample(any(GroupInfoEntityExample.class))).thenReturn(new ArrayList<>());

        // act
        List<GroupInfoEntity> result = groupTagPredTaskDomainService.queryGroupInfo("", pageNo, pageSize);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试countGroupInfo方法，当数据库中存在符合条件的记录时
     */
    @Test
    public void testCountGroupInfoWithRecords() {
        // arrange
        GroupInfoEntityExample example = new GroupInfoEntityExample();
        example.createCriteria().andDeletedEqualTo(false);
        when(groupInfoEntityMapper.countByExample(any(GroupInfoEntityExample.class))).thenReturn(5L);

        // act
        long result = groupTagPredTaskDomainService.countGroupInfo("corpId");

        // assert
        assertEquals(5L, result);
    }

    /**
     * 测试countGroupInfo方法，当数据库中不存在符合条件的记录时
     */
    @Test
    public void testCountGroupInfoWithNoRecords() {
        // arrange
        GroupInfoEntityExample example = new GroupInfoEntityExample();
        example.createCriteria().andDeletedEqualTo(false);
        when(groupInfoEntityMapper.countByExample(any(GroupInfoEntityExample.class))).thenReturn(0L);

        // act
        long result = groupTagPredTaskDomainService.countGroupInfo("corpId");

        // assert
        assertEquals(0L, result);
    }

    /**
     * 测试countGroupInfo方法，当调用过程中发生异常时
     */
    @Test(expected = RuntimeException.class)
    public void testCountGroupInfoWithException() {
        // arrange
        when(groupInfoEntityMapper.countByExample(any(GroupInfoEntityExample.class))).thenThrow(new RuntimeException());

        // act
        groupTagPredTaskDomainService.countGroupInfo("corpId");

        // assert is handled by the expected exception
    }

    /**
     * 测试ruleTaskId小于等于0时，方法应返回0
     */
    @Test
    public void testInsertPredTaskLogWithRuleTaskIdLessThanOrEqualToZero() {
        long result = groupTagPredTaskDomainService.insertPredTaskLog(0);
        assertEquals(0, result);
    }

    /**
     * 测试ruleTaskId大于0但插入失败时，方法应返回0
     */
    @Test
    public void testInsertPredTaskLogWithRuleTaskIdGreaterThanZeroButInsertFail() {
        when(groupTagRuleTaskLogMapper.insertSelective(any(GroupTagRuleTaskLog.class))).thenReturn(0); // 插入失败
        long result = groupTagPredTaskDomainService.insertPredTaskLog(1);
        assertEquals(0, result);
    }

    /**
     * 参数检查失败 - ruleTaskId <= 0
     */
    @Test
    public void testUpdatePredTaskLogTotal_InvalidRuleTaskId() {
        // arrange
        long ruleTaskId = 0;
        int version = 1;
        long logId = 1;

        // act
        groupTagPredTaskDomainService.updatePredTaskLogTotal("",ruleTaskId, version, logId);

        // assert
        verify(groupPredTagRecordMapper, never()).countByExample(any(GroupPredTagRecordExample.class));
        verify(groupTagRuleTaskLogMapper, never()).updateByPrimaryKeySelective(any(GroupTagRuleTaskLog.class));
    }

    /**
     * 参数检查失败 - version <= 0
     */
    @Test
    public void testUpdatePredTaskLogTotal_InvalidVersion() {
        // arrange
        long ruleTaskId = 1;
        int version = 0;
        long logId = 1;

        // act
        groupTagPredTaskDomainService.updatePredTaskLogTotal("",ruleTaskId, version, logId);

        // assert
        verify(groupPredTagRecordMapper, never()).countByExample(any(GroupPredTagRecordExample.class));
        verify(groupTagRuleTaskLogMapper, never()).updateByPrimaryKeySelective(any(GroupTagRuleTaskLog.class));
    }

    /**
     * 参数检查失败 - logId <= 0
     */
    @Test
    public void testUpdatePredTaskLogTotal_InvalidLogId() {
        // arrange
        long ruleTaskId = 1;
        int version = 1;
        long logId = 0;

        // act
        groupTagPredTaskDomainService.updatePredTaskLogTotal("",ruleTaskId, version, logId);

        // assert
        verify(groupPredTagRecordMapper, never()).countByExample(any(GroupPredTagRecordExample.class));
        verify(groupTagRuleTaskLogMapper, never()).updateByPrimaryKeySelective(any(GroupTagRuleTaskLog.class));
    }

    /**
     * 正常更新场景
     */
    @Test
    public void testUpdatePredTaskLogTotal_Success() {
        // arrange
        long ruleTaskId = 1;
        int version = 1;
        long logId = 1;
        when(groupPredTagRecordMapper.countByExample(any(GroupPredTagRecordExample.class))).thenReturn(10L);

        // act
        groupTagPredTaskDomainService.updatePredTaskLogTotal("",ruleTaskId, version, logId);

        // assert
        verify(groupPredTagRecordMapper).countByExample(any(GroupPredTagRecordExample.class));
        verify(groupTagRuleTaskLogMapper).updateByPrimaryKeySelective(any(GroupTagRuleTaskLog.class));
    }

    /**
     * 查询结果为0场景
     */
    @Test
    public void testUpdatePredTaskLogTotal_NoRecords() {
        // arrange
        long ruleTaskId = 1;
        int version = 1;
        long logId = 1;
        when(groupPredTagRecordMapper.countByExample(any(GroupPredTagRecordExample.class))).thenReturn(0L);

        // act
        groupTagPredTaskDomainService.updatePredTaskLogTotal("",ruleTaskId, version, logId);

        // assert
        verify(groupPredTagRecordMapper).countByExample(any(GroupPredTagRecordExample.class));
        verify(groupTagRuleTaskLogMapper).updateByPrimaryKeySelective(any(GroupTagRuleTaskLog.class));
    }
}
