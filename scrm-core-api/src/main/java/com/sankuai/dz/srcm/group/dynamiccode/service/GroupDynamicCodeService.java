package com.sankuai.dz.srcm.group.dynamiccode.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.group.dynamiccode.dto.*;
import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeStatusEnum;

import java.util.List;
import java.util.Map;

/**
 * 企微群活码相关服务
 */
public interface GroupDynamicCodeService {
    /**
     * 创建群活码
     *
     * @param groupDynamicCodeInfo 用于创建群活码的一些信息
     * @return 创建成功后的群活码信息
     */
    RemoteResponse<GroupDynamicCodeInfoDTO> createCode(GroupDynamicCodeInfoDTO groupDynamicCodeInfo);

    /**
     * 修改群活码状态
     *
     * @param appId         业务线 id
     * @param dynamicCodeId 群活码 id
     * @param status        修改后的 {@linkplain DynamicCodeStatusEnum 群活码状态}
     */
    RemoteResponse<Void> updateCodeStatus(String appId, long dynamicCodeId, int status);

    /**
     * 查询群活码支持的城市枚举（创建群活码时需要勾选）
     *
     * @param appId 业务线 id
     * @return 指定业务线中支持群活码的城市枚举列表
     */
    RemoteResponse<List<SupportedCityDTO>> queryCityEnums(String appId);

    /**
     * 查询群活码列表
     *
     * @param request 查询条件。条件的约束见 {@link QueryDynamicCodeRequest#isParamIllegal()}。
     * @return 匹配查询条件的群活码列表
     */
    RemoteResponse<QueryDynamicCodeResponse> queryDynamicCodeList(QueryDynamicCodeRequest request);

    /**
     * 查询群活码覆盖的群列表
     *
     * @param appId         业务线 id
     * @param dynamicCodeId 群活码 id
     * @return 群活码覆盖的群列表
     */
    RemoteResponse<GroupDynamicCodeCoveredGroupsDTO> queryCoveredGroupList(String appId, long dynamicCodeId);

    /**
     * 接收并处理群组成员变化的事件
     *
     * @param event 事件信息
     */
    RemoteResponse<Void> onGroupMemberChangeEvent(GroupMemberChangeEventDTO event);

    RemoteResponse<GroupDynamicCodeSwitchResult> switchDynamicCode(long dynamicCodeId, String appId);

    RemoteResponse<Boolean> updateCodeConfig(GroupDynamicCodeInfoDTO codeInfoDTO);

    RemoteResponse<QueryQRCodePageInfoResponse> queryMultiSceneDynamicCodePage(QueryQRCodePageInfoDTO codePageInfo);

    RemoteResponse<DynamicCodeQueryInfoDTO> queryGroupDynamicCodeInfo(String appId, Long codeId);

}
