package com.sankuai.dz.srcm.pchat.service.superadmin;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import com.sankuai.dz.srcm.pchat.request.im.ImFriendListRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.superadmin.PlatformRobotGroupCreateRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.superadmin.PlatformRobotGroupListRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.superadmin.PlatformRobotGroupLogRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.superadmin.PlatformRobotListRequest;
import com.sankuai.dz.srcm.pchat.response.im.ImFriendListResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.superadmin.PlatformRobotGroupListResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.superadmin.PlatformRobotGroupLogResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.superadmin.PlatformRobotListResponse;
import com.sankuai.dz.srcm.pchat.response.scrm.superadmin.RobotInfoDTO;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/11/4 11:16
 * @Version v1.0.0
 */
public interface ScrmPlatformRobotManageService {
    /**
     * 添加机器人
     * @param request
     * @return
     */
    RemoteResponse<Boolean> addPlatformRobot(PlatformRobotGroupCreateRequest request);

    /**
     * 删除机器人
     * @param request
     * @return
     */
    RemoteResponse<Boolean> removePlatformRobot(PlatformRobotGroupCreateRequest request);

    /**
     * 添加平台号组
     * @param request
     * @return
     */
    RemoteResponse<Boolean> addPlatformRobotGroup(PlatformRobotGroupCreateRequest request);

    /**
     * 修改平台号组
     * @param request
     */
    RemoteResponse<Boolean> modifyPlatformRobotGroup(PlatformRobotGroupCreateRequest request);

    /**
     * 查询平台号组列表
     * @param request
     * @return
     */
    PageRemoteResponse<PlatformRobotGroupListResponse> queryPlatformRobotGroupList(PlatformRobotGroupListRequest request);

    /**
     * 查询平台号组配置的机器人列表
     * @param request
     * @return
     */
    PageRemoteResponse<PlatformRobotListResponse> queryPlatformRobotList(PlatformRobotListRequest request);

    /**
     * 查询机器人列表
     * @param request
     * @return
     */
    PageRemoteResponse<RobotInfoDTO> queryRobotList(PageRequest request);

    /**
     * 查询平台号组操作日志
     * @param request
     * @return
     */
    PageRemoteResponse<PlatformRobotGroupLogResponse> queryPlatformRobotGroupOperationLog(PlatformRobotGroupLogRequest request);
    PageRemoteResponse<ImFriendListResponse> queryFriendList(ImFriendListRequest request);


}
