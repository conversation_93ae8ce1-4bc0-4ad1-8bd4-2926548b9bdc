package com.sankuai.scrm.core.service.user.domain;

import com.sankuai.dz.srcm.user.enums.PrivateSphereUserTagEnum;
import com.sankuai.scrm.core.service.pchat.service.LockService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthYimeiLiveUserTagMapper;
import com.sankuai.scrm.core.service.user.domain.tag.UserTagEsStoreDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/6/11 11:01
 * @Version v1.0.0
 */
@Slf4j
@Component
public class PrivateUserTagStoreDomainService {
    @Resource
    private ScrmUserGrowthYimeiLiveUserTagMapper userGrowthYimeiLiveUserTagMapper;
    @Resource
    private LockService lockService;
    @Resource
    private UserTagEsStoreDomainService userTagEsStoreDomainService;
    @Resource
    private PrivateUserDataHandleDomainService privateUserDataHandleDomainService;


    public void saveOrUpdateTag(List<ScrmUserGrowthYimeiLiveUserTag> liveUserTagList, Long userId, String unionId, Long consultantTaskId, String liveId, int expectTagId, int expectTagType) {
        privateUserDataHandleDomainService.saveOrUpdateTag(liveUserTagList, userId, unionId, consultantTaskId, liveId, expectTagId, expectTagType);
    }

    public List<ScrmUserGrowthYimeiLiveUserTag> queryUserTagList(Long userId, String unionId, Long consultantTaskId, String projectId) {
        return privateUserDataHandleDomainService.queryUserTagList(null, userId, unionId, consultantTaskId, projectId);
    }

    public ScrmUserGrowthYimeiLiveUserTag filterCurrentTag(List<ScrmUserGrowthYimeiLiveUserTag> liveUserTagList, int expectTagType) {
        return privateUserDataHandleDomainService.filterCurrentTag(liveUserTagList, expectTagType);
    }

    public ScrmUserGrowthYimeiLiveUserTag filterTagByTagId(List<ScrmUserGrowthYimeiLiveUserTag> liveUserTagList, int expectTagId) {
        if (CollectionUtils.isEmpty(liveUserTagList)) {
            return null;
        }
        return liveUserTagList.stream().filter(tag -> tag.getTagId() == expectTagId).findFirst().orElse(null);
    }

    public List<ScrmUserGrowthYimeiLiveUserTag> filterCurrentTags(List<ScrmUserGrowthYimeiLiveUserTag> liveUserTagList, int expectTagType) {
        if (CollectionUtils.isEmpty(liveUserTagList)) {
            return new ArrayList<>();
        }
        return liveUserTagList.stream().filter(tag -> {
            int tagId = tag.getTagId();
            PrivateSphereUserTagEnum tagEnum = PrivateSphereUserTagEnum.fromCode(tagId);
            if (tagEnum == null) {
                return false;
            }
            int tagType = tagEnum.getTagType();
            return tagType == expectTagType;
        }).collect(Collectors.toList());
    }

    public ScrmUserGrowthYimeiLiveUserTag buildUserTagAdditionalWxId(boolean isAdditionalWxId, String wxId, Long userId, String unionId, Long consultantTaskId, String projectId, int tagId) {
        return privateUserDataHandleDomainService.buildUserTagAdditionalWxId(isAdditionalWxId, wxId, userId, unionId, consultantTaskId, projectId, tagId);
    }

    public void updateUserTagForOnly(ScrmUserGrowthYimeiLiveUserTag tag, boolean isChangeActionTime) {
        privateUserDataHandleDomainService.updateUserTagForOnly(tag, isChangeActionTime);
    }

    public void idempotentSave(ScrmUserGrowthYimeiLiveUserTag tag) {
        privateUserDataHandleDomainService.idempotentSave(tag);
    }
}
