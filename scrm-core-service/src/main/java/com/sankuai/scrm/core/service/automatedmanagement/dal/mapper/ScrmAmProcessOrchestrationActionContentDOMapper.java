package com.sankuai.scrm.core.service.automatedmanagement.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionContentDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActionContentDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ScrmAmProcessOrchestrationActionContentDOMapper extends MybatisBaseMapper<ScrmAmProcessOrchestrationActionContentDO, ScrmAmProcessOrchestrationActionContentDOExample, Long> {
    int batchInsert(@Param("list") List<ScrmAmProcessOrchestrationActionContentDO> list);
}