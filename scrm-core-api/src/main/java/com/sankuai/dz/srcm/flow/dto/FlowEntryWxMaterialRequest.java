package com.sankuai.dz.srcm.flow.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FlowEntryWxMaterialRequest implements Serializable {

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目id（多类目）
     */
    private List<Long> categoryIds;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 点评门店id
     */
    private Long shopId;//门店id

    /**
     * 美团门店id
     */
    private Long mtShopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 美团城市id
     */
    private Integer mtCityId;

    /**
     * 点评城市id
     */
    private Integer dpCityId;

    /**
     * 美团userId
     */
    private Long userId;

    /**
     * 点评userId
     */
    private Long dpUserId;

    /**
     * 地理位置信息
     */
    private GeoDTO geoDTO;

    /**
     * @see com.sankuai.dz.srcm.flow.enums.PlatformType
     */
    private Integer platform;

    /**
     * 页面类型或位置
     * @see com.sankuai.dz.srcm.flow.enums.PageLocationType
     */
    private Integer pageLocation;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 业务线id
     */
    private String appId;

    //订单的商品类型
    private Integer orderProductType;

    /**
     * 券id
     */
    private Long couponId;

    /**
     * 进入社群方式配置
     */
    private EntryWayDTO entryWayDTO;

    /**
     * APP版本号
     */
    private String csecVersionName;

    /**
     * 判断新旧版本号，1-新版本，0-旧版本
     */
    private Integer version;

}
