package com.sankuai.dz.srcm.pchat.request.robot.agent;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.srcm.pchat.enums.robot.RobotOnlineEnum;
import com.sankuai.dz.srcm.pchat.enums.robot.RobotValidEnum;
import com.sankuai.dz.srcm.pchat.request.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2025/7/18 14:15
 * @Version v1.0.0
 */
@Data
@TypeDoc(description = "机器人信息")
public class RobotListRequest extends PageRequest {

    @FieldDoc(description = "机器人编号")
    private List<String> robotSerialNos;

    /**
     * @see RobotOnlineEnum
     */
    @FieldDoc(description = "在线状态")
    private Integer online;

    /**
     * @see RobotValidEnum
     */
    @FieldDoc(description = "有效状态")
    private Integer valid;

    @FieldDoc(description = "业务线")
    private String bizId;
}
