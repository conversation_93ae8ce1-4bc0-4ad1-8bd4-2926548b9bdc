package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.LocationInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductBizTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopSearchRequest;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ShelfProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.QueryActivityPageShelfProductRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.ShelfProductInfoResultVO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class ProductManagementServiceGetDealProductRequestTest {

    @Mock
    private GeneralProductShopSearchService generalProductShopSearchService;

    @InjectMocks
    private ProductManagementService productManagementService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private DealProductRequest invokeGetDealProductRequest(List<Long> dealIds, int platform, QueryActivityPageShelfProductRequest request) throws Exception {
        Method method = ProductManagementService.class.getDeclaredMethod("getDealProductRequest", List.class, int.class, QueryActivityPageShelfProductRequest.class);
        method.setAccessible(true);
        return (DealProductRequest) method.invoke(productManagementService, dealIds, platform, request);
    }

    /**
     * Test normal case with valid dealIds and shop search response
     */
    @Test
    public void testGetDealProductRequestNormalCase() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1001L, 1002L);
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(10);
        request.setDeviceId("device123");
        request.setLat(39.9042);
        request.setLng(116.4074);
        ProductShopSearchDTO shop1 = new ProductShopSearchDTO();
        shop1.setProductId(1001L);
        shop1.setShopId(2001L);
        ProductShopSearchDTO shop2 = new ProductShopSearchDTO();
        shop2.setProductId(1002L);
        shop2.setShopId(2002L);
        GeneralProductShopSearchResponse response = new GeneralProductShopSearchResponse();
        response.setResult(Arrays.asList(shop1, shop2));
        when(generalProductShopSearchService.searchProductShops(any(GeneralProductShopSearchRequest.class))).thenReturn(response);
        // act
        DealProductRequest result = invokeGetDealProductRequest(dealIds, platform, request);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getDealIds().size());
        assertEquals(2, result.getProductIds().size());
        assertEquals("10002620", result.getPlanId());
        assertNotNull(result.getExtParams());
        assertEquals(123L, result.getExtParams().get("userId"));
        assertNotNull(result.getDealId2ShopIdForLong());
        assertEquals(2, result.getDealId2ShopIdForLong().size());
        assertEquals(2001L, result.getDealId2ShopIdForLong().get(1001L));
    }

    /**
     * Test case when generalProductShopSearchResponse is null
     */
    @Test
    public void testGetDealProductRequestNullResponse() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1001L, 1002L);
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        when(generalProductShopSearchService.searchProductShops(any(GeneralProductShopSearchRequest.class))).thenReturn(null);
        // act
        DealProductRequest result = invokeGetDealProductRequest(dealIds, platform, request);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getDealIds().size());
        // Don't assert extParams as it may be null in this case
        // Should be null when response is null
        assertNull(result.getDealId2ShopIdForLong());
    }

    /**
     * Test case with empty dealIds list
     */
    @Test
    public void testGetDealProductRequestEmptyDealIds() throws Throwable {
        // arrange
        List<Long> dealIds = Collections.emptyList();
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        // act
        DealProductRequest result = invokeGetDealProductRequest(dealIds, platform, request);
        // assert
        assertNotNull(result);
        assertTrue(result.getDealIds().isEmpty());
        assertTrue(result.getProductIds().isEmpty());
        // Don't assert extParams as it may be null in this case
        // Should be null when no deals
        assertNull(result.getDealId2ShopIdForLong());
    }

    /**
     * Test case with dealIds containing values > Integer.MAX_VALUE
     */
    @Test
    public void testGetDealProductRequestLargeDealIds() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(Integer.MAX_VALUE + 1L, Integer.MAX_VALUE + 2L);
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        ProductShopSearchDTO shop1 = new ProductShopSearchDTO();
        shop1.setProductId(Integer.MAX_VALUE + 1L);
        shop1.setShopId(2001L);
        GeneralProductShopSearchResponse response = new GeneralProductShopSearchResponse();
        response.setResult(Collections.singletonList(shop1));
        when(generalProductShopSearchService.searchProductShops(any(GeneralProductShopSearchRequest.class))).thenReturn(response);
        // act
        DealProductRequest result = invokeGetDealProductRequest(dealIds, platform, request);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getDealIds().size());
        // Should filter out values > Integer.MAX_VALUE
        assertTrue(result.getProductIds().isEmpty());
        assertNotNull(result.getDealId2ShopIdForLong());
        assertEquals(1, result.getDealId2ShopIdForLong().size());
    }

    /**
     * Test case with request containing location info
     */
    @Test
    public void testGetDealProductRequestWithLocationInfo() throws Throwable {
        // arrange
        List<Long> dealIds = Arrays.asList(1001L);
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(10);
        request.setLat(39.9042);
        request.setLng(116.4074);
        request.setDeviceId("device123");
        ProductShopSearchDTO shop1 = new ProductShopSearchDTO();
        shop1.setProductId(1001L);
        shop1.setShopId(2001L);
        GeneralProductShopSearchResponse response = new GeneralProductShopSearchResponse();
        response.setResult(Collections.singletonList(shop1));
        when(generalProductShopSearchService.searchProductShops(any(GeneralProductShopSearchRequest.class))).thenReturn(response);
        // act
        DealProductRequest result = invokeGetDealProductRequest(dealIds, platform, request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getDealIds().size());
        assertNotNull(result.getExtParams());
        assertEquals(39.9042, request.getLat());
        assertEquals(116.4074, request.getLng());
        assertEquals(VCClientTypeEnum.MT_XCX.getCode(), result.getExtParams().get("clientType"));
        assertNotNull(result.getDealId2ShopIdForLong());
    }
}
