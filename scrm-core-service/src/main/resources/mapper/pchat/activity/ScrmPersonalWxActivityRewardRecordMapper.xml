<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.scrm.core.service.pchat.dal.activity.mapper.ScrmPersonalWxActivityRewardRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRewardRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_member_id" jdbcType="BIGINT" property="groupMemberId" />
    <result column="invite_count" jdbcType="INTEGER" property="inviteCount" />
    <result column="reward_product_id" jdbcType="BIGINT" property="rewardProductId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="push_state" jdbcType="CHAR" property="pushState" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
    <result column="push_time" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, activity_id, group_id, group_member_id, invite_count, reward_product_id, add_time, 
    app_id, push_state, push_result, push_time, creator, update_time, union_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.example.ScrmPersonalWxActivityRewardRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_ActivityRewardRecord
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from Scrm_PersonalWx_ActivityRewardRecord
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from Scrm_PersonalWx_ActivityRewardRecord
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.example.ScrmPersonalWxActivityRewardRecordExample">
    delete from Scrm_PersonalWx_ActivityRewardRecord
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRewardRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_ActivityRewardRecord (activity_id, group_id, group_member_id, 
      invite_count, reward_product_id, add_time, 
      app_id, push_state, push_result, 
      push_time, creator, update_time, 
      union_id)
    values (#{activityId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{groupMemberId,jdbcType=BIGINT}, 
      #{inviteCount,jdbcType=INTEGER}, #{rewardProductId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{appId,jdbcType=VARCHAR}, #{pushState,jdbcType=CHAR}, #{pushResult,jdbcType=VARCHAR}, 
      #{pushTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{unionId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRewardRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into Scrm_PersonalWx_ActivityRewardRecord
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupMemberId != null">
        group_member_id,
      </if>
      <if test="inviteCount != null">
        invite_count,
      </if>
      <if test="rewardProductId != null">
        reward_product_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="pushState != null">
        push_state,
      </if>
      <if test="pushResult != null">
        push_result,
      </if>
      <if test="pushTime != null">
        push_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupMemberId != null">
        #{groupMemberId,jdbcType=BIGINT},
      </if>
      <if test="inviteCount != null">
        #{inviteCount,jdbcType=INTEGER},
      </if>
      <if test="rewardProductId != null">
        #{rewardProductId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="pushState != null">
        #{pushState,jdbcType=CHAR},
      </if>
      <if test="pushResult != null">
        #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.example.ScrmPersonalWxActivityRewardRecordExample" resultType="java.lang.Long">
    select count(*) from Scrm_PersonalWx_ActivityRewardRecord
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update Scrm_PersonalWx_ActivityRewardRecord
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.groupMemberId != null">
        group_member_id = #{record.groupMemberId,jdbcType=BIGINT},
      </if>
      <if test="record.inviteCount != null">
        invite_count = #{record.inviteCount,jdbcType=INTEGER},
      </if>
      <if test="record.rewardProductId != null">
        reward_product_id = #{record.rewardProductId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.pushState != null">
        push_state = #{record.pushState,jdbcType=CHAR},
      </if>
      <if test="record.pushResult != null">
        push_result = #{record.pushResult,jdbcType=VARCHAR},
      </if>
      <if test="record.pushTime != null">
        push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update Scrm_PersonalWx_ActivityRewardRecord
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      group_member_id = #{record.groupMemberId,jdbcType=BIGINT},
      invite_count = #{record.inviteCount,jdbcType=INTEGER},
      reward_product_id = #{record.rewardProductId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      app_id = #{record.appId,jdbcType=VARCHAR},
      push_state = #{record.pushState,jdbcType=CHAR},
      push_result = #{record.pushResult,jdbcType=VARCHAR},
      push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      union_id = #{record.unionId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRewardRecord">
    update Scrm_PersonalWx_ActivityRewardRecord
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupMemberId != null">
        group_member_id = #{groupMemberId,jdbcType=BIGINT},
      </if>
      <if test="inviteCount != null">
        invite_count = #{inviteCount,jdbcType=INTEGER},
      </if>
      <if test="rewardProductId != null">
        reward_product_id = #{rewardProductId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="pushState != null">
        push_state = #{pushState,jdbcType=CHAR},
      </if>
      <if test="pushResult != null">
        push_result = #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="pushTime != null">
        push_time = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.scrm.core.service.pchat.dal.activity.entity.ScrmPersonalWxActivityRewardRecord">
    update Scrm_PersonalWx_ActivityRewardRecord
    set activity_id = #{activityId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      group_member_id = #{groupMemberId,jdbcType=BIGINT},
      invite_count = #{inviteCount,jdbcType=INTEGER},
      reward_product_id = #{rewardProductId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      app_id = #{appId,jdbcType=VARCHAR},
      push_state = #{pushState,jdbcType=CHAR},
      push_result = #{pushResult,jdbcType=VARCHAR},
      push_time = #{pushTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      union_id = #{unionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into Scrm_PersonalWx_ActivityRewardRecord
    (activity_id, group_id, group_member_id, invite_count, reward_product_id, add_time, 
      app_id, push_state, push_result, push_time, creator, update_time, union_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.activityId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT}, #{item.groupMemberId,jdbcType=BIGINT}, 
        #{item.inviteCount,jdbcType=INTEGER}, #{item.rewardProductId,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.appId,jdbcType=VARCHAR}, #{item.pushState,jdbcType=CHAR}, 
        #{item.pushResult,jdbcType=VARCHAR}, #{item.pushTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.unionId,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>