package com.sankuai.scrm.core.service.pchat.mq.dto;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/2/23 11:46
 * @Version v1.0.0
 */
@Data
public class GroupFollowTask extends DelayTask {
    /**
     * 商家编号
     */
    private String vcMerchantNo;

    /**
     * 机器人编号
     */
    private String vcRobotSerialNo;
    /**
     * 群id
     */
    private String chatroomWxSerialNo;
    /**
     * 最大延迟调用次数
     */
    private final Integer maxCount = 10;
    /**
     * 当前调用次数
     */
    private Integer currentCount;

    public Integer getMaxCount() {
        return maxCount;
    }

    public Integer getCurrentCount() {
        return currentCount == null ? 1 : (currentCount < 0 ? 1 : currentCount);
    }
}
