package com.sankuai.scrm.core.service.user.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class ScrmMeituanActionConsumerTest {

    private ScrmMeituanActionConsumer scrmMeituanActionConsumer;

    private IConsumerProcessor consumer;

    @BeforeEach
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        scrmMeituanActionConsumer = new ScrmMeituanActionConsumer();
        consumer = Mockito.mock(IConsumerProcessor.class);
        // Use reflection to set the consumer field
        Field consumerField = ScrmMeituanActionConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmMeituanActionConsumer, consumer);
    }

    /**
     * 测试destroy方法，消费者为空的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // Use reflection to set the consumer field to null
        Field consumerField = ScrmMeituanActionConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmMeituanActionConsumer, null);
        // act
        scrmMeituanActionConsumer.destroy();
        // assert
        verify(consumer, times(0)).close();
    }

    /**
     * 测试destroy方法，消费者不为空的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // act
        scrmMeituanActionConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
