package com.sankuai.scrm.core.service.pchat.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.util.Pair;
import com.sankuai.dz.srcm.pchat.dto.GroupListDTO;
import com.sankuai.dz.srcm.pchat.dto.SyncInvokeResultDTO;
import com.sankuai.dz.srcm.pchat.request.scrm.ScrmLiveConfigCreateRequest;
import com.sankuai.dz.srcm.pchat.tanjing.RobotInfoService;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveServiceTypeEnum;
import com.sankuai.scrm.core.service.pchat.bo.GroupOwnerStat;
import com.sankuai.scrm.core.service.pchat.bo.RobotFriendBO;
import com.sankuai.scrm.core.service.pchat.config.CreateGroupLimitSwitch;
import com.sankuai.scrm.core.service.pchat.config.PchatConfig;
import com.sankuai.scrm.core.service.pchat.constant.ApiConstants;
import com.sankuai.scrm.core.service.pchat.constant.ExceptionMsg;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxLiveRobotCluster;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotCluster;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxLiveGroupConfigDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxRobotChooseDomainService;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxUserDomainService;
import com.sankuai.scrm.core.service.pchat.dto.RobotChooseDTO;
import com.sankuai.scrm.core.service.pchat.enums.*;
import com.sankuai.scrm.core.service.pchat.enums.im.ImRobotOnlineStateType;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import com.sankuai.scrm.core.service.pchat.exception.RobotNoEnoughException;
import com.sankuai.scrm.core.service.pchat.notify.ClairvoyanceService;
import com.sankuai.scrm.core.service.pchat.notify.NotifyTypeEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/6/14 18:36
 * @Version v1.0.0
 */
@Slf4j
@Service
public class ScrmRobotChooseService {

    @Resource
    private ScrmPersonalWxRobotChooseDomainService wxRobotChooseDomainService;
    @Resource
    private ClairvoyanceService clairvoyanceService;
    @Resource
    private ScrmCacheService scrmCacheService;
    @Resource
    private ScrmPersonalWxGroupManageDomainService groupManageDomainService;
    @Resource
    private ScrmPersonalWxUserDomainService personalWxUserDomainService;
    @Resource
    private RobotInfoService robotInfoService;
    @Resource
    private ScrmRobotChooseV1Service robotChooseV1Service;
    @Resource
    private ScrmPersonalWxLiveGroupConfigDomainService personalWxLiveGroupConfigDomainService;
    @Resource
    private WebcastService webcastService;

    /**
     * 查找直播可用的机器人
     *
     * @param liveId
     * @return
     */
    public RobotChooseDTO queryAvailableRobotSerial(String liveId) {
        log.info("查找直播下可用的机器人：{}", liveId);
        RobotChooseDTO dto = new RobotChooseDTO();
        dto.setClusterData(true);
        List<ScrmPersonalWxLiveRobotCluster> liveRobotClusters = wxRobotChooseDomainService.queryLiveRobotCluster(liveId, PersonalRobotClusterUsingStateEnum.USING);
        List<String> clusterNos;
        if (CollectionUtil.isEmpty(liveRobotClusters)) {
            log.info("直播未配置机器人隔离策略，将使用共享方式：{}", liveId);
            // todo limit share cluster number
            List<ScrmPersonalWxRobotCluster> robotClusters = wxRobotChooseDomainService.queryRobotCluster(PersonalRobotClusterTypeEnum.SHARE);
            clusterNos = (robotClusters.stream().map(ScrmPersonalWxRobotCluster::getClusterNo).distinct().collect(Collectors.toList()));
            dto.setClusterData(false);
            autoCreateConfig(liveId, clusterNos);
        } else {
            log.info("直播配置了机器人隔离策略，将使用独享方式：{}", liveId);
            clusterNos = liveRobotClusters.stream().map(ScrmPersonalWxLiveRobotCluster::getClusterNo).distinct().collect(Collectors.toList());
        }
        log.info("当前直播：{},使用的蔟信息，{}", liveId, JsonUtils.toStr(clusterNos));
        dto.setRobotInfos(personalWxUserDomainService.queryAvailableRobotByClusterNos(clusterNos));
        return dto;
    }

    private void autoCreateConfig(String liveId, List<String> clusterNos) {
        try {
            log.info("直播未配置机器人隔离策略，自动创建配置");
            if (StringUtils.isBlank(liveId) || CollectionUtil.isEmpty(clusterNos)) {
                return;
            }
            ScrmLiveConfigCreateRequest request = new ScrmLiveConfigCreateRequest();
            request.setWebcastId(liveId);
            request.setRobotType(ClusterRobotType.PING_TAI.getCode());
            request.setRemark("直播未配置机器人隔离策略，将使用共享方式");
            request.setClusterList(clusterNos);
            List<ScrmPersonalWxLiveRobotCluster> liveRobotClusters = wxRobotChooseDomainService.queryLiveRobotCluster(liveId, PersonalRobotClusterUsingStateEnum.USING);
            if (CollectionUtil.isNotEmpty(liveRobotClusters)) {
                log.info("直播已配置机器人隔离策略，不自动创建隔离配置");
                clusterNos.clear();
            }

            personalWxLiveGroupConfigDomainService.createConfig(request, clusterNos, null);
        } catch (Exception e) {
            log.error("autoCreateConfig error", e);
        }
    }

    /**
     * 保证：机器人创群均匀分布
     * <p>
     * 1、从数据库查找机器人创建的所有群
     * 2、选取一个机器，其包含最小的一个群数
     * <p>
     * 限制：
     * 一个机器人每天只能创建2个群
     * 一个机器人每分钟只能创建1个群
     *
     * @return
     */
    public String obtainRobotSerialNo(String liveId) {
//        if (StrUtil.isBlank(liveId)) {
//            throw new PChatBusinessException("liveId is null");
//        }
        log.info("创建群时，挑选机器人，liveId:{}", liveId);
        LiveRoomInfo roomInfo = webcastService.queryLiveRoomById(liveId);
        boolean isSaasService = Objects.nonNull(roomInfo) && Objects.equals(LiveServiceTypeEnum.SAAS.getValue(), roomInfo.getServiceType());
        ClusterRobotType clusterRobotType;
        List<String> robotSerialNos;
        if (isSaasService) {
            clusterRobotType = ClusterRobotType.TUO_GUAN;
            List<ScrmPersonalWxRobotInfo> robotInfos = personalWxUserDomainService.queryValidRobotByTenantId(roomInfo.getMainAnchorId(), com.sankuai.scrm.core.service.pchat.enums.LiveServiceTypeEnum.SAAS.getCode());
            robotSerialNos = robotInfos.stream().map(ScrmPersonalWxRobotInfo::getRobotSerialNo).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(robotSerialNos)) {
                robotWarn("saas_obtainRobotSerialNo无可用的机器人建群");
            }
        } else {
            Boolean newChooseSwitch = Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.robot.choose.switch", true);
            if (Boolean.FALSE == newChooseSwitch) {
                log.info("选择机器人使用新版本开关未开");
                return robotChooseV1Service.getRobotSerialNo();
            }
            RobotChooseDTO dto = queryAvailableRobotSerial(liveId);
            if (dto == null || CollectionUtil.isEmpty(dto.getRobotInfos())) {
                robotWarn("obtainRobotSerialNo无可用的机器人建群");
            }
            robotSerialNos = dto.getRobotInfos().stream().map(ScrmPersonalWxRobotInfo::getRobotSerialNo).distinct().collect(Collectors.toList());

            clusterRobotType = personalWxLiveGroupConfigDomainService.queryLiveClusterRobotType(liveId);
            log.info("直播机器人类型，robotType={},liveId={}", clusterRobotType, liveId);
        }
        List<GroupOwnerStat> groupOwnerStats = filterRobot(clusterRobotType, robotSerialNos, isSaasService ? null : PersonalIsBooleanStrEnum.TRUE.getDesc());
        log.info("按照用户最小创建过的群数，以保证均匀   <已创建过的群数，群统计>");
        String robot = chooseRobot(groupOwnerStats);
        log.info("当前直播：{},选中的机器人:robot:{}", liveId, robot);
        return robot;
    }

    /**
     * 过滤符合条件的机器人
     *
     * @param clusterRobotType
     * @param robotSerialNos
     * @return
     */
    private List<GroupOwnerStat> filterRobot(ClusterRobotType clusterRobotType, List<String> robotSerialNos, String needClusterNo) {
        int createGroupMinMemberCount = PchatConfig.getGroupConfig().getCreateGroupMinMemberCount();
        log.info("统计用户已经创建过的群数");
        List<GroupOwnerStat> groupOwnerStats = groupManageDomainService.userGroupCount(createGroupMinMemberCount, robotSerialNos, needClusterNo);
        log.info("过滤机器人被限制，机器人数={}", groupOwnerStats.size());
        if (clusterRobotType != ClusterRobotType.TUO_GUAN) {
            groupOwnerStats = filterCurrentLimitingRobot(groupOwnerStats);
        }
//        groupOwnerStats.sort(Comparator.comparingInt(GroupOwnerStat::getTotal));
        if (CollectionUtil.isEmpty(groupOwnerStats)) {
            robotWarn("filter无可用的机器人建群");
        }
        return groupOwnerStats;
    }

    /**
     * 创群必须有非本人的3个好友，否则创建不成功
     *
     * @return
     */
    public String getFriendSerialNos(String groupOwner) {
        log.info("获取机器人好友关系，robot:{}", groupOwner);
        Boolean newChooseSwitch = Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.robot.choose.switch", true);
        if (Boolean.FALSE == newChooseSwitch) {
            log.info("选择机器人使用新版本开关未开");
            return robotChooseV1Service.getFriendSerialNos(groupOwner);
        }
        return doGetFriendSerialNos(groupOwner);
    }

    public String doGetFriendSerialNos(String groupOwner) {
        log.info("获取机器人好友关系-机器人隔离，robot:{}", groupOwner);
        List<ScrmPersonalWxRobotInfo> robotInfos = personalWxUserDomainService.queryRobotBySerialNo(groupOwner);
        if (CollectionUtil.isEmpty(robotInfos)) {
            return null;
        }
        ScrmPersonalWxRobotInfo robotInfo = robotInfos.get(0);

        int createGroupMinMemberCount = PchatConfig.getGroupConfig().getCreateGroupMinMemberCount();
        List<RobotFriendBO> robotFriendBOS = personalWxUserDomainService.queryRobotAllFriendList(Collections.singletonList(groupOwner));
        List<String> friendSerialNo = robotFriendBOS.stream().filter(r -> PersonalIsBooleanStrEnum.TRUE.getDesc().equals(r.getFriendValid()) && Objects.equals(robotInfo.getClusterNo(), r.getFriendClusterNo())).map(RobotFriendBO::getFriendWxSerialNo).distinct().collect(Collectors.toList());
        if (friendSerialNo.size() < createGroupMinMemberCount) {
            scrmCacheService.groupStatAdd(groupOwner);
            scrmCacheService.methodInOneMinuteInvokeStat("robotCreateChatRoom", groupOwner);
            throw new PChatBusinessException("群主：" + groupOwner + ExceptionMsg.format(ExceptionMsg.CREATE_GROUP_MEMBER_COUNT_EXCEPTION, createGroupMinMemberCount));
        }
        List<String> robotFriendList = friendSerialNo.subList(0, createGroupMinMemberCount);
        log.info("机器好友获取成功,{}", robotFriendList);
        return CollectionUtil.join(robotFriendList, ",");
    }

    /**
     * 切换机器人
     *
     * @param robotSerialNo
     * @param chatroomSerialNo
     * @return true 代表非有效机器人，将不使用
     */
    public Pair<String, Boolean> findValidRobot(String robotSerialNo, String chatroomSerialNo, Function<ScrmPersonalWxRobotInfo, Boolean> processValidFunction) {
        boolean valid = true;
        try {
            List<ScrmPersonalWxRobotInfo> robotInfos = null;
            if (StringUtils.isNotBlank(robotSerialNo)) {
                robotInfos = personalWxUserDomainService.queryRobotBySerialNo(robotSerialNo);
            }
            if (CollectionUtil.isEmpty(robotInfos) || !isValidRobot(robotInfos.get(0), processValidFunction)) {
                valid = false;
                log.warn("当前机器人无效(禁用、离线...)，将找寻其他机器人,robot:{}", robotSerialNo);
                String otherRobot = findOthersValidRobot(chatroomSerialNo, robotSerialNo, processValidFunction);
                if (otherRobot == null) {
                    log.warn("未找到群内或者有效的机器人信息，使用原机器人,robotSerialNo:{}", robotSerialNo);
                    return new Pair<>(robotSerialNo, false);
                }
                log.info("找到其他机器人成功，originRobot:{},newRobot:{}", robotSerialNo, otherRobot);
                return new Pair<>(otherRobot, true);
            }
        } catch (Exception e) {
            log.error("检测机器人信息失败：" + e.getMessage(), e);
            return new Pair<>(robotSerialNo, valid);
        }
        return new Pair<>(robotSerialNo, true);
    }

    public boolean isValidRobot(ScrmPersonalWxRobotInfo robotInfo, Function<ScrmPersonalWxRobotInfo, Boolean> processValidFunction) {
        return ImRobotOnlineStateType.CONNECT.getCode().equals(robotInfo.getOnline()) && PersonalIsBooleanStrEnum.TRUE.getDesc().equals(robotInfo.getValid()) && (processValidFunction == null || processValidFunction.apply(robotInfo));
    }

    /**
     * 找寻其他有效机器人
     *
     * @param chatroomSerialNo
     * @param currentRobotInfo
     * @return
     */
    private String findOthersValidRobot(String chatroomSerialNo, String currentRobotInfo, Function<ScrmPersonalWxRobotInfo, Boolean> processValidFunction) {
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = groupManageDomainService.queryGroupMemberByGroupSerialNo(Collections.singletonList(chatroomSerialNo), PersonalWxGroupMemberTypeEnum.ROBOT);
        if (CollectionUtil.isEmpty(groupMemberInfoEntities)) {
            return null;
        }
        List<String> otherRobotMemberList = groupMemberInfoEntities.stream().map(ScrmPersonalWxGroupMemberInfoEntity::getUserSerialNo).filter(userSerialNo -> !Objects.equals(userSerialNo, currentRobotInfo)).distinct().collect(Collectors.toList());
        List<ScrmPersonalWxRobotInfo> robotInfos = personalWxUserDomainService.queryRobotBySerialNos(otherRobotMemberList);
        robotInfos = robotInfos.stream().filter(r -> isValidRobot(r, processValidFunction)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(robotInfos)) {
            return null;
        }
        log.info("找到其他机器人，robot:{}", robotInfos.get(0).getRobotSerialNo());
        return robotInfos.get(0).getRobotSerialNo();
    }

    /**
     * 精选机器人
     * 从可用的机器人列表中选取一个
     * 选取一个机器，其包含最小的一个群数
     *
     * @param groupOwnerStats
     * @return
     */
    private String chooseRobot(List<GroupOwnerStat> groupOwnerStats) {
        Map<Integer, List<GroupOwnerStat>> totalWithGroupOwnerStat = groupOwnerStats.stream().collect(Collectors.groupingBy(GroupOwnerStat::getTotal));
        // 按照统计的总数进行从小到大排序
        Map<Integer, List<GroupOwnerStat>> treeMap = new TreeMap<>(totalWithGroupOwnerStat);
        // 按照使用群数最小的开始遍历
        GroupOwnerStat groupOwnerStat = null;
        for (Map.Entry<Integer, List<GroupOwnerStat>> integerListEntry : treeMap.entrySet()) {
            List<GroupOwnerStat> currentSequenceList = integerListEntry.getValue();
            if (currentSequenceList.size() == 0) {
                continue;
            }
            // 随机取一个,如果随机选的不满足关注小于30，继续选；如果已经选择的不满足小于30并且选过的大于了总数，则终止；
            // 随机遍历列表
            List<GroupOwnerStat> copyList = new ArrayList<>(currentSequenceList);
            while (!copyList.isEmpty()) {
                int index = RandomUtil.randomInt(copyList.size()); // 生成一个随机索引
                groupOwnerStat = copyList.remove(index);
                if (robotCanFollowGroup(groupOwnerStat.getOwner())) {
                    break;
                }
                groupOwnerStat = null;
            }
            if (groupOwnerStat != null) {
                break;
            }
        }
        if (groupOwnerStat == null) {
            robotWarn("无可用的机器人建群");
            return null;
        }
        return groupOwnerStat.getOwner();
    }

    private boolean robotCanFollowGroup(String serialNo) {
        Boolean limitSwitch = Lion.getBoolean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.robot.choose.follow.limit.switch", true);
        if (Boolean.FALSE == limitSwitch) {
            log.info("机器人建群关注数限制开关未开");
            return true;
        }
        SyncInvokeResultDTO<GroupListDTO> chatRoomList = robotInfoService.getChatRoomList(ApiConstants.merchantNo, serialNo, "", TuseGroupOpenMessageTypeEnum.FOLLOWED.getCode());
        if (chatRoomList.isSuccess() && chatRoomList.getData().length >= PchatConfig.ROBOT_FOLLOW_GROUP_THREAD) {//机器人最大关注群
            log.info("无可用的机器人建群,机器人关注数超过30，robotSerialNo:{}", serialNo);
            clairvoyanceService.sendEvent(NotifyTypeEnum.ROBOT_GROUP_CRATE, "无可用的机器人建群,机器人关注数超过30，robotSerialNo:" + serialNo);
            return false;
        } else if (chatRoomList.isSuccess()) {
            return true;
        } else {
            log.info("查询群关注失败，暂定未可关注，robotSerialNo:{}", serialNo);
        }
        return true;
    }

    private void robotWarn(String msg) {
        log.warn(msg);
        clairvoyanceService.sendEvent(NotifyTypeEnum.ROBOT_GROUP_CRATE, msg);
        throw new RobotNoEnoughException(ExceptionMsg.CREATE_GROUP_NO_ROBOT_EXCEPTION);
    }

    /**
     * 限流
     *
     * @param groupOwnerStats
     * @return
     */
    private List<GroupOwnerStat> filterCurrentLimitingRobot(List<GroupOwnerStat> groupOwnerStats) {
        if (CollectionUtil.isEmpty(groupOwnerStats)) {
            return groupOwnerStats;
        }
        CreateGroupLimitSwitch limitSwitch = Lion.getBean(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.create.group.switch", CreateGroupLimitSwitch.class, new CreateGroupLimitSwitch());
        if (limitSwitch != null && Boolean.TRUE == limitSwitch.getMinLevel()) {
            groupOwnerStats = minLevelLimit(groupOwnerStats);
        }
        if (limitSwitch != null && Boolean.TRUE == limitSwitch.getMinLevel()) {
            groupOwnerStats = dayLevelLimit(groupOwnerStats);
        }

        return groupOwnerStats;
    }

    private List<GroupOwnerStat> dayLevelLimit(List<GroupOwnerStat> groupOwnerStats) {
        log.info("过滤掉一分钟创建过的机器人剩余,{}", groupOwnerStats.size());
        Map<String, Integer> everyDayRobotGroupCreated = scrmCacheService.existedEveryDayRobotGroupCreated();
        groupOwnerStats = groupOwnerStats.stream().filter(g -> {
            Integer count = everyDayRobotGroupCreated.get(g.getOwner());
            return (count == null || count < PchatConfig.getGroupConfig().getEveryRobotCreateGroupMax());
        }).collect(Collectors.toList());
        log.info("过滤掉一天创建过限制次数的剩余,{}", groupOwnerStats.size());
        return groupOwnerStats;
    }

    private List<GroupOwnerStat> minLevelLimit(List<GroupOwnerStat> groupOwnerStats) {
        Set<String> minuteInvokedSet = new HashSet<>();
        Map<String, Object> temp = scrmCacheService.obtainMethodInOneMinuteInvokeStat("robotCreateChatRoom");
        if (temp != null) {
            minuteInvokedSet.addAll(temp.keySet());
        }
        log.info("一分钟内创建过的机器人,{}", temp);
        groupOwnerStats = groupOwnerStats.stream().filter(g -> !minuteInvokedSet.contains(g.getOwner())).collect(Collectors.toList());
        return groupOwnerStats;
    }


}
