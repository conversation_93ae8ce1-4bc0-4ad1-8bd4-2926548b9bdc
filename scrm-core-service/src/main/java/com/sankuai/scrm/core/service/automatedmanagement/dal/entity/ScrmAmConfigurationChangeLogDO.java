package com.sankuai.scrm.core.service.automatedmanagement.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: scrm_a_m_configuration_change_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ScrmAmConfigurationChangeLogDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: changetype
     *   说明: 更新类型
     */
    private Byte changetype;

    /**
     *   字段: target_id
     *   说明: targetId(人群包id,流程编排id,各配置表id)
     */
    private Long targetId;

    /**
     *   字段: operator
     *   说明: 更新人
     */
    private String operator;

    /**
     *   字段: new_version
     *   说明: 更新后生效版本
     */
    private String newVersion;

    /**
     *   字段: pre_version
     *   说明: 更新前生效版本
     */
    private String preVersion;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}