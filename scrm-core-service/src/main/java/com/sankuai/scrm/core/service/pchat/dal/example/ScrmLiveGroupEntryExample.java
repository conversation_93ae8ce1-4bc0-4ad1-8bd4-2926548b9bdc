package com.sankuai.scrm.core.service.pchat.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScrmLiveGroupEntryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ScrmLiveGroupEntryExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ScrmLiveGroupEntryExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ScrmLiveGroupEntryExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ScrmLiveGroupEntryExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNull() {
            addCriterion("live_id is null");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNotNull() {
            addCriterion("live_id is not null");
            return (Criteria) this;
        }

        public Criteria andLiveIdEqualTo(String value) {
            addCriterion("live_id =", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotEqualTo(String value) {
            addCriterion("live_id <>", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThan(String value) {
            addCriterion("live_id >", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThanOrEqualTo(String value) {
            addCriterion("live_id >=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThan(String value) {
            addCriterion("live_id <", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThanOrEqualTo(String value) {
            addCriterion("live_id <=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLike(String value) {
            addCriterion("live_id like", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotLike(String value) {
            addCriterion("live_id not like", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdIn(List<String> values) {
            addCriterion("live_id in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotIn(List<String> values) {
            addCriterion("live_id not in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdBetween(String value1, String value2) {
            addCriterion("live_id between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotBetween(String value1, String value2) {
            addCriterion("live_id not between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserIsNull() {
            addCriterion("ban_risky_user is null");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserIsNotNull() {
            addCriterion("ban_risky_user is not null");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserEqualTo(Boolean value) {
            addCriterion("ban_risky_user =", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserNotEqualTo(Boolean value) {
            addCriterion("ban_risky_user <>", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserGreaterThan(Boolean value) {
            addCriterion("ban_risky_user >", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ban_risky_user >=", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserLessThan(Boolean value) {
            addCriterion("ban_risky_user <", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserLessThanOrEqualTo(Boolean value) {
            addCriterion("ban_risky_user <=", value, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserIn(List<Boolean> values) {
            addCriterion("ban_risky_user in", values, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserNotIn(List<Boolean> values) {
            addCriterion("ban_risky_user not in", values, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserBetween(Boolean value1, Boolean value2) {
            addCriterion("ban_risky_user between", value1, value2, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andBanRiskyUserNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ban_risky_user not between", value1, value2, "banRiskyUser");
            return (Criteria) this;
        }

        public Criteria andEntryCardIsNull() {
            addCriterion("entry_card is null");
            return (Criteria) this;
        }

        public Criteria andEntryCardIsNotNull() {
            addCriterion("entry_card is not null");
            return (Criteria) this;
        }

        public Criteria andEntryCardEqualTo(String value) {
            addCriterion("entry_card =", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardNotEqualTo(String value) {
            addCriterion("entry_card <>", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardGreaterThan(String value) {
            addCriterion("entry_card >", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardGreaterThanOrEqualTo(String value) {
            addCriterion("entry_card >=", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardLessThan(String value) {
            addCriterion("entry_card <", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardLessThanOrEqualTo(String value) {
            addCriterion("entry_card <=", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardLike(String value) {
            addCriterion("entry_card like", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardNotLike(String value) {
            addCriterion("entry_card not like", value, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardIn(List<String> values) {
            addCriterion("entry_card in", values, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardNotIn(List<String> values) {
            addCriterion("entry_card not in", values, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardBetween(String value1, String value2) {
            addCriterion("entry_card between", value1, value2, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryCardNotBetween(String value1, String value2) {
            addCriterion("entry_card not between", value1, value2, "entryCard");
            return (Criteria) this;
        }

        public Criteria andEntryPageIsNull() {
            addCriterion("entry_page is null");
            return (Criteria) this;
        }

        public Criteria andEntryPageIsNotNull() {
            addCriterion("entry_page is not null");
            return (Criteria) this;
        }

        public Criteria andEntryPageEqualTo(String value) {
            addCriterion("entry_page =", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageNotEqualTo(String value) {
            addCriterion("entry_page <>", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageGreaterThan(String value) {
            addCriterion("entry_page >", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageGreaterThanOrEqualTo(String value) {
            addCriterion("entry_page >=", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageLessThan(String value) {
            addCriterion("entry_page <", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageLessThanOrEqualTo(String value) {
            addCriterion("entry_page <=", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageLike(String value) {
            addCriterion("entry_page like", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageNotLike(String value) {
            addCriterion("entry_page not like", value, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageIn(List<String> values) {
            addCriterion("entry_page in", values, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageNotIn(List<String> values) {
            addCriterion("entry_page not in", values, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageBetween(String value1, String value2) {
            addCriterion("entry_page between", value1, value2, "entryPage");
            return (Criteria) this;
        }

        public Criteria andEntryPageNotBetween(String value1, String value2) {
            addCriterion("entry_page not between", value1, value2, "entryPage");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}