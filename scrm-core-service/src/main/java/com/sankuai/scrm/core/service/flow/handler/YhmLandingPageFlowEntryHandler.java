package com.sankuai.scrm.core.service.flow.handler;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.flow.dto.CorpWxFlowMaterialDTO;
import com.sankuai.dz.srcm.flow.dto.CorpWxLandingPageMaterialDTO;
import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
import com.sankuai.dz.srcm.flow.enums.PageLocationType;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.dz.srcm.flowV2.dto.MaterialDTO;
import com.sankuai.dz.srcm.flowV2.enums.AreaType;
import com.sankuai.dz.srcm.flowV2.enums.FilterType;
import com.sankuai.scrm.core.service.flow.config.XyYhmFlowEntryConfig;
import com.sankuai.scrm.core.service.flow.context.FlowEntryContext;
import com.sankuai.scrm.core.service.flow.enums.FlowChannelType;
import com.sankuai.scrm.core.service.flow.enums.ValidateType;
import com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.haima.HaimaAclService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Slf4j
@Component
public class YhmLandingPageFlowEntryHandler extends AbstractFlowEntryHandler<XyYhmFlowEntryConfig> {

    private static final String ENTRY_BANNER_KEY = "yhmorderdetailcommunity";

    @Autowired
    private HaimaAclService haimaAclService;

    @Override
    public boolean canHandle(Integer pageTypeCode) {
        return pageTypeCode != null && PageLocationType.YHM_LANDING_PAGE.getCode() == pageTypeCode;
    }

    @Override
    protected String checkParam(FlowEntryWxMaterialRequest request) {
        Integer platform = request.getPlatform();
        if (platform == null) {
            return "platform不可为空";
        }
        Long userId = request.getUserId();
        if (userId == null || userId <= 0) {
            return "userId不合法";
        }
        Integer dpCityId = request.getDpCityId();
        Integer mtCityId = request.getMtCityId();
        if (dpCityId == null && mtCityId == null) {
            return "城市信息为空";
        }
        Long shopId = request.getShopId();
        if (shopId == null) {
            return "优惠码落地页位置必须传门店信息";
        }
        return null;
    }

    @Override
    protected void preProcess(FlowEntryWxMaterialRequest request) {
        preProcessor.transformCity(request);
        preProcessor.transformCategory(request);
    }

    @Override
    protected List<ValidateType> getValidateTypeList(XyYhmFlowEntryConfig config) {
        List<ValidateType> validateTypeList = Lists.newArrayList();
        validateTypeList.add(ValidateType.ACTIVITY_TIME);
        validateTypeList.add(ValidateType.CATEGORY);
        validateTypeList.add(ValidateType.CITY);
        validateTypeList.add(ValidateType.SHOP_BLACK_LIST);
        if (!config.isAllowDuplicated()) {
            validateTypeList.add(ValidateType.NO_ADD_WX);
        }
        return validateTypeList;
    }

    @Override
    protected List<ValidateType> getValidateTypeList(EntryConfigDetailDTO config) {
        List<ValidateType> validateTypeList = Lists.newArrayList();
        validateTypeList.add(ValidateType.ACTIVITY_TIME);
        validateTypeList.add(ValidateType.CATEGORY);
        AreaType areaType = AreaType.getAreaTypeByCode(config.getScope().getAreaType());
        if (areaType != null) {
            if (AreaType.MT_FRONT_CITY.equals(areaType)) {
                validateTypeList.add(ValidateType.CITY);
            } else if (AreaType.DISTRICT_CODE.equals(areaType)) {
                validateTypeList.add(ValidateType.COUNTY);
            }
        }
        FilterType filterType = FilterType.getFilterTypeByCode(config.getFilterType());
        if (filterType != null) {
            if (FilterType.ADD_FRIEND_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_FRIEND);
            } else if (FilterType.ADD_GROUP_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_GROUP);
            } else if (FilterType.ALL_FILTER.equals(filterType)) {
                validateTypeList.add(ValidateType.NO_ADD_WX);
            }
        }
        return validateTypeList;
    }

    @Override
    protected CorpWxFlowMaterialDTO generateMaterial(FlowEntryWxMaterialRequest request, XyYhmFlowEntryConfig config) {
        CorpWxFlowMaterialDTO materialDTO = new CorpWxFlowMaterialDTO();
        materialDTO.setShowResource(true);
        materialDTO.setJumpUrl(config.getJumpUrl());
        materialDTO.setShowText(config.getMainTitle());
        materialDTO.setSubTitleText(config.getSubtitle());
        flowMaterialDomainService.generateFlowAndMaterialRelation(null, request,
                config.getCorpId(), null, FlowChannelType.YHM.getChannel());
        return materialDTO;
    }

    @Override
    protected CorpWxFlowMaterialDTO generateMaterial(FlowEntryContext<XyYhmFlowEntryConfig> context) {
        FlowEntryWxMaterialRequest request = context.getRequest();
        if (context.isUseConfigV2()) {
            EntryConfigDetailDTO configV2 = context.getConfigV2();
            MaterialDTO material = configV2.getMaterial();
            CorpWxFlowMaterialDTO materialDTO = new CorpWxFlowMaterialDTO();
            materialDTO.setShowResource(true);
            materialDTO.setShowText(material.getTitle());
            materialDTO.setSubTitleText(material.getSubtitle());
            String flowId = UUID.randomUUID().toString();
            materialDTO.setJumpUrl(flowMaterialDomainService.buildFlowJumpUrl(flowId, null, request.getCategoryId()));
            CorpWxLandingPageMaterialDTO landingPageMaterialDTO = getLandingPageMaterialDTO(context);
            flowMaterialDomainService.generateFlowAndMaterialRelation(landingPageMaterialDTO, request,
                    appConfigRepository.getCorpIdByAppId(configV2.getAppId()), flowId,
                    configV2.getExtraConfig().getSourceChannel());
            return materialDTO;
        } else {
            return generateMaterial(request, context.getConfig());
        }
    }

    @Override
    protected List<XyYhmFlowEntryConfig> queryFlowEntryConfigs(FlowEntryWxMaterialRequest request) {
        List<XyYhmFlowEntryConfig> configs = Lists.newArrayList();
        List<FlowEntryConfig> newConfigList = queryNewFlowEntryConfigs(request);
        if (CollectionUtils.isNotEmpty(newConfigList)) {
            newConfigList.forEach(config -> {
                XyYhmFlowEntryConfig xyYhmFlowEntryConfig = new XyYhmFlowEntryConfig();
                xyYhmFlowEntryConfig.setConfigV2(flowEntryConfigDomainService.buildEntryConfigDetailDTO(config));
                configs.add(xyYhmFlowEntryConfig);
            });
        }
        List<HaimaContent> haimaContents = queryHaimaConfig();
        if (CollectionUtils.isEmpty(haimaContents)) {
            return Lists.newArrayList();
        }
        haimaContents.forEach(content -> {
            try {
                XyYhmFlowEntryConfig config = new XyYhmFlowEntryConfig();
                String secondCategoryIdStr = content.getContentString("bizln_cat1_code");
                String shopBlackListStr = content.getContentString("shopBlacklist");
                int cityStrategy = content.getContentInt("cityStrategy");
                String cityWhiteListStr = content.getContentString("cityWhitelist");
                String userIdWhiteListStr = content.getContentString("useridWhitelist");
                config.setCorpId(content.getContentString("organization"));
                config.setAllowDuplicated("0".equals(content.getContentString("deduplication")));
                config.setCategoryIdList(JsonUtils.toList(secondCategoryIdStr, Long.class));
                config.setActivityName(content.getContentString("activityName"));
                config.setActivityTime(content.getContentString("activityTime"));
                config.setShopBlackList(JsonUtils.toList(shopBlackListStr, Long.class));
                if (cityStrategy == 1) {
                    config.setMtCityIdList(JsonUtils.toList(cityWhiteListStr, Integer.class));
                }
                config.setMainTitle(content.getContentString("mainTitle"));
                config.setSubtitle(content.getContentString("subTitlecopy"));
                config.setJumpUrl(content.getContentString("hyperlink"));
                config.setUserIdWhiteList(JsonUtils.toList(userIdWhiteListStr, Long.class));
                configs.add(config);
            } catch (Exception e) {
                log.error("YhmLandingPageFlowEntryHandler.queryFlowEntryConfigs has exception, content:{}", content, e);
            }
        });
        return configs;
    }

    private List<HaimaContent> queryHaimaConfig() {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(ENTRY_BANNER_KEY);
        return haimaAclService.getInnerContentList(request);
    }
}
