package com.sankuai.scrm.core.service.pchat.mq.producer;

import lombok.Getter;

@Getter
public enum AsyncTaskCodeEnum {

    GROUP_MSG_SEND_ASYNC("groupMsgSend.async", "群发消息-异步处理"),

    ;

    private final String code;

    private final String desc;

    AsyncTaskCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AsyncTaskCodeEnum fromCode(String code) {
        for (AsyncTaskCodeEnum enumValue : AsyncTaskCodeEnum.values()) {
            if (enumValue.code.equals(code)) {
                return enumValue;
            }
        }
        return null;
    }

}
