package com.sankuai.scrm.core.service.pchat.service;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveUserBindTaskRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveUserIntentionService;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.pchat.request.scrm.ScrmGroupMemberMatchQueryRequest;
import com.sankuai.dz.srcm.pchat.request.scrm.ScrmGroupMemberMatchRequest;
import com.sankuai.dz.srcm.pchat.response.scrm.ScrmGroupMemberMatchInfoResponse;
import com.sankuai.dz.srcm.pchat.service.ScrmGroupMemberMatchService;
import com.sankuai.scrm.core.service.pchat.acl.AutoCheckPermission;
import com.sankuai.scrm.core.service.pchat.acl.CheckPermissionUtil;
import com.sankuai.scrm.core.service.pchat.acl.authorization.enums.AuthorityCodeEnum;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxUserEnterMiniProgramLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxUserEnterMiniProgramLogExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMemberInfoEntityMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserEnterMiniProgramLogCustomMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxUserEnterMiniProgramLogMapper;
import com.sankuai.scrm.core.service.pchat.domain.ScrmPersonalWxGroupManageDomainService;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.pchat.utils.SqlUtil;
import com.sankuai.scrm.core.service.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 群成员与小程序手动匹配
 * <AUTHOR>
 * @Create On 2024/8/21 17:51
 * @Version v1.0.0
 */
@Slf4j
@MdpPigeonServer
public class ScrmGroupMemberMatchServiceImpl implements ScrmGroupMemberMatchService {
    @Resource
    private ScrmPersonalWxGroupManageDomainService personalWxGroupManageDomainService;
    @Resource
    private ScrmPersonalWxUserEnterMiniProgramLogMapper userEnterMiniProgramLogMapper;
    @Resource
    private ScrmPersonalWxUserEnterMiniProgramLogCustomMapper userEnterMiniProgramLogCustomMapper;
    @Resource
    private ScrmPersonalWxGroupMemberInfoEntityMapper personalWxGroupMemberInfoEntityMapper;
    @Resource
    private ScrmIdentifyUserService identifyUserService;
    @Resource
    private PrivateLiveUserIntentionService privateLiveUserIntentionService;
    @Resource
    private PrivateLiveUserIntentionService newPrivateLiveUserIntentionService;
    @MdpConfig("consultant.service.migration.switch:false")
    private Boolean useNewAppkey;
    @Resource
    private PrivateLiveConsultantVerifyService privateLiveConsultantVerifyService;
    @Resource
    private PrivateLiveConsultantVerifyService newPrivateLiveConsultantVerifyService;


    @Override
    @AutoCheckPermission(authorityCodeEnum = AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION)
    public PageRemoteResponse<ScrmGroupMemberMatchInfoResponse> queryLiveMinipList(ScrmGroupMemberMatchQueryRequest request) {
        request.validParam();
        log.info("查询小程序数据列表：{}", JsonUtils.toStr(request));
        PagerList<ScrmPersonalWxUserEnterMiniProgramLog> pagerList = queryLatestUserEnterMiniProgramLogByNickname(request.getKeyword(), request.getWebcastId(), request.getPageNo(), request.getPageSize());
        if (pagerList.getTotal() == 0) {
            return PageRemoteResponse.success(new ArrayList<>(), 0, true);
        }
        List<ScrmGroupMemberMatchInfoResponse> responses = pagerList.getData().stream().map(this::fillMatchInfoResponse).collect(Collectors.toList());
        return PageRemoteResponse.success(responses, pagerList.getTotal(), responses.size() < request.getPageSize());
    }


    /**
     * 如果小程序用户为咨询师，或者已经有归属，且归属咨询师和微信用户不一样，不给匹配。其余情况都可以匹配且回刷
     *
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<Boolean> groupMemberMatchMinip(ScrmGroupMemberMatchRequest request) {
        request.validParam();
        log.info("手工匹配小程序数据:{}", JsonUtils.toStr(request));
        ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity = personalWxGroupManageDomainService.queryGroupMemberById(request.getMemberId());
        if (groupMemberInfoEntity == null) {
            return RemoteResponse.fail("匹配失败，群成员不存在");
        }
        if (StringUtils.isNotBlank(groupMemberInfoEntity.getUnionId())) {
            return RemoteResponse.fail("匹配失败，群成员已匹配过");
        }
        ScrmPersonalWxUserEnterMiniProgramLog scrmPersonalWxUserEnterMiniProgramLog = queryUserEnterMiniProgramLogById(request.getMinipId());
        if (scrmPersonalWxUserEnterMiniProgramLog == null) {
            return RemoteResponse.fail("匹配失败，小程序数据不存在");
        }
        ScrmPersonalWxGroupMemberInfoEntity existedGroupMemberByUnionId = queryExistedGroupMemberByUnionId(scrmPersonalWxUserEnterMiniProgramLog.getUnionId());
        if (existedGroupMemberByUnionId != null && existedGroupMemberByUnionId.getWxId() != null && !existedGroupMemberByUnionId.getWxId().equals(groupMemberInfoEntity.getWxId())) {
            return RemoteResponse.fail("匹配失败，小程序用户已匹配");
        }
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(groupMemberInfoEntity.getGroupId());
        if (groupInfoEntity == null) {
            return RemoteResponse.fail("匹配失败，群数据不存在");
        }
        if (!StringUtils.equals(groupInfoEntity.getProjectId(), request.getWebcastId())) {
            return RemoteResponse.fail("匹配失败，直播数据不匹配");
        }
        if (StringUtils.isNotBlank(groupInfoEntity.getProjectId())) {
            CheckPermissionUtil.checkUserPermission(groupInfoEntity.getProjectId(), AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        }

        if (isConsultant(scrmPersonalWxUserEnterMiniProgramLog.getUnionId(), request.getWebcastId())) {
            return RemoteResponse.fail("匹配失败，小程序人员是咨询师");
        }
        Long consultantTaskId = getConsultantTaskId(groupMemberInfoEntity.getWxId(), null, request.getWebcastId());
        Long miniConsultantTaskId = getConsultantTaskId(null, scrmPersonalWxUserEnterMiniProgramLog.getUnionId(), request.getWebcastId());

        if (consultantTaskId != null && miniConsultantTaskId != null && miniConsultantTaskId != 0L && consultantTaskId != 0L && !consultantTaskId.equals(miniConsultantTaskId)) {
            return RemoteResponse.fail("匹配失败，该用户有已归属，请联系工作人员");
        }

        groupMemberInfoEntity.setUnionId(scrmPersonalWxUserEnterMiniProgramLog.getUnionId());
        groupMemberInfoEntity.setUserId(scrmPersonalWxUserEnterMiniProgramLog.getMtUserId());
        identifyUserService.updateGroupMemberUnionId(groupMemberInfoEntity);
        log.info("手工匹配小程序数据成功:memberId:{},wxId:{},unionId:{},userId:{}", groupMemberInfoEntity.getId(), groupMemberInfoEntity.getWxId(), scrmPersonalWxUserEnterMiniProgramLog.getUnionId(), scrmPersonalWxUserEnterMiniProgramLog.getMtUserId());
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<ScrmGroupMemberMatchInfoResponse> groupMemberMatchInfo(ScrmGroupMemberMatchRequest request) {
        Long memberId = request.getMemberId();
        if (memberId == null) {
            return RemoteResponse.fail("群成员id不能为空");
        }
        ScrmPersonalWxGroupMemberInfoEntity groupMemberInfoEntity = personalWxGroupManageDomainService.queryGroupMemberById(memberId);
        if (groupMemberInfoEntity == null) {
            return RemoteResponse.fail("群成员不存在");
        }
        ScrmPersonalWxGroupInfoEntity groupInfoEntity = personalWxGroupManageDomainService.queryGroupByChatroomSerialNo(groupMemberInfoEntity.getGroupId());
        if (groupInfoEntity == null) {
            return RemoteResponse.fail("群数据不存在");
        }
        if (StringUtils.isNotBlank(groupInfoEntity.getProjectId())) {
            CheckPermissionUtil.checkUserPermission(groupInfoEntity.getProjectId(), AuthorityCodeEnum.LIVE_ROOM_OPERATION_PERMISSION);
        }
        if (StringUtils.isBlank(groupMemberInfoEntity.getUnionId())) {
            return RemoteResponse.fail("群成员未匹配过");
        }
        ScrmPersonalWxUserEnterMiniProgramLog userEnterMiniProgramLog = queryUserEnterMiniProgramLogByUnionId(groupMemberInfoEntity.getUnionId());
        if (userEnterMiniProgramLog == null) {
            return RemoteResponse.fail("小程序用户不存在");
        }
        return RemoteResponse.success(fillMatchInfoResponse(userEnterMiniProgramLog));
    }

    private ScrmGroupMemberMatchInfoResponse fillMatchInfoResponse(ScrmPersonalWxUserEnterMiniProgramLog g) {
        ScrmGroupMemberMatchInfoResponse response = new ScrmGroupMemberMatchInfoResponse();
        response.setUnionId(g.getUnionId());
        response.setNickname(g.getNickname());
        response.setAvatar(g.getAvatarUrl());
        response.setMinipId(g.getId());
        response.setUpdateTime(g.getUpdateTime() != null ? Long.valueOf(g.getUpdateTime().getTime()) : (g.getAddTime() != null ? g.getAddTime().getTime() : null));
        return response;
    }

    private ScrmPersonalWxUserEnterMiniProgramLog queryUserEnterMiniProgramLogById(Long id) {
        return userEnterMiniProgramLogMapper.selectByPrimaryKey(id);
    }

    private ScrmPersonalWxGroupMemberInfoEntity queryExistedGroupMemberByUnionId(String unionId) {
        ScrmPersonalWxGroupMemberInfoEntityExample example = new ScrmPersonalWxGroupMemberInfoEntityExample();
        example.createCriteria().andUnionIdEqualTo(unionId);
        example.limit(1);
        List<ScrmPersonalWxGroupMemberInfoEntity> groupMemberInfoEntities = personalWxGroupMemberInfoEntityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(groupMemberInfoEntities)) {
            return null;
        }
        return groupMemberInfoEntities.get(0);
    }

    private PagerList<ScrmPersonalWxUserEnterMiniProgramLog> queryUserEnterMiniProgramLog(String keyword, String liveId, Integer pageNo, Integer pageSize) {
        ScrmPersonalWxUserEnterMiniProgramLogExample example = new ScrmPersonalWxUserEnterMiniProgramLogExample();
        ScrmPersonalWxUserEnterMiniProgramLogExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(keyword)) {
            criteria.andNicknameLike(SqlUtil.like(keyword));
        }
        criteria.andUnionIdIsNotNull();
        criteria.andAvatarUrlIsNotNull();
        criteria.andLiveIdEqualTo(liveId);

        long total = userEnterMiniProgramLogMapper.countByExample(example);
        if (total == 0) {
            return PagerList.empty();
        }
        example.page(pageNo, pageSize);
        example.setOrderByClause("update_time desc");
        return PagerList.of(total, userEnterMiniProgramLogMapper.selectByExample(example));
    }

    public PagerList<ScrmPersonalWxUserEnterMiniProgramLog> queryLatestUserEnterMiniProgramLogByNickname(String keyword, String webcastId, Integer pageNo, Integer pageSize) {
        if (StringUtils.isEmpty(keyword)) {
            return PagerList.empty();
        }
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.scrm.core.identity.user.days.offset", -7)));
        keyword = SqlUtil.like(keyword);
        Long total = userEnterMiniProgramLogCustomMapper.countLatestUserEnterMiniProgramLogByNickname(keyword, startTime);
        if (total == null || total == 0L) {
            return PagerList.empty();
        }
        List<String> openIds = userEnterMiniProgramLogCustomMapper.queryLatestUserEnterMiniProgramLogByNickname(keyword, startTime, pageSize, pageNo * pageSize);
        if (CollectionUtils.isEmpty(openIds)) {
            return PagerList.empty();
        }
        List<ScrmPersonalWxUserEnterMiniProgramLog> programLogs = queryUserEnterMiniProgramLogByOpenIds(startTime, openIds);

        return PagerList.of(total, fillUserEnterMiniProgramLog(pageSize, programLogs));
    }

    private List<ScrmPersonalWxUserEnterMiniProgramLog> fillUserEnterMiniProgramLog(Integer pageSize, List<ScrmPersonalWxUserEnterMiniProgramLog> programLogs) {
        Set<String> dupOpenIds = new HashSet<>();
        List<ScrmPersonalWxUserEnterMiniProgramLog> result = new ArrayList<>();
        programLogs.forEach(p -> {
            if (dupOpenIds.contains(p.getOpenId())) {
                return;
            }
            if (result.size() > pageSize) {
                return;
            }
            result.add(p);
            dupOpenIds.add(p.getOpenId());
        });
        return result;
    }

    private List<ScrmPersonalWxUserEnterMiniProgramLog> queryUserEnterMiniProgramLogByOpenIds(Date startTime, List<String> openIds) {
        ScrmPersonalWxUserEnterMiniProgramLogExample example = new ScrmPersonalWxUserEnterMiniProgramLogExample();
        ScrmPersonalWxUserEnterMiniProgramLogExample.Criteria criteria = example.createCriteria();
        criteria.andOpenIdIn(openIds);
        criteria.andAvatarUrlIsNotNull();
        criteria.andUnionIdIsNotNull();
        criteria.andUpdateTimeGreaterThanOrEqualTo(startTime);
        example.setOrderByClause("update_time desc");
        List<ScrmPersonalWxUserEnterMiniProgramLog> programLogs = userEnterMiniProgramLogMapper.selectByExample(example);
        return programLogs;
    }

    private ScrmPersonalWxUserEnterMiniProgramLog queryUserEnterMiniProgramLogByUnionId(String unionId) {

        ScrmPersonalWxUserEnterMiniProgramLogExample example = new ScrmPersonalWxUserEnterMiniProgramLogExample();
        ScrmPersonalWxUserEnterMiniProgramLogExample.Criteria criteria = example.createCriteria();
        criteria.andUnionIdEqualTo(unionId);

        example.setOrderByClause("update_time desc");
        example.limit(1);
        List<ScrmPersonalWxUserEnterMiniProgramLog> miniProgramLogs = userEnterMiniProgramLogMapper.selectByExample(example);

        return CollectionUtils.isEmpty(miniProgramLogs) ? null : miniProgramLogs.get(0);
    }

    private Long getConsultantTaskId(String wxId, String unionId, String projectId) {
        try {
            PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
            request.setLiveId(projectId);
            request.setWxId(wxId);
            request.setUnionId(unionId);
            RemoteResponse<Long> remoteResponse = useNewAppkey ? newPrivateLiveUserIntentionService.queryUserBindTask(request) : privateLiveUserIntentionService.queryUserBindTask(request);
            log.info("getConsultantTaskId result is {}", remoteResponse);
            if (remoteResponse == null || remoteResponse.getData() == null) {
                return 0L;
            }
            return remoteResponse.getData();
        } catch (Exception e) {
            log.error("getConsultantTaskId has exception", e);
            return 0L;
        }
    }

    /**
     * 判断当前用户是否是咨询师
     *
     * @param unionId
     * @param webcastId
     * @return
     */
    private boolean isConsultant(String unionId, String webcastId) {
        try {
            RemoteResponse<Long> remoteResponse = useNewAppkey ? newPrivateLiveConsultantVerifyService.checkByUnionIdAndLiveId(unionId, webcastId) : privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(unionId, webcastId);
            log.info("isConsultant result is {}", remoteResponse);
            if (remoteResponse == null || remoteResponse.getData() == null) {
                return false;
            }
            return remoteResponse.getData() != null && remoteResponse.getData() != 0L;
        } catch (Exception e) {
            log.error("isConsultant has exception", e);
            return false;
        }
    }
}
