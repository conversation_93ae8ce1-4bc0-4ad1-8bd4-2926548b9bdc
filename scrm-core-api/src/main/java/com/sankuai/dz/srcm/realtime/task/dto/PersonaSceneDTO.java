package com.sankuai.dz.srcm.realtime.task.dto;

import com.sankuai.dz.srcm.realtime.task.enums.SceneType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PersonaSceneDTO implements Serializable {
    private String sceneDesc;
    private Integer sceneId;
    private String sceneName;
    /**
     * 场景类型码
     * UNKNOWN(0, "未知"),
     * PERSONA(1,"persona实时感知")
     */
    private Integer sceneTypeCode;
    private String currentEffectiveVersion;
}
