package com.sankuai.scrm.core.service.infrastructure.config;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.List;


/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/2/23 17:32
 * @Version v1.0.0
 */

@Slf4j
@Configuration
public class NotifyConfig {
    public static Data config;
    private static String lionConfigKey = "com.sankuai.medicalcosmetology.scrm.core.pchat.notify";

    static {
        lionConfigChange();
        Lion.getConfigRepository(Environment.getAppName()).addConfigListener(lionConfigKey, configEvent -> lionConfigChange());
    }

    static void lionConfigChange() {
        log.info("加载个微notify后台配置");
        config = Lion.getBean(Environment.getAppName(), lionConfig<PERSON>ey, Data.class);

    }

    @lombok.Data
    public static class Data {
        private List<GroupConfig> groupConfig;
        private List<String> notifyMisId;
    }

    @lombok.Data
    public static class GroupConfig {
        private Integer code;
        private Long groupId;
        private String remark;
    }


}
