package com.sankuai.scrm.core.service.pchat.domain;

import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotInfo;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxRobotLoginLog;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxRobotLoginLogExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotInfoMapper;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxRobotLoginLogMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmPersonalWxRobotLoginDomainServiceInsertHandleLogTest {

    @InjectMocks
    private ScrmPersonalWxRobotLoginDomainService scrmPersonalWxRobotLoginDomainService;

    @Mock(lenient = true)
    private ScrmPersonalWxRobotLoginLogMapper wxRobotLoginLogMapper;

    @Mock(lenient = true)
    private ScrmPersonalWxRobotInfoMapper robotInfoMapper;

    @BeforeEach
    public void setUp() {
        when(wxRobotLoginLogMapper.selectByExample(any(ScrmPersonalWxRobotLoginLogExample.class))).thenReturn(Collections.emptyList());
    }

    /**
     * 测试insertHandleLog方法，当httpSerial对应的ScrmPersonalWxRobotLoginLog存在于数据库中时，方法直接返回
     */
    @Test
    public void testInsertHandleLog_ExistInDB() {
        // arrange
        String httpSerial = "testHttpSerial";
        String robotSerial = "testRobotSerial";
        String handlerMis = "testHandlerMis";
        when(wxRobotLoginLogMapper.selectByExample(any(ScrmPersonalWxRobotLoginLogExample.class))).thenReturn(Collections.singletonList(new ScrmPersonalWxRobotLoginLog()));
        // act
        scrmPersonalWxRobotLoginDomainService.insertHandleLog(httpSerial, robotSerial, handlerMis, isNull());
        // assert
        verify(wxRobotLoginLogMapper, times(1)).selectByExample(any(ScrmPersonalWxRobotLoginLogExample.class));
        verify(wxRobotLoginLogMapper, times(0)).insert(any(ScrmPersonalWxRobotLoginLog.class));
    }

    /**
     * 测试insertHandleLog方法，当httpSerial对应的ScrmPersonalWxRobotLoginLog不存在于数据库中时，方法执行插入操作
     */
    @Test
    public void testInsertHandleLog_NotExistInDB() {
        // arrange
        String httpSerial = "testHttpSerial";
        String robotSerial = "testRobotSerial";
        String handlerMis = "testHandlerMis";
        // act
        scrmPersonalWxRobotLoginDomainService.insertHandleLog(httpSerial, robotSerial, handlerMis, isNull());
        // assert
        verify(wxRobotLoginLogMapper, times(1)).selectByExample(any(ScrmPersonalWxRobotLoginLogExample.class));
        verify(wxRobotLoginLogMapper, times(1)).insert(any(ScrmPersonalWxRobotLoginLog.class));
    }

    // as it directly contradicts the method's current implementation.
    @Test
    public void testGetRobotListAllNotNull() throws Throwable {
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxRobotInfo> result = scrmPersonalWxRobotLoginDomainService.getRobotList("clusterId", 1, 1, 1, 10);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetRobotListValidIsZero() throws Throwable {
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxRobotInfo> result = scrmPersonalWxRobotLoginDomainService.getRobotList("clusterId", 0, 1, 1, 10);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetRobotListOnlineIsZero() throws Throwable {
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxRobotInfo> result = scrmPersonalWxRobotLoginDomainService.getRobotList("clusterId", 1, 0, 1, 10);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetRobotListReturnEmptyList() throws Throwable {
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxRobotInfo> result = scrmPersonalWxRobotLoginDomainService.getRobotList("clusterId", 1, 1, 1, 10);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetRobotListReturnNonEmptyList() throws Throwable {
        when(robotInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmPersonalWxRobotInfo()));
        List<ScrmPersonalWxRobotInfo> result = scrmPersonalWxRobotLoginDomainService.getRobotList("clusterId", 1, 1, 1, 10);
        assertEquals(1, result.size());
    }
}
