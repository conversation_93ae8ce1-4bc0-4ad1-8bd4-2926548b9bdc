package com.sankuai.scrm.core.service.pchat.mq.producer;

import cn.hutool.core.lang.Assert;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.scrm.core.service.BaseMockTest;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Properties;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/5 10:24
 * @Version v1.0.0
 */
public class ScrmTuseMsg4501SlowProducerTest extends BaseMockTest {
    @InjectMocks
    private ScrmTuseMsg4501SlowProducer scrmTuseMsg4501SlowProducer;

    @Test
    public void testAfterPropertiesSet() {
        boolean success = mqTest("daozong", "com.sankuai.medicalcosmetology.scrm.core", "scrm.pchat.tuse.callback.msg.4501.slow");
        Assert.isTrue(success);
    }


    public boolean mqTest(String namespace, String mafkaClientAppkey, String topic) {
        boolean success = true;
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, namespace);
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, mafkaClientAppkey);
        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        try (MockedStatic<MafkaClient> mockStatic = Mockito.mockStatic(MafkaClient.class)) {
            IProducerProcessor<?, String> producer = Mockito.mock(IProducerProcessor.class);
            mockStatic.when(() -> MafkaClient.buildProduceFactory(properties, topic)).thenReturn(producer);

            try {
                scrmTuseMsg4501SlowProducer.afterPropertiesSet();
            } catch (Exception e) {
                success = false;
            }
        }
        return success;
    }
}
