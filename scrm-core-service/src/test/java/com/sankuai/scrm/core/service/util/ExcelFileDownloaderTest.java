package com.sankuai.scrm.core.service.util;

import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExcelFileDownloaderTest {

    private File mockFile;

    private File tempFile;

    @After
    public void cleanup() {
        if (tempFile != null && tempFile.exists()) {
            tempFile.delete();
        }
    }

    /**
     * Creates a temporary file with the given content
     */
    private File createTempFile(byte[] content) throws IOException {
        File tempFile = File.createTempFile("test", ".tmp");
        tempFile.deleteOnExit();
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(content);
        }
        return tempFile;
    }

    /**
     * Creates a mock InputStream that properly supports mark and reset operations
     */
    private InputStream createMarkSupportedInputStream(byte[] data) {
        return new BufferedInputStream(new ByteArrayInputStream(data)) {

            @Override
            public synchronized void mark(int readlimit) {
                super.mark(readlimit);
            }

            @Override
            public synchronized void reset() throws IOException {
                super.reset();
            }

            @Override
            public boolean markSupported() {
                return true;
            }
        };
    }

    /**
     * Test case for XLS file format
     */
    @Test
    public void testIsExcelFile_XlsFile() throws Throwable {
        // XLS file magic number
        byte[] xlsContent = new byte[] { (byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0, (byte) 0xA1, (byte) 0xB1, (byte) 0x1A, (byte) 0xE1 };
        File file = createTempFile(xlsContent);
        boolean result = ExcelFileDownloader.isExcelFile(file);
        assertTrue(result);
    }

    /**
     * Test case for XLSX file format
     */
    @Test
    public void testIsExcelFile_XlsxFile() throws Throwable {
        // XLSX file magic number (ZIP format)
        byte[] xlsxContent = new byte[] { 0x50, 0x4B, 0x03, 0x04, 0x14, 0x00, 0x06, 0x00 };
        File file = createTempFile(xlsxContent);
        boolean result = ExcelFileDownloader.isExcelFile(file);
        assertTrue(result);
    }

    /**
     * Test case for file that contains both XLS and XLSX signatures
     */
    @Test
    public void testIsExcelFile_BothXlsAndXlsx() throws Throwable {
        // Using XLS format for this test since a file can't actually be both
        byte[] xlsContent = new byte[] { (byte) 0xD0, (byte) 0xCF, (byte) 0x11, (byte) 0xE0, (byte) 0xA1, (byte) 0xB1, (byte) 0x1A, (byte) 0xE1 };
        File file = createTempFile(xlsContent);
        boolean result = ExcelFileDownloader.isExcelFile(file);
        assertTrue(result);
    }

    /**
     * Test case for non-Excel file
     */
    @Test
    public void testIsExcelFile_NotExcelFile() throws Throwable {
        // Create a file with non-Excel content
        byte[] nonExcelContent = "This is not an Excel file".getBytes();
        File file = createTempFile(nonExcelContent);
        boolean result = ExcelFileDownloader.isExcelFile(file);
        assertFalse(result);
    }

    /**
     * Test case for null file input
     */
    @Test(expected = NullPointerException.class)
    public void testIsExcelFile_NullFile() throws Throwable {
        ExcelFileDownloader.isExcelFile(null);
    }

    /**
     * Test handling of non-success HTTP status code
     */
    @Test(expected = IOException.class)
    public void testDownloadExcel_NonSuccessStatusCode() throws Throwable {
        String url = "http://test.com/invalid.xlsx";
        CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);
        StatusLine statusLine = mock(StatusLine.class);
        try (MockedStatic<HttpClients> httpClientsMock = Mockito.mockStatic(HttpClients.class)) {
            httpClientsMock.when(HttpClients::createDefault).thenReturn(httpClient);
            when(httpClient.execute(any(HttpGet.class))).thenReturn(response);
            when(response.getStatusLine()).thenReturn(statusLine);
            when(statusLine.getStatusCode()).thenReturn(404);
            ExcelFileDownloader.downloadExcel(url);
        }
    }

    /**
     * Test handling of null HTTP entity
     */
    @Test(expected = IOException.class)
    public void testDownloadExcel_NullEntity() throws Throwable {
        String url = "http://test.com/empty.xlsx";
        CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
        CloseableHttpResponse response = mock(CloseableHttpResponse.class);
        StatusLine statusLine = mock(StatusLine.class);
        try (MockedStatic<HttpClients> httpClientsMock = Mockito.mockStatic(HttpClients.class)) {
            httpClientsMock.when(HttpClients::createDefault).thenReturn(httpClient);
            when(httpClient.execute(any(HttpGet.class))).thenReturn(response);
            when(response.getStatusLine()).thenReturn(statusLine);
            when(statusLine.getStatusCode()).thenReturn(200);
            when(response.getEntity()).thenReturn(null);
            ExcelFileDownloader.downloadExcel(url);
        }
    }

    /**
     * Test handling of network exceptions
     */
    @Test(expected = IOException.class)
    public void testDownloadExcel_NetworkException() throws Throwable {
        String url = "http://test.com/error.xlsx";
        CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
        try (MockedStatic<HttpClients> httpClientsMock = Mockito.mockStatic(HttpClients.class)) {
            httpClientsMock.when(HttpClients::createDefault).thenReturn(httpClient);
            when(httpClient.execute(any(HttpGet.class))).thenThrow(new IOException("Network error"));
            ExcelFileDownloader.downloadExcel(url);
        }
    }

    /**
     * Test handling of invalid URLs
     */
    @Test(expected = org.apache.http.client.ClientProtocolException.class)
    public void testDownloadExcel_InvalidUrl() throws Throwable {
        String url = "invalid-url";
        ExcelFileDownloader.downloadExcel(url);
    }

}
